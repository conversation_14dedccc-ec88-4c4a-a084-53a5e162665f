#!/bin/sh

#PBS -q normal
#PBS -l select=1:ngpus=1
#PBS -l walltime=12:00:00
###PBS -P 12004256
#PBS -P personal-s240076
#PBS -N ss-gcnn-train
#PBS -j oe

# 进入工作目录
cd $PBS_O_WORKDIR || exit $?

# 记录作业开始时间和节点信息
echo "Job start at: $(date)"
echo "Running on node: $(hostname)"
echo "GPU Information:"
nvidia-smi

# 加载必要的模块
module load singularity

echo "==================== Shastry-Sutherland GCNN 训练 ===================="

# ==================== 系统参数配置 ====================
# 晶格尺寸
L_VALUES="4"

# J2耦合强度范围
J2_VALUES="0.00"

# J1耦合强度
J1_VALUES="0.07"

echo "系统参数配置:"
echo "L values: $L_VALUES"
echo "J2 values: $J2_VALUES"
echo "J1 values: $J1_VALUES"

# ==================== 训练参数配置 ====================
LEARNING_RATE=0.015            # 学习率
N_SAMPLES=16384                 # 样本数量
CHUNK_SIZE=2048                # 批处理大小

# 退火参数（自动计算总迭代次数）
N_CYCLES=3                      # 退火周期数
INITIAL_PERIOD=150              # 初始周期长度
PERIOD_MULT=2.0                 # 周期倍数
MAX_TEMPERATURE=1.0             # 最大温度
MIN_TEMPERATURE=0.0            # 最小温度

# 模型参数
NUM_FEATURES=4                  # 特征维度
NUM_LAYERS=4                    # 编码器层数

# 其他参数
DIAG_SHIFT=0.20                 # 对角线位移
GRAD_CLIP=1.0                   # 梯度裁剪

echo "训练参数配置:"
echo "Learning rate: $LEARNING_RATE"
echo "Samples: $N_SAMPLES"
echo "Annealing cycles: $N_CYCLES"
echo "Initial period: $INITIAL_PERIOD"
echo "Period multiplier: $PERIOD_MULT"
echo "Temperature range: $MIN_TEMPERATURE - $MAX_TEMPERATURE"

# ==================== Checkpoint 配置 ====================
# 是否启用checkpoint保存 (true/false)
ENABLE_CHECKPOINT=true          # 是否启用checkpoint保存

# checkpoint保存间隔（迭代次数）
CHECKPOINT_INTERVAL=500          # checkpoint保存间隔（迭代次数）

# 从checkpoint恢复训练的路径（可选）
# 示例：results/L=4/J2=0.50/J1=1.00/training/checkpoints/checkpoint_iter_001000.pkl
# 留空表示从头开始训练
RESUME_FROM_CHECKPOINT="results/L=4/J2=0.00/J1=0.06/training/checkpoints/final_GCNN.pkl"       # 从checkpoint恢复训练的路径（可选）

# 是否保留历史checkpoint (true/false)
KEEP_CHECKPOINT_HISTORY=true    # 是否保留历史checkpoint (true/false)

# 构建checkpoint相关的命令行参数
CHECKPOINT_ARGS=""              # 构建checkpoint相关的命令行参数
if [ "$ENABLE_CHECKPOINT" = "true" ]; then
    CHECKPOINT_ARGS="--enable_checkpoint --save_interval $CHECKPOINT_INTERVAL"
    
    if [ "$KEEP_CHECKPOINT_HISTORY" = "true" ]; then
        CHECKPOINT_ARGS="$CHECKPOINT_ARGS --keep_history"
    fi
    
    if [ -n "$RESUME_FROM_CHECKPOINT" ]; then
        echo "Resuming from checkpoint: $RESUME_FROM_CHECKPOINT"
        CHECKPOINT_ARGS="$CHECKPOINT_ARGS --resume_from $RESUME_FROM_CHECKPOINT"
    fi
    
    echo "Checkpoint enabled with interval: $CHECKPOINT_INTERVAL"
else
    echo "Checkpoint disabled"
fi

echo "==================== 开始批量训练 ===================="

# 并行任务最大数量
max_tasks=1
current_tasks=0

for L in $L_VALUES; do
    for J2 in $J2_VALUES; do
        for J1 in $J1_VALUES; do
            echo "Starting computation L=$L, J2=$J2, J1=$J1 at: $(date)"

            # 提交任务到后台运行
            singularity exec --nv -B /scratch,/app \
                /home/<USER>/ntu/s240076/Repositories/Jupyter_server/config/netket.sif \
                python scripts/train.py $L $J2 $J1 \
                --learning_rate $LEARNING_RATE \
                --n_samples $N_SAMPLES \
                --chunk_size $CHUNK_SIZE \
                --n_cycles $N_CYCLES \
                --initial_period $INITIAL_PERIOD \
                --period_mult $PERIOD_MULT \
                --max_temperature $MAX_TEMPERATURE \
                --min_temperature $MIN_TEMPERATURE \
                --num_features $NUM_FEATURES \
                --num_layers $NUM_LAYERS \
                --diag_shift $DIAG_SHIFT \
                --grad_clip $GRAD_CLIP \
                $CHECKPOINT_ARGS &

            current_tasks=$((current_tasks + 1))

            # 如果达到最大并行任务数，则等待这批任务全部结束，再继续提交
            if [ $current_tasks -ge $max_tasks ]; then
                wait
                current_tasks=0
            fi

            echo "Submitted job L=$L, J2=$J2, J1=$J1 at: $(date)"
        done
    done
done

# 等待剩余任务
wait

echo "==================== 训练完成 ===================="
echo "所有训练任务已完成！"
echo "Job finished at: $(date)"
