"""
量子态生成和管理模块，提供创建和重建量子态的功能。
"""

import pickle
import jax
import jax.numpy as jnp
import netket as nk
import numpy as np
from netket.utils.group import PointGroup, Identity
from netket.utils.group.planar import rotation, glide_group

class ModelConfig:
    # 模型参数
    num_features = 4      # 特征维度
    num_layers = 4        # 编码器层数

from src.physics.shastry_sutherland import (
    shastry_sutherland_lattice, 
    shastry_sutherland_hamiltonian, 
    shastry_sutherland_all_symmetries
)

def create_quantum_state(L, J2, J1, n_samples=None, n_discard=None, chunk_size=None):
    """
    创建Shastry-Sutherland模型的量子态
    
    参数:
    L: 晶格大小
    J2: J2耦合强度
    J1: J1耦合强度
    n_samples: 采样数量，如果为None则使用默认值
    n_discard: 丢弃的样本数，如果为None则使用默认值
    chunk_size: 批处理大小，如果为None则使用默认值
    
    返回:
    vqs: 变分量子态
    lattice: 晶格
    hilbert: 希尔伯特空间
    hamiltonian: 哈密顿量
    """
    # 使用默认值（这些值需要从外部传入或设置为合理的默认值）
    if n_samples is None:
        n_samples = 2**12  # 默认样本数量
    if n_discard is None:
        n_discard = 0      # 默认丢弃样本数
    if chunk_size is None:
        chunk_size = 2**10 # 默认批处理大小
    
    # 计算Q参数
    Q = 1.00 - J2
    
    # 创建晶格和哈密顿量
    lattice = shastry_sutherland_lattice(L, L)
    hamiltonian, hilbert = shastry_sutherland_hamiltonian(lattice, J1, J2, Q)
    
    distance = lattice.distances()
    max_distance = np.max(distance)

    # 创建采样器
    sampler = nk.sampler.MetropolisExchange(
        hilbert=hilbert, 
        graph=lattice, 
        n_chains=n_samples, 
        d_max=max_distance
    )
    
    # 定义局部掩码
    mask = jnp.zeros(lattice.n_nodes, dtype=bool)
    for i in range(lattice.n_nodes):
        mask = mask.at[i].set(True)
    
    # 获取对称性
    symmetries = shastry_sutherland_all_symmetries(lattice)
    
    # 设置动量和表示
    nc = 4
    cyclic_4 = PointGroup(
        [Identity()] + [rotation((360 / nc) * i) for i in range(1, nc)],
        ndim=2,
    )
    C4v = glide_group(trans=(1, 1), origin=(0, 0)) @ cyclic_4
    sgb = lattice.space_group_builder(point_group=C4v)
    momentum = [0.0, 0.0]
    chi = sgb.space_group_irreps(momentum)[0]
    
    # 创建GCNN模型
    model = nk.models.GCNN(
        symmetries=symmetries,
        layers=ModelConfig.num_layers,
        param_dtype=jnp.complex128,
        features=ModelConfig.num_features,
        equal_amplitudes=False,
        parity=1,
        input_mask=mask,
        characters=chi
    )
    
    # 创建变分量子态
    vqs = nk.vqs.MCState(
        sampler=sampler,
        model=model,
        n_samples=n_samples,
        n_discard_per_chain=n_discard,
        chunk_size=chunk_size,
    )
    
    return vqs, lattice, hilbert, hamiltonian
