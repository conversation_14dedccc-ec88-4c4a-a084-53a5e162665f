#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Shastry-Sutherland_model运行器模块
提供了一种更面向对象的方式来进行Shastry-Sutherland模型模拟
"""

import os
import time
import numpy as np
import jax
import jax.numpy as jnp
import netket as nk
import netket.optimizer as nk_opt
from datetime import datetime
import pytz

from src.models.free_energy import CustomFreeEnergyVMC_SRt
from src.utils.logging import log_message
from src.physics.shastry_sutherland import (
    shastry_sutherland_lattice, 
    shastry_sutherland_hamiltonian, 
    shastry_sutherland_all_symmetries
)

class SSRunner:
    """Shastry-Sutherland模型运行器类，提供面向对象的接口"""
    
    def __init__(self, L, J1, J2, 
                 model_class=None, model_config=None, training_config=None, 
                 reference_energy=None, output_dir=None, checkpoint_config=None):
        """
        初始化运行器
        
        Args:
            L: 晶格尺寸
            J1: J1耦合强度
            J2: J2耦合强度
            model_class: 模型类
            model_config: 模型配置类
            training_config: 训练配置类
            reference_energy: 参考能量，用于显示相对误差
            output_dir: 输出目录。如果提供，将覆盖默认的目录结构。
            checkpoint_config: checkpoint配置字典
        """
        self.L = L
        self.J1 = J1
        self.J2 = J2
        self.Q = 1.00 - J2
        self.reference_energy = reference_energy
        
        # 存储模型相关信息
        self.model_class = model_class
        
        # 处理配置（可以是字典、类或实例）
        self.model_config = self._process_config(model_config, 'model')
        self.training_config = self._process_config(training_config, 'training')
        
        # 如果没有提供模型类，使用默认的GCNN
        if self.model_class is None:
            self.model_class = "GCNN"  # 标识符
            self.model_config = self._dict_to_object({
                'num_features': 4,
                'num_layers': 4
            })
        
        # 设置输出目录
        self.output_dir = output_dir or self._create_output_dir()
        
        # 使用train.log作为日志文件
        self.energy_log = os.path.join(self.output_dir, "train.log")
        
        # 初始化checkpoint配置
        self.checkpoint_config = checkpoint_config or {}
        self.checkpoint_dir = os.path.join(self.output_dir, "checkpoints")
        if self.checkpoint_config.get('enable', False):
            os.makedirs(self.checkpoint_dir, exist_ok=True)
        
        # 初始化物理系统
        self.lattice = shastry_sutherland_lattice(L, L)
        self.N = self.lattice.n_nodes
    
    def _process_config(self, config, config_type):
        """
        处理配置，统一转换为支持属性访问的对象
        
        Args:
            config: 配置，可以是字典、类或实例
            config_type: 配置类型 ('model' 或 'training')
        
        Returns:
            支持属性访问的配置对象
        """
        if config is None:
            # 如果没有提供配置，创建默认配置对象
            if config_type == 'model':
                return self._dict_to_object({
                    'num_features': 4,
                    'num_layers': 4
                })
            else:
                return self._dict_to_object({
                    'learning_rate': 0.015,
                    'n_cycles': 1,
                    'initial_period': 100,
                    'period_mult': 2.0,
                    'max_temperature': 1.0,
                    'min_temperature': 0.0,
                    'n_samples': 2**12,
                    'n_discard_per_chain': 0,
                    'chunk_size': 2**10,
                    'diag_shift': 0.20,
                    'grad_clip': 1.0,
                    'n_iters': 1000
                })
        
        if isinstance(config, dict):
            # 如果是字典，转换为支持属性访问的对象
            return self._dict_to_object(config)
        elif isinstance(config, type):
            # 如果是类，创建实例
            return config()
        else:
            # 如果已经是实例，直接使用
            return config
    
    def _dict_to_object(self, config_dict):
        """将字典转换为支持属性访问的对象"""
        class ConfigObject:
            def __init__(self, config_dict):
                for key, value in config_dict.items():
                    setattr(self, key, value)
            
            def get_total_iterations(self):
                """计算总迭代次数（用于训练配置）"""
                try:
                    n_cycles = getattr(self, 'n_cycles', 8)
                    initial_period = getattr(self, 'initial_period', 100)
                    period_mult = getattr(self, 'period_mult', 2.0)
                    
                    total_iters = 0
                    current_period = initial_period
                    for cycle in range(n_cycles):
                        total_iters += int(current_period * (period_mult ** cycle))
                    return total_iters
                except Exception:
                    return getattr(self, 'n_iters', 1000)
        
        return ConfigObject(config_dict)
    
    def _create_output_dir(self):
        """创建默认输出目录"""
        # 创建基于物理系统参数的输出目录
        output_dir = f"results/L={self.L}/J2={self.J2:.2f}/J1={self.J1:.2f}/training"
        os.makedirs(output_dir, exist_ok=True)
        return output_dir

    def setup_model(self):
        """设置模型和优化器"""
        # 检查是否需要从checkpoint恢复
        checkpoint_data = None
        if self.checkpoint_config.get('resume_from'):
            checkpoint_data = self._load_checkpoint()

        # 创建Hamiltonian
        self.H, self.hi = shastry_sutherland_hamiltonian(
            lattice=self.lattice,
            J1=self.J1,
            J2=self.J2,
            spin=0.5,
            Q=self.Q,
            total_sz=0
        )

        # 设置采样器
        distance = self.lattice.distances()
        max_distance = np.max(distance)

        self.sampler = nk.sampler.MetropolisExchange(
            hilbert=self.hi,
            graph=self.lattice,
            n_chains=self.training_config.n_samples,
            d_max=max_distance
        )

        # 根据模型类型设置模型
        if self.model_class == "GCNN":
            # 定义局部掩码
            mask = jnp.zeros(self.lattice.n_nodes, dtype=bool)
            for i in range(self.lattice.n_nodes):
                mask = mask.at[i].set(True)

            # 获取对称性
            symmetries = shastry_sutherland_all_symmetries(self.lattice)

            # 设置动量和表示
            from netket.utils.group import PointGroup, Identity
            from netket.utils.group.planar import rotation, glide_group

            nc = 4
            cyclic_4 = PointGroup(
                [Identity()] + [rotation((360 / nc) * i) for i in range(1, nc)],
                ndim=2,
            )
            C4v = glide_group(trans=(1, 1), origin=(0, 0)) @ cyclic_4
            sgb = self.lattice.space_group_builder(point_group=C4v)
            momentum = [0.0, 0.0]
            chi = sgb.space_group_irreps(momentum)[0]

            # 创建GCNN模型
            model_no_symm = nk.models.GCNN(
                symmetries=symmetries,
                layers=self.model_config.num_layers,
                param_dtype=jnp.complex128,
                features=self.model_config.num_features,
                equal_amplitudes=False,
                parity=1,
                input_mask=mask,
                characters=chi
            )

            # 不使用额外的对称性包装，GCNN已经内置了对称性
            self.model = model_no_symm
        else:
            raise ValueError(f"不支持的模型类型: {self.model_class}")

        # 创建变分量子态
        n_samples = self.training_config.n_samples
        chunk_size = self.training_config.chunk_size

        self.vqs = nk.vqs.MCState(
            sampler=self.sampler,
            model=self.model,
            n_samples=n_samples,
            n_discard_per_chain=self.training_config.n_discard_per_chain,
            chunk_size=chunk_size,
        )

        # 从checkpoint恢复参数（如果有）
        if checkpoint_data is not None:
            # 直接设置参数，不使用replace方法
            self.vqs.parameters = checkpoint_data['parameters']
            log_message(self.energy_log, "✓ 变分状态参数已从checkpoint恢复")
            # 保存起始迭代次数以便后续使用
            # 确保start_iteration是整数类型
            iteration_value = checkpoint_data['iteration']
            if isinstance(iteration_value, str) and iteration_value == 'final':
                # 如果是final状态，从0开始继续训练
                self.start_iteration = 0
                log_message(self.energy_log, "✓ 从final状态恢复, 重置迭代计数为0")
            else:
                # 确保是整数类型
                self.start_iteration = int(iteration_value)
        else:
            self.start_iteration = 0

        return self

    def _count_model_parameters(self):
        """计算模型总参数数量"""
        param_count = 0
        for param in jax.tree_leaves(self.vqs.parameters):
            param_count += param.size
        return param_count

    def _log_training_info(self, n_iter):
        """记录训练开始前的详细信息"""
        # 获取物理模型名称
        physics_model_name = "Shastry-Sutherland Model"

        log_message(self.energy_log, "="*50)
        log_message(self.energy_log, f"GCNN for {physics_model_name}")
        log_message(self.energy_log, "="*50)

        # 系统参数
        log_message(self.energy_log, "System parameters:")
        log_message(self.energy_log, f"  - System size: L={self.L}, N={self.N}")
        log_message(self.energy_log, f"  - System parameters: J1={self.J1}, J2={self.J2}, Q={self.Q}")
        if self.reference_energy is not None:
            log_message(self.energy_log, f"  - Reference energy: {self.reference_energy}")

        # 模型参数
        log_message(self.energy_log, "-"*50)
        log_message(self.energy_log, "Model parameters:")
        log_message(self.energy_log, f"  - Number of layers = {self.model_config.num_layers}")
        log_message(self.energy_log, f"  - Number of features = {self.model_config.num_features}")

        # 计算并显示总参数数
        total_params = self._count_model_parameters()
        log_message(self.energy_log, f"  - Total parameters = {total_params}")

        # 训练参数
        log_message(self.energy_log, "-"*50)
        log_message(self.energy_log, "Training parameters:")
        log_message(self.energy_log, f"  - Learning rate: {self.training_config.learning_rate}")
        log_message(self.energy_log, f"  - Total iterations: {n_iter}")
        log_message(self.energy_log, f"  - Annealing cycles: {getattr(self.training_config, 'n_cycles', 1)}")
        log_message(self.energy_log, f"  - Initial period: {getattr(self.training_config, 'initial_period', 100)}")
        log_message(self.energy_log, f"  - Period multiplier: {getattr(self.training_config, 'period_mult', 2.0)}")
        log_message(self.energy_log, f"  - Temperature range: {getattr(self.training_config, 'min_temperature', 0.0)}-{getattr(self.training_config, 'max_temperature', 1.0)}")
        log_message(self.energy_log, f"  - Samples: {self.training_config.n_samples}")
        log_message(self.energy_log, f"  - Discarded samples: {getattr(self.training_config, 'n_discard_per_chain', 0)}")
        log_message(self.energy_log, f"  - Chunk size: {self.training_config.chunk_size}")
        log_message(self.energy_log, f"  - Diagonal shift: {getattr(self.training_config, 'diag_shift', 0.01)}")
        log_message(self.energy_log, f"  - Gradient clipping: {getattr(self.training_config, 'grad_clip', 1.0)}")

        # Checkpoint状态
        if self.checkpoint_config.get('enable', False):
            log_message(self.energy_log, f"  - Checkpoint enabled: interval={self.checkpoint_config.get('save_interval', 500)}")
            log_message(self.energy_log, f"  - Checkpoint directory: {os.path.relpath(self.checkpoint_dir)}")
            if hasattr(self, 'start_iteration') and (isinstance(self.start_iteration, (int, float)) and self.start_iteration > 0 or isinstance(self.start_iteration, str) and self.start_iteration == 'final'):
                log_message(self.energy_log, f"  - Resuming from iteration: {self.start_iteration}")
        else:
            log_message(self.energy_log, f"  - Checkpoint disabled")

        # 设备状态
        log_message(self.energy_log, "-"*50)
        log_message(self.energy_log, "Device status:")

        # 获取GPU信息
        devices = jax.devices()
        num_devices = len(devices)

        # 尝试获取GPU型号
        try:
            # 获取第一个GPU设备的平台信息
            if devices and devices[0].platform == 'gpu':
                device_info = str(devices[0].device_kind)
                # 从设备信息中提取GPU型号，通常包含在device_kind中
                if 'A100' in device_info.upper():
                    gpu_model = 'A100'
                elif 'V100' in device_info.upper():
                    gpu_model = 'V100'
                elif 'T4' in device_info.upper():
                    gpu_model = 'T4'
                elif 'RTX' in device_info.upper():
                    gpu_model = 'RTX'
                else:
                    # 如果无法识别具体型号，显示原始信息
                    gpu_model = device_info
            else:
                gpu_model = 'Unknown'
        except:
            gpu_model = 'Unknown'

        log_message(self.energy_log, f"  - Devices model: {gpu_model}")
        log_message(self.energy_log, f"  - Number of devices: {num_devices}")
        log_message(self.energy_log, f"  - Sharding: {nk.config.netket_experimental_sharding}")

        log_message(self.energy_log, "="*60)

        return self

    def run(self):
        """运行模拟"""
        # 运行完整的训练流程
        self._run_training()
        return self

    def _run_training(self):
        """统一的训练流程"""
        # 计算总迭代次数
        n_iter = self.training_config.get_total_iterations()

        # 记录详细的训练信息
        self._log_training_info(n_iter)

        # 记录时间
        start = time.time()

        # 创建优化器
        optimizer = nk_opt.Sgd(learning_rate=self.training_config.learning_rate)

        # 准备checkpoint回调
        checkpoint_callback = None
        checkpoint_interval = 0
        if self.checkpoint_config.get('enable', False):
            # 确保start_iteration是整数类型
            start_iter = int(self.start_iteration) if self.start_iteration is not None else 0
            checkpoint_callback = lambda iter_num, energy_mean, energy_error: self._save_checkpoint(
                start_iter + iter_num, energy_mean, energy_error
            )
            checkpoint_interval = self.checkpoint_config.get('save_interval', 500)

        # 使用新的 CustomFreeEnergyVMC_SRt 方法，传递热重启参数
        clip_norm = getattr(self.training_config, 'grad_clip', 1.0)
        vmc = CustomFreeEnergyVMC_SRt(
            reference_energy=self.reference_energy,
            initial_period=self.training_config.initial_period,
            period_mult=self.training_config.period_mult,
            max_temperature=self.training_config.max_temperature,
            min_temperature=self.training_config.min_temperature,
            clip_norm=clip_norm,
            checkpoint_callback=checkpoint_callback,
            checkpoint_interval=checkpoint_interval,
            hamiltonian=self.H,
            optimizer=optimizer,
            diag_shift=self.training_config.diag_shift,
            variational_state=self.vqs
        )

        # 运行优化
        vmc.run(n_iter=n_iter, energy_log=self.energy_log)

        end = time.time()

        runtime = end - start
        log_message(self.energy_log, "="*60)
        log_message(self.energy_log, f"Training completed | Runtime: {runtime:.1f}s")

        # 保存最终状态
        self._save_final_state()

    def _save_checkpoint(self, iteration, energy_mean=None, energy_error=None):
        """保存checkpoint到checkpoints子目录"""
        if not self.checkpoint_config.get('enable', False):
            return

        import pickle

        # 创建checkpoint数据
        # 确保energy值可以被pickle序列化（保持JAX数组用于模型恢复）
        energy_data = None
        if energy_mean is not None:
            energy_data = {'mean': energy_mean, 'error': energy_error}

        # 获取新加坡时区时间戳
        singapore_tz = pytz.timezone('Asia/Singapore')
        singapore_time = datetime.now(singapore_tz)
        
        checkpoint_data = {
            'iteration': iteration,
            'parameters': self.vqs.parameters,
            'model_type': self.model_class,
            'model_config': self.model_config.__dict__ if hasattr(self.model_config, '__dict__') else vars(self.model_config),
            'training_config': self.training_config.__dict__ if hasattr(self.training_config, '__dict__') else vars(self.training_config),
            'system_config': {
                'L': self.L, 'J1': self.J1, 'J2': self.J2, 'Q': self.Q
            },
            'energy': energy_data,
            'timestamp': singapore_time.isoformat()
        }

        # 保存checkpoint文件
        if self.checkpoint_config.get('keep_history', True):
            checkpoint_file = os.path.join(self.checkpoint_dir, f"checkpoint_iter_{iteration:06d}.pkl")
        else:
            checkpoint_file = os.path.join(self.checkpoint_dir, "latest_checkpoint.pkl")

        with open(checkpoint_file, "wb") as f:
            pickle.dump(checkpoint_data, f)

        log_message(self.energy_log, f"✓ Checkpoint saved: {os.path.basename(checkpoint_file)}")

    def _load_checkpoint(self, checkpoint_path=None):
        """从checkpoint恢复训练状态"""
        import pickle

        # 确定checkpoint路径
        if checkpoint_path is None:
            checkpoint_path = self.checkpoint_config.get('resume_from')

        if checkpoint_path is None:
            return None

        # 如果是相对路径，相对于当前工作目录解析
        if not os.path.isabs(checkpoint_path):
            checkpoint_path = os.path.abspath(checkpoint_path)

        # 如果路径是目录，查找最新的checkpoint
        if os.path.isdir(checkpoint_path):
            # 直接查找最新的checkpoint文件
            checkpoint_files = [f for f in os.listdir(checkpoint_path) if f.endswith('.pkl')]
            if not checkpoint_files:
                log_message(self.energy_log, f"⚠️  在目录 {checkpoint_path} 中未找到checkpoint文件")
                return None
            checkpoint_files.sort()
            checkpoint_file = os.path.join(checkpoint_path, checkpoint_files[-1])
        else:
            checkpoint_file = checkpoint_path

        if not os.path.exists(checkpoint_file):
            log_message(self.energy_log, f"⚠️  Checkpoint文件不存在: {checkpoint_file}")
            return None

        try:
            with open(checkpoint_file, "rb") as f:
                checkpoint_data = pickle.load(f)

            # 显示相对路径
            try:
                relative_path = os.path.relpath(checkpoint_file)
                log_message(self.energy_log, f"✓ 从checkpoint恢复: {relative_path}")
            except ValueError:
                # 如果无法计算相对路径（比如在不同驱动器上），则显示绝对路径
                log_message(self.energy_log, f"✓ 从checkpoint恢复: {checkpoint_file}")
            log_message(self.energy_log, f"  - 迭代次数: {checkpoint_data['iteration']}")
            if checkpoint_data.get('energy'):
                energy = checkpoint_data['energy']
                log_message(self.energy_log, f"  - 能量: {energy['mean']:.6f} ± {energy['error']:.6f}")
            log_message(self.energy_log, f"  - 时间戳: {checkpoint_data['timestamp']}")

            return checkpoint_data

        except Exception as e:
            log_message(self.energy_log, f"⚠️  加载checkpoint失败: {e}")
            return None

    def _save_final_state(self):
        """保存最终状态"""
        import pickle

        # 保存到checkpoint目录
        state_file = os.path.join(self.checkpoint_dir, f"final_GCNN.pkl")

        # 保存完整的checkpoint数据作为最终状态
        final_energy = self.vqs.expect(self.H)
        
        # 获取新加坡时区时间戳
        singapore_tz = pytz.timezone('Asia/Singapore')
        singapore_time = datetime.now(singapore_tz)
        
        checkpoint_data = {
            'iteration': 'final',
            'parameters': self.vqs.parameters,
            'model_type': self.model_class,
            'model_config': self.model_config.__dict__ if hasattr(self.model_config, '__dict__') else vars(self.model_config),
            'training_config': self.training_config.__dict__ if hasattr(self.training_config, '__dict__') else vars(self.training_config),
            'system_config': {
                'L': self.L, 'J1': self.J1, 'J2': self.J2, 'Q': self.Q
            },
            'energy': {'mean': final_energy.mean, 'error': final_energy.error_of_mean},
            'timestamp': singapore_time.isoformat(),
            'is_final': True
        }

        with open(state_file, "wb") as f_state:
            pickle.dump(checkpoint_data, f_state)

        log_message(self.energy_log, f"✓ Final state saved: checkpoints/{os.path.basename(state_file)}")

        log_message(self.energy_log, "="*60)


