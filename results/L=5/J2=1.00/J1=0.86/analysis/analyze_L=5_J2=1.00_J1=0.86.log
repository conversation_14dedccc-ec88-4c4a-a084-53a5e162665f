[2025-07-25 14:08:13] ================================================================================
[2025-07-25 14:08:13] 加载量子态: L=5, J2=1.00, J1=0.86
[2025-07-25 14:08:13] 设置样本数为: 1048576
[2025-07-25 14:08:13] 开始生成共享样本集...
[2025-07-25 14:11:33] 样本生成完成,耗时: 199.971 秒
[2025-07-25 14:11:33] ================================================================================
[2025-07-25 14:11:33] 开始计算自旋结构因子...
[2025-07-25 14:11:33] 初始化操作符缓存...
[2025-07-25 14:11:33] 预构建所有自旋相关操作符...
[2025-07-25 14:11:33] 开始计算自旋相关函数...
[2025-07-25 14:11:45] 自旋算符进度: 1/100, 当前算符: S_0 · S_0, 耗时: 11.678s
[2025-07-25 14:11:57] 自旋算符进度: 2/100, 当前算符: S_0 · S_1, 耗时: 11.977s
[2025-07-25 14:12:03] 自旋算符进度: 3/100, 当前算符: S_0 · S_2, 耗时: 6.305s
[2025-07-25 14:12:10] 自旋算符进度: 4/100, 当前算符: S_0 · S_3, 耗时: 6.303s
[2025-07-25 14:12:16] 自旋算符进度: 5/100, 当前算符: S_0 · S_4, 耗时: 6.271s
[2025-07-25 14:12:22] 自旋算符进度: 6/100, 当前算符: S_0 · S_5, 耗时: 6.319s
[2025-07-25 14:12:29] 自旋算符进度: 7/100, 当前算符: S_0 · S_6, 耗时: 6.305s
[2025-07-25 14:12:35] 自旋算符进度: 8/100, 当前算符: S_0 · S_7, 耗时: 6.273s
[2025-07-25 14:12:41] 自旋算符进度: 9/100, 当前算符: S_0 · S_8, 耗时: 6.272s
[2025-07-25 14:12:47] 自旋算符进度: 10/100, 当前算符: S_0 · S_9, 耗时: 6.274s
[2025-07-25 14:12:54] 自旋算符进度: 11/100, 当前算符: S_0 · S_10, 耗时: 6.276s
[2025-07-25 14:13:00] 自旋算符进度: 12/100, 当前算符: S_0 · S_11, 耗时: 6.272s
[2025-07-25 14:13:06] 自旋算符进度: 13/100, 当前算符: S_0 · S_12, 耗时: 6.305s
[2025-07-25 14:13:13] 自旋算符进度: 14/100, 当前算符: S_0 · S_13, 耗时: 6.306s
[2025-07-25 14:13:19] 自旋算符进度: 15/100, 当前算符: S_0 · S_14, 耗时: 6.304s
[2025-07-25 14:13:25] 自旋算符进度: 16/100, 当前算符: S_0 · S_15, 耗时: 6.272s
[2025-07-25 14:13:31] 自旋算符进度: 17/100, 当前算符: S_0 · S_16, 耗时: 6.273s
[2025-07-25 14:13:38] 自旋算符进度: 18/100, 当前算符: S_0 · S_17, 耗时: 6.272s
[2025-07-25 14:13:44] 自旋算符进度: 19/100, 当前算符: S_0 · S_18, 耗时: 6.305s
[2025-07-25 14:13:50] 自旋算符进度: 20/100, 当前算符: S_0 · S_19, 耗时: 6.306s
[2025-07-25 14:13:57] 自旋算符进度: 21/100, 当前算符: S_0 · S_20, 耗时: 6.272s
[2025-07-25 14:14:03] 自旋算符进度: 22/100, 当前算符: S_0 · S_21, 耗时: 6.273s
[2025-07-25 14:14:09] 自旋算符进度: 23/100, 当前算符: S_0 · S_22, 耗时: 6.273s
[2025-07-25 14:14:15] 自旋算符进度: 24/100, 当前算符: S_0 · S_23, 耗时: 6.304s
[2025-07-25 14:14:22] 自旋算符进度: 25/100, 当前算符: S_0 · S_24, 耗时: 6.305s
[2025-07-25 14:14:28] 自旋算符进度: 26/100, 当前算符: S_0 · S_25, 耗时: 6.305s
[2025-07-25 14:14:34] 自旋算符进度: 27/100, 当前算符: S_0 · S_26, 耗时: 6.273s
[2025-07-25 14:14:41] 自旋算符进度: 28/100, 当前算符: S_0 · S_27, 耗时: 6.274s
[2025-07-25 14:14:47] 自旋算符进度: 29/100, 当前算符: S_0 · S_28, 耗时: 6.272s
[2025-07-25 14:14:53] 自旋算符进度: 30/100, 当前算符: S_0 · S_29, 耗时: 6.304s
[2025-07-25 14:14:59] 自旋算符进度: 31/100, 当前算符: S_0 · S_30, 耗时: 6.318s
[2025-07-25 14:15:06] 自旋算符进度: 32/100, 当前算符: S_0 · S_31, 耗时: 6.304s
[2025-07-25 14:15:12] 自旋算符进度: 33/100, 当前算符: S_0 · S_32, 耗时: 6.273s
[2025-07-25 14:15:18] 自旋算符进度: 34/100, 当前算符: S_0 · S_33, 耗时: 6.273s
[2025-07-25 14:15:25] 自旋算符进度: 35/100, 当前算符: S_0 · S_34, 耗时: 6.273s
[2025-07-25 14:15:31] 自旋算符进度: 36/100, 当前算符: S_0 · S_35, 耗时: 6.271s
[2025-07-25 14:15:37] 自旋算符进度: 37/100, 当前算符: S_0 · S_36, 耗时: 6.272s
[2025-07-25 14:15:43] 自旋算符进度: 38/100, 当前算符: S_0 · S_37, 耗时: 6.272s
[2025-07-25 14:15:50] 自旋算符进度: 39/100, 当前算符: S_0 · S_38, 耗时: 6.304s
[2025-07-25 14:15:56] 自旋算符进度: 40/100, 当前算符: S_0 · S_39, 耗时: 6.305s
[2025-07-25 14:16:02] 自旋算符进度: 41/100, 当前算符: S_0 · S_40, 耗时: 6.305s
[2025-07-25 14:16:09] 自旋算符进度: 42/100, 当前算符: S_0 · S_41, 耗时: 6.305s
[2025-07-25 14:16:15] 自旋算符进度: 43/100, 当前算符: S_0 · S_42, 耗时: 6.303s
[2025-07-25 14:16:21] 自旋算符进度: 44/100, 当前算符: S_0 · S_43, 耗时: 6.305s
[2025-07-25 14:16:27] 自旋算符进度: 45/100, 当前算符: S_0 · S_44, 耗时: 6.274s
[2025-07-25 14:16:34] 自旋算符进度: 46/100, 当前算符: S_0 · S_45, 耗时: 6.272s
[2025-07-25 14:16:40] 自旋算符进度: 47/100, 当前算符: S_0 · S_46, 耗时: 6.303s
[2025-07-25 14:16:46] 自旋算符进度: 48/100, 当前算符: S_0 · S_47, 耗时: 6.305s
[2025-07-25 14:16:53] 自旋算符进度: 49/100, 当前算符: S_0 · S_48, 耗时: 6.398s
[2025-07-25 14:16:59] 自旋算符进度: 50/100, 当前算符: S_0 · S_49, 耗时: 6.272s
[2025-07-25 14:17:05] 自旋算符进度: 51/100, 当前算符: S_0 · S_50, 耗时: 6.272s
[2025-07-25 14:17:12] 自旋算符进度: 52/100, 当前算符: S_0 · S_51, 耗时: 6.272s
[2025-07-25 14:17:18] 自旋算符进度: 53/100, 当前算符: S_0 · S_52, 耗时: 6.304s
[2025-07-25 14:17:24] 自旋算符进度: 54/100, 当前算符: S_0 · S_53, 耗时: 6.371s
[2025-07-25 14:17:31] 自旋算符进度: 55/100, 当前算符: S_0 · S_54, 耗时: 6.272s
[2025-07-25 14:17:37] 自旋算符进度: 56/100, 当前算符: S_0 · S_55, 耗时: 6.273s
[2025-07-25 14:17:43] 自旋算符进度: 57/100, 当前算符: S_0 · S_56, 耗时: 6.272s
[2025-07-25 14:17:49] 自旋算符进度: 58/100, 当前算符: S_0 · S_57, 耗时: 6.303s
[2025-07-25 14:17:56] 自旋算符进度: 59/100, 当前算符: S_0 · S_58, 耗时: 6.304s
[2025-07-25 14:18:02] 自旋算符进度: 60/100, 当前算符: S_0 · S_59, 耗时: 6.305s
[2025-07-25 14:18:08] 自旋算符进度: 61/100, 当前算符: S_0 · S_60, 耗时: 6.273s
[2025-07-25 14:18:15] 自旋算符进度: 62/100, 当前算符: S_0 · S_61, 耗时: 6.272s
[2025-07-25 14:18:21] 自旋算符进度: 63/100, 当前算符: S_0 · S_62, 耗时: 6.272s
[2025-07-25 14:18:27] 自旋算符进度: 64/100, 当前算符: S_0 · S_63, 耗时: 6.304s
[2025-07-25 14:18:33] 自旋算符进度: 65/100, 当前算符: S_0 · S_64, 耗时: 6.304s
[2025-07-25 14:18:40] 自旋算符进度: 66/100, 当前算符: S_0 · S_65, 耗时: 6.303s
[2025-07-25 14:18:46] 自旋算符进度: 67/100, 当前算符: S_0 · S_66, 耗时: 6.272s
[2025-07-25 14:18:52] 自旋算符进度: 68/100, 当前算符: S_0 · S_67, 耗时: 6.272s
[2025-07-25 14:18:59] 自旋算符进度: 69/100, 当前算符: S_0 · S_68, 耗时: 6.305s
[2025-07-25 14:19:05] 自旋算符进度: 70/100, 当前算符: S_0 · S_69, 耗时: 6.305s
[2025-07-25 14:19:11] 自旋算符进度: 71/100, 当前算符: S_0 · S_70, 耗时: 6.304s
[2025-07-25 14:19:17] 自旋算符进度: 72/100, 当前算符: S_0 · S_71, 耗时: 6.272s
[2025-07-25 14:19:24] 自旋算符进度: 73/100, 当前算符: S_0 · S_72, 耗时: 6.273s
[2025-07-25 14:19:30] 自旋算符进度: 74/100, 当前算符: S_0 · S_73, 耗时: 6.273s
[2025-07-25 14:19:36] 自旋算符进度: 75/100, 当前算符: S_0 · S_74, 耗时: 6.303s
[2025-07-25 14:19:43] 自旋算符进度: 76/100, 当前算符: S_0 · S_75, 耗时: 6.305s
[2025-07-25 14:19:49] 自旋算符进度: 77/100, 当前算符: S_0 · S_76, 耗时: 6.272s
[2025-07-25 14:19:55] 自旋算符进度: 78/100, 当前算符: S_0 · S_77, 耗时: 6.272s
[2025-07-25 14:20:01] 自旋算符进度: 79/100, 当前算符: S_0 · S_78, 耗时: 6.272s
[2025-07-25 14:20:08] 自旋算符进度: 80/100, 当前算符: S_0 · S_79, 耗时: 6.305s
[2025-07-25 14:20:14] 自旋算符进度: 81/100, 当前算符: S_0 · S_80, 耗时: 6.305s
[2025-07-25 14:20:20] 自旋算符进度: 82/100, 当前算符: S_0 · S_81, 耗时: 6.272s
[2025-07-25 14:20:27] 自旋算符进度: 83/100, 当前算符: S_0 · S_82, 耗时: 6.272s
[2025-07-25 14:20:33] 自旋算符进度: 84/100, 当前算符: S_0 · S_83, 耗时: 6.272s
[2025-07-25 14:20:39] 自旋算符进度: 85/100, 当前算符: S_0 · S_84, 耗时: 6.303s
[2025-07-25 14:20:45] 自旋算符进度: 86/100, 当前算符: S_0 · S_85, 耗时: 6.304s
[2025-07-25 14:20:52] 自旋算符进度: 87/100, 当前算符: S_0 · S_86, 耗时: 6.349s
[2025-07-25 14:20:58] 自旋算符进度: 88/100, 当前算符: S_0 · S_87, 耗时: 6.272s
[2025-07-25 14:21:04] 自旋算符进度: 89/100, 当前算符: S_0 · S_88, 耗时: 6.272s
[2025-07-25 14:21:11] 自旋算符进度: 90/100, 当前算符: S_0 · S_89, 耗时: 6.304s
[2025-07-25 14:21:17] 自旋算符进度: 91/100, 当前算符: S_0 · S_90, 耗时: 6.304s
[2025-07-25 14:21:23] 自旋算符进度: 92/100, 当前算符: S_0 · S_91, 耗时: 6.304s
[2025-07-25 14:21:30] 自旋算符进度: 93/100, 当前算符: S_0 · S_92, 耗时: 6.272s
[2025-07-25 14:21:36] 自旋算符进度: 94/100, 当前算符: S_0 · S_93, 耗时: 6.303s
[2025-07-25 14:21:42] 自旋算符进度: 95/100, 当前算符: S_0 · S_94, 耗时: 6.273s
[2025-07-25 14:21:48] 自旋算符进度: 96/100, 当前算符: S_0 · S_95, 耗时: 6.272s
[2025-07-25 14:21:55] 自旋算符进度: 97/100, 当前算符: S_0 · S_96, 耗时: 6.304s
[2025-07-25 14:22:01] 自旋算符进度: 98/100, 当前算符: S_0 · S_97, 耗时: 6.306s
[2025-07-25 14:22:07] 自旋算符进度: 99/100, 当前算符: S_0 · S_98, 耗时: 6.303s
[2025-07-25 14:22:14] 自旋算符进度: 100/100, 当前算符: S_0 · S_99, 耗时: 6.273s
[2025-07-25 14:22:14] 自旋相关函数计算完成,总耗时 640.24 秒
[2025-07-25 14:22:14] 计算傅里叶变换...
[2025-07-25 14:22:14] 自旋结构因子计算完成
[2025-07-25 14:22:15] 自旋相关函数平均误差: 0.000578
[2025-07-25 14:22:15] ================================================================================
[2025-07-25 14:22:15] 开始计算对角二聚体结构因子...
[2025-07-25 14:22:15] 识别所有对角二聚体...
[2025-07-25 14:22:15] 总共找到 100 个西北-东南方向对角二聚体和 100 个西南-东北方向对角二聚体
[2025-07-25 14:22:15] 预计算对角二聚体操作符...
[2025-07-25 14:22:18] 开始计算西北-东南方向对角二聚体相关函数 (100 个算符)...
[2025-07-25 14:22:24] NW-SE对角算符进度: 1/100, 当前算符: D_(0, 18) * D_(0, 18), 耗时: 6.304s
[2025-07-25 14:22:41] NW-SE对角算符进度: 2/100, 当前算符: D_(0, 18) * D_(1, 39), 耗时: 16.170s
[2025-07-25 14:22:51] NW-SE对角算符进度: 3/100, 当前算符: D_(0, 18) * D_(2, 20), 耗时: 10.619s
[2025-07-25 14:23:02] NW-SE对角算符进度: 4/100, 当前算符: D_(0, 18) * D_(3, 1), 耗时: 10.550s
[2025-07-25 14:23:12] NW-SE对角算符进度: 5/100, 当前算符: D_(0, 18) * D_(4, 2), 耗时: 10.550s
[2025-07-25 14:23:23] NW-SE对角算符进度: 6/100, 当前算符: D_(0, 18) * D_(5, 23), 耗时: 10.617s
[2025-07-25 14:23:33] NW-SE对角算符进度: 7/100, 当前算符: D_(0, 18) * D_(6, 24), 耗时: 10.551s
[2025-07-25 14:23:44] NW-SE对角算符进度: 8/100, 当前算符: D_(0, 18) * D_(7, 5), 耗时: 10.550s
[2025-07-25 14:23:55] NW-SE对角算符进度: 9/100, 当前算符: D_(0, 18) * D_(8, 6), 耗时: 10.554s
[2025-07-25 14:24:05] NW-SE对角算符进度: 10/100, 当前算符: D_(0, 18) * D_(9, 27), 耗时: 10.551s
[2025-07-25 14:24:16] NW-SE对角算符进度: 11/100, 当前算符: D_(0, 18) * D_(10, 28), 耗时: 10.552s
[2025-07-25 14:24:26] NW-SE对角算符进度: 12/100, 当前算符: D_(0, 18) * D_(11, 9), 耗时: 10.551s
[2025-07-25 14:24:37] NW-SE对角算符进度: 13/100, 当前算符: D_(0, 18) * D_(12, 10), 耗时: 10.619s
[2025-07-25 14:24:47] NW-SE对角算符进度: 14/100, 当前算符: D_(0, 18) * D_(13, 31), 耗时: 10.619s
[2025-07-25 14:24:58] NW-SE对角算符进度: 15/100, 当前算符: D_(0, 18) * D_(14, 32), 耗时: 10.551s
[2025-07-25 14:25:09] NW-SE对角算符进度: 16/100, 当前算符: D_(0, 18) * D_(15, 13), 耗时: 10.557s
[2025-07-25 14:25:19] NW-SE对角算符进度: 17/100, 当前算符: D_(0, 18) * D_(16, 14), 耗时: 10.556s
[2025-07-25 14:25:30] NW-SE对角算符进度: 18/100, 当前算符: D_(0, 18) * D_(17, 35), 耗时: 10.623s
[2025-07-25 14:25:48] NW-SE对角算符进度: 19/100, 当前算符: D_(0, 18) * D_(18, 36), 耗时: 18.674s
[2025-07-25 14:25:59] NW-SE对角算符进度: 20/100, 当前算符: D_(0, 18) * D_(19, 17), 耗时: 10.583s
[2025-07-25 14:26:09] NW-SE对角算符进度: 21/100, 当前算符: D_(0, 18) * D_(20, 38), 耗时: 10.524s
[2025-07-25 14:26:20] NW-SE对角算符进度: 22/100, 当前算符: D_(0, 18) * D_(21, 59), 耗时: 10.521s
[2025-07-25 14:26:31] NW-SE对角算符进度: 23/100, 当前算符: D_(0, 18) * D_(22, 40), 耗时: 10.584s
[2025-07-25 14:26:41] NW-SE对角算符进度: 24/100, 当前算符: D_(0, 18) * D_(23, 21), 耗时: 10.587s
[2025-07-25 14:26:52] NW-SE对角算符进度: 25/100, 当前算符: D_(0, 18) * D_(24, 22), 耗时: 10.636s
[2025-07-25 14:27:02] NW-SE对角算符进度: 26/100, 当前算符: D_(0, 18) * D_(25, 43), 耗时: 10.588s
[2025-07-25 14:27:13] NW-SE对角算符进度: 27/100, 当前算符: D_(0, 18) * D_(26, 44), 耗时: 10.526s
[2025-07-25 14:27:23] NW-SE对角算符进度: 28/100, 当前算符: D_(0, 18) * D_(27, 25), 耗时: 10.526s
[2025-07-25 14:27:34] NW-SE对角算符进度: 29/100, 当前算符: D_(0, 18) * D_(28, 26), 耗时: 10.587s
[2025-07-25 14:27:45] NW-SE对角算符进度: 30/100, 当前算符: D_(0, 18) * D_(29, 47), 耗时: 10.590s
[2025-07-25 14:27:55] NW-SE对角算符进度: 31/100, 当前算符: D_(0, 18) * D_(30, 48), 耗时: 10.588s
[2025-07-25 14:28:06] NW-SE对角算符进度: 32/100, 当前算符: D_(0, 18) * D_(31, 29), 耗时: 10.527s
[2025-07-25 14:28:16] NW-SE对角算符进度: 33/100, 当前算符: D_(0, 18) * D_(32, 30), 耗时: 10.529s
[2025-07-25 14:28:27] NW-SE对角算符进度: 34/100, 当前算符: D_(0, 18) * D_(33, 51), 耗时: 10.530s
[2025-07-25 14:28:37] NW-SE对角算符进度: 35/100, 当前算符: D_(0, 18) * D_(34, 52), 耗时: 10.590s
[2025-07-25 14:28:48] NW-SE对角算符进度: 36/100, 当前算符: D_(0, 18) * D_(35, 33), 耗时: 10.587s
[2025-07-25 14:28:59] NW-SE对角算符进度: 37/100, 当前算符: D_(0, 18) * D_(36, 34), 耗时: 10.526s
[2025-07-25 14:29:09] NW-SE对角算符进度: 38/100, 当前算符: D_(0, 18) * D_(37, 55), 耗时: 10.530s
[2025-07-25 14:29:20] NW-SE对角算符进度: 39/100, 当前算符: D_(0, 18) * D_(38, 56), 耗时: 10.590s
[2025-07-25 14:29:30] NW-SE对角算符进度: 40/100, 当前算符: D_(0, 18) * D_(39, 37), 耗时: 10.589s
[2025-07-25 14:29:41] NW-SE对角算符进度: 41/100, 当前算符: D_(0, 18) * D_(40, 58), 耗时: 10.588s
[2025-07-25 14:29:51] NW-SE对角算符进度: 42/100, 当前算符: D_(0, 18) * D_(41, 79), 耗时: 10.590s
[2025-07-25 14:30:02] NW-SE对角算符进度: 43/100, 当前算符: D_(0, 18) * D_(42, 60), 耗时: 10.586s
[2025-07-25 14:30:13] NW-SE对角算符进度: 44/100, 当前算符: D_(0, 18) * D_(43, 41), 耗时: 10.527s
[2025-07-25 14:30:23] NW-SE对角算符进度: 45/100, 当前算符: D_(0, 18) * D_(44, 42), 耗时: 10.526s
[2025-07-25 14:30:34] NW-SE对角算符进度: 46/100, 当前算符: D_(0, 18) * D_(45, 63), 耗时: 10.528s
[2025-07-25 14:30:44] NW-SE对角算符进度: 47/100, 当前算符: D_(0, 18) * D_(46, 64), 耗时: 10.591s
[2025-07-25 14:30:55] NW-SE对角算符进度: 48/100, 当前算符: D_(0, 18) * D_(47, 45), 耗时: 10.588s
[2025-07-25 14:31:05] NW-SE对角算符进度: 49/100, 当前算符: D_(0, 18) * D_(48, 46), 耗时: 10.529s
[2025-07-25 14:31:16] NW-SE对角算符进度: 50/100, 当前算符: D_(0, 18) * D_(49, 67), 耗时: 10.529s
[2025-07-25 14:31:26] NW-SE对角算符进度: 51/100, 当前算符: D_(0, 18) * D_(50, 68), 耗时: 10.529s
[2025-07-25 14:31:37] NW-SE对角算符进度: 52/100, 当前算符: D_(0, 18) * D_(51, 49), 耗时: 10.589s
[2025-07-25 14:31:48] NW-SE对角算符进度: 53/100, 当前算符: D_(0, 18) * D_(52, 50), 耗时: 10.589s
[2025-07-25 14:31:58] NW-SE对角算符进度: 54/100, 当前算符: D_(0, 18) * D_(53, 71), 耗时: 10.588s
[2025-07-25 14:32:09] NW-SE对角算符进度: 55/100, 当前算符: D_(0, 18) * D_(54, 72), 耗时: 10.528s
[2025-07-25 14:32:19] NW-SE对角算符进度: 56/100, 当前算符: D_(0, 18) * D_(55, 53), 耗时: 10.530s
[2025-07-25 14:32:30] NW-SE对角算符进度: 57/100, 当前算符: D_(0, 18) * D_(56, 54), 耗时: 10.587s
[2025-07-25 14:32:40] NW-SE对角算符进度: 58/100, 当前算符: D_(0, 18) * D_(57, 75), 耗时: 10.591s
[2025-07-25 14:32:51] NW-SE对角算符进度: 59/100, 当前算符: D_(0, 18) * D_(58, 76), 耗时: 10.590s
[2025-07-25 14:33:01] NW-SE对角算符进度: 60/100, 当前算符: D_(0, 18) * D_(59, 57), 耗时: 10.528s
[2025-07-25 14:33:12] NW-SE对角算符进度: 61/100, 当前算符: D_(0, 18) * D_(60, 78), 耗时: 10.529s
[2025-07-25 14:33:23] NW-SE对角算符进度: 62/100, 当前算符: D_(0, 18) * D_(61, 99), 耗时: 10.591s
[2025-07-25 14:33:33] NW-SE对角算符进度: 63/100, 当前算符: D_(0, 18) * D_(62, 80), 耗时: 10.590s
[2025-07-25 14:33:44] NW-SE对角算符进度: 64/100, 当前算符: D_(0, 18) * D_(63, 61), 耗时: 10.588s
[2025-07-25 14:33:54] NW-SE对角算符进度: 65/100, 当前算符: D_(0, 18) * D_(64, 62), 耗时: 10.528s
[2025-07-25 14:34:05] NW-SE对角算符进度: 66/100, 当前算符: D_(0, 18) * D_(65, 83), 耗时: 10.528s
[2025-07-25 14:34:15] NW-SE对角算符进度: 67/100, 当前算符: D_(0, 18) * D_(66, 84), 耗时: 10.528s
[2025-07-25 14:34:26] NW-SE对角算符进度: 68/100, 当前算符: D_(0, 18) * D_(67, 65), 耗时: 10.589s
[2025-07-25 14:34:37] NW-SE对角算符进度: 69/100, 当前算符: D_(0, 18) * D_(68, 66), 耗时: 10.591s
[2025-07-25 14:34:47] NW-SE对角算符进度: 70/100, 当前算符: D_(0, 18) * D_(69, 87), 耗时: 10.529s
[2025-07-25 14:34:58] NW-SE对角算符进度: 71/100, 当前算符: D_(0, 18) * D_(70, 88), 耗时: 10.527s
[2025-07-25 14:35:08] NW-SE对角算符进度: 72/100, 当前算符: D_(0, 18) * D_(71, 69), 耗时: 10.526s
[2025-07-25 14:35:19] NW-SE对角算符进度: 73/100, 当前算符: D_(0, 18) * D_(72, 70), 耗时: 10.590s
[2025-07-25 14:35:29] NW-SE对角算符进度: 74/100, 当前算符: D_(0, 18) * D_(73, 91), 耗时: 10.588s
[2025-07-25 14:35:40] NW-SE对角算符进度: 75/100, 当前算符: D_(0, 18) * D_(74, 92), 耗时: 10.589s
[2025-07-25 14:35:50] NW-SE对角算符进度: 76/100, 当前算符: D_(0, 18) * D_(75, 73), 耗时: 10.529s
[2025-07-25 14:36:01] NW-SE对角算符进度: 77/100, 当前算符: D_(0, 18) * D_(76, 74), 耗时: 10.529s
[2025-07-25 14:36:12] NW-SE对角算符进度: 78/100, 当前算符: D_(0, 18) * D_(77, 95), 耗时: 10.528s
[2025-07-25 14:36:22] NW-SE对角算符进度: 79/100, 当前算符: D_(0, 18) * D_(78, 96), 耗时: 10.528s
[2025-07-25 14:36:33] NW-SE对角算符进度: 80/100, 当前算符: D_(0, 18) * D_(79, 77), 耗时: 10.588s
[2025-07-25 14:36:43] NW-SE对角算符进度: 81/100, 当前算符: D_(0, 18) * D_(80, 98), 耗时: 10.590s
[2025-07-25 14:36:54] NW-SE对角算符进度: 82/100, 当前算符: D_(0, 18) * D_(81, 19), 耗时: 10.587s
[2025-07-25 14:37:03] NW-SE对角算符进度: 83/100, 当前算符: D_(0, 18) * D_(82, 0), 耗时: 8.828s
[2025-07-25 14:37:13] NW-SE对角算符进度: 84/100, 当前算符: D_(0, 18) * D_(83, 81), 耗时: 10.525s
[2025-07-25 14:37:24] NW-SE对角算符进度: 85/100, 当前算符: D_(0, 18) * D_(84, 82), 耗时: 10.590s
[2025-07-25 14:37:34] NW-SE对角算符进度: 86/100, 当前算符: D_(0, 18) * D_(85, 3), 耗时: 10.591s
[2025-07-25 14:37:45] NW-SE对角算符进度: 87/100, 当前算符: D_(0, 18) * D_(86, 4), 耗时: 10.589s
[2025-07-25 14:37:55] NW-SE对角算符进度: 88/100, 当前算符: D_(0, 18) * D_(87, 85), 耗时: 10.529s
[2025-07-25 14:38:06] NW-SE对角算符进度: 89/100, 当前算符: D_(0, 18) * D_(88, 86), 耗时: 10.530s
[2025-07-25 14:38:17] NW-SE对角算符进度: 90/100, 当前算符: D_(0, 18) * D_(89, 7), 耗时: 10.530s
[2025-07-25 14:38:27] NW-SE对角算符进度: 91/100, 当前算符: D_(0, 18) * D_(90, 8), 耗时: 10.590s
[2025-07-25 14:38:38] NW-SE对角算符进度: 92/100, 当前算符: D_(0, 18) * D_(91, 89), 耗时: 10.591s
[2025-07-25 14:38:48] NW-SE对角算符进度: 93/100, 当前算符: D_(0, 18) * D_(92, 90), 耗时: 10.592s
[2025-07-25 14:38:59] NW-SE对角算符进度: 94/100, 当前算符: D_(0, 18) * D_(93, 11), 耗时: 10.590s
[2025-07-25 14:39:09] NW-SE对角算符进度: 95/100, 当前算符: D_(0, 18) * D_(94, 12), 耗时: 10.531s
[2025-07-25 14:39:20] NW-SE对角算符进度: 96/100, 当前算符: D_(0, 18) * D_(95, 93), 耗时: 10.530s
[2025-07-25 14:39:30] NW-SE对角算符进度: 97/100, 当前算符: D_(0, 18) * D_(96, 94), 耗时: 10.530s
[2025-07-25 14:39:41] NW-SE对角算符进度: 98/100, 当前算符: D_(0, 18) * D_(97, 15), 耗时: 10.591s
[2025-07-25 14:39:52] NW-SE对角算符进度: 99/100, 当前算符: D_(0, 18) * D_(98, 16), 耗时: 10.591s
[2025-07-25 14:40:02] NW-SE对角算符进度: 100/100, 当前算符: D_(0, 18) * D_(99, 97), 耗时: 10.589s
[2025-07-25 14:40:02] 西北-东南方向对角二聚体相关函数计算完成,耗时: 1064.21 秒
[2025-07-25 14:40:02] ================================================================================
[2025-07-25 14:40:02] 开始计算西南-东北方向对角二聚体相关函数 (100 个算符)...
[2025-07-25 14:40:09] SW-NE对角算符进度: 1/100, 当前算符: D_(0, 2) * D_(0, 2), 耗时: 6.298s
[2025-07-25 14:40:19] SW-NE对角算符进度: 2/100, 当前算符: D_(0, 2) * D_(1, 23), 耗时: 10.599s
[2025-07-25 14:40:28] SW-NE对角算符进度: 3/100, 当前算符: D_(0, 2) * D_(2, 24), 耗时: 8.824s
[2025-07-25 14:40:39] SW-NE对角算符进度: 4/100, 当前算符: D_(0, 2) * D_(3, 5), 耗时: 10.536s
[2025-07-25 14:40:49] SW-NE对角算符进度: 5/100, 当前算符: D_(0, 2) * D_(4, 6), 耗时: 10.535s
[2025-07-25 14:41:00] SW-NE对角算符进度: 6/100, 当前算符: D_(0, 2) * D_(5, 27), 耗时: 10.595s
[2025-07-25 14:41:10] SW-NE对角算符进度: 7/100, 当前算符: D_(0, 2) * D_(6, 28), 耗时: 10.598s
[2025-07-25 14:41:21] SW-NE对角算符进度: 8/100, 当前算符: D_(0, 2) * D_(7, 9), 耗时: 10.594s
[2025-07-25 14:41:31] SW-NE对角算符进度: 9/100, 当前算符: D_(0, 2) * D_(8, 10), 耗时: 10.537s
[2025-07-25 14:41:42] SW-NE对角算符进度: 10/100, 当前算符: D_(0, 2) * D_(9, 31), 耗时: 10.597s
[2025-07-25 14:41:53] SW-NE对角算符进度: 11/100, 当前算符: D_(0, 2) * D_(10, 32), 耗时: 10.535s
[2025-07-25 14:42:03] SW-NE对角算符进度: 12/100, 当前算符: D_(0, 2) * D_(11, 13), 耗时: 10.537s
[2025-07-25 14:42:14] SW-NE对角算符进度: 13/100, 当前算符: D_(0, 2) * D_(12, 14), 耗时: 10.537s
[2025-07-25 14:42:24] SW-NE对角算符进度: 14/100, 当前算符: D_(0, 2) * D_(13, 35), 耗时: 10.662s
[2025-07-25 14:42:35] SW-NE对角算符进度: 15/100, 当前算符: D_(0, 2) * D_(14, 36), 耗时: 10.597s
[2025-07-25 14:42:45] SW-NE对角算符进度: 16/100, 当前算符: D_(0, 2) * D_(15, 17), 耗时: 10.536s
[2025-07-25 14:42:56] SW-NE对角算符进度: 17/100, 当前算符: D_(0, 2) * D_(16, 18), 耗时: 10.537s
[2025-07-25 14:43:06] SW-NE对角算符进度: 18/100, 当前算符: D_(0, 2) * D_(17, 39), 耗时: 10.534s
[2025-07-25 14:43:17] SW-NE对角算符进度: 19/100, 当前算符: D_(0, 2) * D_(18, 20), 耗时: 10.594s
[2025-07-25 14:43:28] SW-NE对角算符进度: 20/100, 当前算符: D_(0, 2) * D_(19, 1), 耗时: 10.612s
[2025-07-25 14:43:38] SW-NE对角算符进度: 21/100, 当前算符: D_(0, 2) * D_(20, 22), 耗时: 10.595s
[2025-07-25 14:43:49] SW-NE对角算符进度: 22/100, 当前算符: D_(0, 2) * D_(21, 43), 耗时: 10.537s
[2025-07-25 14:43:59] SW-NE对角算符进度: 23/100, 当前算符: D_(0, 2) * D_(22, 44), 耗时: 10.537s
[2025-07-25 14:44:10] SW-NE对角算符进度: 24/100, 当前算符: D_(0, 2) * D_(23, 25), 耗时: 10.595s
[2025-07-25 14:44:21] SW-NE对角算符进度: 25/100, 当前算符: D_(0, 2) * D_(24, 26), 耗时: 10.595s
[2025-07-25 14:44:31] SW-NE对角算符进度: 26/100, 当前算符: D_(0, 2) * D_(25, 47), 耗时: 10.596s
[2025-07-25 14:44:42] SW-NE对角算符进度: 27/100, 当前算符: D_(0, 2) * D_(26, 48), 耗时: 10.596s
[2025-07-25 14:44:52] SW-NE对角算符进度: 28/100, 当前算符: D_(0, 2) * D_(27, 29), 耗时: 10.595s
[2025-07-25 14:45:03] SW-NE对角算符进度: 29/100, 当前算符: D_(0, 2) * D_(28, 30), 耗时: 10.536s
[2025-07-25 14:45:13] SW-NE对角算符进度: 30/100, 当前算符: D_(0, 2) * D_(29, 51), 耗时: 10.534s
[2025-07-25 14:45:24] SW-NE对角算符进度: 31/100, 当前算符: D_(0, 2) * D_(30, 52), 耗时: 10.534s
[2025-07-25 14:45:35] SW-NE对角算符进度: 32/100, 当前算符: D_(0, 2) * D_(31, 33), 耗时: 10.597s
[2025-07-25 14:45:45] SW-NE对角算符进度: 33/100, 当前算符: D_(0, 2) * D_(32, 34), 耗时: 10.598s
[2025-07-25 14:45:56] SW-NE对角算符进度: 34/100, 当前算符: D_(0, 2) * D_(33, 55), 耗时: 10.596s
[2025-07-25 14:46:06] SW-NE对角算符进度: 35/100, 当前算符: D_(0, 2) * D_(34, 56), 耗时: 10.535s
[2025-07-25 14:46:17] SW-NE对角算符进度: 36/100, 当前算符: D_(0, 2) * D_(35, 37), 耗时: 10.537s
[2025-07-25 14:46:27] SW-NE对角算符进度: 37/100, 当前算符: D_(0, 2) * D_(36, 38), 耗时: 10.599s
[2025-07-25 14:46:38] SW-NE对角算符进度: 38/100, 当前算符: D_(0, 2) * D_(37, 59), 耗时: 10.596s
[2025-07-25 14:46:49] SW-NE对角算符进度: 39/100, 当前算符: D_(0, 2) * D_(38, 40), 耗时: 10.597s
[2025-07-25 14:46:59] SW-NE对角算符进度: 40/100, 当前算符: D_(0, 2) * D_(39, 21), 耗时: 10.536s
[2025-07-25 14:47:10] SW-NE对角算符进度: 41/100, 当前算符: D_(0, 2) * D_(40, 42), 耗时: 10.536s
[2025-07-25 14:47:20] SW-NE对角算符进度: 42/100, 当前算符: D_(0, 2) * D_(41, 63), 耗时: 10.536s
[2025-07-25 14:47:31] SW-NE对角算符进度: 43/100, 当前算符: D_(0, 2) * D_(42, 64), 耗时: 10.596s
[2025-07-25 14:47:41] SW-NE对角算符进度: 44/100, 当前算符: D_(0, 2) * D_(43, 45), 耗时: 10.599s
[2025-07-25 14:47:52] SW-NE对角算符进度: 45/100, 当前算符: D_(0, 2) * D_(44, 46), 耗时: 10.537s
[2025-07-25 14:48:02] SW-NE对角算符进度: 46/100, 当前算符: D_(0, 2) * D_(45, 67), 耗时: 10.535s
[2025-07-25 14:48:13] SW-NE对角算符进度: 47/100, 当前算符: D_(0, 2) * D_(46, 68), 耗时: 10.535s
[2025-07-25 14:48:24] SW-NE对角算符进度: 48/100, 当前算符: D_(0, 2) * D_(47, 49), 耗时: 10.535s
[2025-07-25 14:48:34] SW-NE对角算符进度: 49/100, 当前算符: D_(0, 2) * D_(48, 50), 耗时: 10.535s
[2025-07-25 14:48:45] SW-NE对角算符进度: 50/100, 当前算符: D_(0, 2) * D_(49, 71), 耗时: 10.537s
[2025-07-25 14:48:55] SW-NE对角算符进度: 51/100, 当前算符: D_(0, 2) * D_(50, 72), 耗时: 10.599s
[2025-07-25 14:49:06] SW-NE对角算符进度: 52/100, 当前算符: D_(0, 2) * D_(51, 53), 耗时: 10.597s
[2025-07-25 14:49:16] SW-NE对角算符进度: 53/100, 当前算符: D_(0, 2) * D_(52, 54), 耗时: 10.536s
[2025-07-25 14:49:27] SW-NE对角算符进度: 54/100, 当前算符: D_(0, 2) * D_(53, 75), 耗时: 10.538s
[2025-07-25 14:49:37] SW-NE对角算符进度: 55/100, 当前算符: D_(0, 2) * D_(54, 76), 耗时: 10.534s
[2025-07-25 14:49:48] SW-NE对角算符进度: 56/100, 当前算符: D_(0, 2) * D_(55, 57), 耗时: 10.595s
[2025-07-25 14:49:59] SW-NE对角算符进度: 57/100, 当前算符: D_(0, 2) * D_(56, 58), 耗时: 10.596s
[2025-07-25 14:50:09] SW-NE对角算符进度: 58/100, 当前算符: D_(0, 2) * D_(57, 79), 耗时: 10.598s
[2025-07-25 14:50:20] SW-NE对角算符进度: 59/100, 当前算符: D_(0, 2) * D_(58, 60), 耗时: 10.537s
[2025-07-25 14:50:30] SW-NE对角算符进度: 60/100, 当前算符: D_(0, 2) * D_(59, 41), 耗时: 10.536s
[2025-07-25 14:50:41] SW-NE对角算符进度: 61/100, 当前算符: D_(0, 2) * D_(60, 62), 耗时: 10.595s
[2025-07-25 14:50:52] SW-NE对角算符进度: 62/100, 当前算符: D_(0, 2) * D_(61, 83), 耗时: 10.598s
[2025-07-25 14:51:02] SW-NE对角算符进度: 63/100, 当前算符: D_(0, 2) * D_(62, 84), 耗时: 10.600s
[2025-07-25 14:51:13] SW-NE对角算符进度: 64/100, 当前算符: D_(0, 2) * D_(63, 65), 耗时: 10.534s
[2025-07-25 14:51:23] SW-NE对角算符进度: 65/100, 当前算符: D_(0, 2) * D_(64, 66), 耗时: 10.535s
[2025-07-25 14:51:34] SW-NE对角算符进度: 66/100, 当前算符: D_(0, 2) * D_(65, 87), 耗时: 10.535s
[2025-07-25 14:51:44] SW-NE对角算符进度: 67/100, 当前算符: D_(0, 2) * D_(66, 88), 耗时: 10.598s
[2025-07-25 14:51:55] SW-NE对角算符进度: 68/100, 当前算符: D_(0, 2) * D_(67, 69), 耗时: 10.598s
[2025-07-25 14:52:05] SW-NE对角算符进度: 69/100, 当前算符: D_(0, 2) * D_(68, 70), 耗时: 10.536s
[2025-07-25 14:52:16] SW-NE对角算符进度: 70/100, 当前算符: D_(0, 2) * D_(69, 91), 耗时: 10.538s
[2025-07-25 14:52:27] SW-NE对角算符进度: 71/100, 当前算符: D_(0, 2) * D_(70, 92), 耗时: 10.537s
[2025-07-25 14:52:37] SW-NE对角算符进度: 72/100, 当前算符: D_(0, 2) * D_(71, 73), 耗时: 10.595s
[2025-07-25 14:52:48] SW-NE对角算符进度: 73/100, 当前算符: D_(0, 2) * D_(72, 74), 耗时: 10.597s
[2025-07-25 14:52:58] SW-NE对角算符进度: 74/100, 当前算符: D_(0, 2) * D_(73, 95), 耗时: 10.598s
[2025-07-25 14:53:09] SW-NE对角算符进度: 75/100, 当前算符: D_(0, 2) * D_(74, 96), 耗时: 10.536s
[2025-07-25 14:53:19] SW-NE对角算符进度: 76/100, 当前算符: D_(0, 2) * D_(75, 77), 耗时: 10.536s
[2025-07-25 14:53:30] SW-NE对角算符进度: 77/100, 当前算符: D_(0, 2) * D_(76, 78), 耗时: 10.596s
[2025-07-25 14:53:41] SW-NE对角算符进度: 78/100, 当前算符: D_(0, 2) * D_(77, 99), 耗时: 10.597s
[2025-07-25 14:53:51] SW-NE对角算符进度: 79/100, 当前算符: D_(0, 2) * D_(78, 80), 耗时: 10.596s
[2025-07-25 14:54:02] SW-NE对角算符进度: 80/100, 当前算符: D_(0, 2) * D_(79, 61), 耗时: 10.534s
[2025-07-25 14:54:12] SW-NE对角算符进度: 81/100, 当前算符: D_(0, 2) * D_(80, 82), 耗时: 10.534s
[2025-07-25 14:54:23] SW-NE对角算符进度: 82/100, 当前算符: D_(0, 2) * D_(81, 3), 耗时: 10.534s
[2025-07-25 14:54:33] SW-NE对角算符进度: 83/100, 当前算符: D_(0, 2) * D_(82, 4), 耗时: 10.597s
[2025-07-25 14:54:44] SW-NE对角算符进度: 84/100, 当前算符: D_(0, 2) * D_(83, 85), 耗时: 10.599s
[2025-07-25 14:54:55] SW-NE对角算符进度: 85/100, 当前算符: D_(0, 2) * D_(84, 86), 耗时: 10.535s
[2025-07-25 14:55:05] SW-NE对角算符进度: 86/100, 当前算符: D_(0, 2) * D_(85, 7), 耗时: 10.536s
[2025-07-25 14:55:16] SW-NE对角算符进度: 87/100, 当前算符: D_(0, 2) * D_(86, 8), 耗时: 10.536s
[2025-07-25 14:55:26] SW-NE对角算符进度: 88/100, 当前算符: D_(0, 2) * D_(87, 89), 耗时: 10.595s
[2025-07-25 14:55:37] SW-NE对角算符进度: 89/100, 当前算符: D_(0, 2) * D_(88, 90), 耗时: 10.595s
[2025-07-25 14:55:47] SW-NE对角算符进度: 90/100, 当前算符: D_(0, 2) * D_(89, 11), 耗时: 10.597s
[2025-07-25 14:55:58] SW-NE对角算符进度: 91/100, 当前算符: D_(0, 2) * D_(90, 12), 耗时: 10.596s
[2025-07-25 14:56:09] SW-NE对角算符进度: 92/100, 当前算符: D_(0, 2) * D_(91, 93), 耗时: 10.595s
[2025-07-25 14:56:19] SW-NE对角算符进度: 93/100, 当前算符: D_(0, 2) * D_(92, 94), 耗时: 10.534s
[2025-07-25 14:56:30] SW-NE对角算符进度: 94/100, 当前算符: D_(0, 2) * D_(93, 15), 耗时: 10.534s
[2025-07-25 14:56:40] SW-NE对角算符进度: 95/100, 当前算符: D_(0, 2) * D_(94, 16), 耗时: 10.536s
[2025-07-25 14:56:51] SW-NE对角算符进度: 96/100, 当前算符: D_(0, 2) * D_(95, 97), 耗时: 10.594s
[2025-07-25 14:57:01] SW-NE对角算符进度: 97/100, 当前算符: D_(0, 2) * D_(96, 98), 耗时: 10.594s
[2025-07-25 14:57:12] SW-NE对角算符进度: 98/100, 当前算符: D_(0, 2) * D_(97, 19), 耗时: 10.608s
[2025-07-25 14:57:21] SW-NE对角算符进度: 99/100, 当前算符: D_(0, 2) * D_(98, 0), 耗时: 8.778s
[2025-07-25 14:57:31] SW-NE对角算符进度: 100/100, 当前算符: D_(0, 2) * D_(99, 81), 耗时: 10.536s
[2025-07-25 14:57:31] 西南-东北方向对角二聚体相关函数计算完成,耗时: 1049.06 秒
[2025-07-25 14:57:31] 计算傅里叶变换...
[2025-07-25 14:57:32] 对角二聚体结构因子计算完成
[2025-07-25 14:57:33] 对角二聚体相关函数平均误差: 0.000122
