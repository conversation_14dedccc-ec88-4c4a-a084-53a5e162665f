[2025-07-25 09:09:12] ================================================================================
[2025-07-25 09:09:12] 加载量子态: L=5, J2=1.00, J1=0.80
[2025-07-25 09:09:12] 设置样本数为: 1048576
[2025-07-25 09:09:12] 开始生成共享样本集...
[2025-07-25 09:12:31] 样本生成完成,耗时: 199.572 秒
[2025-07-25 09:12:31] ================================================================================
[2025-07-25 09:12:31] 开始计算自旋结构因子...
[2025-07-25 09:12:31] 初始化操作符缓存...
[2025-07-25 09:12:31] 预构建所有自旋相关操作符...
[2025-07-25 09:12:31] 开始计算自旋相关函数...
[2025-07-25 09:12:43] 自旋算符进度: 1/100, 当前算符: S_0 · S_0, 耗时: 11.216s
[2025-07-25 09:12:55] 自旋算符进度: 2/100, 当前算符: S_0 · S_1, 耗时: 12.157s
[2025-07-25 09:13:01] 自旋算符进度: 3/100, 当前算符: S_0 · S_2, 耗时: 6.307s
[2025-07-25 09:13:07] 自旋算符进度: 4/100, 当前算符: S_0 · S_3, 耗时: 6.303s
[2025-07-25 09:13:14] 自旋算符进度: 5/100, 当前算符: S_0 · S_4, 耗时: 6.277s
[2025-07-25 09:13:20] 自旋算符进度: 6/100, 当前算符: S_0 · S_5, 耗时: 6.278s
[2025-07-25 09:13:26] 自旋算符进度: 7/100, 当前算符: S_0 · S_6, 耗时: 6.307s
[2025-07-25 09:13:32] 自旋算符进度: 8/100, 当前算符: S_0 · S_7, 耗时: 6.277s
[2025-07-25 09:13:39] 自旋算符进度: 9/100, 当前算符: S_0 · S_8, 耗时: 6.276s
[2025-07-25 09:13:45] 自旋算符进度: 10/100, 当前算符: S_0 · S_9, 耗时: 6.276s
[2025-07-25 09:13:51] 自旋算符进度: 11/100, 当前算符: S_0 · S_10, 耗时: 6.278s
[2025-07-25 09:13:58] 自旋算符进度: 12/100, 当前算符: S_0 · S_11, 耗时: 6.277s
[2025-07-25 09:14:04] 自旋算符进度: 13/100, 当前算符: S_0 · S_12, 耗时: 6.308s
[2025-07-25 09:14:10] 自旋算符进度: 14/100, 当前算符: S_0 · S_13, 耗时: 6.306s
[2025-07-25 09:14:17] 自旋算符进度: 15/100, 当前算符: S_0 · S_14, 耗时: 6.307s
[2025-07-25 09:14:23] 自旋算符进度: 16/100, 当前算符: S_0 · S_15, 耗时: 6.279s
[2025-07-25 09:14:29] 自旋算符进度: 17/100, 当前算符: S_0 · S_16, 耗时: 6.280s
[2025-07-25 09:14:35] 自旋算符进度: 18/100, 当前算符: S_0 · S_17, 耗时: 6.304s
[2025-07-25 09:14:42] 自旋算符进度: 19/100, 当前算符: S_0 · S_18, 耗时: 6.312s
[2025-07-25 09:14:48] 自旋算符进度: 20/100, 当前算符: S_0 · S_19, 耗时: 6.310s
[2025-07-25 09:14:54] 自旋算符进度: 21/100, 当前算符: S_0 · S_20, 耗时: 6.280s
[2025-07-25 09:15:01] 自旋算符进度: 22/100, 当前算符: S_0 · S_21, 耗时: 6.278s
[2025-07-25 09:15:07] 自旋算符进度: 23/100, 当前算符: S_0 · S_22, 耗时: 6.280s
[2025-07-25 09:15:13] 自旋算符进度: 24/100, 当前算符: S_0 · S_23, 耗时: 6.308s
[2025-07-25 09:15:19] 自旋算符进度: 25/100, 当前算符: S_0 · S_24, 耗时: 6.310s
[2025-07-25 09:15:26] 自旋算符进度: 26/100, 当前算符: S_0 · S_25, 耗时: 6.308s
[2025-07-25 09:15:32] 自旋算符进度: 27/100, 当前算符: S_0 · S_26, 耗时: 6.277s
[2025-07-25 09:15:38] 自旋算符进度: 28/100, 当前算符: S_0 · S_27, 耗时: 6.280s
[2025-07-25 09:15:45] 自旋算符进度: 29/100, 当前算符: S_0 · S_28, 耗时: 6.280s
[2025-07-25 09:15:51] 自旋算符进度: 30/100, 当前算符: S_0 · S_29, 耗时: 6.310s
[2025-07-25 09:15:57] 自旋算符进度: 31/100, 当前算符: S_0 · S_30, 耗时: 6.309s
[2025-07-25 09:16:04] 自旋算符进度: 32/100, 当前算符: S_0 · S_31, 耗时: 6.308s
[2025-07-25 09:16:10] 自旋算符进度: 33/100, 当前算符: S_0 · S_32, 耗时: 6.279s
[2025-07-25 09:16:16] 自旋算符进度: 34/100, 当前算符: S_0 · S_33, 耗时: 6.279s
[2025-07-25 09:16:22] 自旋算符进度: 35/100, 当前算符: S_0 · S_34, 耗时: 6.280s
[2025-07-25 09:16:29] 自旋算符进度: 36/100, 当前算符: S_0 · S_35, 耗时: 6.280s
[2025-07-25 09:16:35] 自旋算符进度: 37/100, 当前算符: S_0 · S_36, 耗时: 6.279s
[2025-07-25 09:16:41] 自旋算符进度: 38/100, 当前算符: S_0 · S_37, 耗时: 6.278s
[2025-07-25 09:16:48] 自旋算符进度: 39/100, 当前算符: S_0 · S_38, 耗时: 6.312s
[2025-07-25 09:16:54] 自旋算符进度: 40/100, 当前算符: S_0 · S_39, 耗时: 6.310s
[2025-07-25 09:17:00] 自旋算符进度: 41/100, 当前算符: S_0 · S_40, 耗时: 6.311s
[2025-07-25 09:17:07] 自旋算符进度: 42/100, 当前算符: S_0 · S_41, 耗时: 6.310s
[2025-07-25 09:17:13] 自旋算符进度: 43/100, 当前算符: S_0 · S_42, 耗时: 6.311s
[2025-07-25 09:17:19] 自旋算符进度: 44/100, 当前算符: S_0 · S_43, 耗时: 6.310s
[2025-07-25 09:17:25] 自旋算符进度: 45/100, 当前算符: S_0 · S_44, 耗时: 6.280s
[2025-07-25 09:17:32] 自旋算符进度: 46/100, 当前算符: S_0 · S_45, 耗时: 6.283s
[2025-07-25 09:17:38] 自旋算符进度: 47/100, 当前算符: S_0 · S_46, 耗时: 6.312s
[2025-07-25 09:17:44] 自旋算符进度: 48/100, 当前算符: S_0 · S_47, 耗时: 6.312s
[2025-07-25 09:17:51] 自旋算符进度: 49/100, 当前算符: S_0 · S_48, 耗时: 6.312s
[2025-07-25 09:17:57] 自旋算符进度: 50/100, 当前算符: S_0 · S_49, 耗时: 6.282s
[2025-07-25 09:18:03] 自旋算符进度: 51/100, 当前算符: S_0 · S_50, 耗时: 6.284s
[2025-07-25 09:18:09] 自旋算符进度: 52/100, 当前算符: S_0 · S_51, 耗时: 6.281s
[2025-07-25 09:18:16] 自旋算符进度: 53/100, 当前算符: S_0 · S_52, 耗时: 6.314s
[2025-07-25 09:18:22] 自旋算符进度: 54/100, 当前算符: S_0 · S_53, 耗时: 6.370s
[2025-07-25 09:18:28] 自旋算符进度: 55/100, 当前算符: S_0 · S_54, 耗时: 6.284s
[2025-07-25 09:18:35] 自旋算符进度: 56/100, 当前算符: S_0 · S_55, 耗时: 6.283s
[2025-07-25 09:18:41] 自旋算符进度: 57/100, 当前算符: S_0 · S_56, 耗时: 6.283s
[2025-07-25 09:18:47] 自旋算符进度: 58/100, 当前算符: S_0 · S_57, 耗时: 6.313s
[2025-07-25 09:18:54] 自旋算符进度: 59/100, 当前算符: S_0 · S_58, 耗时: 6.314s
[2025-07-25 09:19:00] 自旋算符进度: 60/100, 当前算符: S_0 · S_59, 耗时: 6.314s
[2025-07-25 09:19:06] 自旋算符进度: 61/100, 当前算符: S_0 · S_60, 耗时: 6.286s
[2025-07-25 09:19:13] 自旋算符进度: 62/100, 当前算符: S_0 · S_61, 耗时: 6.285s
[2025-07-25 09:19:19] 自旋算符进度: 63/100, 当前算符: S_0 · S_62, 耗时: 6.285s
[2025-07-25 09:19:25] 自旋算符进度: 64/100, 当前算符: S_0 · S_63, 耗时: 6.315s
[2025-07-25 09:19:31] 自旋算符进度: 65/100, 当前算符: S_0 · S_64, 耗时: 6.314s
[2025-07-25 09:19:38] 自旋算符进度: 66/100, 当前算符: S_0 · S_65, 耗时: 6.309s
[2025-07-25 09:19:44] 自旋算符进度: 67/100, 当前算符: S_0 · S_66, 耗时: 6.284s
[2025-07-25 09:19:50] 自旋算符进度: 68/100, 当前算符: S_0 · S_67, 耗时: 6.282s
[2025-07-25 09:19:57] 自旋算符进度: 69/100, 当前算符: S_0 · S_68, 耗时: 6.313s
[2025-07-25 09:20:03] 自旋算符进度: 70/100, 当前算符: S_0 · S_69, 耗时: 6.313s
[2025-07-25 09:20:09] 自旋算符进度: 71/100, 当前算符: S_0 · S_70, 耗时: 6.312s
[2025-07-25 09:20:16] 自旋算符进度: 72/100, 当前算符: S_0 · S_71, 耗时: 6.284s
[2025-07-25 09:20:22] 自旋算符进度: 73/100, 当前算符: S_0 · S_72, 耗时: 6.285s
[2025-07-25 09:20:28] 自旋算符进度: 74/100, 当前算符: S_0 · S_73, 耗时: 6.283s
[2025-07-25 09:20:34] 自旋算符进度: 75/100, 当前算符: S_0 · S_74, 耗时: 6.313s
[2025-07-25 09:20:41] 自旋算符进度: 76/100, 当前算符: S_0 · S_75, 耗时: 6.315s
[2025-07-25 09:20:47] 自旋算符进度: 77/100, 当前算符: S_0 · S_76, 耗时: 6.285s
[2025-07-25 09:20:53] 自旋算符进度: 78/100, 当前算符: S_0 · S_77, 耗时: 6.283s
[2025-07-25 09:21:00] 自旋算符进度: 79/100, 当前算符: S_0 · S_78, 耗时: 6.282s
[2025-07-25 09:21:06] 自旋算符进度: 80/100, 当前算符: S_0 · S_79, 耗时: 6.315s
[2025-07-25 09:21:12] 自旋算符进度: 81/100, 当前算符: S_0 · S_80, 耗时: 6.314s
[2025-07-25 09:21:19] 自旋算符进度: 82/100, 当前算符: S_0 · S_81, 耗时: 6.284s
[2025-07-25 09:21:25] 自旋算符进度: 83/100, 当前算符: S_0 · S_82, 耗时: 6.284s
[2025-07-25 09:21:31] 自旋算符进度: 84/100, 当前算符: S_0 · S_83, 耗时: 6.283s
[2025-07-25 09:21:37] 自旋算符进度: 85/100, 当前算符: S_0 · S_84, 耗时: 6.313s
[2025-07-25 09:21:44] 自旋算符进度: 86/100, 当前算符: S_0 · S_85, 耗时: 6.313s
[2025-07-25 09:21:50] 自旋算符进度: 87/100, 当前算符: S_0 · S_86, 耗时: 6.315s
[2025-07-25 09:21:56] 自旋算符进度: 88/100, 当前算符: S_0 · S_87, 耗时: 6.284s
[2025-07-25 09:22:03] 自旋算符进度: 89/100, 当前算符: S_0 · S_88, 耗时: 6.283s
[2025-07-25 09:22:09] 自旋算符进度: 90/100, 当前算符: S_0 · S_89, 耗时: 6.315s
[2025-07-25 09:22:15] 自旋算符进度: 91/100, 当前算符: S_0 · S_90, 耗时: 6.315s
[2025-07-25 09:22:22] 自旋算符进度: 92/100, 当前算符: S_0 · S_91, 耗时: 6.315s
[2025-07-25 09:22:28] 自旋算符进度: 93/100, 当前算符: S_0 · S_92, 耗时: 6.285s
[2025-07-25 09:22:34] 自旋算符进度: 94/100, 当前算符: S_0 · S_93, 耗时: 6.306s
[2025-07-25 09:22:40] 自旋算符进度: 95/100, 当前算符: S_0 · S_94, 耗时: 6.283s
[2025-07-25 09:22:47] 自旋算符进度: 96/100, 当前算符: S_0 · S_95, 耗时: 6.281s
[2025-07-25 09:22:53] 自旋算符进度: 97/100, 当前算符: S_0 · S_96, 耗时: 6.313s
[2025-07-25 09:22:59] 自旋算符进度: 98/100, 当前算符: S_0 · S_97, 耗时: 6.314s
[2025-07-25 09:23:06] 自旋算符进度: 99/100, 当前算符: S_0 · S_98, 耗时: 6.311s
[2025-07-25 09:23:12] 自旋算符进度: 100/100, 当前算符: S_0 · S_99, 耗时: 6.285s
[2025-07-25 09:23:12] 自旋相关函数计算完成,总耗时 640.60 秒
[2025-07-25 09:23:12] 计算傅里叶变换...
[2025-07-25 09:23:13] 自旋结构因子计算完成
[2025-07-25 09:23:14] 自旋相关函数平均误差: 0.000729
[2025-07-25 09:23:14] ================================================================================
[2025-07-25 09:23:14] 开始计算对角二聚体结构因子...
[2025-07-25 09:23:14] 识别所有对角二聚体...
[2025-07-25 09:23:14] 总共找到 100 个西北-东南方向对角二聚体和 100 个西南-东北方向对角二聚体
[2025-07-25 09:23:14] 预计算对角二聚体操作符...
[2025-07-25 09:23:16] 开始计算西北-东南方向对角二聚体相关函数 (100 个算符)...
[2025-07-25 09:23:23] NW-SE对角算符进度: 1/100, 当前算符: D_(0, 18) * D_(0, 18), 耗时: 6.331s
[2025-07-25 09:23:39] NW-SE对角算符进度: 2/100, 当前算符: D_(0, 18) * D_(1, 39), 耗时: 16.282s
[2025-07-25 09:23:50] NW-SE对角算符进度: 3/100, 当前算符: D_(0, 18) * D_(2, 20), 耗时: 10.632s
[2025-07-25 09:24:00] NW-SE对角算符进度: 4/100, 当前算符: D_(0, 18) * D_(3, 1), 耗时: 10.563s
[2025-07-25 09:24:11] NW-SE对角算符进度: 5/100, 当前算符: D_(0, 18) * D_(4, 2), 耗时: 10.554s
[2025-07-25 09:24:21] NW-SE对角算符进度: 6/100, 当前算符: D_(0, 18) * D_(5, 23), 耗时: 10.628s
[2025-07-25 09:24:32] NW-SE对角算符进度: 7/100, 当前算符: D_(0, 18) * D_(6, 24), 耗时: 10.556s
[2025-07-25 09:24:43] NW-SE对角算符进度: 8/100, 当前算符: D_(0, 18) * D_(7, 5), 耗时: 10.563s
[2025-07-25 09:24:53] NW-SE对角算符进度: 9/100, 当前算符: D_(0, 18) * D_(8, 6), 耗时: 10.568s
[2025-07-25 09:25:04] NW-SE对角算符进度: 10/100, 当前算符: D_(0, 18) * D_(9, 27), 耗时: 10.551s
[2025-07-25 09:25:14] NW-SE对角算符进度: 11/100, 当前算符: D_(0, 18) * D_(10, 28), 耗时: 10.552s
[2025-07-25 09:25:25] NW-SE对角算符进度: 12/100, 当前算符: D_(0, 18) * D_(11, 9), 耗时: 10.561s
[2025-07-25 09:25:35] NW-SE对角算符进度: 13/100, 当前算符: D_(0, 18) * D_(12, 10), 耗时: 10.640s
[2025-07-25 09:25:46] NW-SE对角算符进度: 14/100, 当前算符: D_(0, 18) * D_(13, 31), 耗时: 10.633s
[2025-07-25 09:25:57] NW-SE对角算符进度: 15/100, 当前算符: D_(0, 18) * D_(14, 32), 耗时: 10.559s
[2025-07-25 09:26:07] NW-SE对角算符进度: 16/100, 当前算符: D_(0, 18) * D_(15, 13), 耗时: 10.573s
[2025-07-25 09:26:18] NW-SE对角算符进度: 17/100, 当前算符: D_(0, 18) * D_(16, 14), 耗时: 10.570s
[2025-07-25 09:26:28] NW-SE对角算符进度: 18/100, 当前算符: D_(0, 18) * D_(17, 35), 耗时: 10.638s
[2025-07-25 09:26:47] NW-SE对角算符进度: 19/100, 当前算符: D_(0, 18) * D_(18, 36), 耗时: 18.503s
[2025-07-25 09:26:57] NW-SE对角算符进度: 20/100, 当前算符: D_(0, 18) * D_(19, 17), 耗时: 10.598s
[2025-07-25 09:27:08] NW-SE对角算符进度: 21/100, 当前算符: D_(0, 18) * D_(20, 38), 耗时: 10.552s
[2025-07-25 09:27:19] NW-SE对角算符进度: 22/100, 当前算符: D_(0, 18) * D_(21, 59), 耗时: 10.552s
[2025-07-25 09:27:29] NW-SE对角算符进度: 23/100, 当前算符: D_(0, 18) * D_(22, 40), 耗时: 10.601s
[2025-07-25 09:27:40] NW-SE对角算符进度: 24/100, 当前算符: D_(0, 18) * D_(23, 21), 耗时: 10.603s
[2025-07-25 09:27:50] NW-SE对角算符进度: 25/100, 当前算符: D_(0, 18) * D_(24, 22), 耗时: 10.605s
[2025-07-25 09:28:01] NW-SE对角算符进度: 26/100, 当前算符: D_(0, 18) * D_(25, 43), 耗时: 10.592s
[2025-07-25 09:28:12] NW-SE对角算符进度: 27/100, 当前算符: D_(0, 18) * D_(26, 44), 耗时: 10.550s
[2025-07-25 09:28:22] NW-SE对角算符进度: 28/100, 当前算符: D_(0, 18) * D_(27, 25), 耗时: 10.623s
[2025-07-25 09:28:33] NW-SE对角算符进度: 29/100, 当前算符: D_(0, 18) * D_(28, 26), 耗时: 10.603s
[2025-07-25 09:28:43] NW-SE对角算符进度: 30/100, 当前算符: D_(0, 18) * D_(29, 47), 耗时: 10.607s
[2025-07-25 09:28:54] NW-SE对角算符进度: 31/100, 当前算符: D_(0, 18) * D_(30, 48), 耗时: 10.601s
[2025-07-25 09:29:05] NW-SE对角算符进度: 32/100, 当前算符: D_(0, 18) * D_(31, 29), 耗时: 10.549s
[2025-07-25 09:29:15] NW-SE对角算符进度: 33/100, 当前算符: D_(0, 18) * D_(32, 30), 耗时: 10.553s
[2025-07-25 09:29:26] NW-SE对角算符进度: 34/100, 当前算符: D_(0, 18) * D_(33, 51), 耗时: 10.553s
[2025-07-25 09:29:36] NW-SE对角算符进度: 35/100, 当前算符: D_(0, 18) * D_(34, 52), 耗时: 10.612s
[2025-07-25 09:29:47] NW-SE对角算符进度: 36/100, 当前算符: D_(0, 18) * D_(35, 33), 耗时: 10.602s
[2025-07-25 09:29:57] NW-SE对角算符进度: 37/100, 当前算符: D_(0, 18) * D_(36, 34), 耗时: 10.550s
[2025-07-25 09:30:08] NW-SE对角算符进度: 38/100, 当前算符: D_(0, 18) * D_(37, 55), 耗时: 10.550s
[2025-07-25 09:30:19] NW-SE对角算符进度: 39/100, 当前算符: D_(0, 18) * D_(38, 56), 耗时: 10.602s
[2025-07-25 09:30:29] NW-SE对角算符进度: 40/100, 当前算符: D_(0, 18) * D_(39, 37), 耗时: 10.603s
[2025-07-25 09:30:40] NW-SE对角算符进度: 41/100, 当前算符: D_(0, 18) * D_(40, 58), 耗时: 10.605s
[2025-07-25 09:30:50] NW-SE对角算符进度: 42/100, 当前算符: D_(0, 18) * D_(41, 79), 耗时: 10.612s
[2025-07-25 09:31:01] NW-SE对角算符进度: 43/100, 当前算符: D_(0, 18) * D_(42, 60), 耗时: 10.609s
[2025-07-25 09:31:12] NW-SE对角算符进度: 44/100, 当前算符: D_(0, 18) * D_(43, 41), 耗时: 10.556s
[2025-07-25 09:31:22] NW-SE对角算符进度: 45/100, 当前算符: D_(0, 18) * D_(44, 42), 耗时: 10.610s
[2025-07-25 09:31:33] NW-SE对角算符进度: 46/100, 当前算符: D_(0, 18) * D_(45, 63), 耗时: 10.553s
[2025-07-25 09:31:43] NW-SE对角算符进度: 47/100, 当前算符: D_(0, 18) * D_(46, 64), 耗时: 10.607s
[2025-07-25 09:31:54] NW-SE对角算符进度: 48/100, 当前算符: D_(0, 18) * D_(47, 45), 耗时: 10.605s
[2025-07-25 09:32:04] NW-SE对角算符进度: 49/100, 当前算符: D_(0, 18) * D_(48, 46), 耗时: 10.554s
[2025-07-25 09:32:15] NW-SE对角算符进度: 50/100, 当前算符: D_(0, 18) * D_(49, 67), 耗时: 10.547s
[2025-07-25 09:32:26] NW-SE对角算符进度: 51/100, 当前算符: D_(0, 18) * D_(50, 68), 耗时: 10.554s
[2025-07-25 09:32:36] NW-SE对角算符进度: 52/100, 当前算符: D_(0, 18) * D_(51, 49), 耗时: 10.605s
[2025-07-25 09:32:47] NW-SE对角算符进度: 53/100, 当前算符: D_(0, 18) * D_(52, 50), 耗时: 10.606s
[2025-07-25 09:32:57] NW-SE对角算符进度: 54/100, 当前算符: D_(0, 18) * D_(53, 71), 耗时: 10.606s
[2025-07-25 09:33:08] NW-SE对角算符进度: 55/100, 当前算符: D_(0, 18) * D_(54, 72), 耗时: 10.541s
[2025-07-25 09:33:19] NW-SE对角算符进度: 56/100, 当前算符: D_(0, 18) * D_(55, 53), 耗时: 10.546s
[2025-07-25 09:33:29] NW-SE对角算符进度: 57/100, 当前算符: D_(0, 18) * D_(56, 54), 耗时: 10.609s
[2025-07-25 09:33:40] NW-SE对角算符进度: 58/100, 当前算符: D_(0, 18) * D_(57, 75), 耗时: 10.612s
[2025-07-25 09:33:50] NW-SE对角算符进度: 59/100, 当前算符: D_(0, 18) * D_(58, 76), 耗时: 10.609s
[2025-07-25 09:34:01] NW-SE对角算符进度: 60/100, 当前算符: D_(0, 18) * D_(59, 57), 耗时: 10.550s
[2025-07-25 09:34:11] NW-SE对角算符进度: 61/100, 当前算符: D_(0, 18) * D_(60, 78), 耗时: 10.558s
[2025-07-25 09:34:22] NW-SE对角算符进度: 62/100, 当前算符: D_(0, 18) * D_(61, 99), 耗时: 10.607s
[2025-07-25 09:34:33] NW-SE对角算符进度: 63/100, 当前算符: D_(0, 18) * D_(62, 80), 耗时: 10.709s
[2025-07-25 09:34:43] NW-SE对角算符进度: 64/100, 当前算符: D_(0, 18) * D_(63, 61), 耗时: 10.603s
[2025-07-25 09:34:54] NW-SE对角算符进度: 65/100, 当前算符: D_(0, 18) * D_(64, 62), 耗时: 10.551s
[2025-07-25 09:35:04] NW-SE对角算符进度: 66/100, 当前算符: D_(0, 18) * D_(65, 83), 耗时: 10.552s
[2025-07-25 09:35:15] NW-SE对角算符进度: 67/100, 当前算符: D_(0, 18) * D_(66, 84), 耗时: 10.547s
[2025-07-25 09:35:26] NW-SE对角算符进度: 68/100, 当前算符: D_(0, 18) * D_(67, 65), 耗时: 10.607s
[2025-07-25 09:35:36] NW-SE对角算符进度: 69/100, 当前算符: D_(0, 18) * D_(68, 66), 耗时: 10.602s
[2025-07-25 09:35:47] NW-SE对角算符进度: 70/100, 当前算符: D_(0, 18) * D_(69, 87), 耗时: 10.550s
[2025-07-25 09:35:57] NW-SE对角算符进度: 71/100, 当前算符: D_(0, 18) * D_(70, 88), 耗时: 10.551s
[2025-07-25 09:36:08] NW-SE对角算符进度: 72/100, 当前算符: D_(0, 18) * D_(71, 69), 耗时: 10.546s
[2025-07-25 09:36:19] NW-SE对角算符进度: 73/100, 当前算符: D_(0, 18) * D_(72, 70), 耗时: 10.610s
[2025-07-25 09:36:29] NW-SE对角算符进度: 74/100, 当前算符: D_(0, 18) * D_(73, 91), 耗时: 10.605s
[2025-07-25 09:36:40] NW-SE对角算符进度: 75/100, 当前算符: D_(0, 18) * D_(74, 92), 耗时: 10.588s
[2025-07-25 09:36:50] NW-SE对角算符进度: 76/100, 当前算符: D_(0, 18) * D_(75, 73), 耗时: 10.552s
[2025-07-25 09:37:01] NW-SE对角算符进度: 77/100, 当前算符: D_(0, 18) * D_(76, 74), 耗时: 10.545s
[2025-07-25 09:37:11] NW-SE对角算符进度: 78/100, 当前算符: D_(0, 18) * D_(77, 95), 耗时: 10.548s
[2025-07-25 09:37:22] NW-SE对角算符进度: 79/100, 当前算符: D_(0, 18) * D_(78, 96), 耗时: 10.549s
[2025-07-25 09:37:33] NW-SE对角算符进度: 80/100, 当前算符: D_(0, 18) * D_(79, 77), 耗时: 10.605s
[2025-07-25 09:37:43] NW-SE对角算符进度: 81/100, 当前算符: D_(0, 18) * D_(80, 98), 耗时: 10.607s
[2025-07-25 09:37:54] NW-SE对角算符进度: 82/100, 当前算符: D_(0, 18) * D_(81, 19), 耗时: 10.593s
[2025-07-25 09:38:03] NW-SE对角算符进度: 83/100, 当前算符: D_(0, 18) * D_(82, 0), 耗时: 8.833s
[2025-07-25 09:38:13] NW-SE对角算符进度: 84/100, 当前算符: D_(0, 18) * D_(83, 81), 耗时: 10.551s
[2025-07-25 09:38:24] NW-SE对角算符进度: 85/100, 当前算符: D_(0, 18) * D_(84, 82), 耗时: 10.606s
[2025-07-25 09:38:34] NW-SE对角算符进度: 86/100, 当前算符: D_(0, 18) * D_(85, 3), 耗时: 10.607s
[2025-07-25 09:38:45] NW-SE对角算符进度: 87/100, 当前算符: D_(0, 18) * D_(86, 4), 耗时: 10.601s
[2025-07-25 09:38:55] NW-SE对角算符进度: 88/100, 当前算符: D_(0, 18) * D_(87, 85), 耗时: 10.544s
[2025-07-25 09:39:06] NW-SE对角算符进度: 89/100, 当前算符: D_(0, 18) * D_(88, 86), 耗时: 10.557s
[2025-07-25 09:39:17] NW-SE对角算符进度: 90/100, 当前算符: D_(0, 18) * D_(89, 7), 耗时: 10.554s
[2025-07-25 09:39:27] NW-SE对角算符进度: 91/100, 当前算符: D_(0, 18) * D_(90, 8), 耗时: 10.602s
[2025-07-25 09:39:38] NW-SE对角算符进度: 92/100, 当前算符: D_(0, 18) * D_(91, 89), 耗时: 10.607s
[2025-07-25 09:39:48] NW-SE对角算符进度: 93/100, 当前算符: D_(0, 18) * D_(92, 90), 耗时: 10.608s
[2025-07-25 09:39:59] NW-SE对角算符进度: 94/100, 当前算符: D_(0, 18) * D_(93, 11), 耗时: 10.600s
[2025-07-25 09:40:10] NW-SE对角算符进度: 95/100, 当前算符: D_(0, 18) * D_(94, 12), 耗时: 10.555s
[2025-07-25 09:40:20] NW-SE对角算符进度: 96/100, 当前算符: D_(0, 18) * D_(95, 93), 耗时: 10.551s
[2025-07-25 09:40:31] NW-SE对角算符进度: 97/100, 当前算符: D_(0, 18) * D_(96, 94), 耗时: 10.549s
[2025-07-25 09:40:41] NW-SE对角算符进度: 98/100, 当前算符: D_(0, 18) * D_(97, 15), 耗时: 10.613s
[2025-07-25 09:40:52] NW-SE对角算符进度: 99/100, 当前算符: D_(0, 18) * D_(98, 16), 耗时: 10.617s
[2025-07-25 09:41:03] NW-SE对角算符进度: 100/100, 当前算符: D_(0, 18) * D_(99, 97), 耗时: 10.612s
[2025-07-25 09:41:03] 西北-东南方向对角二聚体相关函数计算完成,耗时: 1066.10 秒
[2025-07-25 09:41:03] ================================================================================
[2025-07-25 09:41:03] 开始计算西南-东北方向对角二聚体相关函数 (100 个算符)...
[2025-07-25 09:41:09] SW-NE对角算符进度: 1/100, 当前算符: D_(0, 2) * D_(0, 2), 耗时: 6.304s
[2025-07-25 09:41:19] SW-NE对角算符进度: 2/100, 当前算符: D_(0, 2) * D_(1, 23), 耗时: 10.614s
[2025-07-25 09:41:28] SW-NE对角算符进度: 3/100, 当前算符: D_(0, 2) * D_(2, 24), 耗时: 8.835s
[2025-07-25 09:41:39] SW-NE对角算符进度: 4/100, 当前算符: D_(0, 2) * D_(3, 5), 耗时: 10.552s
[2025-07-25 09:41:49] SW-NE对角算符进度: 5/100, 当前算符: D_(0, 2) * D_(4, 6), 耗时: 10.552s
[2025-07-25 09:42:00] SW-NE对角算符进度: 6/100, 当前算符: D_(0, 2) * D_(5, 27), 耗时: 10.613s
[2025-07-25 09:42:11] SW-NE对角算符进度: 7/100, 当前算符: D_(0, 2) * D_(6, 28), 耗时: 10.616s
[2025-07-25 09:42:21] SW-NE对角算符进度: 8/100, 当前算符: D_(0, 2) * D_(7, 9), 耗时: 10.606s
[2025-07-25 09:42:32] SW-NE对角算符进度: 9/100, 当前算符: D_(0, 2) * D_(8, 10), 耗时: 10.562s
[2025-07-25 09:42:42] SW-NE对角算符进度: 10/100, 当前算符: D_(0, 2) * D_(9, 31), 耗时: 10.617s
[2025-07-25 09:42:53] SW-NE对角算符进度: 11/100, 当前算符: D_(0, 2) * D_(10, 32), 耗时: 10.555s
[2025-07-25 09:43:04] SW-NE对角算符进度: 12/100, 当前算符: D_(0, 2) * D_(11, 13), 耗时: 10.560s
[2025-07-25 09:43:14] SW-NE对角算符进度: 13/100, 当前算符: D_(0, 2) * D_(12, 14), 耗时: 10.555s
[2025-07-25 09:43:25] SW-NE对角算符进度: 14/100, 当前算符: D_(0, 2) * D_(13, 35), 耗时: 10.615s
[2025-07-25 09:43:35] SW-NE对角算符进度: 15/100, 当前算符: D_(0, 2) * D_(14, 36), 耗时: 10.618s
[2025-07-25 09:43:46] SW-NE对角算符进度: 16/100, 当前算符: D_(0, 2) * D_(15, 17), 耗时: 10.558s
[2025-07-25 09:43:56] SW-NE对角算符进度: 17/100, 当前算符: D_(0, 2) * D_(16, 18), 耗时: 10.559s
[2025-07-25 09:44:07] SW-NE对角算符进度: 18/100, 当前算符: D_(0, 2) * D_(17, 39), 耗时: 10.553s
[2025-07-25 09:44:18] SW-NE对角算符进度: 19/100, 当前算符: D_(0, 2) * D_(18, 20), 耗时: 10.610s
[2025-07-25 09:44:28] SW-NE对角算符进度: 20/100, 当前算符: D_(0, 2) * D_(19, 1), 耗时: 10.615s
[2025-07-25 09:44:39] SW-NE对角算符进度: 21/100, 当前算符: D_(0, 2) * D_(20, 22), 耗时: 10.612s
[2025-07-25 09:44:49] SW-NE对角算符进度: 22/100, 当前算符: D_(0, 2) * D_(21, 43), 耗时: 10.556s
[2025-07-25 09:45:00] SW-NE对角算符进度: 23/100, 当前算符: D_(0, 2) * D_(22, 44), 耗时: 10.563s
[2025-07-25 09:45:11] SW-NE对角算符进度: 24/100, 当前算符: D_(0, 2) * D_(23, 25), 耗时: 10.607s
[2025-07-25 09:45:21] SW-NE对角算符进度: 25/100, 当前算符: D_(0, 2) * D_(24, 26), 耗时: 10.616s
[2025-07-25 09:45:32] SW-NE对角算符进度: 26/100, 当前算符: D_(0, 2) * D_(25, 47), 耗时: 10.614s
[2025-07-25 09:45:42] SW-NE对角算符进度: 27/100, 当前算符: D_(0, 2) * D_(26, 48), 耗时: 10.618s
[2025-07-25 09:45:53] SW-NE对角算符进度: 28/100, 当前算符: D_(0, 2) * D_(27, 29), 耗时: 10.618s
[2025-07-25 09:46:04] SW-NE对角算符进度: 29/100, 当前算符: D_(0, 2) * D_(28, 30), 耗时: 10.565s
[2025-07-25 09:46:14] SW-NE对角算符进度: 30/100, 当前算符: D_(0, 2) * D_(29, 51), 耗时: 10.558s
[2025-07-25 09:46:25] SW-NE对角算符进度: 31/100, 当前算符: D_(0, 2) * D_(30, 52), 耗时: 10.556s
[2025-07-25 09:46:35] SW-NE对角算符进度: 32/100, 当前算符: D_(0, 2) * D_(31, 33), 耗时: 10.618s
[2025-07-25 09:46:46] SW-NE对角算符进度: 33/100, 当前算符: D_(0, 2) * D_(32, 34), 耗时: 10.617s
[2025-07-25 09:46:57] SW-NE对角算符进度: 34/100, 当前算符: D_(0, 2) * D_(33, 55), 耗时: 10.618s
[2025-07-25 09:47:07] SW-NE对角算符进度: 35/100, 当前算符: D_(0, 2) * D_(34, 56), 耗时: 10.556s
[2025-07-25 09:47:18] SW-NE对角算符进度: 36/100, 当前算符: D_(0, 2) * D_(35, 37), 耗时: 10.560s
[2025-07-25 09:47:28] SW-NE对角算符进度: 37/100, 当前算符: D_(0, 2) * D_(36, 38), 耗时: 10.604s
[2025-07-25 09:47:39] SW-NE对角算符进度: 38/100, 当前算符: D_(0, 2) * D_(37, 59), 耗时: 10.610s
[2025-07-25 09:47:50] SW-NE对角算符进度: 39/100, 当前算符: D_(0, 2) * D_(38, 40), 耗时: 10.617s
[2025-07-25 09:48:00] SW-NE对角算符进度: 40/100, 当前算符: D_(0, 2) * D_(39, 21), 耗时: 10.560s
[2025-07-25 09:48:11] SW-NE对角算符进度: 41/100, 当前算符: D_(0, 2) * D_(40, 42), 耗时: 10.561s
[2025-07-25 09:48:21] SW-NE对角算符进度: 42/100, 当前算符: D_(0, 2) * D_(41, 63), 耗时: 10.560s
[2025-07-25 09:48:32] SW-NE对角算符进度: 43/100, 当前算符: D_(0, 2) * D_(42, 64), 耗时: 10.596s
[2025-07-25 09:48:42] SW-NE对角算符进度: 44/100, 当前算符: D_(0, 2) * D_(43, 45), 耗时: 10.606s
[2025-07-25 09:48:53] SW-NE对角算符进度: 45/100, 当前算符: D_(0, 2) * D_(44, 46), 耗时: 10.563s
[2025-07-25 09:49:04] SW-NE对角算符进度: 46/100, 当前算符: D_(0, 2) * D_(45, 67), 耗时: 10.546s
[2025-07-25 09:49:14] SW-NE对角算符进度: 47/100, 当前算符: D_(0, 2) * D_(46, 68), 耗时: 10.550s
[2025-07-25 09:49:25] SW-NE对角算符进度: 48/100, 当前算符: D_(0, 2) * D_(47, 49), 耗时: 10.545s
[2025-07-25 09:49:35] SW-NE对角算符进度: 49/100, 当前算符: D_(0, 2) * D_(48, 50), 耗时: 10.558s
[2025-07-25 09:49:46] SW-NE对角算符进度: 50/100, 当前算符: D_(0, 2) * D_(49, 71), 耗时: 10.560s
[2025-07-25 09:49:56] SW-NE对角算符进度: 51/100, 当前算符: D_(0, 2) * D_(50, 72), 耗时: 10.610s
[2025-07-25 09:50:07] SW-NE对角算符进度: 52/100, 当前算符: D_(0, 2) * D_(51, 53), 耗时: 10.619s
[2025-07-25 09:50:18] SW-NE对角算符进度: 53/100, 当前算符: D_(0, 2) * D_(52, 54), 耗时: 10.557s
[2025-07-25 09:50:28] SW-NE对角算符进度: 54/100, 当前算符: D_(0, 2) * D_(53, 75), 耗时: 10.564s
[2025-07-25 09:50:39] SW-NE对角算符进度: 55/100, 当前算符: D_(0, 2) * D_(54, 76), 耗时: 10.553s
[2025-07-25 09:50:49] SW-NE对角算符进度: 56/100, 当前算符: D_(0, 2) * D_(55, 57), 耗时: 10.598s
[2025-07-25 09:51:00] SW-NE对角算符进度: 57/100, 当前算符: D_(0, 2) * D_(56, 58), 耗时: 10.616s
[2025-07-25 09:51:10] SW-NE对角算符进度: 58/100, 当前算符: D_(0, 2) * D_(57, 79), 耗时: 10.613s
[2025-07-25 09:51:21] SW-NE对角算符进度: 59/100, 当前算符: D_(0, 2) * D_(58, 60), 耗时: 10.555s
[2025-07-25 09:51:32] SW-NE对角算符进度: 60/100, 当前算符: D_(0, 2) * D_(59, 41), 耗时: 10.560s
[2025-07-25 09:51:42] SW-NE对角算符进度: 61/100, 当前算符: D_(0, 2) * D_(60, 62), 耗时: 10.606s
[2025-07-25 09:51:53] SW-NE对角算符进度: 62/100, 当前算符: D_(0, 2) * D_(61, 83), 耗时: 10.612s
[2025-07-25 09:52:03] SW-NE对角算符进度: 63/100, 当前算符: D_(0, 2) * D_(62, 84), 耗时: 10.616s
[2025-07-25 09:52:14] SW-NE对角算符进度: 64/100, 当前算符: D_(0, 2) * D_(63, 65), 耗时: 10.556s
[2025-07-25 09:52:25] SW-NE对角算符进度: 65/100, 当前算符: D_(0, 2) * D_(64, 66), 耗时: 10.557s
[2025-07-25 09:52:35] SW-NE对角算符进度: 66/100, 当前算符: D_(0, 2) * D_(65, 87), 耗时: 10.556s
[2025-07-25 09:52:46] SW-NE对角算符进度: 67/100, 当前算符: D_(0, 2) * D_(66, 88), 耗时: 10.609s
[2025-07-25 09:52:56] SW-NE对角算符进度: 68/100, 当前算符: D_(0, 2) * D_(67, 69), 耗时: 10.613s
[2025-07-25 09:53:07] SW-NE对角算符进度: 69/100, 当前算符: D_(0, 2) * D_(68, 70), 耗时: 10.561s
[2025-07-25 09:53:17] SW-NE对角算符进度: 70/100, 当前算符: D_(0, 2) * D_(69, 91), 耗时: 10.560s
[2025-07-25 09:53:28] SW-NE对角算符进度: 71/100, 当前算符: D_(0, 2) * D_(70, 92), 耗时: 10.547s
[2025-07-25 09:53:39] SW-NE对角算符进度: 72/100, 当前算符: D_(0, 2) * D_(71, 73), 耗时: 10.609s
[2025-07-25 09:53:49] SW-NE对角算符进度: 73/100, 当前算符: D_(0, 2) * D_(72, 74), 耗时: 10.612s
[2025-07-25 09:54:00] SW-NE对角算符进度: 74/100, 当前算符: D_(0, 2) * D_(73, 95), 耗时: 10.612s
[2025-07-25 09:54:10] SW-NE对角算符进度: 75/100, 当前算符: D_(0, 2) * D_(74, 96), 耗时: 10.563s
[2025-07-25 09:54:21] SW-NE对角算符进度: 76/100, 当前算符: D_(0, 2) * D_(75, 77), 耗时: 10.555s
[2025-07-25 09:54:32] SW-NE对角算符进度: 77/100, 当前算符: D_(0, 2) * D_(76, 78), 耗时: 10.617s
[2025-07-25 09:54:42] SW-NE对角算符进度: 78/100, 当前算符: D_(0, 2) * D_(77, 99), 耗时: 10.615s
[2025-07-25 09:54:53] SW-NE对角算符进度: 79/100, 当前算符: D_(0, 2) * D_(78, 80), 耗时: 10.619s
[2025-07-25 09:55:03] SW-NE对角算符进度: 80/100, 当前算符: D_(0, 2) * D_(79, 61), 耗时: 10.555s
[2025-07-25 09:55:14] SW-NE对角算符进度: 81/100, 当前算符: D_(0, 2) * D_(80, 82), 耗时: 10.552s
[2025-07-25 09:55:24] SW-NE对角算符进度: 82/100, 当前算符: D_(0, 2) * D_(81, 3), 耗时: 10.551s
[2025-07-25 09:55:35] SW-NE对角算符进度: 83/100, 当前算符: D_(0, 2) * D_(82, 4), 耗时: 10.610s
[2025-07-25 09:55:46] SW-NE对角算符进度: 84/100, 当前算符: D_(0, 2) * D_(83, 85), 耗时: 10.610s
[2025-07-25 09:55:56] SW-NE对角算符进度: 85/100, 当前算符: D_(0, 2) * D_(84, 86), 耗时: 10.551s
[2025-07-25 09:56:07] SW-NE对角算符进度: 86/100, 当前算符: D_(0, 2) * D_(85, 7), 耗时: 10.559s
[2025-07-25 09:56:17] SW-NE对角算符进度: 87/100, 当前算符: D_(0, 2) * D_(86, 8), 耗时: 10.558s
[2025-07-25 09:56:28] SW-NE对角算符进度: 88/100, 当前算符: D_(0, 2) * D_(87, 89), 耗时: 10.611s
[2025-07-25 09:56:39] SW-NE对角算符进度: 89/100, 当前算符: D_(0, 2) * D_(88, 90), 耗时: 10.607s
[2025-07-25 09:56:49] SW-NE对角算符进度: 90/100, 当前算符: D_(0, 2) * D_(89, 11), 耗时: 10.615s
[2025-07-25 09:57:00] SW-NE对角算符进度: 91/100, 当前算符: D_(0, 2) * D_(90, 12), 耗时: 10.618s
[2025-07-25 09:57:10] SW-NE对角算符进度: 92/100, 当前算符: D_(0, 2) * D_(91, 93), 耗时: 10.612s
[2025-07-25 09:57:21] SW-NE对角算符进度: 93/100, 当前算符: D_(0, 2) * D_(92, 94), 耗时: 10.558s
[2025-07-25 09:57:32] SW-NE对角算符进度: 94/100, 当前算符: D_(0, 2) * D_(93, 15), 耗时: 10.557s
[2025-07-25 09:57:42] SW-NE对角算符进度: 95/100, 当前算符: D_(0, 2) * D_(94, 16), 耗时: 10.565s
[2025-07-25 09:57:53] SW-NE对角算符进度: 96/100, 当前算符: D_(0, 2) * D_(95, 97), 耗时: 10.618s
[2025-07-25 09:58:03] SW-NE对角算符进度: 97/100, 当前算符: D_(0, 2) * D_(96, 98), 耗时: 10.607s
[2025-07-25 09:58:14] SW-NE对角算符进度: 98/100, 当前算符: D_(0, 2) * D_(97, 19), 耗时: 10.620s
[2025-07-25 09:58:23] SW-NE对角算符进度: 99/100, 当前算符: D_(0, 2) * D_(98, 0), 耗时: 8.795s
[2025-07-25 09:58:33] SW-NE对角算符进度: 100/100, 当前算符: D_(0, 2) * D_(99, 81), 耗时: 10.564s
[2025-07-25 09:58:33] 西南-东北方向对角二聚体相关函数计算完成,耗时: 1050.83 秒
[2025-07-25 09:58:33] 计算傅里叶变换...
[2025-07-25 09:58:35] 对角二聚体结构因子计算完成
[2025-07-25 09:58:35] 对角二聚体相关函数平均误差: 0.000135
