[2025-07-25 08:19:39] ================================================================================
[2025-07-25 08:19:39] 加载量子态: L=5, J2=1.00, J1=0.79
[2025-07-25 08:19:39] 设置样本数为: 1048576
[2025-07-25 08:19:39] 开始生成共享样本集...
[2025-07-25 08:22:57] 样本生成完成,耗时: 198.315 秒
[2025-07-25 08:22:57] ================================================================================
[2025-07-25 08:22:57] 开始计算自旋结构因子...
[2025-07-25 08:22:57] 初始化操作符缓存...
[2025-07-25 08:22:57] 预构建所有自旋相关操作符...
[2025-07-25 08:22:57] 开始计算自旋相关函数...
[2025-07-25 08:23:09] 自旋算符进度: 1/100, 当前算符: S_0 · S_0, 耗时: 11.449s
[2025-07-25 08:23:21] 自旋算符进度: 2/100, 当前算符: S_0 · S_1, 耗时: 12.066s
[2025-07-25 08:23:27] 自旋算符进度: 3/100, 当前算符: S_0 · S_2, 耗时: 6.265s
[2025-07-25 08:23:33] 自旋算符进度: 4/100, 当前算符: S_0 · S_3, 耗时: 6.272s
[2025-07-25 08:23:40] 自旋算符进度: 5/100, 当前算符: S_0 · S_4, 耗时: 6.248s
[2025-07-25 08:23:46] 自旋算符进度: 6/100, 当前算符: S_0 · S_5, 耗时: 6.267s
[2025-07-25 08:23:52] 自旋算符进度: 7/100, 当前算符: S_0 · S_6, 耗时: 6.269s
[2025-07-25 08:23:58] 自旋算符进度: 8/100, 当前算符: S_0 · S_7, 耗时: 6.253s
[2025-07-25 08:24:05] 自旋算符进度: 9/100, 当前算符: S_0 · S_8, 耗时: 6.272s
[2025-07-25 08:24:11] 自旋算符进度: 10/100, 当前算符: S_0 · S_9, 耗时: 6.252s
[2025-07-25 08:24:17] 自旋算符进度: 11/100, 当前算符: S_0 · S_10, 耗时: 6.261s
[2025-07-25 08:24:23] 自旋算符进度: 12/100, 当前算符: S_0 · S_11, 耗时: 6.244s
[2025-07-25 08:24:30] 自旋算符进度: 13/100, 当前算符: S_0 · S_12, 耗时: 6.272s
[2025-07-25 08:24:36] 自旋算符进度: 14/100, 当前算符: S_0 · S_13, 耗时: 6.271s
[2025-07-25 08:24:42] 自旋算符进度: 15/100, 当前算符: S_0 · S_14, 耗时: 6.272s
[2025-07-25 08:24:49] 自旋算符进度: 16/100, 当前算符: S_0 · S_15, 耗时: 6.251s
[2025-07-25 08:24:55] 自旋算符进度: 17/100, 当前算符: S_0 · S_16, 耗时: 6.276s
[2025-07-25 08:25:01] 自旋算符进度: 18/100, 当前算符: S_0 · S_17, 耗时: 6.251s
[2025-07-25 08:25:07] 自旋算符进度: 19/100, 当前算符: S_0 · S_18, 耗时: 6.270s
[2025-07-25 08:25:14] 自旋算符进度: 20/100, 当前算符: S_0 · S_19, 耗时: 6.273s
[2025-07-25 08:25:20] 自旋算符进度: 21/100, 当前算符: S_0 · S_20, 耗时: 6.243s
[2025-07-25 08:25:26] 自旋算符进度: 22/100, 当前算符: S_0 · S_21, 耗时: 6.260s
[2025-07-25 08:25:32] 自旋算符进度: 23/100, 当前算符: S_0 · S_22, 耗时: 6.274s
[2025-07-25 08:25:39] 自旋算符进度: 24/100, 当前算符: S_0 · S_23, 耗时: 6.271s
[2025-07-25 08:25:45] 自旋算符进度: 25/100, 当前算符: S_0 · S_24, 耗时: 6.271s
[2025-07-25 08:25:51] 自旋算符进度: 26/100, 当前算符: S_0 · S_25, 耗时: 6.272s
[2025-07-25 08:25:57] 自旋算符进度: 27/100, 当前算符: S_0 · S_26, 耗时: 6.249s
[2025-07-25 08:26:04] 自旋算符进度: 28/100, 当前算符: S_0 · S_27, 耗时: 6.275s
[2025-07-25 08:26:10] 自旋算符进度: 29/100, 当前算符: S_0 · S_28, 耗时: 6.253s
[2025-07-25 08:26:16] 自旋算符进度: 30/100, 当前算符: S_0 · S_29, 耗时: 6.269s
[2025-07-25 08:26:23] 自旋算符进度: 31/100, 当前算符: S_0 · S_30, 耗时: 6.271s
[2025-07-25 08:26:29] 自旋算符进度: 32/100, 当前算符: S_0 · S_31, 耗时: 6.271s
[2025-07-25 08:26:35] 自旋算符进度: 33/100, 当前算符: S_0 · S_32, 耗时: 6.246s
[2025-07-25 08:26:41] 自旋算符进度: 34/100, 当前算符: S_0 · S_33, 耗时: 6.241s
[2025-07-25 08:26:48] 自旋算符进度: 35/100, 当前算符: S_0 · S_34, 耗时: 6.260s
[2025-07-25 08:26:54] 自旋算符进度: 36/100, 当前算符: S_0 · S_35, 耗时: 6.245s
[2025-07-25 08:27:00] 自旋算符进度: 37/100, 当前算符: S_0 · S_36, 耗时: 6.278s
[2025-07-25 08:27:06] 自旋算符进度: 38/100, 当前算符: S_0 · S_37, 耗时: 6.271s
[2025-07-25 08:27:13] 自旋算符进度: 39/100, 当前算符: S_0 · S_38, 耗时: 6.270s
[2025-07-25 08:27:19] 自旋算符进度: 40/100, 当前算符: S_0 · S_39, 耗时: 6.271s
[2025-07-25 08:27:25] 自旋算符进度: 41/100, 当前算符: S_0 · S_40, 耗时: 6.272s
[2025-07-25 08:27:31] 自旋算符进度: 42/100, 当前算符: S_0 · S_41, 耗时: 6.272s
[2025-07-25 08:27:38] 自旋算符进度: 43/100, 当前算符: S_0 · S_42, 耗时: 6.271s
[2025-07-25 08:27:44] 自旋算符进度: 44/100, 当前算符: S_0 · S_43, 耗时: 6.271s
[2025-07-25 08:27:50] 自旋算符进度: 45/100, 当前算符: S_0 · S_44, 耗时: 6.269s
[2025-07-25 08:27:57] 自旋算符进度: 46/100, 当前算符: S_0 · S_45, 耗时: 6.276s
[2025-07-25 08:28:03] 自旋算符进度: 47/100, 当前算符: S_0 · S_46, 耗时: 6.270s
[2025-07-25 08:28:09] 自旋算符进度: 48/100, 当前算符: S_0 · S_47, 耗时: 6.272s
[2025-07-25 08:28:15] 自旋算符进度: 49/100, 当前算符: S_0 · S_48, 耗时: 6.272s
[2025-07-25 08:28:22] 自旋算符进度: 50/100, 当前算符: S_0 · S_49, 耗时: 6.246s
[2025-07-25 08:28:28] 自旋算符进度: 51/100, 当前算符: S_0 · S_50, 耗时: 6.265s
[2025-07-25 08:28:34] 自旋算符进度: 52/100, 当前算符: S_0 · S_51, 耗时: 6.252s
[2025-07-25 08:28:40] 自旋算符进度: 53/100, 当前算符: S_0 · S_52, 耗时: 6.270s
[2025-07-25 08:28:47] 自旋算符进度: 54/100, 当前算符: S_0 · S_53, 耗时: 6.266s
[2025-07-25 08:28:53] 自旋算符进度: 55/100, 当前算符: S_0 · S_54, 耗时: 6.268s
[2025-07-25 08:28:59] 自旋算符进度: 56/100, 当前算符: S_0 · S_55, 耗时: 6.239s
[2025-07-25 08:29:05] 自旋算符进度: 57/100, 当前算符: S_0 · S_56, 耗时: 6.261s
[2025-07-25 08:29:12] 自旋算符进度: 58/100, 当前算符: S_0 · S_57, 耗时: 6.276s
[2025-07-25 08:29:18] 自旋算符进度: 59/100, 当前算符: S_0 · S_58, 耗时: 6.271s
[2025-07-25 08:29:24] 自旋算符进度: 60/100, 当前算符: S_0 · S_59, 耗时: 6.274s
[2025-07-25 08:29:31] 自旋算符进度: 61/100, 当前算符: S_0 · S_60, 耗时: 6.257s
[2025-07-25 08:29:37] 自旋算符进度: 62/100, 当前算符: S_0 · S_61, 耗时: 6.246s
[2025-07-25 08:29:43] 自旋算符进度: 63/100, 当前算符: S_0 · S_62, 耗时: 6.275s
[2025-07-25 08:29:49] 自旋算符进度: 64/100, 当前算符: S_0 · S_63, 耗时: 6.270s
[2025-07-25 08:29:56] 自旋算符进度: 65/100, 当前算符: S_0 · S_64, 耗时: 6.270s
[2025-07-25 08:30:02] 自旋算符进度: 66/100, 当前算符: S_0 · S_65, 耗时: 6.278s
[2025-07-25 08:30:08] 自旋算符进度: 67/100, 当前算符: S_0 · S_66, 耗时: 6.250s
[2025-07-25 08:30:14] 自旋算符进度: 68/100, 当前算符: S_0 · S_67, 耗时: 6.268s
[2025-07-25 08:30:21] 自旋算符进度: 69/100, 当前算符: S_0 · S_68, 耗时: 6.271s
[2025-07-25 08:30:27] 自旋算符进度: 70/100, 当前算符: S_0 · S_69, 耗时: 6.272s
[2025-07-25 08:30:33] 自旋算符进度: 71/100, 当前算符: S_0 · S_70, 耗时: 6.273s
[2025-07-25 08:30:39] 自旋算符进度: 72/100, 当前算符: S_0 · S_71, 耗时: 6.247s
[2025-07-25 08:30:46] 自旋算符进度: 73/100, 当前算符: S_0 · S_72, 耗时: 6.266s
[2025-07-25 08:30:52] 自旋算符进度: 74/100, 当前算符: S_0 · S_73, 耗时: 6.264s
[2025-07-25 08:30:58] 自旋算符进度: 75/100, 当前算符: S_0 · S_74, 耗时: 6.270s
[2025-07-25 08:31:05] 自旋算符进度: 76/100, 当前算符: S_0 · S_75, 耗时: 6.274s
[2025-07-25 08:31:11] 自旋算符进度: 77/100, 当前算符: S_0 · S_76, 耗时: 6.259s
[2025-07-25 08:31:17] 自旋算符进度: 78/100, 当前算符: S_0 · S_77, 耗时: 6.244s
[2025-07-25 08:31:23] 自旋算符进度: 79/100, 当前算符: S_0 · S_78, 耗时: 6.273s
[2025-07-25 08:31:30] 自旋算符进度: 80/100, 当前算符: S_0 · S_79, 耗时: 6.271s
[2025-07-25 08:31:36] 自旋算符进度: 81/100, 当前算符: S_0 · S_80, 耗时: 6.272s
[2025-07-25 08:31:42] 自旋算符进度: 82/100, 当前算符: S_0 · S_81, 耗时: 6.262s
[2025-07-25 08:31:48] 自旋算符进度: 83/100, 当前算符: S_0 · S_82, 耗时: 6.242s
[2025-07-25 08:31:55] 自旋算符进度: 84/100, 当前算符: S_0 · S_83, 耗时: 6.261s
[2025-07-25 08:32:01] 自旋算符进度: 85/100, 当前算符: S_0 · S_84, 耗时: 6.269s
[2025-07-25 08:32:07] 自旋算符进度: 86/100, 当前算符: S_0 · S_85, 耗时: 6.270s
[2025-07-25 08:32:13] 自旋算符进度: 87/100, 当前算符: S_0 · S_86, 耗时: 6.271s
[2025-07-25 08:32:20] 自旋算符进度: 88/100, 当前算符: S_0 · S_87, 耗时: 6.271s
[2025-07-25 08:32:26] 自旋算符进度: 89/100, 当前算符: S_0 · S_88, 耗时: 6.243s
[2025-07-25 08:32:32] 自旋算符进度: 90/100, 当前算符: S_0 · S_89, 耗时: 6.270s
[2025-07-25 08:32:39] 自旋算符进度: 91/100, 当前算符: S_0 · S_90, 耗时: 6.270s
[2025-07-25 08:32:45] 自旋算符进度: 92/100, 当前算符: S_0 · S_91, 耗时: 6.271s
[2025-07-25 08:32:51] 自旋算符进度: 93/100, 当前算符: S_0 · S_92, 耗时: 6.274s
[2025-07-25 08:32:57] 自旋算符进度: 94/100, 当前算符: S_0 · S_93, 耗时: 6.266s
[2025-07-25 08:33:04] 自旋算符进度: 95/100, 当前算符: S_0 · S_94, 耗时: 6.247s
[2025-07-25 08:33:10] 自旋算符进度: 96/100, 当前算符: S_0 · S_95, 耗时: 6.267s
[2025-07-25 08:33:16] 自旋算符进度: 97/100, 当前算符: S_0 · S_96, 耗时: 6.272s
[2025-07-25 08:33:22] 自旋算符进度: 98/100, 当前算符: S_0 · S_97, 耗时: 6.272s
[2025-07-25 08:33:29] 自旋算符进度: 99/100, 当前算符: S_0 · S_98, 耗时: 6.268s
[2025-07-25 08:33:35] 自旋算符进度: 100/100, 当前算符: S_0 · S_99, 耗时: 6.247s
[2025-07-25 08:33:35] 自旋相关函数计算完成,总耗时 637.56 秒
[2025-07-25 08:33:35] 计算傅里叶变换...
[2025-07-25 08:33:36] 自旋结构因子计算完成
[2025-07-25 08:33:37] 自旋相关函数平均误差: 0.000592
[2025-07-25 08:33:37] ================================================================================
[2025-07-25 08:33:37] 开始计算对角二聚体结构因子...
[2025-07-25 08:33:37] 识别所有对角二聚体...
[2025-07-25 08:33:37] 总共找到 100 个西北-东南方向对角二聚体和 100 个西南-东北方向对角二聚体
[2025-07-25 08:33:37] 预计算对角二聚体操作符...
[2025-07-25 08:33:39] 开始计算西北-东南方向对角二聚体相关函数 (100 个算符)...
[2025-07-25 08:33:46] NW-SE对角算符进度: 1/100, 当前算符: D_(0, 18) * D_(0, 18), 耗时: 6.296s
[2025-07-25 08:34:02] NW-SE对角算符进度: 2/100, 当前算符: D_(0, 18) * D_(1, 39), 耗时: 16.159s
[2025-07-25 08:34:12] NW-SE对角算符进度: 3/100, 当前算符: D_(0, 18) * D_(2, 20), 耗时: 10.523s
[2025-07-25 08:34:23] NW-SE对角算符进度: 4/100, 当前算符: D_(0, 18) * D_(3, 1), 耗时: 10.468s
[2025-07-25 08:34:33] NW-SE对角算符进度: 5/100, 当前算符: D_(0, 18) * D_(4, 2), 耗时: 10.511s
[2025-07-25 08:34:44] NW-SE对角算符进度: 6/100, 当前算符: D_(0, 18) * D_(5, 23), 耗时: 10.528s
[2025-07-25 08:34:54] NW-SE对角算符进度: 7/100, 当前算符: D_(0, 18) * D_(6, 24), 耗时: 10.511s
[2025-07-25 08:35:05] NW-SE对角算符进度: 8/100, 当前算符: D_(0, 18) * D_(7, 5), 耗时: 10.476s
[2025-07-25 08:35:15] NW-SE对角算符进度: 9/100, 当前算符: D_(0, 18) * D_(8, 6), 耗时: 10.508s
[2025-07-25 08:35:26] NW-SE对角算符进度: 10/100, 当前算符: D_(0, 18) * D_(9, 27), 耗时: 10.473s
[2025-07-25 08:35:36] NW-SE对角算符进度: 11/100, 当前算符: D_(0, 18) * D_(10, 28), 耗时: 10.509s
[2025-07-25 08:35:47] NW-SE对角算符进度: 12/100, 当前算符: D_(0, 18) * D_(11, 9), 耗时: 10.510s
[2025-07-25 08:35:58] NW-SE对角算符进度: 13/100, 当前算符: D_(0, 18) * D_(12, 10), 耗时: 10.531s
[2025-07-25 08:36:08] NW-SE对角算符进度: 14/100, 当前算符: D_(0, 18) * D_(13, 31), 耗时: 10.529s
[2025-07-25 08:36:19] NW-SE对角算符进度: 15/100, 当前算符: D_(0, 18) * D_(14, 32), 耗时: 10.475s
[2025-07-25 08:36:29] NW-SE对角算符进度: 16/100, 当前算符: D_(0, 18) * D_(15, 13), 耗时: 10.509s
[2025-07-25 08:36:39] NW-SE对角算符进度: 17/100, 当前算符: D_(0, 18) * D_(16, 14), 耗时: 10.474s
[2025-07-25 08:36:50] NW-SE对角算符进度: 18/100, 当前算符: D_(0, 18) * D_(17, 35), 耗时: 10.530s
[2025-07-25 08:37:09] NW-SE对角算符进度: 19/100, 当前算符: D_(0, 18) * D_(18, 36), 耗时: 19.024s
[2025-07-25 08:37:20] NW-SE对角算符进度: 20/100, 当前算符: D_(0, 18) * D_(19, 17), 耗时: 10.495s
[2025-07-25 08:37:30] NW-SE对角算符进度: 21/100, 当前算符: D_(0, 18) * D_(20, 38), 耗时: 10.500s
[2025-07-25 08:37:41] NW-SE对角算符进度: 22/100, 当前算符: D_(0, 18) * D_(21, 59), 耗时: 10.450s
[2025-07-25 08:37:51] NW-SE对角算符进度: 23/100, 当前算符: D_(0, 18) * D_(22, 40), 耗时: 10.521s
[2025-07-25 08:38:02] NW-SE对角算符进度: 24/100, 当前算符: D_(0, 18) * D_(23, 21), 耗时: 10.514s
[2025-07-25 08:38:12] NW-SE对角算符进度: 25/100, 当前算符: D_(0, 18) * D_(24, 22), 耗时: 10.519s
[2025-07-25 08:38:23] NW-SE对角算符进度: 26/100, 当前算符: D_(0, 18) * D_(25, 43), 耗时: 10.497s
[2025-07-25 08:38:33] NW-SE对角算符进度: 27/100, 当前算符: D_(0, 18) * D_(26, 44), 耗时: 10.475s
[2025-07-25 08:38:44] NW-SE对角算符进度: 28/100, 当前算符: D_(0, 18) * D_(27, 25), 耗时: 10.499s
[2025-07-25 08:38:54] NW-SE对角算符进度: 29/100, 当前算符: D_(0, 18) * D_(28, 26), 耗时: 10.507s
[2025-07-25 08:39:05] NW-SE对角算符进度: 30/100, 当前算符: D_(0, 18) * D_(29, 47), 耗时: 10.512s
[2025-07-25 08:39:15] NW-SE对角算符进度: 31/100, 当前算符: D_(0, 18) * D_(30, 48), 耗时: 10.509s
[2025-07-25 08:39:26] NW-SE对角算符进度: 32/100, 当前算符: D_(0, 18) * D_(31, 29), 耗时: 10.521s
[2025-07-25 08:39:36] NW-SE对角算符进度: 33/100, 当前算符: D_(0, 18) * D_(32, 30), 耗时: 10.446s
[2025-07-25 08:39:46] NW-SE对角算符进度: 34/100, 当前算符: D_(0, 18) * D_(33, 51), 耗时: 10.445s
[2025-07-25 08:39:57] NW-SE对角算符进度: 35/100, 当前算符: D_(0, 18) * D_(34, 52), 耗时: 10.514s
[2025-07-25 08:40:08] NW-SE对角算符进度: 36/100, 当前算符: D_(0, 18) * D_(35, 33), 耗时: 10.508s
[2025-07-25 08:40:18] NW-SE对角算符进度: 37/100, 当前算符: D_(0, 18) * D_(36, 34), 耗时: 10.513s
[2025-07-25 08:40:28] NW-SE对角算符进度: 38/100, 当前算符: D_(0, 18) * D_(37, 55), 耗时: 10.452s
[2025-07-25 08:40:39] NW-SE对角算符进度: 39/100, 当前算符: D_(0, 18) * D_(38, 56), 耗时: 10.514s
[2025-07-25 08:40:50] NW-SE对角算符进度: 40/100, 当前算符: D_(0, 18) * D_(39, 37), 耗时: 10.511s
[2025-07-25 08:41:00] NW-SE对角算符进度: 41/100, 当前算符: D_(0, 18) * D_(40, 58), 耗时: 10.506s
[2025-07-25 08:41:11] NW-SE对角算符进度: 42/100, 当前算符: D_(0, 18) * D_(41, 79), 耗时: 10.511s
[2025-07-25 08:41:21] NW-SE对角算符进度: 43/100, 当前算符: D_(0, 18) * D_(42, 60), 耗时: 10.515s
[2025-07-25 08:41:32] NW-SE对角算符进度: 44/100, 当前算符: D_(0, 18) * D_(43, 41), 耗时: 10.506s
[2025-07-25 08:41:42] NW-SE对角算符进度: 45/100, 当前算符: D_(0, 18) * D_(44, 42), 耗时: 10.451s
[2025-07-25 08:41:53] NW-SE对角算符进度: 46/100, 当前算符: D_(0, 18) * D_(45, 63), 耗时: 10.502s
[2025-07-25 08:42:03] NW-SE对角算符进度: 47/100, 当前算符: D_(0, 18) * D_(46, 64), 耗时: 10.511s
[2025-07-25 08:42:14] NW-SE对角算符进度: 48/100, 当前算符: D_(0, 18) * D_(47, 45), 耗时: 10.507s
[2025-07-25 08:42:24] NW-SE对角算符进度: 49/100, 当前算符: D_(0, 18) * D_(48, 46), 耗时: 10.468s
[2025-07-25 08:42:34] NW-SE对角算符进度: 50/100, 当前算符: D_(0, 18) * D_(49, 67), 耗时: 10.453s
[2025-07-25 08:42:45] NW-SE对角算符进度: 51/100, 当前算符: D_(0, 18) * D_(50, 68), 耗时: 10.509s
[2025-07-25 08:42:55] NW-SE对角算符进度: 52/100, 当前算符: D_(0, 18) * D_(51, 49), 耗时: 10.511s
[2025-07-25 08:43:06] NW-SE对角算符进度: 53/100, 当前算符: D_(0, 18) * D_(52, 50), 耗时: 10.521s
[2025-07-25 08:43:17] NW-SE对角算符进度: 54/100, 当前算符: D_(0, 18) * D_(53, 71), 耗时: 10.518s
[2025-07-25 08:43:27] NW-SE对角算符进度: 55/100, 当前算符: D_(0, 18) * D_(54, 72), 耗时: 10.502s
[2025-07-25 08:43:37] NW-SE对角算符进度: 56/100, 当前算符: D_(0, 18) * D_(55, 53), 耗时: 10.458s
[2025-07-25 08:43:48] NW-SE对角算符进度: 57/100, 当前算符: D_(0, 18) * D_(56, 54), 耗时: 10.518s
[2025-07-25 08:43:59] NW-SE对角算符进度: 58/100, 当前算符: D_(0, 18) * D_(57, 75), 耗时: 10.512s
[2025-07-25 08:44:09] NW-SE对角算符进度: 59/100, 当前算符: D_(0, 18) * D_(58, 76), 耗时: 10.514s
[2025-07-25 08:44:19] NW-SE对角算符进度: 60/100, 当前算符: D_(0, 18) * D_(59, 57), 耗时: 10.454s
[2025-07-25 08:44:30] NW-SE对角算符进度: 61/100, 当前算符: D_(0, 18) * D_(60, 78), 耗时: 10.510s
[2025-07-25 08:44:41] NW-SE对角算符进度: 62/100, 当前算符: D_(0, 18) * D_(61, 99), 耗时: 10.515s
[2025-07-25 08:44:51] NW-SE对角算符进度: 63/100, 当前算符: D_(0, 18) * D_(62, 80), 耗时: 10.511s
[2025-07-25 08:45:02] NW-SE对角算符进度: 64/100, 当前算符: D_(0, 18) * D_(63, 61), 耗时: 10.508s
[2025-07-25 08:45:12] NW-SE对角算符进度: 65/100, 当前算符: D_(0, 18) * D_(64, 62), 耗时: 10.501s
[2025-07-25 08:45:23] NW-SE对角算符进度: 66/100, 当前算符: D_(0, 18) * D_(65, 83), 耗时: 10.494s
[2025-07-25 08:45:33] NW-SE对角算符进度: 67/100, 当前算符: D_(0, 18) * D_(66, 84), 耗时: 10.458s
[2025-07-25 08:45:43] NW-SE对角算符进度: 68/100, 当前算符: D_(0, 18) * D_(67, 65), 耗时: 10.513s
[2025-07-25 08:45:54] NW-SE对角算符进度: 69/100, 当前算符: D_(0, 18) * D_(68, 66), 耗时: 10.515s
[2025-07-25 08:46:04] NW-SE对角算符进度: 70/100, 当前算符: D_(0, 18) * D_(69, 87), 耗时: 10.450s
[2025-07-25 08:46:15] NW-SE对角算符进度: 71/100, 当前算符: D_(0, 18) * D_(70, 88), 耗时: 10.495s
[2025-07-25 08:46:25] NW-SE对角算符进度: 72/100, 当前算符: D_(0, 18) * D_(71, 69), 耗时: 10.456s
[2025-07-25 08:46:36] NW-SE对角算符进度: 73/100, 当前算符: D_(0, 18) * D_(72, 70), 耗时: 10.514s
[2025-07-25 08:46:46] NW-SE对角算符进度: 74/100, 当前算符: D_(0, 18) * D_(73, 91), 耗时: 10.513s
[2025-07-25 08:46:57] NW-SE对角算符进度: 75/100, 当前算符: D_(0, 18) * D_(74, 92), 耗时: 10.499s
[2025-07-25 08:47:07] NW-SE对角算符进度: 76/100, 当前算符: D_(0, 18) * D_(75, 73), 耗时: 10.476s
[2025-07-25 08:47:18] NW-SE对角算符进度: 77/100, 当前算符: D_(0, 18) * D_(76, 74), 耗时: 10.499s
[2025-07-25 08:47:28] NW-SE对角算符进度: 78/100, 当前算符: D_(0, 18) * D_(77, 95), 耗时: 10.449s
[2025-07-25 08:47:39] NW-SE对角算符进度: 79/100, 当前算符: D_(0, 18) * D_(78, 96), 耗时: 10.517s
[2025-07-25 08:47:49] NW-SE对角算符进度: 80/100, 当前算符: D_(0, 18) * D_(79, 77), 耗时: 10.511s
[2025-07-25 08:48:00] NW-SE对角算符进度: 81/100, 当前算符: D_(0, 18) * D_(80, 98), 耗时: 10.511s
[2025-07-25 08:48:10] NW-SE对角算符进度: 82/100, 当前算符: D_(0, 18) * D_(81, 19), 耗时: 10.514s
[2025-07-25 08:48:19] NW-SE对角算符进度: 83/100, 当前算符: D_(0, 18) * D_(82, 0), 耗时: 8.763s
[2025-07-25 08:48:30] NW-SE对角算符进度: 84/100, 当前算符: D_(0, 18) * D_(83, 81), 耗时: 10.495s
[2025-07-25 08:48:40] NW-SE对角算符进度: 85/100, 当前算符: D_(0, 18) * D_(84, 82), 耗时: 10.511s
[2025-07-25 08:48:51] NW-SE对角算符进度: 86/100, 当前算符: D_(0, 18) * D_(85, 3), 耗时: 10.508s
[2025-07-25 08:49:01] NW-SE对角算符进度: 87/100, 当前算符: D_(0, 18) * D_(86, 4), 耗时: 10.508s
[2025-07-25 08:49:12] NW-SE对角算符进度: 88/100, 当前算符: D_(0, 18) * D_(87, 85), 耗时: 10.456s
[2025-07-25 08:49:22] NW-SE对角算符进度: 89/100, 当前算符: D_(0, 18) * D_(88, 86), 耗时: 10.499s
[2025-07-25 08:49:33] NW-SE对角算符进度: 90/100, 当前算符: D_(0, 18) * D_(89, 7), 耗时: 10.450s
[2025-07-25 08:49:43] NW-SE对角算符进度: 91/100, 当前算符: D_(0, 18) * D_(90, 8), 耗时: 10.510s
[2025-07-25 08:49:54] NW-SE对角算符进度: 92/100, 当前算符: D_(0, 18) * D_(91, 89), 耗时: 10.513s
[2025-07-25 08:50:04] NW-SE对角算符进度: 93/100, 当前算符: D_(0, 18) * D_(92, 90), 耗时: 10.516s
[2025-07-25 08:50:15] NW-SE对角算符进度: 94/100, 当前算符: D_(0, 18) * D_(93, 11), 耗时: 10.503s
[2025-07-25 08:50:25] NW-SE对角算符进度: 95/100, 当前算符: D_(0, 18) * D_(94, 12), 耗时: 10.458s
[2025-07-25 08:50:36] NW-SE对角算符进度: 96/100, 当前算符: D_(0, 18) * D_(95, 93), 耗时: 10.505s
[2025-07-25 08:50:46] NW-SE对角算符进度: 97/100, 当前算符: D_(0, 18) * D_(96, 94), 耗时: 10.458s
[2025-07-25 08:50:57] NW-SE对角算符进度: 98/100, 当前算符: D_(0, 18) * D_(97, 15), 耗时: 10.512s
[2025-07-25 08:51:07] NW-SE对角算符进度: 99/100, 当前算符: D_(0, 18) * D_(98, 16), 耗时: 10.511s
[2025-07-25 08:51:18] NW-SE对角算符进度: 100/100, 当前算符: D_(0, 18) * D_(99, 97), 耗时: 10.517s
[2025-07-25 08:51:18] 西北-东南方向对角二聚体相关函数计算完成,耗时: 1058.17 秒
[2025-07-25 08:51:18] ================================================================================
[2025-07-25 08:51:18] 开始计算西南-东北方向对角二聚体相关函数 (100 个算符)...
[2025-07-25 08:51:24] SW-NE对角算符进度: 1/100, 当前算符: D_(0, 2) * D_(0, 2), 耗时: 6.263s
[2025-07-25 08:51:34] SW-NE对角算符进度: 2/100, 当前算符: D_(0, 2) * D_(1, 23), 耗时: 10.528s
[2025-07-25 08:51:43] SW-NE对角算符进度: 3/100, 当前算符: D_(0, 2) * D_(2, 24), 耗时: 8.763s
[2025-07-25 08:51:54] SW-NE对角算符进度: 4/100, 当前算符: D_(0, 2) * D_(3, 5), 耗时: 10.466s
[2025-07-25 08:52:04] SW-NE对角算符进度: 5/100, 当前算符: D_(0, 2) * D_(4, 6), 耗时: 10.465s
[2025-07-25 08:52:15] SW-NE对角算符进度: 6/100, 当前算符: D_(0, 2) * D_(5, 27), 耗时: 10.533s
[2025-07-25 08:52:25] SW-NE对角算符进度: 7/100, 当前算符: D_(0, 2) * D_(6, 28), 耗时: 10.517s
[2025-07-25 08:52:36] SW-NE对角算符进度: 8/100, 当前算符: D_(0, 2) * D_(7, 9), 耗时: 10.524s
[2025-07-25 08:52:46] SW-NE对角算符进度: 9/100, 当前算符: D_(0, 2) * D_(8, 10), 耗时: 10.496s
[2025-07-25 08:52:57] SW-NE对角算符进度: 10/100, 当前算符: D_(0, 2) * D_(9, 31), 耗时: 10.516s
[2025-07-25 08:53:07] SW-NE对角算符进度: 11/100, 当前算符: D_(0, 2) * D_(10, 32), 耗时: 10.485s
[2025-07-25 08:53:18] SW-NE对角算符进度: 12/100, 当前算符: D_(0, 2) * D_(11, 13), 耗时: 10.509s
[2025-07-25 08:53:28] SW-NE对角算符进度: 13/100, 当前算符: D_(0, 2) * D_(12, 14), 耗时: 10.462s
[2025-07-25 08:53:39] SW-NE对角算符进度: 14/100, 当前算符: D_(0, 2) * D_(13, 35), 耗时: 10.536s
[2025-07-25 08:53:49] SW-NE对角算符进度: 15/100, 当前算符: D_(0, 2) * D_(14, 36), 耗时: 10.518s
[2025-07-25 08:54:00] SW-NE对角算符进度: 16/100, 当前算符: D_(0, 2) * D_(15, 17), 耗时: 10.541s
[2025-07-25 08:54:10] SW-NE对角算符进度: 17/100, 当前算符: D_(0, 2) * D_(16, 18), 耗时: 10.480s
[2025-07-25 08:54:21] SW-NE对角算符进度: 18/100, 当前算符: D_(0, 2) * D_(17, 39), 耗时: 10.523s
[2025-07-25 08:54:31] SW-NE对角算符进度: 19/100, 当前算符: D_(0, 2) * D_(18, 20), 耗时: 10.510s
[2025-07-25 08:54:42] SW-NE对角算符进度: 20/100, 当前算符: D_(0, 2) * D_(19, 1), 耗时: 10.516s
[2025-07-25 08:54:52] SW-NE对角算符进度: 21/100, 当前算符: D_(0, 2) * D_(20, 22), 耗时: 10.541s
[2025-07-25 08:55:03] SW-NE对角算符进度: 22/100, 当前算符: D_(0, 2) * D_(21, 43), 耗时: 10.462s
[2025-07-25 08:55:13] SW-NE对角算符进度: 23/100, 当前算符: D_(0, 2) * D_(22, 44), 耗时: 10.524s
[2025-07-25 08:55:24] SW-NE对角算符进度: 24/100, 当前算符: D_(0, 2) * D_(23, 25), 耗时: 10.503s
[2025-07-25 08:55:34] SW-NE对角算符进度: 25/100, 当前算符: D_(0, 2) * D_(24, 26), 耗时: 10.512s
[2025-07-25 08:55:45] SW-NE对角算符进度: 26/100, 当前算符: D_(0, 2) * D_(25, 47), 耗时: 10.513s
[2025-07-25 08:55:55] SW-NE对角算符进度: 27/100, 当前算符: D_(0, 2) * D_(26, 48), 耗时: 10.514s
[2025-07-25 08:56:06] SW-NE对角算符进度: 28/100, 当前算符: D_(0, 2) * D_(27, 29), 耗时: 10.511s
[2025-07-25 08:56:16] SW-NE对角算符进度: 29/100, 当前算符: D_(0, 2) * D_(28, 30), 耗时: 10.516s
[2025-07-25 08:56:27] SW-NE对角算符进度: 30/100, 当前算符: D_(0, 2) * D_(29, 51), 耗时: 10.468s
[2025-07-25 08:56:37] SW-NE对角算符进度: 31/100, 当前算符: D_(0, 2) * D_(30, 52), 耗时: 10.540s
[2025-07-25 08:56:48] SW-NE对角算符进度: 32/100, 当前算符: D_(0, 2) * D_(31, 33), 耗时: 10.514s
[2025-07-25 08:56:58] SW-NE对角算符进度: 33/100, 当前算符: D_(0, 2) * D_(32, 34), 耗时: 10.511s
[2025-07-25 08:57:09] SW-NE对角算符进度: 34/100, 当前算符: D_(0, 2) * D_(33, 55), 耗时: 10.508s
[2025-07-25 08:57:20] SW-NE对角算符进度: 35/100, 当前算符: D_(0, 2) * D_(34, 56), 耗时: 10.524s
[2025-07-25 08:57:30] SW-NE对角算符进度: 36/100, 当前算符: D_(0, 2) * D_(35, 37), 耗时: 10.479s
[2025-07-25 08:57:41] SW-NE对角算符进度: 37/100, 当前算符: D_(0, 2) * D_(36, 38), 耗时: 10.517s
[2025-07-25 08:57:51] SW-NE对角算符进度: 38/100, 当前算符: D_(0, 2) * D_(37, 59), 耗时: 10.513s
[2025-07-25 08:58:02] SW-NE对角算符进度: 39/100, 当前算符: D_(0, 2) * D_(38, 40), 耗时: 10.520s
[2025-07-25 08:58:12] SW-NE对角算符进度: 40/100, 当前算符: D_(0, 2) * D_(39, 21), 耗时: 10.480s
[2025-07-25 08:58:23] SW-NE对角算符进度: 41/100, 当前算符: D_(0, 2) * D_(40, 42), 耗时: 10.542s
[2025-07-25 08:58:33] SW-NE对角算符进度: 42/100, 当前算符: D_(0, 2) * D_(41, 63), 耗时: 10.471s
[2025-07-25 08:58:44] SW-NE对角算符进度: 43/100, 当前算符: D_(0, 2) * D_(42, 64), 耗时: 10.525s
[2025-07-25 08:58:54] SW-NE对角算符进度: 44/100, 当前算符: D_(0, 2) * D_(43, 45), 耗时: 10.514s
[2025-07-25 08:59:05] SW-NE对角算符进度: 45/100, 当前算符: D_(0, 2) * D_(44, 46), 耗时: 10.529s
[2025-07-25 08:59:15] SW-NE对角算符进度: 46/100, 当前算符: D_(0, 2) * D_(45, 67), 耗时: 10.476s
[2025-07-25 08:59:26] SW-NE对角算符进度: 47/100, 当前算符: D_(0, 2) * D_(46, 68), 耗时: 10.520s
[2025-07-25 08:59:36] SW-NE对角算符进度: 48/100, 当前算符: D_(0, 2) * D_(47, 49), 耗时: 10.512s
[2025-07-25 08:59:47] SW-NE对角算符进度: 49/100, 当前算符: D_(0, 2) * D_(48, 50), 耗时: 10.465s
[2025-07-25 08:59:57] SW-NE对角算符进度: 50/100, 当前算符: D_(0, 2) * D_(49, 71), 耗时: 10.519s
[2025-07-25 09:00:08] SW-NE对角算符进度: 51/100, 当前算符: D_(0, 2) * D_(50, 72), 耗时: 10.541s
[2025-07-25 09:00:18] SW-NE对角算符进度: 52/100, 当前算符: D_(0, 2) * D_(51, 53), 耗时: 10.519s
[2025-07-25 09:00:29] SW-NE对角算符进度: 53/100, 当前算符: D_(0, 2) * D_(52, 54), 耗时: 10.510s
[2025-07-25 09:00:39] SW-NE对角算符进度: 54/100, 当前算符: D_(0, 2) * D_(53, 75), 耗时: 10.489s
[2025-07-25 09:00:50] SW-NE对角算符进度: 55/100, 当前算符: D_(0, 2) * D_(54, 76), 耗时: 10.515s
[2025-07-25 09:01:00] SW-NE对角算符进度: 56/100, 当前算符: D_(0, 2) * D_(55, 57), 耗时: 10.501s
[2025-07-25 09:01:11] SW-NE对角算符进度: 57/100, 当前算符: D_(0, 2) * D_(56, 58), 耗时: 10.532s
[2025-07-25 09:01:21] SW-NE对角算符进度: 58/100, 当前算符: D_(0, 2) * D_(57, 79), 耗时: 10.517s
[2025-07-25 09:01:32] SW-NE对角算符进度: 59/100, 当前算符: D_(0, 2) * D_(58, 60), 耗时: 10.536s
[2025-07-25 09:01:42] SW-NE对角算符进度: 60/100, 当前算符: D_(0, 2) * D_(59, 41), 耗时: 10.480s
[2025-07-25 09:01:53] SW-NE对角算符进度: 61/100, 当前算符: D_(0, 2) * D_(60, 62), 耗时: 10.540s
[2025-07-25 09:02:03] SW-NE对角算符进度: 62/100, 当前算符: D_(0, 2) * D_(61, 83), 耗时: 10.515s
[2025-07-25 09:02:14] SW-NE对角算符进度: 63/100, 当前算符: D_(0, 2) * D_(62, 84), 耗时: 10.536s
[2025-07-25 09:02:24] SW-NE对角算符进度: 64/100, 当前算符: D_(0, 2) * D_(63, 65), 耗时: 10.477s
[2025-07-25 09:02:35] SW-NE对角算符进度: 65/100, 当前算符: D_(0, 2) * D_(64, 66), 耗时: 10.463s
[2025-07-25 09:02:45] SW-NE对角算符进度: 66/100, 当前算符: D_(0, 2) * D_(65, 87), 耗时: 10.536s
[2025-07-25 09:02:56] SW-NE对角算符进度: 67/100, 当前算符: D_(0, 2) * D_(66, 88), 耗时: 10.520s
[2025-07-25 09:03:06] SW-NE对角算符进度: 68/100, 当前算符: D_(0, 2) * D_(67, 69), 耗时: 10.520s
[2025-07-25 09:03:17] SW-NE对角算符进度: 69/100, 当前算符: D_(0, 2) * D_(68, 70), 耗时: 10.542s
[2025-07-25 09:03:27] SW-NE对角算符进度: 70/100, 当前算符: D_(0, 2) * D_(69, 91), 耗时: 10.463s
[2025-07-25 09:03:38] SW-NE对角算符进度: 71/100, 当前算符: D_(0, 2) * D_(70, 92), 耗时: 10.513s
[2025-07-25 09:03:48] SW-NE对角算符进度: 72/100, 当前算符: D_(0, 2) * D_(71, 73), 耗时: 10.510s
[2025-07-25 09:03:59] SW-NE对角算符进度: 73/100, 当前算符: D_(0, 2) * D_(72, 74), 耗时: 10.541s
[2025-07-25 09:04:09] SW-NE对角算符进度: 74/100, 当前算符: D_(0, 2) * D_(73, 95), 耗时: 10.520s
[2025-07-25 09:04:20] SW-NE对角算符进度: 75/100, 当前算符: D_(0, 2) * D_(74, 96), 耗时: 10.517s
[2025-07-25 09:04:30] SW-NE对角算符进度: 76/100, 当前算符: D_(0, 2) * D_(75, 77), 耗时: 10.467s
[2025-07-25 09:04:41] SW-NE对角算符进度: 77/100, 当前算符: D_(0, 2) * D_(76, 78), 耗时: 10.544s
[2025-07-25 09:04:52] SW-NE对角算符进度: 78/100, 当前算符: D_(0, 2) * D_(77, 99), 耗时: 10.519s
[2025-07-25 09:05:02] SW-NE对角算符进度: 79/100, 当前算符: D_(0, 2) * D_(78, 80), 耗时: 10.513s
[2025-07-25 09:05:13] SW-NE对角算符进度: 80/100, 当前算符: D_(0, 2) * D_(79, 61), 耗时: 10.473s
[2025-07-25 09:05:23] SW-NE对角算符进度: 81/100, 当前算符: D_(0, 2) * D_(80, 82), 耗时: 10.471s
[2025-07-25 09:05:34] SW-NE对角算符进度: 82/100, 当前算符: D_(0, 2) * D_(81, 3), 耗时: 10.510s
[2025-07-25 09:05:44] SW-NE对角算符进度: 83/100, 当前算符: D_(0, 2) * D_(82, 4), 耗时: 10.519s
[2025-07-25 09:05:55] SW-NE对角算符进度: 84/100, 当前算符: D_(0, 2) * D_(83, 85), 耗时: 10.520s
[2025-07-25 09:06:05] SW-NE对角算符进度: 85/100, 当前算符: D_(0, 2) * D_(84, 86), 耗时: 10.475s
[2025-07-25 09:06:16] SW-NE对角算符进度: 86/100, 当前算符: D_(0, 2) * D_(85, 7), 耗时: 10.535s
[2025-07-25 09:06:26] SW-NE对角算符进度: 87/100, 当前算符: D_(0, 2) * D_(86, 8), 耗时: 10.485s
[2025-07-25 09:06:37] SW-NE对角算符进度: 88/100, 当前算符: D_(0, 2) * D_(87, 89), 耗时: 10.527s
[2025-07-25 09:06:47] SW-NE对角算符进度: 89/100, 当前算符: D_(0, 2) * D_(88, 90), 耗时: 10.516s
[2025-07-25 09:06:58] SW-NE对角算符进度: 90/100, 当前算符: D_(0, 2) * D_(89, 11), 耗时: 10.523s
[2025-07-25 09:07:08] SW-NE对角算符进度: 91/100, 当前算符: D_(0, 2) * D_(90, 12), 耗时: 10.514s
[2025-07-25 09:07:19] SW-NE对角算符进度: 92/100, 当前算符: D_(0, 2) * D_(91, 93), 耗时: 10.516s
[2025-07-25 09:07:29] SW-NE对角算符进度: 93/100, 当前算符: D_(0, 2) * D_(92, 94), 耗时: 10.464s
[2025-07-25 09:07:40] SW-NE对角算符进度: 94/100, 当前算符: D_(0, 2) * D_(93, 15), 耗时: 10.536s
[2025-07-25 09:07:50] SW-NE对角算符进度: 95/100, 当前算符: D_(0, 2) * D_(94, 16), 耗时: 10.482s
[2025-07-25 09:08:01] SW-NE对角算符进度: 96/100, 当前算符: D_(0, 2) * D_(95, 97), 耗时: 10.527s
[2025-07-25 09:08:11] SW-NE对角算符进度: 97/100, 当前算符: D_(0, 2) * D_(96, 98), 耗时: 10.518s
[2025-07-25 09:08:22] SW-NE对角算符进度: 98/100, 当前算符: D_(0, 2) * D_(97, 19), 耗时: 10.597s
[2025-07-25 09:08:31] SW-NE对角算符进度: 99/100, 当前算符: D_(0, 2) * D_(98, 0), 耗时: 8.736s
[2025-07-25 09:08:41] SW-NE对角算符进度: 100/100, 当前算符: D_(0, 2) * D_(99, 81), 耗时: 10.475s
[2025-07-25 09:08:41] 西南-东北方向对角二聚体相关函数计算完成,耗时: 1043.36 秒
[2025-07-25 09:08:41] 计算傅里叶变换...
[2025-07-25 09:08:42] 对角二聚体结构因子计算完成
[2025-07-25 09:08:43] 对角二聚体相关函数平均误差: 0.000125
