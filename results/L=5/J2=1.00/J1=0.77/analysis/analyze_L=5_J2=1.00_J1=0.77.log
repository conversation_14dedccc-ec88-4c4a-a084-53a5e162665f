[2025-07-25 06:40:11] ================================================================================
[2025-07-25 06:40:11] 加载量子态: L=5, J2=1.00, J1=0.77
[2025-07-25 06:40:11] 设置样本数为: 1048576
[2025-07-25 06:40:11] 开始生成共享样本集...
[2025-07-25 06:43:30] 样本生成完成,耗时: 199.036 秒
[2025-07-25 06:43:30] ================================================================================
[2025-07-25 06:43:30] 开始计算自旋结构因子...
[2025-07-25 06:43:30] 初始化操作符缓存...
[2025-07-25 06:43:30] 预构建所有自旋相关操作符...
[2025-07-25 06:43:30] 开始计算自旋相关函数...
[2025-07-25 06:43:40] 自旋算符进度: 1/100, 当前算符: S_0 · S_0, 耗时: 9.557s
[2025-07-25 06:43:51] 自旋算符进度: 2/100, 当前算符: S_0 · S_1, 耗时: 11.288s
[2025-07-25 06:43:57] 自旋算符进度: 3/100, 当前算符: S_0 · S_2, 耗时: 6.310s
[2025-07-25 06:44:03] 自旋算符进度: 4/100, 当前算符: S_0 · S_3, 耗时: 6.306s
[2025-07-25 06:44:10] 自旋算符进度: 5/100, 当前算符: S_0 · S_4, 耗时: 6.293s
[2025-07-25 06:44:16] 自旋算符进度: 6/100, 当前算符: S_0 · S_5, 耗时: 6.306s
[2025-07-25 06:44:22] 自旋算符进度: 7/100, 当前算符: S_0 · S_6, 耗时: 6.308s
[2025-07-25 06:44:29] 自旋算符进度: 8/100, 当前算符: S_0 · S_7, 耗时: 6.293s
[2025-07-25 06:44:35] 自旋算符进度: 9/100, 当前算符: S_0 · S_8, 耗时: 6.306s
[2025-07-25 06:44:41] 自旋算符进度: 10/100, 当前算符: S_0 · S_9, 耗时: 6.294s
[2025-07-25 06:44:48] 自旋算符进度: 11/100, 当前算符: S_0 · S_10, 耗时: 6.306s
[2025-07-25 06:44:54] 自旋算符进度: 12/100, 当前算符: S_0 · S_11, 耗时: 6.294s
[2025-07-25 06:45:00] 自旋算符进度: 13/100, 当前算符: S_0 · S_12, 耗时: 6.311s
[2025-07-25 06:45:07] 自旋算符进度: 14/100, 当前算符: S_0 · S_13, 耗时: 6.311s
[2025-07-25 06:45:13] 自旋算符进度: 15/100, 当前算符: S_0 · S_14, 耗时: 6.308s
[2025-07-25 06:45:19] 自旋算符进度: 16/100, 当前算符: S_0 · S_15, 耗时: 6.294s
[2025-07-25 06:45:25] 自旋算符进度: 17/100, 当前算符: S_0 · S_16, 耗时: 6.307s
[2025-07-25 06:45:32] 自旋算符进度: 18/100, 当前算符: S_0 · S_17, 耗时: 6.294s
[2025-07-25 06:45:38] 自旋算符进度: 19/100, 当前算符: S_0 · S_18, 耗时: 6.312s
[2025-07-25 06:45:44] 自旋算符进度: 20/100, 当前算符: S_0 · S_19, 耗时: 6.312s
[2025-07-25 06:45:51] 自旋算符进度: 21/100, 当前算符: S_0 · S_20, 耗时: 6.295s
[2025-07-25 06:45:57] 自旋算符进度: 22/100, 当前算符: S_0 · S_21, 耗时: 6.293s
[2025-07-25 06:46:03] 自旋算符进度: 23/100, 当前算符: S_0 · S_22, 耗时: 6.306s
[2025-07-25 06:46:10] 自旋算符进度: 24/100, 当前算符: S_0 · S_23, 耗时: 6.310s
[2025-07-25 06:46:16] 自旋算符进度: 25/100, 当前算符: S_0 · S_24, 耗时: 6.313s
[2025-07-25 06:46:22] 自旋算符进度: 26/100, 当前算符: S_0 · S_25, 耗时: 6.314s
[2025-07-25 06:46:28] 自旋算符进度: 27/100, 当前算符: S_0 · S_26, 耗时: 6.298s
[2025-07-25 06:46:35] 自旋算符进度: 28/100, 当前算符: S_0 · S_27, 耗时: 6.306s
[2025-07-25 06:46:41] 自旋算符进度: 29/100, 当前算符: S_0 · S_28, 耗时: 6.294s
[2025-07-25 06:46:47] 自旋算符进度: 30/100, 当前算符: S_0 · S_29, 耗时: 6.310s
[2025-07-25 06:46:54] 自旋算符进度: 31/100, 当前算符: S_0 · S_30, 耗时: 6.315s
[2025-07-25 06:47:00] 自旋算符进度: 32/100, 当前算符: S_0 · S_31, 耗时: 6.317s
[2025-07-25 06:47:06] 自旋算符进度: 33/100, 当前算符: S_0 · S_32, 耗时: 6.296s
[2025-07-25 06:47:13] 自旋算符进度: 34/100, 当前算符: S_0 · S_33, 耗时: 6.297s
[2025-07-25 06:47:19] 自旋算符进度: 35/100, 当前算符: S_0 · S_34, 耗时: 6.305s
[2025-07-25 06:47:25] 自旋算符进度: 36/100, 当前算符: S_0 · S_35, 耗时: 6.298s
[2025-07-25 06:47:32] 自旋算符进度: 37/100, 当前算符: S_0 · S_36, 耗时: 6.306s
[2025-07-25 06:47:38] 自旋算符进度: 38/100, 当前算符: S_0 · S_37, 耗时: 6.305s
[2025-07-25 06:47:44] 自旋算符进度: 39/100, 当前算符: S_0 · S_38, 耗时: 6.316s
[2025-07-25 06:47:51] 自旋算符进度: 40/100, 当前算符: S_0 · S_39, 耗时: 6.318s
[2025-07-25 06:47:57] 自旋算符进度: 41/100, 当前算符: S_0 · S_40, 耗时: 6.318s
[2025-07-25 06:48:03] 自旋算符进度: 42/100, 当前算符: S_0 · S_41, 耗时: 6.320s
[2025-07-25 06:48:09] 自旋算符进度: 43/100, 当前算符: S_0 · S_42, 耗时: 6.311s
[2025-07-25 06:48:16] 自旋算符进度: 44/100, 当前算符: S_0 · S_43, 耗时: 6.317s
[2025-07-25 06:48:22] 自旋算符进度: 45/100, 当前算符: S_0 · S_44, 耗时: 6.385s
[2025-07-25 06:48:28] 自旋算符进度: 46/100, 当前算符: S_0 · S_45, 耗时: 6.307s
[2025-07-25 06:48:35] 自旋算符进度: 47/100, 当前算符: S_0 · S_46, 耗时: 6.310s
[2025-07-25 06:48:41] 自旋算符进度: 48/100, 当前算符: S_0 · S_47, 耗时: 6.314s
[2025-07-25 06:48:47] 自旋算符进度: 49/100, 当前算符: S_0 · S_48, 耗时: 6.315s
[2025-07-25 06:48:54] 自旋算符进度: 50/100, 当前算符: S_0 · S_49, 耗时: 6.295s
[2025-07-25 06:49:00] 自旋算符进度: 51/100, 当前算符: S_0 · S_50, 耗时: 6.305s
[2025-07-25 06:49:06] 自旋算符进度: 52/100, 当前算符: S_0 · S_51, 耗时: 6.294s
[2025-07-25 06:49:13] 自旋算符进度: 53/100, 当前算符: S_0 · S_52, 耗时: 6.316s
[2025-07-25 06:49:19] 自旋算符进度: 54/100, 当前算符: S_0 · S_53, 耗时: 6.309s
[2025-07-25 06:49:25] 自旋算符进度: 55/100, 当前算符: S_0 · S_54, 耗时: 6.306s
[2025-07-25 06:49:32] 自旋算符进度: 56/100, 当前算符: S_0 · S_55, 耗时: 6.297s
[2025-07-25 06:49:38] 自旋算符进度: 57/100, 当前算符: S_0 · S_56, 耗时: 6.305s
[2025-07-25 06:49:44] 自旋算符进度: 58/100, 当前算符: S_0 · S_57, 耗时: 6.308s
[2025-07-25 06:49:50] 自旋算符进度: 59/100, 当前算符: S_0 · S_58, 耗时: 6.312s
[2025-07-25 06:49:57] 自旋算符进度: 60/100, 当前算符: S_0 · S_59, 耗时: 6.313s
[2025-07-25 06:50:03] 自旋算符进度: 61/100, 当前算符: S_0 · S_60, 耗时: 6.297s
[2025-07-25 06:50:09] 自旋算符进度: 62/100, 当前算符: S_0 · S_61, 耗时: 6.297s
[2025-07-25 06:50:16] 自旋算符进度: 63/100, 当前算符: S_0 · S_62, 耗时: 6.306s
[2025-07-25 06:50:22] 自旋算符进度: 64/100, 当前算符: S_0 · S_63, 耗时: 6.312s
[2025-07-25 06:50:28] 自旋算符进度: 65/100, 当前算符: S_0 · S_64, 耗时: 6.312s
[2025-07-25 06:50:35] 自旋算符进度: 66/100, 当前算符: S_0 · S_65, 耗时: 6.306s
[2025-07-25 06:50:41] 自旋算符进度: 67/100, 当前算符: S_0 · S_66, 耗时: 6.295s
[2025-07-25 06:50:47] 自旋算符进度: 68/100, 当前算符: S_0 · S_67, 耗时: 6.306s
[2025-07-25 06:50:54] 自旋算符进度: 69/100, 当前算符: S_0 · S_68, 耗时: 6.313s
[2025-07-25 06:51:00] 自旋算符进度: 70/100, 当前算符: S_0 · S_69, 耗时: 6.312s
[2025-07-25 06:51:06] 自旋算符进度: 71/100, 当前算符: S_0 · S_70, 耗时: 6.308s
[2025-07-25 06:51:12] 自旋算符进度: 72/100, 当前算符: S_0 · S_71, 耗时: 6.296s
[2025-07-25 06:51:19] 自旋算符进度: 73/100, 当前算符: S_0 · S_72, 耗时: 6.305s
[2025-07-25 06:51:25] 自旋算符进度: 74/100, 当前算符: S_0 · S_73, 耗时: 6.305s
[2025-07-25 06:51:31] 自旋算符进度: 75/100, 当前算符: S_0 · S_74, 耗时: 6.308s
[2025-07-25 06:51:38] 自旋算符进度: 76/100, 当前算符: S_0 · S_75, 耗时: 6.315s
[2025-07-25 06:51:44] 自旋算符进度: 77/100, 当前算符: S_0 · S_76, 耗时: 6.294s
[2025-07-25 06:51:50] 自旋算符进度: 78/100, 当前算符: S_0 · S_77, 耗时: 6.296s
[2025-07-25 06:51:57] 自旋算符进度: 79/100, 当前算符: S_0 · S_78, 耗时: 6.306s
[2025-07-25 06:52:03] 自旋算符进度: 80/100, 当前算符: S_0 · S_79, 耗时: 6.314s
[2025-07-25 06:52:09] 自旋算符进度: 81/100, 当前算符: S_0 · S_80, 耗时: 6.315s
[2025-07-25 06:52:16] 自旋算符进度: 82/100, 当前算符: S_0 · S_81, 耗时: 6.305s
[2025-07-25 06:52:22] 自旋算符进度: 83/100, 当前算符: S_0 · S_82, 耗时: 6.299s
[2025-07-25 06:52:28] 自旋算符进度: 84/100, 当前算符: S_0 · S_83, 耗时: 6.305s
[2025-07-25 06:52:34] 自旋算符进度: 85/100, 当前算符: S_0 · S_84, 耗时: 6.311s
[2025-07-25 06:52:41] 自旋算符进度: 86/100, 当前算符: S_0 · S_85, 耗时: 6.317s
[2025-07-25 06:52:47] 自旋算符进度: 87/100, 当前算符: S_0 · S_86, 耗时: 6.316s
[2025-07-25 06:52:53] 自旋算符进度: 88/100, 当前算符: S_0 · S_87, 耗时: 6.306s
[2025-07-25 06:53:00] 自旋算符进度: 89/100, 当前算符: S_0 · S_88, 耗时: 6.295s
[2025-07-25 06:53:06] 自旋算符进度: 90/100, 当前算符: S_0 · S_89, 耗时: 6.314s
[2025-07-25 06:53:12] 自旋算符进度: 91/100, 当前算符: S_0 · S_90, 耗时: 6.311s
[2025-07-25 06:53:19] 自旋算符进度: 92/100, 当前算符: S_0 · S_91, 耗时: 6.312s
[2025-07-25 06:53:25] 自旋算符进度: 93/100, 当前算符: S_0 · S_92, 耗时: 6.307s
[2025-07-25 06:53:31] 自旋算符进度: 94/100, 当前算符: S_0 · S_93, 耗时: 6.306s
[2025-07-25 06:53:38] 自旋算符进度: 95/100, 当前算符: S_0 · S_94, 耗时: 6.299s
[2025-07-25 06:53:44] 自旋算符进度: 96/100, 当前算符: S_0 · S_95, 耗时: 6.305s
[2025-07-25 06:53:50] 自旋算符进度: 97/100, 当前算符: S_0 · S_96, 耗时: 6.316s
[2025-07-25 06:53:56] 自旋算符进度: 98/100, 当前算符: S_0 · S_97, 耗时: 6.317s
[2025-07-25 06:54:03] 自旋算符进度: 99/100, 当前算符: S_0 · S_98, 耗时: 6.312s
[2025-07-25 06:54:09] 自旋算符进度: 100/100, 当前算符: S_0 · S_99, 耗时: 6.300s
[2025-07-25 06:54:09] 自旋相关函数计算完成,总耗时 639.08 秒
[2025-07-25 06:54:09] 计算傅里叶变换...
[2025-07-25 06:54:10] 自旋结构因子计算完成
[2025-07-25 06:54:11] 自旋相关函数平均误差: 0.000596
[2025-07-25 06:54:11] ================================================================================
[2025-07-25 06:54:11] 开始计算对角二聚体结构因子...
[2025-07-25 06:54:11] 识别所有对角二聚体...
[2025-07-25 06:54:11] 总共找到 100 个西北-东南方向对角二聚体和 100 个西南-东北方向对角二聚体
[2025-07-25 06:54:11] 预计算对角二聚体操作符...
[2025-07-25 06:54:13] 开始计算西北-东南方向对角二聚体相关函数 (100 个算符)...
[2025-07-25 06:54:19] NW-SE对角算符进度: 1/100, 当前算符: D_(0, 18) * D_(0, 18), 耗时: 6.329s
[2025-07-25 06:54:35] NW-SE对角算符进度: 2/100, 当前算符: D_(0, 18) * D_(1, 39), 耗时: 15.580s
[2025-07-25 06:54:46] NW-SE对角算符进度: 3/100, 当前算符: D_(0, 18) * D_(2, 20), 耗时: 10.630s
[2025-07-25 06:54:56] NW-SE对角算符进度: 4/100, 当前算符: D_(0, 18) * D_(3, 1), 耗时: 10.577s
[2025-07-25 06:55:07] NW-SE对角算符进度: 5/100, 当前算符: D_(0, 18) * D_(4, 2), 耗时: 10.626s
[2025-07-25 06:55:17] NW-SE对角算符进度: 6/100, 当前算符: D_(0, 18) * D_(5, 23), 耗时: 10.624s
[2025-07-25 06:55:28] NW-SE对角算符进度: 7/100, 当前算符: D_(0, 18) * D_(6, 24), 耗时: 10.624s
[2025-07-25 06:55:39] NW-SE对角算符进度: 8/100, 当前算符: D_(0, 18) * D_(7, 5), 耗时: 10.576s
[2025-07-25 06:55:49] NW-SE对角算符进度: 9/100, 当前算符: D_(0, 18) * D_(8, 6), 耗时: 10.625s
[2025-07-25 06:56:00] NW-SE对角算符进度: 10/100, 当前算符: D_(0, 18) * D_(9, 27), 耗时: 10.562s
[2025-07-25 06:56:10] NW-SE对角算符进度: 11/100, 当前算符: D_(0, 18) * D_(10, 28), 耗时: 10.626s
[2025-07-25 06:56:21] NW-SE对角算符进度: 12/100, 当前算符: D_(0, 18) * D_(11, 9), 耗时: 10.634s
[2025-07-25 06:56:32] NW-SE对角算符进度: 13/100, 当前算符: D_(0, 18) * D_(12, 10), 耗时: 10.630s
[2025-07-25 06:56:42] NW-SE对角算符进度: 14/100, 当前算符: D_(0, 18) * D_(13, 31), 耗时: 10.630s
[2025-07-25 06:56:53] NW-SE对角算符进度: 15/100, 当前算符: D_(0, 18) * D_(14, 32), 耗时: 10.570s
[2025-07-25 06:57:04] NW-SE对角算符进度: 16/100, 当前算符: D_(0, 18) * D_(15, 13), 耗时: 10.631s
[2025-07-25 06:57:14] NW-SE对角算符进度: 17/100, 当前算符: D_(0, 18) * D_(16, 14), 耗时: 10.584s
[2025-07-25 06:57:25] NW-SE对角算符进度: 18/100, 当前算符: D_(0, 18) * D_(17, 35), 耗时: 10.632s
[2025-07-25 06:57:42] NW-SE对角算符进度: 19/100, 当前算符: D_(0, 18) * D_(18, 36), 耗时: 17.673s
[2025-07-25 06:57:53] NW-SE对角算符进度: 20/100, 当前算符: D_(0, 18) * D_(19, 17), 耗时: 10.593s
[2025-07-25 06:58:04] NW-SE对角算符进度: 21/100, 当前算符: D_(0, 18) * D_(20, 38), 耗时: 10.595s
[2025-07-25 06:58:14] NW-SE对角算符进度: 22/100, 当前算符: D_(0, 18) * D_(21, 59), 耗时: 10.572s
[2025-07-25 06:58:25] NW-SE对角算符进度: 23/100, 当前算符: D_(0, 18) * D_(22, 40), 耗时: 10.617s
[2025-07-25 06:58:35] NW-SE对角算符进度: 24/100, 当前算符: D_(0, 18) * D_(23, 21), 耗时: 10.616s
[2025-07-25 06:58:46] NW-SE对角算符进度: 25/100, 当前算符: D_(0, 18) * D_(24, 22), 耗时: 10.606s
[2025-07-25 06:58:57] NW-SE对角算符进度: 26/100, 当前算符: D_(0, 18) * D_(25, 43), 耗时: 10.598s
[2025-07-25 06:59:07] NW-SE对角算符进度: 27/100, 当前算符: D_(0, 18) * D_(26, 44), 耗时: 10.577s
[2025-07-25 06:59:18] NW-SE对角算符进度: 28/100, 当前算符: D_(0, 18) * D_(27, 25), 耗时: 10.596s
[2025-07-25 06:59:28] NW-SE对角算符进度: 29/100, 当前算符: D_(0, 18) * D_(28, 26), 耗时: 10.596s
[2025-07-25 06:59:39] NW-SE对角算符进度: 30/100, 当前算符: D_(0, 18) * D_(29, 47), 耗时: 10.612s
[2025-07-25 06:59:50] NW-SE对角算符进度: 31/100, 当前算符: D_(0, 18) * D_(30, 48), 耗时: 10.597s
[2025-07-25 07:00:00] NW-SE对角算符进度: 32/100, 当前算符: D_(0, 18) * D_(31, 29), 耗时: 10.595s
[2025-07-25 07:00:11] NW-SE对角算符进度: 33/100, 当前算符: D_(0, 18) * D_(32, 30), 耗时: 10.570s
[2025-07-25 07:00:21] NW-SE对角算符进度: 34/100, 当前算符: D_(0, 18) * D_(33, 51), 耗时: 10.577s
[2025-07-25 07:00:32] NW-SE对角算符进度: 35/100, 当前算符: D_(0, 18) * D_(34, 52), 耗时: 10.615s
[2025-07-25 07:00:43] NW-SE对角算符进度: 36/100, 当前算符: D_(0, 18) * D_(35, 33), 耗时: 10.595s
[2025-07-25 07:00:53] NW-SE对角算符进度: 37/100, 当前算符: D_(0, 18) * D_(36, 34), 耗时: 10.597s
[2025-07-25 07:01:04] NW-SE对角算符进度: 38/100, 当前算符: D_(0, 18) * D_(37, 55), 耗时: 10.573s
[2025-07-25 07:01:14] NW-SE对角算符进度: 39/100, 当前算符: D_(0, 18) * D_(38, 56), 耗时: 10.614s
[2025-07-25 07:01:25] NW-SE对角算符进度: 40/100, 当前算符: D_(0, 18) * D_(39, 37), 耗时: 10.600s
[2025-07-25 07:01:36] NW-SE对角算符进度: 41/100, 当前算符: D_(0, 18) * D_(40, 58), 耗时: 10.599s
[2025-07-25 07:01:46] NW-SE对角算符进度: 42/100, 当前算符: D_(0, 18) * D_(41, 79), 耗时: 10.614s
[2025-07-25 07:01:57] NW-SE对角算符进度: 43/100, 当前算符: D_(0, 18) * D_(42, 60), 耗时: 10.615s
[2025-07-25 07:02:07] NW-SE对角算符进度: 44/100, 当前算符: D_(0, 18) * D_(43, 41), 耗时: 10.596s
[2025-07-25 07:02:18] NW-SE对角算符进度: 45/100, 当前算符: D_(0, 18) * D_(44, 42), 耗时: 10.579s
[2025-07-25 07:02:29] NW-SE对角算符进度: 46/100, 当前算符: D_(0, 18) * D_(45, 63), 耗时: 10.595s
[2025-07-25 07:02:39] NW-SE对角算符进度: 47/100, 当前算符: D_(0, 18) * D_(46, 64), 耗时: 10.616s
[2025-07-25 07:02:50] NW-SE对角算符进度: 48/100, 当前算符: D_(0, 18) * D_(47, 45), 耗时: 10.596s
[2025-07-25 07:03:00] NW-SE对角算符进度: 49/100, 当前算符: D_(0, 18) * D_(48, 46), 耗时: 10.579s
[2025-07-25 07:03:11] NW-SE对角算符进度: 50/100, 当前算符: D_(0, 18) * D_(49, 67), 耗时: 10.579s
[2025-07-25 07:03:22] NW-SE对角算符进度: 51/100, 当前算符: D_(0, 18) * D_(50, 68), 耗时: 10.595s
[2025-07-25 07:03:32] NW-SE对角算符进度: 52/100, 当前算符: D_(0, 18) * D_(51, 49), 耗时: 10.605s
[2025-07-25 07:03:43] NW-SE对角算符进度: 53/100, 当前算符: D_(0, 18) * D_(52, 50), 耗时: 10.602s
[2025-07-25 07:03:53] NW-SE对角算符进度: 54/100, 当前算符: D_(0, 18) * D_(53, 71), 耗时: 10.607s
[2025-07-25 07:04:04] NW-SE对角算符进度: 55/100, 当前算符: D_(0, 18) * D_(54, 72), 耗时: 10.595s
[2025-07-25 07:04:15] NW-SE对角算符进度: 56/100, 当前算符: D_(0, 18) * D_(55, 53), 耗时: 10.578s
[2025-07-25 07:04:25] NW-SE对角算符进度: 57/100, 当前算符: D_(0, 18) * D_(56, 54), 耗时: 10.609s
[2025-07-25 07:04:36] NW-SE对角算符进度: 58/100, 当前算符: D_(0, 18) * D_(57, 75), 耗时: 10.616s
[2025-07-25 07:04:46] NW-SE对角算符进度: 59/100, 当前算符: D_(0, 18) * D_(58, 76), 耗时: 10.616s
[2025-07-25 07:04:57] NW-SE对角算符进度: 60/100, 当前算符: D_(0, 18) * D_(59, 57), 耗时: 10.580s
[2025-07-25 07:05:08] NW-SE对角算符进度: 61/100, 当前算符: D_(0, 18) * D_(60, 78), 耗时: 10.596s
[2025-07-25 07:05:18] NW-SE对角算符进度: 62/100, 当前算符: D_(0, 18) * D_(61, 99), 耗时: 10.620s
[2025-07-25 07:05:29] NW-SE对角算符进度: 63/100, 当前算符: D_(0, 18) * D_(62, 80), 耗时: 10.615s
[2025-07-25 07:05:39] NW-SE对角算符进度: 64/100, 当前算符: D_(0, 18) * D_(63, 61), 耗时: 10.607s
[2025-07-25 07:05:50] NW-SE对角算符进度: 65/100, 当前算符: D_(0, 18) * D_(64, 62), 耗时: 10.596s
[2025-07-25 07:06:01] NW-SE对角算符进度: 66/100, 当前算符: D_(0, 18) * D_(65, 83), 耗时: 10.595s
[2025-07-25 07:06:11] NW-SE对角算符进度: 67/100, 当前算符: D_(0, 18) * D_(66, 84), 耗时: 10.579s
[2025-07-25 07:06:22] NW-SE对角算符进度: 68/100, 当前算符: D_(0, 18) * D_(67, 65), 耗时: 10.617s
[2025-07-25 07:06:32] NW-SE对角算符进度: 69/100, 当前算符: D_(0, 18) * D_(68, 66), 耗时: 10.616s
[2025-07-25 07:06:43] NW-SE对角算符进度: 70/100, 当前算符: D_(0, 18) * D_(69, 87), 耗时: 10.577s
[2025-07-25 07:06:54] NW-SE对角算符进度: 71/100, 当前算符: D_(0, 18) * D_(70, 88), 耗时: 10.597s
[2025-07-25 07:07:04] NW-SE对角算符进度: 72/100, 当前算符: D_(0, 18) * D_(71, 69), 耗时: 10.579s
[2025-07-25 07:07:15] NW-SE对角算符进度: 73/100, 当前算符: D_(0, 18) * D_(72, 70), 耗时: 10.616s
[2025-07-25 07:07:25] NW-SE对角算符进度: 74/100, 当前算符: D_(0, 18) * D_(73, 91), 耗时: 10.617s
[2025-07-25 07:07:36] NW-SE对角算符进度: 75/100, 当前算符: D_(0, 18) * D_(74, 92), 耗时: 10.595s
[2025-07-25 07:07:47] NW-SE对角算符进度: 76/100, 当前算符: D_(0, 18) * D_(75, 73), 耗时: 10.577s
[2025-07-25 07:07:57] NW-SE对角算符进度: 77/100, 当前算符: D_(0, 18) * D_(76, 74), 耗时: 10.595s
[2025-07-25 07:08:08] NW-SE对角算符进度: 78/100, 当前算符: D_(0, 18) * D_(77, 95), 耗时: 10.582s
[2025-07-25 07:08:18] NW-SE对角算符进度: 79/100, 当前算符: D_(0, 18) * D_(78, 96), 耗时: 10.597s
[2025-07-25 07:08:29] NW-SE对角算符进度: 80/100, 当前算符: D_(0, 18) * D_(79, 77), 耗时: 10.608s
[2025-07-25 07:08:40] NW-SE对角算符进度: 81/100, 当前算符: D_(0, 18) * D_(80, 98), 耗时: 10.612s
[2025-07-25 07:08:50] NW-SE对角算符进度: 82/100, 当前算符: D_(0, 18) * D_(81, 19), 耗时: 10.597s
[2025-07-25 07:08:59] NW-SE对角算符进度: 83/100, 当前算符: D_(0, 18) * D_(82, 0), 耗时: 8.839s
[2025-07-25 07:09:10] NW-SE对角算符进度: 84/100, 当前算符: D_(0, 18) * D_(83, 81), 耗时: 10.598s
[2025-07-25 07:09:20] NW-SE对角算符进度: 85/100, 当前算符: D_(0, 18) * D_(84, 82), 耗时: 10.617s
[2025-07-25 07:09:31] NW-SE对角算符进度: 86/100, 当前算符: D_(0, 18) * D_(85, 3), 耗时: 10.616s
[2025-07-25 07:09:41] NW-SE对角算符进度: 87/100, 当前算符: D_(0, 18) * D_(86, 4), 耗时: 10.598s
[2025-07-25 07:09:52] NW-SE对角算符进度: 88/100, 当前算符: D_(0, 18) * D_(87, 85), 耗时: 10.581s
[2025-07-25 07:10:03] NW-SE对角算符进度: 89/100, 当前算符: D_(0, 18) * D_(88, 86), 耗时: 10.698s
[2025-07-25 07:10:13] NW-SE对角算符进度: 90/100, 当前算符: D_(0, 18) * D_(89, 7), 耗时: 10.581s
[2025-07-25 07:10:24] NW-SE对角算符进度: 91/100, 当前算符: D_(0, 18) * D_(90, 8), 耗时: 10.597s
[2025-07-25 07:10:35] NW-SE对角算符进度: 92/100, 当前算符: D_(0, 18) * D_(91, 89), 耗时: 10.617s
[2025-07-25 07:10:45] NW-SE对角算符进度: 93/100, 当前算符: D_(0, 18) * D_(92, 90), 耗时: 10.619s
[2025-07-25 07:10:56] NW-SE对角算符进度: 94/100, 当前算符: D_(0, 18) * D_(93, 11), 耗时: 10.595s
[2025-07-25 07:11:06] NW-SE对角算符进度: 95/100, 当前算符: D_(0, 18) * D_(94, 12), 耗时: 10.576s
[2025-07-25 07:11:17] NW-SE对角算符进度: 96/100, 当前算符: D_(0, 18) * D_(95, 93), 耗时: 10.595s
[2025-07-25 07:11:28] NW-SE对角算符进度: 97/100, 当前算符: D_(0, 18) * D_(96, 94), 耗时: 10.587s
[2025-07-25 07:11:38] NW-SE对角算符进度: 98/100, 当前算符: D_(0, 18) * D_(97, 15), 耗时: 10.615s
[2025-07-25 07:11:49] NW-SE对角算符进度: 99/100, 当前算符: D_(0, 18) * D_(98, 16), 耗时: 10.612s
[2025-07-25 07:11:59] NW-SE对角算符进度: 100/100, 当前算符: D_(0, 18) * D_(99, 97), 耗时: 10.609s
[2025-07-25 07:11:59] 西北-东南方向对角二聚体相关函数计算完成,耗时: 1066.31 秒
[2025-07-25 07:11:59] ================================================================================
[2025-07-25 07:11:59] 开始计算西南-东北方向对角二聚体相关函数 (100 个算符)...
[2025-07-25 07:12:06] SW-NE对角算符进度: 1/100, 当前算符: D_(0, 2) * D_(0, 2), 耗时: 6.303s
[2025-07-25 07:12:16] SW-NE对角算符进度: 2/100, 当前算符: D_(0, 2) * D_(1, 23), 耗时: 10.625s
[2025-07-25 07:12:25] SW-NE对角算符进度: 3/100, 当前算符: D_(0, 2) * D_(2, 24), 耗时: 8.833s
[2025-07-25 07:12:36] SW-NE对角算符进度: 4/100, 当前算符: D_(0, 2) * D_(3, 5), 耗时: 10.583s
[2025-07-25 07:12:46] SW-NE对角算符进度: 5/100, 当前算符: D_(0, 2) * D_(4, 6), 耗时: 10.587s
[2025-07-25 07:12:57] SW-NE对角算符进度: 6/100, 当前算符: D_(0, 2) * D_(5, 27), 耗时: 10.613s
[2025-07-25 07:13:08] SW-NE对角算符进度: 7/100, 当前算符: D_(0, 2) * D_(6, 28), 耗时: 10.618s
[2025-07-25 07:13:18] SW-NE对角算符进度: 8/100, 当前算符: D_(0, 2) * D_(7, 9), 耗时: 10.611s
[2025-07-25 07:13:29] SW-NE对角算符进度: 9/100, 当前算符: D_(0, 2) * D_(8, 10), 耗时: 10.581s
[2025-07-25 07:13:39] SW-NE对角算符进度: 10/100, 当前算符: D_(0, 2) * D_(9, 31), 耗时: 10.616s
[2025-07-25 07:13:50] SW-NE对角算符进度: 11/100, 当前算符: D_(0, 2) * D_(10, 32), 耗时: 10.583s
[2025-07-25 07:14:01] SW-NE对角算符进度: 12/100, 当前算符: D_(0, 2) * D_(11, 13), 耗时: 10.607s
[2025-07-25 07:14:11] SW-NE对角算符进度: 13/100, 当前算符: D_(0, 2) * D_(12, 14), 耗时: 10.583s
[2025-07-25 07:14:22] SW-NE对角算符进度: 14/100, 当前算符: D_(0, 2) * D_(13, 35), 耗时: 10.660s
[2025-07-25 07:14:32] SW-NE对角算符进度: 15/100, 当前算符: D_(0, 2) * D_(14, 36), 耗时: 10.616s
[2025-07-25 07:14:43] SW-NE对角算符进度: 16/100, 当前算符: D_(0, 2) * D_(15, 17), 耗时: 10.610s
[2025-07-25 07:14:54] SW-NE对角算符进度: 17/100, 当前算符: D_(0, 2) * D_(16, 18), 耗时: 10.577s
[2025-07-25 07:15:04] SW-NE对角算符进度: 18/100, 当前算符: D_(0, 2) * D_(17, 39), 耗时: 10.610s
[2025-07-25 07:15:15] SW-NE对角算符进度: 19/100, 当前算符: D_(0, 2) * D_(18, 20), 耗时: 10.604s
[2025-07-25 07:15:25] SW-NE对角算符进度: 20/100, 当前算符: D_(0, 2) * D_(19, 1), 耗时: 10.623s
[2025-07-25 07:15:36] SW-NE对角算符进度: 21/100, 当前算符: D_(0, 2) * D_(20, 22), 耗时: 10.610s
[2025-07-25 07:15:47] SW-NE对角算符进度: 22/100, 当前算符: D_(0, 2) * D_(21, 43), 耗时: 10.579s
[2025-07-25 07:15:57] SW-NE对角算符进度: 23/100, 当前算符: D_(0, 2) * D_(22, 44), 耗时: 10.611s
[2025-07-25 07:16:08] SW-NE对角算符进度: 24/100, 当前算符: D_(0, 2) * D_(23, 25), 耗时: 10.602s
[2025-07-25 07:16:18] SW-NE对角算符进度: 25/100, 当前算符: D_(0, 2) * D_(24, 26), 耗时: 10.609s
[2025-07-25 07:16:29] SW-NE对角算符进度: 26/100, 当前算符: D_(0, 2) * D_(25, 47), 耗时: 10.616s
[2025-07-25 07:16:40] SW-NE对角算符进度: 27/100, 当前算符: D_(0, 2) * D_(26, 48), 耗时: 10.618s
[2025-07-25 07:16:50] SW-NE对角算符进度: 28/100, 当前算符: D_(0, 2) * D_(27, 29), 耗时: 10.615s
[2025-07-25 07:17:01] SW-NE对角算符进度: 29/100, 当前算符: D_(0, 2) * D_(28, 30), 耗时: 10.610s
[2025-07-25 07:17:12] SW-NE对角算符进度: 30/100, 当前算符: D_(0, 2) * D_(29, 51), 耗时: 10.589s
[2025-07-25 07:17:22] SW-NE对角算符进度: 31/100, 当前算符: D_(0, 2) * D_(30, 52), 耗时: 10.641s
[2025-07-25 07:17:33] SW-NE对角算符进度: 32/100, 当前算符: D_(0, 2) * D_(31, 33), 耗时: 10.622s
[2025-07-25 07:17:43] SW-NE对角算符进度: 33/100, 当前算符: D_(0, 2) * D_(32, 34), 耗时: 10.622s
[2025-07-25 07:17:54] SW-NE对角算符进度: 34/100, 当前算符: D_(0, 2) * D_(33, 55), 耗时: 10.609s
[2025-07-25 07:18:05] SW-NE对角算符进度: 35/100, 当前算符: D_(0, 2) * D_(34, 56), 耗时: 10.610s
[2025-07-25 07:18:15] SW-NE对角算符进度: 36/100, 当前算符: D_(0, 2) * D_(35, 37), 耗时: 10.585s
[2025-07-25 07:18:26] SW-NE对角算符进度: 37/100, 当前算符: D_(0, 2) * D_(36, 38), 耗时: 10.621s
[2025-07-25 07:18:36] SW-NE对角算符进度: 38/100, 当前算符: D_(0, 2) * D_(37, 59), 耗时: 10.605s
[2025-07-25 07:18:47] SW-NE对角算符进度: 39/100, 当前算符: D_(0, 2) * D_(38, 40), 耗时: 10.615s
[2025-07-25 07:18:58] SW-NE对角算符进度: 40/100, 当前算符: D_(0, 2) * D_(39, 21), 耗时: 10.584s
[2025-07-25 07:19:08] SW-NE对角算符进度: 41/100, 当前算符: D_(0, 2) * D_(40, 42), 耗时: 10.610s
[2025-07-25 07:19:19] SW-NE对角算符进度: 42/100, 当前算符: D_(0, 2) * D_(41, 63), 耗时: 10.584s
[2025-07-25 07:19:29] SW-NE对角算符进度: 43/100, 当前算符: D_(0, 2) * D_(42, 64), 耗时: 10.611s
[2025-07-25 07:19:40] SW-NE对角算符进度: 44/100, 当前算符: D_(0, 2) * D_(43, 45), 耗时: 10.623s
[2025-07-25 07:19:51] SW-NE对角算符进度: 45/100, 当前算符: D_(0, 2) * D_(44, 46), 耗时: 10.611s
[2025-07-25 07:20:01] SW-NE对角算符进度: 46/100, 当前算符: D_(0, 2) * D_(45, 67), 耗时: 10.584s
[2025-07-25 07:20:12] SW-NE对角算符进度: 47/100, 当前算符: D_(0, 2) * D_(46, 68), 耗时: 10.611s
[2025-07-25 07:20:22] SW-NE对角算符进度: 48/100, 当前算符: D_(0, 2) * D_(47, 49), 耗时: 10.583s
[2025-07-25 07:20:33] SW-NE对角算符进度: 49/100, 当前算符: D_(0, 2) * D_(48, 50), 耗时: 10.587s
[2025-07-25 07:20:44] SW-NE对角算符进度: 50/100, 当前算符: D_(0, 2) * D_(49, 71), 耗时: 10.610s
[2025-07-25 07:20:54] SW-NE对角算符进度: 51/100, 当前算符: D_(0, 2) * D_(50, 72), 耗时: 10.619s
[2025-07-25 07:21:05] SW-NE对角算符进度: 52/100, 当前算符: D_(0, 2) * D_(51, 53), 耗时: 10.619s
[2025-07-25 07:21:16] SW-NE对角算符进度: 53/100, 当前算符: D_(0, 2) * D_(52, 54), 耗时: 10.609s
[2025-07-25 07:21:26] SW-NE对角算符进度: 54/100, 当前算符: D_(0, 2) * D_(53, 75), 耗时: 10.580s
[2025-07-25 07:21:37] SW-NE对角算符进度: 55/100, 当前算符: D_(0, 2) * D_(54, 76), 耗时: 10.611s
[2025-07-25 07:21:47] SW-NE对角算符进度: 56/100, 当前算符: D_(0, 2) * D_(55, 57), 耗时: 10.602s
[2025-07-25 07:21:58] SW-NE对角算符进度: 57/100, 当前算符: D_(0, 2) * D_(56, 58), 耗时: 10.627s
[2025-07-25 07:22:09] SW-NE对角算符进度: 58/100, 当前算符: D_(0, 2) * D_(57, 79), 耗时: 10.626s
[2025-07-25 07:22:19] SW-NE对角算符进度: 59/100, 当前算符: D_(0, 2) * D_(58, 60), 耗时: 10.611s
[2025-07-25 07:22:30] SW-NE对角算符进度: 60/100, 当前算符: D_(0, 2) * D_(59, 41), 耗时: 10.588s
[2025-07-25 07:22:40] SW-NE对角算符进度: 61/100, 当前算符: D_(0, 2) * D_(60, 62), 耗时: 10.610s
[2025-07-25 07:22:51] SW-NE对角算符进度: 62/100, 当前算符: D_(0, 2) * D_(61, 83), 耗时: 10.647s
[2025-07-25 07:23:02] SW-NE对角算符进度: 63/100, 当前算符: D_(0, 2) * D_(62, 84), 耗时: 10.627s
[2025-07-25 07:23:12] SW-NE对角算符进度: 64/100, 当前算符: D_(0, 2) * D_(63, 65), 耗时: 10.594s
[2025-07-25 07:23:23] SW-NE对角算符进度: 65/100, 当前算符: D_(0, 2) * D_(64, 66), 耗时: 10.586s
[2025-07-25 07:23:33] SW-NE对角算符进度: 66/100, 当前算符: D_(0, 2) * D_(65, 87), 耗时: 10.611s
[2025-07-25 07:23:44] SW-NE对角算符进度: 67/100, 当前算符: D_(0, 2) * D_(66, 88), 耗时: 10.627s
[2025-07-25 07:23:55] SW-NE对角算符进度: 68/100, 当前算符: D_(0, 2) * D_(67, 69), 耗时: 10.628s
[2025-07-25 07:24:05] SW-NE对角算符进度: 69/100, 当前算符: D_(0, 2) * D_(68, 70), 耗时: 10.610s
[2025-07-25 07:24:16] SW-NE对角算符进度: 70/100, 当前算符: D_(0, 2) * D_(69, 91), 耗时: 10.584s
[2025-07-25 07:24:27] SW-NE对角算符进度: 71/100, 当前算符: D_(0, 2) * D_(70, 92), 耗时: 10.609s
[2025-07-25 07:24:37] SW-NE对角算符进度: 72/100, 当前算符: D_(0, 2) * D_(71, 73), 耗时: 10.608s
[2025-07-25 07:24:48] SW-NE对角算符进度: 73/100, 当前算符: D_(0, 2) * D_(72, 74), 耗时: 10.629s
[2025-07-25 07:24:58] SW-NE对角算符进度: 74/100, 当前算符: D_(0, 2) * D_(73, 95), 耗时: 10.624s
[2025-07-25 07:25:09] SW-NE对角算符进度: 75/100, 当前算符: D_(0, 2) * D_(74, 96), 耗时: 10.610s
[2025-07-25 07:25:20] SW-NE对角算符进度: 76/100, 当前算符: D_(0, 2) * D_(75, 77), 耗时: 10.586s
[2025-07-25 07:25:30] SW-NE对角算符进度: 77/100, 当前算符: D_(0, 2) * D_(76, 78), 耗时: 10.610s
[2025-07-25 07:25:41] SW-NE对角算符进度: 78/100, 当前算符: D_(0, 2) * D_(77, 99), 耗时: 10.620s
[2025-07-25 07:25:51] SW-NE对角算符进度: 79/100, 当前算符: D_(0, 2) * D_(78, 80), 耗时: 10.617s
[2025-07-25 07:26:02] SW-NE对角算符进度: 80/100, 当前算符: D_(0, 2) * D_(79, 61), 耗时: 10.586s
[2025-07-25 07:26:13] SW-NE对角算符进度: 81/100, 当前算符: D_(0, 2) * D_(80, 82), 耗时: 10.588s
[2025-07-25 07:26:23] SW-NE对角算符进度: 82/100, 当前算符: D_(0, 2) * D_(81, 3), 耗时: 10.610s
[2025-07-25 07:26:34] SW-NE对角算符进度: 83/100, 当前算符: D_(0, 2) * D_(82, 4), 耗时: 10.619s
[2025-07-25 07:26:44] SW-NE对角算符进度: 84/100, 当前算符: D_(0, 2) * D_(83, 85), 耗时: 10.621s
[2025-07-25 07:26:55] SW-NE对角算符进度: 85/100, 当前算符: D_(0, 2) * D_(84, 86), 耗时: 10.582s
[2025-07-25 07:27:06] SW-NE对角算符进度: 86/100, 当前算符: D_(0, 2) * D_(85, 7), 耗时: 10.610s
[2025-07-25 07:27:16] SW-NE对角算符进度: 87/100, 当前算符: D_(0, 2) * D_(86, 8), 耗时: 10.584s
[2025-07-25 07:27:27] SW-NE对角算符进度: 88/100, 当前算符: D_(0, 2) * D_(87, 89), 耗时: 10.610s
[2025-07-25 07:27:37] SW-NE对角算符进度: 89/100, 当前算符: D_(0, 2) * D_(88, 90), 耗时: 10.602s
[2025-07-25 07:27:48] SW-NE对角算符进度: 90/100, 当前算符: D_(0, 2) * D_(89, 11), 耗时: 10.623s
[2025-07-25 07:27:59] SW-NE对角算符进度: 91/100, 当前算符: D_(0, 2) * D_(90, 12), 耗时: 10.612s
[2025-07-25 07:28:09] SW-NE对角算符进度: 92/100, 当前算符: D_(0, 2) * D_(91, 93), 耗时: 10.612s
[2025-07-25 07:28:20] SW-NE对角算符进度: 93/100, 当前算符: D_(0, 2) * D_(92, 94), 耗时: 10.587s
[2025-07-25 07:28:31] SW-NE对角算符进度: 94/100, 当前算符: D_(0, 2) * D_(93, 15), 耗时: 10.611s
[2025-07-25 07:28:41] SW-NE对角算符进度: 95/100, 当前算符: D_(0, 2) * D_(94, 16), 耗时: 10.585s
[2025-07-25 07:28:52] SW-NE对角算符进度: 96/100, 当前算符: D_(0, 2) * D_(95, 97), 耗时: 10.711s
[2025-07-25 07:29:02] SW-NE对角算符进度: 97/100, 当前算符: D_(0, 2) * D_(96, 98), 耗时: 10.611s
[2025-07-25 07:29:13] SW-NE对角算符进度: 98/100, 当前算符: D_(0, 2) * D_(97, 19), 耗时: 10.620s
[2025-07-25 07:29:22] SW-NE对角算符进度: 99/100, 当前算符: D_(0, 2) * D_(98, 0), 耗时: 8.816s
[2025-07-25 07:29:32] SW-NE对角算符进度: 100/100, 当前算符: D_(0, 2) * D_(99, 81), 耗时: 10.614s
[2025-07-25 07:29:32] 西南-东北方向对角二聚体相关函数计算完成,耗时: 1053.10 秒
[2025-07-25 07:29:33] 计算傅里叶变换...
[2025-07-25 07:29:33] 对角二聚体结构因子计算完成
[2025-07-25 07:29:34] 对角二聚体相关函数平均误差: 0.000129
