[2025-07-25 12:28:22] ================================================================================
[2025-07-25 12:28:22] 加载量子态: L=5, J2=1.00, J1=0.84
[2025-07-25 12:28:22] 设置样本数为: 1048576
[2025-07-25 12:28:22] 开始生成共享样本集...
[2025-07-25 12:31:41] 样本生成完成,耗时: 199.764 秒
[2025-07-25 12:31:41] ================================================================================
[2025-07-25 12:31:41] 开始计算自旋结构因子...
[2025-07-25 12:31:41] 初始化操作符缓存...
[2025-07-25 12:31:41] 预构建所有自旋相关操作符...
[2025-07-25 12:31:42] 开始计算自旋相关函数...
[2025-07-25 12:31:53] 自旋算符进度: 1/100, 当前算符: S_0 · S_0, 耗时: 11.621s
[2025-07-25 12:32:05] 自旋算符进度: 2/100, 当前算符: S_0 · S_1, 耗时: 12.103s
[2025-07-25 12:32:12] 自旋算符进度: 3/100, 当前算符: S_0 · S_2, 耗时: 6.299s
[2025-07-25 12:32:18] 自旋算符进度: 4/100, 当前算符: S_0 · S_3, 耗时: 6.314s
[2025-07-25 12:32:24] 自旋算符进度: 5/100, 当前算符: S_0 · S_4, 耗时: 6.351s
[2025-07-25 12:32:31] 自旋算符进度: 6/100, 当前算符: S_0 · S_5, 耗时: 6.314s
[2025-07-25 12:32:37] 自旋算符进度: 7/100, 当前算符: S_0 · S_6, 耗时: 6.314s
[2025-07-25 12:32:43] 自旋算符进度: 8/100, 当前算符: S_0 · S_7, 耗时: 6.284s
[2025-07-25 12:32:49] 自旋算符进度: 9/100, 当前算符: S_0 · S_8, 耗时: 6.314s
[2025-07-25 12:32:56] 自旋算符进度: 10/100, 当前算符: S_0 · S_9, 耗时: 6.283s
[2025-07-25 12:33:02] 自旋算符进度: 11/100, 当前算符: S_0 · S_10, 耗时: 6.309s
[2025-07-25 12:33:08] 自旋算符进度: 12/100, 当前算符: S_0 · S_11, 耗时: 6.284s
[2025-07-25 12:33:15] 自旋算符进度: 13/100, 当前算符: S_0 · S_12, 耗时: 6.313s
[2025-07-25 12:33:21] 自旋算符进度: 14/100, 当前算符: S_0 · S_13, 耗时: 6.309s
[2025-07-25 12:33:27] 自旋算符进度: 15/100, 当前算符: S_0 · S_14, 耗时: 6.312s
[2025-07-25 12:33:34] 自旋算符进度: 16/100, 当前算符: S_0 · S_15, 耗时: 6.283s
[2025-07-25 12:33:40] 自旋算符进度: 17/100, 当前算符: S_0 · S_16, 耗时: 6.313s
[2025-07-25 12:33:46] 自旋算符进度: 18/100, 当前算符: S_0 · S_17, 耗时: 6.284s
[2025-07-25 12:33:52] 自旋算符进度: 19/100, 当前算符: S_0 · S_18, 耗时: 6.304s
[2025-07-25 12:33:59] 自旋算符进度: 20/100, 当前算符: S_0 · S_19, 耗时: 6.313s
[2025-07-25 12:34:05] 自旋算符进度: 21/100, 当前算符: S_0 · S_20, 耗时: 6.283s
[2025-07-25 12:34:11] 自旋算符进度: 22/100, 当前算符: S_0 · S_21, 耗时: 6.283s
[2025-07-25 12:34:18] 自旋算符进度: 23/100, 当前算符: S_0 · S_22, 耗时: 6.313s
[2025-07-25 12:34:24] 自旋算符进度: 24/100, 当前算符: S_0 · S_23, 耗时: 6.302s
[2025-07-25 12:34:30] 自旋算符进度: 25/100, 当前算符: S_0 · S_24, 耗时: 6.312s
[2025-07-25 12:34:37] 自旋算符进度: 26/100, 当前算符: S_0 · S_25, 耗时: 6.313s
[2025-07-25 12:34:43] 自旋算符进度: 27/100, 当前算符: S_0 · S_26, 耗时: 6.283s
[2025-07-25 12:34:49] 自旋算符进度: 28/100, 当前算符: S_0 · S_27, 耗时: 6.312s
[2025-07-25 12:34:55] 自旋算符进度: 29/100, 当前算符: S_0 · S_28, 耗时: 6.283s
[2025-07-25 12:35:02] 自旋算符进度: 30/100, 当前算符: S_0 · S_29, 耗时: 6.302s
[2025-07-25 12:35:08] 自旋算符进度: 31/100, 当前算符: S_0 · S_30, 耗时: 6.312s
[2025-07-25 12:35:14] 自旋算符进度: 32/100, 当前算符: S_0 · S_31, 耗时: 6.313s
[2025-07-25 12:35:21] 自旋算符进度: 33/100, 当前算符: S_0 · S_32, 耗时: 6.282s
[2025-07-25 12:35:27] 自旋算符进度: 34/100, 当前算符: S_0 · S_33, 耗时: 6.281s
[2025-07-25 12:35:33] 自旋算符进度: 35/100, 当前算符: S_0 · S_34, 耗时: 6.308s
[2025-07-25 12:35:40] 自旋算符进度: 36/100, 当前算符: S_0 · S_35, 耗时: 6.281s
[2025-07-25 12:35:46] 自旋算符进度: 37/100, 当前算符: S_0 · S_36, 耗时: 6.313s
[2025-07-25 12:35:52] 自旋算符进度: 38/100, 当前算符: S_0 · S_37, 耗时: 6.312s
[2025-07-25 12:35:59] 自旋算符进度: 39/100, 当前算符: S_0 · S_38, 耗时: 6.304s
[2025-07-25 12:36:05] 自旋算符进度: 40/100, 当前算符: S_0 · S_39, 耗时: 6.312s
[2025-07-25 12:36:11] 自旋算符进度: 41/100, 当前算符: S_0 · S_40, 耗时: 6.305s
[2025-07-25 12:36:17] 自旋算符进度: 42/100, 当前算符: S_0 · S_41, 耗时: 6.304s
[2025-07-25 12:36:24] 自旋算符进度: 43/100, 当前算符: S_0 · S_42, 耗时: 6.305s
[2025-07-25 12:36:30] 自旋算符进度: 44/100, 当前算符: S_0 · S_43, 耗时: 6.305s
[2025-07-25 12:36:36] 自旋算符进度: 45/100, 当前算符: S_0 · S_44, 耗时: 6.312s
[2025-07-25 12:36:43] 自旋算符进度: 46/100, 当前算符: S_0 · S_45, 耗时: 6.312s
[2025-07-25 12:36:49] 自旋算符进度: 47/100, 当前算符: S_0 · S_46, 耗时: 6.303s
[2025-07-25 12:36:55] 自旋算符进度: 48/100, 当前算符: S_0 · S_47, 耗时: 6.310s
[2025-07-25 12:37:02] 自旋算符进度: 49/100, 当前算符: S_0 · S_48, 耗时: 6.304s
[2025-07-25 12:37:08] 自旋算符进度: 50/100, 当前算符: S_0 · S_49, 耗时: 6.282s
[2025-07-25 12:37:14] 自旋算符进度: 51/100, 当前算符: S_0 · S_50, 耗时: 6.313s
[2025-07-25 12:37:20] 自旋算符进度: 52/100, 当前算符: S_0 · S_51, 耗时: 6.282s
[2025-07-25 12:37:27] 自旋算符进度: 53/100, 当前算符: S_0 · S_52, 耗时: 6.304s
[2025-07-25 12:37:33] 自旋算符进度: 54/100, 当前算符: S_0 · S_53, 耗时: 6.302s
[2025-07-25 12:37:39] 自旋算符进度: 55/100, 当前算符: S_0 · S_54, 耗时: 6.312s
[2025-07-25 12:37:46] 自旋算符进度: 56/100, 当前算符: S_0 · S_55, 耗时: 6.283s
[2025-07-25 12:37:52] 自旋算符进度: 57/100, 当前算符: S_0 · S_56, 耗时: 6.310s
[2025-07-25 12:37:58] 自旋算符进度: 58/100, 当前算符: S_0 · S_57, 耗时: 6.311s
[2025-07-25 12:38:05] 自旋算符进度: 59/100, 当前算符: S_0 · S_58, 耗时: 6.303s
[2025-07-25 12:38:11] 自旋算符进度: 60/100, 当前算符: S_0 · S_59, 耗时: 6.311s
[2025-07-25 12:38:17] 自旋算符进度: 61/100, 当前算符: S_0 · S_60, 耗时: 6.282s
[2025-07-25 12:38:23] 自旋算符进度: 62/100, 当前算符: S_0 · S_61, 耗时: 6.282s
[2025-07-25 12:38:30] 自旋算符进度: 63/100, 当前算符: S_0 · S_62, 耗时: 6.312s
[2025-07-25 12:38:36] 自旋算符进度: 64/100, 当前算符: S_0 · S_63, 耗时: 6.303s
[2025-07-25 12:38:42] 自旋算符进度: 65/100, 当前算符: S_0 · S_64, 耗时: 6.312s
[2025-07-25 12:38:49] 自旋算符进度: 66/100, 当前算符: S_0 · S_65, 耗时: 6.312s
[2025-07-25 12:38:55] 自旋算符进度: 67/100, 当前算符: S_0 · S_66, 耗时: 6.282s
[2025-07-25 12:39:01] 自旋算符进度: 68/100, 当前算符: S_0 · S_67, 耗时: 6.312s
[2025-07-25 12:39:08] 自旋算符进度: 69/100, 当前算符: S_0 · S_68, 耗时: 6.304s
[2025-07-25 12:39:14] 自旋算符进度: 70/100, 当前算符: S_0 · S_69, 耗时: 6.305s
[2025-07-25 12:39:20] 自旋算符进度: 71/100, 当前算符: S_0 · S_70, 耗时: 6.312s
[2025-07-25 12:39:27] 自旋算符进度: 72/100, 当前算符: S_0 · S_71, 耗时: 6.282s
[2025-07-25 12:39:33] 自旋算符进度: 73/100, 当前算符: S_0 · S_72, 耗时: 6.312s
[2025-07-25 12:39:39] 自旋算符进度: 74/100, 当前算符: S_0 · S_73, 耗时: 6.312s
[2025-07-25 12:39:45] 自旋算符进度: 75/100, 当前算符: S_0 · S_74, 耗时: 6.302s
[2025-07-25 12:39:52] 自旋算符进度: 76/100, 当前算符: S_0 · S_75, 耗时: 6.355s
[2025-07-25 12:39:58] 自旋算符进度: 77/100, 当前算符: S_0 · S_76, 耗时: 6.283s
[2025-07-25 12:40:04] 自旋算符进度: 78/100, 当前算符: S_0 · S_77, 耗时: 6.282s
[2025-07-25 12:40:11] 自旋算符进度: 79/100, 当前算符: S_0 · S_78, 耗时: 6.312s
[2025-07-25 12:40:17] 自旋算符进度: 80/100, 当前算符: S_0 · S_79, 耗时: 6.305s
[2025-07-25 12:40:23] 自旋算符进度: 81/100, 当前算符: S_0 · S_80, 耗时: 6.312s
[2025-07-25 12:40:30] 自旋算符进度: 82/100, 当前算符: S_0 · S_81, 耗时: 6.313s
[2025-07-25 12:40:36] 自旋算符进度: 83/100, 当前算符: S_0 · S_82, 耗时: 6.282s
[2025-07-25 12:40:42] 自旋算符进度: 84/100, 当前算符: S_0 · S_83, 耗时: 6.313s
[2025-07-25 12:40:49] 自旋算符进度: 85/100, 当前算符: S_0 · S_84, 耗时: 6.312s
[2025-07-25 12:40:55] 自旋算符进度: 86/100, 当前算符: S_0 · S_85, 耗时: 6.313s
[2025-07-25 12:41:01] 自旋算符进度: 87/100, 当前算符: S_0 · S_86, 耗时: 6.305s
[2025-07-25 12:41:07] 自旋算符进度: 88/100, 当前算符: S_0 · S_87, 耗时: 6.312s
[2025-07-25 12:41:14] 自旋算符进度: 89/100, 当前算符: S_0 · S_88, 耗时: 6.282s
[2025-07-25 12:41:20] 自旋算符进度: 90/100, 当前算符: S_0 · S_89, 耗时: 6.304s
[2025-07-25 12:41:26] 自旋算符进度: 91/100, 当前算符: S_0 · S_90, 耗时: 6.312s
[2025-07-25 12:41:33] 自旋算符进度: 92/100, 当前算符: S_0 · S_91, 耗时: 6.304s
[2025-07-25 12:41:39] 自旋算符进度: 93/100, 当前算符: S_0 · S_92, 耗时: 6.312s
[2025-07-25 12:41:45] 自旋算符进度: 94/100, 当前算符: S_0 · S_93, 耗时: 6.313s
[2025-07-25 12:41:52] 自旋算符进度: 95/100, 当前算符: S_0 · S_94, 耗时: 6.283s
[2025-07-25 12:41:58] 自旋算符进度: 96/100, 当前算符: S_0 · S_95, 耗时: 6.313s
[2025-07-25 12:42:04] 自旋算符进度: 97/100, 当前算符: S_0 · S_96, 耗时: 6.305s
[2025-07-25 12:42:11] 自旋算符进度: 98/100, 当前算符: S_0 · S_97, 耗时: 6.315s
[2025-07-25 12:42:17] 自旋算符进度: 99/100, 当前算符: S_0 · S_98, 耗时: 6.310s
[2025-07-25 12:42:23] 自旋算符进度: 100/100, 当前算符: S_0 · S_99, 耗时: 6.282s
[2025-07-25 12:42:23] 自旋相关函数计算完成,总耗时 641.56 秒
[2025-07-25 12:42:23] 计算傅里叶变换...
[2025-07-25 12:42:24] 自旋结构因子计算完成
[2025-07-25 12:42:25] 自旋相关函数平均误差: 0.000652
[2025-07-25 12:42:25] ================================================================================
[2025-07-25 12:42:25] 开始计算对角二聚体结构因子...
[2025-07-25 12:42:25] 识别所有对角二聚体...
[2025-07-25 12:42:25] 总共找到 100 个西北-东南方向对角二聚体和 100 个西南-东北方向对角二聚体
[2025-07-25 12:42:25] 预计算对角二聚体操作符...
[2025-07-25 12:42:28] 开始计算西北-东南方向对角二聚体相关函数 (100 个算符)...
[2025-07-25 12:42:34] NW-SE对角算符进度: 1/100, 当前算符: D_(0, 18) * D_(0, 18), 耗时: 6.345s
[2025-07-25 12:42:50] NW-SE对角算符进度: 2/100, 当前算符: D_(0, 18) * D_(1, 39), 耗时: 16.242s
[2025-07-25 12:43:01] NW-SE对角算符进度: 3/100, 当前算符: D_(0, 18) * D_(2, 20), 耗时: 10.640s
[2025-07-25 12:43:11] NW-SE对角算符进度: 4/100, 当前算符: D_(0, 18) * D_(3, 1), 耗时: 10.576s
[2025-07-25 12:43:22] NW-SE对角算符进度: 5/100, 当前算符: D_(0, 18) * D_(4, 2), 耗时: 10.628s
[2025-07-25 12:43:33] NW-SE对角算符进度: 6/100, 当前算符: D_(0, 18) * D_(5, 23), 耗时: 10.617s
[2025-07-25 12:43:43] NW-SE对角算符进度: 7/100, 当前算符: D_(0, 18) * D_(6, 24), 耗时: 10.632s
[2025-07-25 12:43:54] NW-SE对角算符进度: 8/100, 当前算符: D_(0, 18) * D_(7, 5), 耗时: 10.578s
[2025-07-25 12:44:04] NW-SE对角算符进度: 9/100, 当前算符: D_(0, 18) * D_(8, 6), 耗时: 10.640s
[2025-07-25 12:44:15] NW-SE对角算符进度: 10/100, 当前算符: D_(0, 18) * D_(9, 27), 耗时: 10.580s
[2025-07-25 12:44:26] NW-SE对角算符进度: 11/100, 当前算符: D_(0, 18) * D_(10, 28), 耗时: 10.633s
[2025-07-25 12:44:36] NW-SE对角算符进度: 12/100, 当前算符: D_(0, 18) * D_(11, 9), 耗时: 10.639s
[2025-07-25 12:44:47] NW-SE对角算符进度: 13/100, 当前算符: D_(0, 18) * D_(12, 10), 耗时: 10.622s
[2025-07-25 12:44:58] NW-SE对角算符进度: 14/100, 当前算符: D_(0, 18) * D_(13, 31), 耗时: 10.640s
[2025-07-25 12:45:08] NW-SE对角算符进度: 15/100, 当前算符: D_(0, 18) * D_(14, 32), 耗时: 10.579s
[2025-07-25 12:45:19] NW-SE对角算符进度: 16/100, 当前算符: D_(0, 18) * D_(15, 13), 耗时: 10.642s
[2025-07-25 12:45:29] NW-SE对角算符进度: 17/100, 当前算符: D_(0, 18) * D_(16, 14), 耗时: 10.579s
[2025-07-25 12:45:40] NW-SE对角算符进度: 18/100, 当前算符: D_(0, 18) * D_(17, 35), 耗时: 10.629s
[2025-07-25 12:45:59] NW-SE对角算符进度: 19/100, 当前算符: D_(0, 18) * D_(18, 36), 耗时: 19.075s
[2025-07-25 12:46:10] NW-SE对角算符进度: 20/100, 当前算符: D_(0, 18) * D_(19, 17), 耗时: 10.578s
[2025-07-25 12:46:20] NW-SE对角算符进度: 21/100, 当前算符: D_(0, 18) * D_(20, 38), 耗时: 10.619s
[2025-07-25 12:46:31] NW-SE对角算符进度: 22/100, 当前算符: D_(0, 18) * D_(21, 59), 耗时: 10.560s
[2025-07-25 12:46:41] NW-SE对角算符进度: 23/100, 当前算符: D_(0, 18) * D_(22, 40), 耗时: 10.613s
[2025-07-25 12:46:52] NW-SE对角算符进度: 24/100, 当前算符: D_(0, 18) * D_(23, 21), 耗时: 10.579s
[2025-07-25 12:47:03] NW-SE对角算符进度: 25/100, 当前算符: D_(0, 18) * D_(24, 22), 耗时: 10.617s
[2025-07-25 12:47:13] NW-SE对角算符进度: 26/100, 当前算符: D_(0, 18) * D_(25, 43), 耗时: 10.609s
[2025-07-25 12:47:24] NW-SE对角算符进度: 27/100, 当前算符: D_(0, 18) * D_(26, 44), 耗时: 10.554s
[2025-07-25 12:47:34] NW-SE对角算符进度: 28/100, 当前算符: D_(0, 18) * D_(27, 25), 耗时: 10.616s
[2025-07-25 12:47:45] NW-SE对角算符进度: 29/100, 当前算符: D_(0, 18) * D_(28, 26), 耗时: 10.578s
[2025-07-25 12:47:56] NW-SE对角算符进度: 30/100, 当前算符: D_(0, 18) * D_(29, 47), 耗时: 10.617s
[2025-07-25 12:48:06] NW-SE对角算符进度: 31/100, 当前算符: D_(0, 18) * D_(30, 48), 耗时: 10.579s
[2025-07-25 12:48:17] NW-SE对角算符进度: 32/100, 当前算符: D_(0, 18) * D_(31, 29), 耗时: 10.614s
[2025-07-25 12:48:27] NW-SE对角算符进度: 33/100, 当前算符: D_(0, 18) * D_(32, 30), 耗时: 10.563s
[2025-07-25 12:48:38] NW-SE对角算符进度: 34/100, 当前算符: D_(0, 18) * D_(33, 51), 耗时: 10.563s
[2025-07-25 12:48:49] NW-SE对角算符进度: 35/100, 当前算符: D_(0, 18) * D_(34, 52), 耗时: 10.617s
[2025-07-25 12:48:59] NW-SE对角算符进度: 36/100, 当前算符: D_(0, 18) * D_(35, 33), 耗时: 10.580s
[2025-07-25 12:49:10] NW-SE对角算符进度: 37/100, 当前算符: D_(0, 18) * D_(36, 34), 耗时: 10.614s
[2025-07-25 12:49:20] NW-SE对角算符进度: 38/100, 当前算符: D_(0, 18) * D_(37, 55), 耗时: 10.557s
[2025-07-25 12:49:31] NW-SE对角算符进度: 39/100, 当前算符: D_(0, 18) * D_(38, 56), 耗时: 10.610s
[2025-07-25 12:49:42] NW-SE对角算符进度: 40/100, 当前算符: D_(0, 18) * D_(39, 37), 耗时: 10.586s
[2025-07-25 12:49:52] NW-SE对角算符进度: 41/100, 当前算符: D_(0, 18) * D_(40, 58), 耗时: 10.711s
[2025-07-25 12:50:03] NW-SE对角算符进度: 42/100, 当前算符: D_(0, 18) * D_(41, 79), 耗时: 10.579s
[2025-07-25 12:50:13] NW-SE对角算符进度: 43/100, 当前算符: D_(0, 18) * D_(42, 60), 耗时: 10.578s
[2025-07-25 12:50:24] NW-SE对角算符进度: 44/100, 当前算符: D_(0, 18) * D_(43, 41), 耗时: 10.619s
[2025-07-25 12:50:35] NW-SE对角算符进度: 45/100, 当前算符: D_(0, 18) * D_(44, 42), 耗时: 10.565s
[2025-07-25 12:50:45] NW-SE对角算符进度: 46/100, 当前算符: D_(0, 18) * D_(45, 63), 耗时: 10.617s
[2025-07-25 12:50:56] NW-SE对角算符进度: 47/100, 当前算符: D_(0, 18) * D_(46, 64), 耗时: 10.580s
[2025-07-25 12:51:06] NW-SE对角算符进度: 48/100, 当前算符: D_(0, 18) * D_(47, 45), 耗时: 10.617s
[2025-07-25 12:51:17] NW-SE对角算符进度: 49/100, 当前算符: D_(0, 18) * D_(48, 46), 耗时: 10.554s
[2025-07-25 12:51:27] NW-SE对角算符进度: 50/100, 当前算符: D_(0, 18) * D_(49, 67), 耗时: 10.557s
[2025-07-25 12:51:38] NW-SE对角算符进度: 51/100, 当前算符: D_(0, 18) * D_(50, 68), 耗时: 10.615s
[2025-07-25 12:51:49] NW-SE对角算符进度: 52/100, 当前算符: D_(0, 18) * D_(51, 49), 耗时: 10.579s
[2025-07-25 12:51:59] NW-SE对角算符进度: 53/100, 当前算符: D_(0, 18) * D_(52, 50), 耗时: 10.612s
[2025-07-25 12:52:10] NW-SE对角算符进度: 54/100, 当前算符: D_(0, 18) * D_(53, 71), 耗时: 10.578s
[2025-07-25 12:52:21] NW-SE对角算符进度: 55/100, 当前算符: D_(0, 18) * D_(54, 72), 耗时: 10.615s
[2025-07-25 12:52:31] NW-SE对角算符进度: 56/100, 当前算符: D_(0, 18) * D_(55, 53), 耗时: 10.561s
[2025-07-25 12:52:42] NW-SE对角算符进度: 57/100, 当前算符: D_(0, 18) * D_(56, 54), 耗时: 10.615s
[2025-07-25 12:52:52] NW-SE对角算符进度: 58/100, 当前算符: D_(0, 18) * D_(57, 75), 耗时: 10.586s
[2025-07-25 12:53:03] NW-SE对角算符进度: 59/100, 当前算符: D_(0, 18) * D_(58, 76), 耗时: 10.616s
[2025-07-25 12:53:13] NW-SE对角算符进度: 60/100, 当前算符: D_(0, 18) * D_(59, 57), 耗时: 10.556s
[2025-07-25 12:53:24] NW-SE对角算符进度: 61/100, 当前算符: D_(0, 18) * D_(60, 78), 耗时: 10.615s
[2025-07-25 12:53:35] NW-SE对角算符进度: 62/100, 当前算符: D_(0, 18) * D_(61, 99), 耗时: 10.585s
[2025-07-25 12:53:45] NW-SE对角算符进度: 63/100, 当前算符: D_(0, 18) * D_(62, 80), 耗时: 10.616s
[2025-07-25 12:53:56] NW-SE对角算符进度: 64/100, 当前算符: D_(0, 18) * D_(63, 61), 耗时: 10.585s
[2025-07-25 12:54:06] NW-SE对角算符进度: 65/100, 当前算符: D_(0, 18) * D_(64, 62), 耗时: 10.620s
[2025-07-25 12:54:17] NW-SE对角算符进度: 66/100, 当前算符: D_(0, 18) * D_(65, 83), 耗时: 10.616s
[2025-07-25 12:54:28] NW-SE对角算符进度: 67/100, 当前算符: D_(0, 18) * D_(66, 84), 耗时: 10.554s
[2025-07-25 12:54:38] NW-SE对角算符进度: 68/100, 当前算符: D_(0, 18) * D_(67, 65), 耗时: 10.616s
[2025-07-25 12:54:49] NW-SE对角算符进度: 69/100, 当前算符: D_(0, 18) * D_(68, 66), 耗时: 10.616s
[2025-07-25 12:54:59] NW-SE对角算符进度: 70/100, 当前算符: D_(0, 18) * D_(69, 87), 耗时: 10.558s
[2025-07-25 12:55:10] NW-SE对角算符进度: 71/100, 当前算符: D_(0, 18) * D_(70, 88), 耗时: 10.615s
[2025-07-25 12:55:21] NW-SE对角算符进度: 72/100, 当前算符: D_(0, 18) * D_(71, 69), 耗时: 10.555s
[2025-07-25 12:55:31] NW-SE对角算符进度: 73/100, 当前算符: D_(0, 18) * D_(72, 70), 耗时: 10.616s
[2025-07-25 12:55:42] NW-SE对角算符进度: 74/100, 当前算符: D_(0, 18) * D_(73, 91), 耗时: 10.581s
[2025-07-25 12:55:52] NW-SE对角算符进度: 75/100, 当前算符: D_(0, 18) * D_(74, 92), 耗时: 10.616s
[2025-07-25 12:56:03] NW-SE对角算符进度: 76/100, 当前算符: D_(0, 18) * D_(75, 73), 耗时: 10.552s
[2025-07-25 12:56:14] NW-SE对角算符进度: 77/100, 当前算符: D_(0, 18) * D_(76, 74), 耗时: 10.616s
[2025-07-25 12:56:24] NW-SE对角算符进度: 78/100, 当前算符: D_(0, 18) * D_(77, 95), 耗时: 10.672s
[2025-07-25 12:56:35] NW-SE对角算符进度: 79/100, 当前算符: D_(0, 18) * D_(78, 96), 耗时: 10.613s
[2025-07-25 12:56:45] NW-SE对角算符进度: 80/100, 当前算符: D_(0, 18) * D_(79, 77), 耗时: 10.577s
[2025-07-25 12:56:56] NW-SE对角算符进度: 81/100, 当前算符: D_(0, 18) * D_(80, 98), 耗时: 10.614s
[2025-07-25 12:57:07] NW-SE对角算符进度: 82/100, 当前算符: D_(0, 18) * D_(81, 19), 耗时: 10.613s
[2025-07-25 12:57:16] NW-SE对角算符进度: 83/100, 当前算符: D_(0, 18) * D_(82, 0), 耗时: 8.822s
[2025-07-25 12:57:26] NW-SE对角算符进度: 84/100, 当前算符: D_(0, 18) * D_(83, 81), 耗时: 10.605s
[2025-07-25 12:57:37] NW-SE对角算符进度: 85/100, 当前算符: D_(0, 18) * D_(84, 82), 耗时: 10.590s
[2025-07-25 12:57:47] NW-SE对角算符进度: 86/100, 当前算符: D_(0, 18) * D_(85, 3), 耗时: 10.590s
[2025-07-25 12:57:58] NW-SE对角算符进度: 87/100, 当前算符: D_(0, 18) * D_(86, 4), 耗时: 10.618s
[2025-07-25 12:58:08] NW-SE对角算符进度: 88/100, 当前算符: D_(0, 18) * D_(87, 85), 耗时: 10.560s
[2025-07-25 12:58:19] NW-SE对角算符进度: 89/100, 当前算符: D_(0, 18) * D_(88, 86), 耗时: 10.620s
[2025-07-25 12:58:30] NW-SE对角算符进度: 90/100, 当前算符: D_(0, 18) * D_(89, 7), 耗时: 10.561s
[2025-07-25 12:58:40] NW-SE对角算符进度: 91/100, 当前算符: D_(0, 18) * D_(90, 8), 耗时: 10.618s
[2025-07-25 12:58:51] NW-SE对角算符进度: 92/100, 当前算符: D_(0, 18) * D_(91, 89), 耗时: 10.590s
[2025-07-25 12:59:01] NW-SE对角算符进度: 93/100, 当前算符: D_(0, 18) * D_(92, 90), 耗时: 10.620s
[2025-07-25 12:59:12] NW-SE对角算符进度: 94/100, 当前算符: D_(0, 18) * D_(93, 11), 耗时: 10.590s
[2025-07-25 12:59:23] NW-SE对角算符进度: 95/100, 当前算符: D_(0, 18) * D_(94, 12), 耗时: 10.557s
[2025-07-25 12:59:33] NW-SE对角算符进度: 96/100, 当前算符: D_(0, 18) * D_(95, 93), 耗时: 10.619s
[2025-07-25 12:59:44] NW-SE对角算符进度: 97/100, 当前算符: D_(0, 18) * D_(96, 94), 耗时: 10.558s
[2025-07-25 12:59:54] NW-SE对角算符进度: 98/100, 当前算符: D_(0, 18) * D_(97, 15), 耗时: 10.618s
[2025-07-25 13:00:05] NW-SE对角算符进度: 99/100, 当前算符: D_(0, 18) * D_(98, 16), 耗时: 10.590s
[2025-07-25 13:00:16] NW-SE对角算符进度: 100/100, 当前算符: D_(0, 18) * D_(99, 97), 耗时: 10.619s
[2025-07-25 13:00:16] 西北-东南方向对角二聚体相关函数计算完成,耗时: 1068.10 秒
[2025-07-25 13:00:16] ================================================================================
[2025-07-25 13:00:16] 开始计算西南-东北方向对角二聚体相关函数 (100 个算符)...
[2025-07-25 13:00:22] SW-NE对角算符进度: 1/100, 当前算符: D_(0, 2) * D_(0, 2), 耗时: 6.298s
[2025-07-25 13:00:33] SW-NE对角算符进度: 2/100, 当前算符: D_(0, 2) * D_(1, 23), 耗时: 10.626s
[2025-07-25 13:00:41] SW-NE对角算符进度: 3/100, 当前算符: D_(0, 2) * D_(2, 24), 耗时: 8.826s
[2025-07-25 13:00:52] SW-NE对角算符进度: 4/100, 当前算符: D_(0, 2) * D_(3, 5), 耗时: 10.558s
[2025-07-25 13:01:03] SW-NE对角算符进度: 5/100, 当前算符: D_(0, 2) * D_(4, 6), 耗时: 10.558s
[2025-07-25 13:01:13] SW-NE对角算符进度: 6/100, 当前算符: D_(0, 2) * D_(5, 27), 耗时: 10.621s
[2025-07-25 13:01:24] SW-NE对角算符进度: 7/100, 当前算符: D_(0, 2) * D_(6, 28), 耗时: 10.595s
[2025-07-25 13:01:34] SW-NE对角算符进度: 8/100, 当前算符: D_(0, 2) * D_(7, 9), 耗时: 10.618s
[2025-07-25 13:01:45] SW-NE对角算符进度: 9/100, 当前算符: D_(0, 2) * D_(8, 10), 耗时: 10.558s
[2025-07-25 13:01:56] SW-NE对角算符进度: 10/100, 当前算符: D_(0, 2) * D_(9, 31), 耗时: 10.618s
[2025-07-25 13:02:06] SW-NE对角算符进度: 11/100, 当前算符: D_(0, 2) * D_(10, 32), 耗时: 10.557s
[2025-07-25 13:02:17] SW-NE对角算符进度: 12/100, 当前算符: D_(0, 2) * D_(11, 13), 耗时: 10.606s
[2025-07-25 13:02:27] SW-NE对角算符进度: 13/100, 当前算符: D_(0, 2) * D_(12, 14), 耗时: 10.558s
[2025-07-25 13:02:38] SW-NE对角算符进度: 14/100, 当前算符: D_(0, 2) * D_(13, 35), 耗时: 10.623s
[2025-07-25 13:02:48] SW-NE对角算符进度: 15/100, 当前算符: D_(0, 2) * D_(14, 36), 耗时: 10.588s
[2025-07-25 13:02:59] SW-NE对角算符进度: 16/100, 当前算符: D_(0, 2) * D_(15, 17), 耗时: 10.625s
[2025-07-25 13:03:10] SW-NE对角算符进度: 17/100, 当前算符: D_(0, 2) * D_(16, 18), 耗时: 10.555s
[2025-07-25 13:03:20] SW-NE对角算符进度: 18/100, 当前算符: D_(0, 2) * D_(17, 39), 耗时: 10.619s
[2025-07-25 13:03:31] SW-NE对角算符进度: 19/100, 当前算符: D_(0, 2) * D_(18, 20), 耗时: 10.591s
[2025-07-25 13:03:41] SW-NE对角算符进度: 20/100, 当前算符: D_(0, 2) * D_(19, 1), 耗时: 10.589s
[2025-07-25 13:03:52] SW-NE对角算符进度: 21/100, 当前算符: D_(0, 2) * D_(20, 22), 耗时: 10.624s
[2025-07-25 13:04:03] SW-NE对角算符进度: 22/100, 当前算符: D_(0, 2) * D_(21, 43), 耗时: 10.560s
[2025-07-25 13:04:13] SW-NE对角算符进度: 23/100, 当前算符: D_(0, 2) * D_(22, 44), 耗时: 10.619s
[2025-07-25 13:04:24] SW-NE对角算符进度: 24/100, 当前算符: D_(0, 2) * D_(23, 25), 耗时: 10.593s
[2025-07-25 13:04:34] SW-NE对角算符进度: 25/100, 当前算符: D_(0, 2) * D_(24, 26), 耗时: 10.607s
[2025-07-25 13:04:45] SW-NE对角算符进度: 26/100, 当前算符: D_(0, 2) * D_(25, 47), 耗时: 10.583s
[2025-07-25 13:04:56] SW-NE对角算符进度: 27/100, 当前算符: D_(0, 2) * D_(26, 48), 耗时: 10.618s
[2025-07-25 13:05:06] SW-NE对角算符进度: 28/100, 当前算符: D_(0, 2) * D_(27, 29), 耗时: 10.585s
[2025-07-25 13:05:17] SW-NE对角算符进度: 29/100, 当前算符: D_(0, 2) * D_(28, 30), 耗时: 10.619s
[2025-07-25 13:05:27] SW-NE对角算符进度: 30/100, 当前算符: D_(0, 2) * D_(29, 51), 耗时: 10.557s
[2025-07-25 13:05:38] SW-NE对角算符进度: 31/100, 当前算符: D_(0, 2) * D_(30, 52), 耗时: 10.624s
[2025-07-25 13:05:49] SW-NE对角算符进度: 32/100, 当前算符: D_(0, 2) * D_(31, 33), 耗时: 10.586s
[2025-07-25 13:05:59] SW-NE对角算符进度: 33/100, 当前算符: D_(0, 2) * D_(32, 34), 耗时: 10.586s
[2025-07-25 13:06:10] SW-NE对角算符进度: 34/100, 当前算符: D_(0, 2) * D_(33, 55), 耗时: 10.585s
[2025-07-25 13:06:20] SW-NE对角算符进度: 35/100, 当前算符: D_(0, 2) * D_(34, 56), 耗时: 10.620s
[2025-07-25 13:06:31] SW-NE对角算符进度: 36/100, 当前算符: D_(0, 2) * D_(35, 37), 耗时: 10.556s
[2025-07-25 13:06:42] SW-NE对角算符进度: 37/100, 当前算符: D_(0, 2) * D_(36, 38), 耗时: 10.612s
[2025-07-25 13:06:52] SW-NE对角算符进度: 38/100, 当前算符: D_(0, 2) * D_(37, 59), 耗时: 10.621s
[2025-07-25 13:07:03] SW-NE对角算符进度: 39/100, 当前算符: D_(0, 2) * D_(38, 40), 耗时: 10.607s
[2025-07-25 13:07:13] SW-NE对角算符进度: 40/100, 当前算符: D_(0, 2) * D_(39, 21), 耗时: 10.556s
[2025-07-25 13:07:24] SW-NE对角算符进度: 41/100, 当前算符: D_(0, 2) * D_(40, 42), 耗时: 10.625s
[2025-07-25 13:07:35] SW-NE对角算符进度: 42/100, 当前算符: D_(0, 2) * D_(41, 63), 耗时: 10.557s
[2025-07-25 13:07:45] SW-NE对角算符进度: 43/100, 当前算符: D_(0, 2) * D_(42, 64), 耗时: 10.620s
[2025-07-25 13:07:56] SW-NE对角算符进度: 44/100, 当前算符: D_(0, 2) * D_(43, 45), 耗时: 10.587s
[2025-07-25 13:08:06] SW-NE对角算符进度: 45/100, 当前算符: D_(0, 2) * D_(44, 46), 耗时: 10.621s
[2025-07-25 13:08:17] SW-NE对角算符进度: 46/100, 当前算符: D_(0, 2) * D_(45, 67), 耗时: 10.557s
[2025-07-25 13:08:28] SW-NE对角算符进度: 47/100, 当前算符: D_(0, 2) * D_(46, 68), 耗时: 10.619s
[2025-07-25 13:08:38] SW-NE对角算符进度: 48/100, 当前算符: D_(0, 2) * D_(47, 49), 耗时: 10.561s
[2025-07-25 13:08:49] SW-NE对角算符进度: 49/100, 当前算符: D_(0, 2) * D_(48, 50), 耗时: 10.557s
[2025-07-25 13:08:59] SW-NE对角算符进度: 50/100, 当前算符: D_(0, 2) * D_(49, 71), 耗时: 10.618s
[2025-07-25 13:09:10] SW-NE对角算符进度: 51/100, 当前算符: D_(0, 2) * D_(50, 72), 耗时: 10.624s
[2025-07-25 13:09:21] SW-NE对角算符进度: 52/100, 当前算符: D_(0, 2) * D_(51, 53), 耗时: 10.584s
[2025-07-25 13:09:31] SW-NE对角算符进度: 53/100, 当前算符: D_(0, 2) * D_(52, 54), 耗时: 10.619s
[2025-07-25 13:09:42] SW-NE对角算符进度: 54/100, 当前算符: D_(0, 2) * D_(53, 75), 耗时: 10.558s
[2025-07-25 13:09:52] SW-NE对角算符进度: 55/100, 当前算符: D_(0, 2) * D_(54, 76), 耗时: 10.619s
[2025-07-25 13:10:03] SW-NE对角算符进度: 56/100, 当前算符: D_(0, 2) * D_(55, 57), 耗时: 10.584s
[2025-07-25 13:10:14] SW-NE对角算符进度: 57/100, 当前算符: D_(0, 2) * D_(56, 58), 耗时: 10.621s
[2025-07-25 13:10:24] SW-NE对角算符进度: 58/100, 当前算符: D_(0, 2) * D_(57, 79), 耗时: 10.584s
[2025-07-25 13:10:35] SW-NE对角算符进度: 59/100, 当前算符: D_(0, 2) * D_(58, 60), 耗时: 10.621s
[2025-07-25 13:10:45] SW-NE对角算符进度: 60/100, 当前算符: D_(0, 2) * D_(59, 41), 耗时: 10.556s
[2025-07-25 13:10:56] SW-NE对角算符进度: 61/100, 当前算符: D_(0, 2) * D_(60, 62), 耗时: 10.624s
[2025-07-25 13:11:06] SW-NE对角算符进度: 62/100, 当前算符: D_(0, 2) * D_(61, 83), 耗时: 10.584s
[2025-07-25 13:11:17] SW-NE对角算符进度: 63/100, 当前算符: D_(0, 2) * D_(62, 84), 耗时: 10.623s
[2025-07-25 13:11:28] SW-NE对角算符进度: 64/100, 当前算符: D_(0, 2) * D_(63, 65), 耗时: 10.557s
[2025-07-25 13:11:38] SW-NE对角算符进度: 65/100, 当前算符: D_(0, 2) * D_(64, 66), 耗时: 10.558s
[2025-07-25 13:11:49] SW-NE对角算符进度: 66/100, 当前算符: D_(0, 2) * D_(65, 87), 耗时: 10.624s
[2025-07-25 13:11:59] SW-NE对角算符进度: 67/100, 当前算符: D_(0, 2) * D_(66, 88), 耗时: 10.585s
[2025-07-25 13:12:10] SW-NE对角算符进度: 68/100, 当前算符: D_(0, 2) * D_(67, 69), 耗时: 10.583s
[2025-07-25 13:12:21] SW-NE对角算符进度: 69/100, 当前算符: D_(0, 2) * D_(68, 70), 耗时: 10.625s
[2025-07-25 13:12:31] SW-NE对角算符进度: 70/100, 当前算符: D_(0, 2) * D_(69, 91), 耗时: 10.559s
[2025-07-25 13:12:42] SW-NE对角算符进度: 71/100, 当前算符: D_(0, 2) * D_(70, 92), 耗时: 10.619s
[2025-07-25 13:12:52] SW-NE对角算符进度: 72/100, 当前算符: D_(0, 2) * D_(71, 73), 耗时: 10.590s
[2025-07-25 13:13:03] SW-NE对角算符进度: 73/100, 当前算符: D_(0, 2) * D_(72, 74), 耗时: 10.625s
[2025-07-25 13:13:14] SW-NE对角算符进度: 74/100, 当前算符: D_(0, 2) * D_(73, 95), 耗时: 10.585s
[2025-07-25 13:13:24] SW-NE对角算符进度: 75/100, 当前算符: D_(0, 2) * D_(74, 96), 耗时: 10.618s
[2025-07-25 13:13:35] SW-NE对角算符进度: 76/100, 当前算符: D_(0, 2) * D_(75, 77), 耗时: 10.557s
[2025-07-25 13:13:45] SW-NE对角算符进度: 77/100, 当前算符: D_(0, 2) * D_(76, 78), 耗时: 10.619s
[2025-07-25 13:13:56] SW-NE对角算符进度: 78/100, 当前算符: D_(0, 2) * D_(77, 99), 耗时: 10.587s
[2025-07-25 13:14:07] SW-NE对角算符进度: 79/100, 当前算符: D_(0, 2) * D_(78, 80), 耗时: 10.612s
[2025-07-25 13:14:17] SW-NE对角算符进度: 80/100, 当前算符: D_(0, 2) * D_(79, 61), 耗时: 10.557s
[2025-07-25 13:14:28] SW-NE对角算符进度: 81/100, 当前算符: D_(0, 2) * D_(80, 82), 耗时: 10.556s
[2025-07-25 13:14:38] SW-NE对角算符进度: 82/100, 当前算符: D_(0, 2) * D_(81, 3), 耗时: 10.619s
[2025-07-25 13:14:49] SW-NE对角算符进度: 83/100, 当前算符: D_(0, 2) * D_(82, 4), 耗时: 10.587s
[2025-07-25 13:15:00] SW-NE对角算符进度: 84/100, 当前算符: D_(0, 2) * D_(83, 85), 耗时: 10.618s
[2025-07-25 13:15:10] SW-NE对角算符进度: 85/100, 当前算符: D_(0, 2) * D_(84, 86), 耗时: 10.557s
[2025-07-25 13:15:21] SW-NE对角算符进度: 86/100, 当前算符: D_(0, 2) * D_(85, 7), 耗时: 10.623s
[2025-07-25 13:15:31] SW-NE对角算符进度: 87/100, 当前算符: D_(0, 2) * D_(86, 8), 耗时: 10.557s
[2025-07-25 13:15:42] SW-NE对角算符进度: 88/100, 当前算符: D_(0, 2) * D_(87, 89), 耗时: 10.620s
[2025-07-25 13:15:53] SW-NE对角算符进度: 89/100, 当前算符: D_(0, 2) * D_(88, 90), 耗时: 10.582s
[2025-07-25 13:16:03] SW-NE对角算符进度: 90/100, 当前算符: D_(0, 2) * D_(89, 11), 耗时: 10.619s
[2025-07-25 13:16:14] SW-NE对角算符进度: 91/100, 当前算符: D_(0, 2) * D_(90, 12), 耗时: 10.589s
[2025-07-25 13:16:24] SW-NE对角算符进度: 92/100, 当前算符: D_(0, 2) * D_(91, 93), 耗时: 10.613s
[2025-07-25 13:16:35] SW-NE对角算符进度: 93/100, 当前算符: D_(0, 2) * D_(92, 94), 耗时: 10.557s
[2025-07-25 13:16:46] SW-NE对角算符进度: 94/100, 当前算符: D_(0, 2) * D_(93, 15), 耗时: 10.623s
[2025-07-25 13:16:56] SW-NE对角算符进度: 95/100, 当前算符: D_(0, 2) * D_(94, 16), 耗时: 10.558s
[2025-07-25 13:17:07] SW-NE对角算符进度: 96/100, 当前算符: D_(0, 2) * D_(95, 97), 耗时: 10.623s
[2025-07-25 13:17:17] SW-NE对角算符进度: 97/100, 当前算符: D_(0, 2) * D_(96, 98), 耗时: 10.620s
[2025-07-25 13:17:28] SW-NE对角算符进度: 98/100, 当前算符: D_(0, 2) * D_(97, 19), 耗时: 10.585s
[2025-07-25 13:17:37] SW-NE对角算符进度: 99/100, 当前算符: D_(0, 2) * D_(98, 0), 耗时: 8.824s
[2025-07-25 13:17:47] SW-NE对角算符进度: 100/100, 当前算符: D_(0, 2) * D_(99, 81), 耗时: 10.566s
[2025-07-25 13:17:47] 西南-东北方向对角二聚体相关函数计算完成,耗时: 1051.67 秒
[2025-07-25 13:17:47] 计算傅里叶变换...
[2025-07-25 13:17:48] 对角二聚体结构因子计算完成
[2025-07-25 13:17:49] 对角二聚体相关函数平均误差: 0.000137
