[2025-07-25 11:38:35] ================================================================================
[2025-07-25 11:38:35] 加载量子态: L=5, J2=1.00, J1=0.83
[2025-07-25 11:38:35] 设置样本数为: 1048576
[2025-07-25 11:38:35] 开始生成共享样本集...
[2025-07-25 11:41:55] 样本生成完成,耗时: 200.194 秒
[2025-07-25 11:41:55] ================================================================================
[2025-07-25 11:41:55] 开始计算自旋结构因子...
[2025-07-25 11:41:55] 初始化操作符缓存...
[2025-07-25 11:41:55] 预构建所有自旋相关操作符...
[2025-07-25 11:41:56] 开始计算自旋相关函数...
[2025-07-25 11:42:06] 自旋算符进度: 1/100, 当前算符: S_0 · S_0, 耗时: 10.762s
[2025-07-25 11:42:18] 自旋算符进度: 2/100, 当前算符: S_0 · S_1, 耗时: 11.340s
[2025-07-25 11:42:24] 自旋算符进度: 3/100, 当前算符: S_0 · S_2, 耗时: 6.302s
[2025-07-25 11:42:30] 自旋算符进度: 4/100, 当前算符: S_0 · S_3, 耗时: 6.300s
[2025-07-25 11:42:37] 自旋算符进度: 5/100, 当前算符: S_0 · S_4, 耗时: 6.272s
[2025-07-25 11:42:43] 自旋算符进度: 6/100, 当前算符: S_0 · S_5, 耗时: 6.272s
[2025-07-25 11:42:49] 自旋算符进度: 7/100, 当前算符: S_0 · S_6, 耗时: 6.301s
[2025-07-25 11:42:55] 自旋算符进度: 8/100, 当前算符: S_0 · S_7, 耗时: 6.273s
[2025-07-25 11:43:02] 自旋算符进度: 9/100, 当前算符: S_0 · S_8, 耗时: 6.273s
[2025-07-25 11:43:08] 自旋算符进度: 10/100, 当前算符: S_0 · S_9, 耗时: 6.272s
[2025-07-25 11:43:14] 自旋算符进度: 11/100, 当前算符: S_0 · S_10, 耗时: 6.270s
[2025-07-25 11:43:21] 自旋算符进度: 12/100, 当前算符: S_0 · S_11, 耗时: 6.271s
[2025-07-25 11:43:27] 自旋算符进度: 13/100, 当前算符: S_0 · S_12, 耗时: 6.300s
[2025-07-25 11:43:33] 自旋算符进度: 14/100, 当前算符: S_0 · S_13, 耗时: 6.300s
[2025-07-25 11:43:39] 自旋算符进度: 15/100, 当前算符: S_0 · S_14, 耗时: 6.300s
[2025-07-25 11:43:46] 自旋算符进度: 16/100, 当前算符: S_0 · S_15, 耗时: 6.271s
[2025-07-25 11:43:52] 自旋算符进度: 17/100, 当前算符: S_0 · S_16, 耗时: 6.276s
[2025-07-25 11:43:58] 自旋算符进度: 18/100, 当前算符: S_0 · S_17, 耗时: 6.272s
[2025-07-25 11:44:05] 自旋算符进度: 19/100, 当前算符: S_0 · S_18, 耗时: 6.305s
[2025-07-25 11:44:11] 自旋算符进度: 20/100, 当前算符: S_0 · S_19, 耗时: 6.302s
[2025-07-25 11:44:17] 自旋算符进度: 21/100, 当前算符: S_0 · S_20, 耗时: 6.271s
[2025-07-25 11:44:23] 自旋算符进度: 22/100, 当前算符: S_0 · S_21, 耗时: 6.276s
[2025-07-25 11:44:30] 自旋算符进度: 23/100, 当前算符: S_0 · S_22, 耗时: 6.276s
[2025-07-25 11:44:36] 自旋算符进度: 24/100, 当前算符: S_0 · S_23, 耗时: 6.307s
[2025-07-25 11:44:42] 自旋算符进度: 25/100, 当前算符: S_0 · S_24, 耗时: 6.305s
[2025-07-25 11:44:49] 自旋算符进度: 26/100, 当前算符: S_0 · S_25, 耗时: 6.304s
[2025-07-25 11:44:55] 自旋算符进度: 27/100, 当前算符: S_0 · S_26, 耗时: 6.272s
[2025-07-25 11:45:01] 自旋算符进度: 28/100, 当前算符: S_0 · S_27, 耗时: 6.273s
[2025-07-25 11:45:07] 自旋算符进度: 29/100, 当前算符: S_0 · S_28, 耗时: 6.271s
[2025-07-25 11:45:14] 自旋算符进度: 30/100, 当前算符: S_0 · S_29, 耗时: 6.301s
[2025-07-25 11:45:20] 自旋算符进度: 31/100, 当前算符: S_0 · S_30, 耗时: 6.300s
[2025-07-25 11:45:26] 自旋算符进度: 32/100, 当前算符: S_0 · S_31, 耗时: 6.301s
[2025-07-25 11:45:33] 自旋算符进度: 33/100, 当前算符: S_0 · S_32, 耗时: 6.273s
[2025-07-25 11:45:39] 自旋算符进度: 34/100, 当前算符: S_0 · S_33, 耗时: 6.273s
[2025-07-25 11:45:45] 自旋算符进度: 35/100, 当前算符: S_0 · S_34, 耗时: 6.275s
[2025-07-25 11:45:51] 自旋算符进度: 36/100, 当前算符: S_0 · S_35, 耗时: 6.274s
[2025-07-25 11:45:58] 自旋算符进度: 37/100, 当前算符: S_0 · S_36, 耗时: 6.277s
[2025-07-25 11:46:04] 自旋算符进度: 38/100, 当前算符: S_0 · S_37, 耗时: 6.275s
[2025-07-25 11:46:10] 自旋算符进度: 39/100, 当前算符: S_0 · S_38, 耗时: 6.303s
[2025-07-25 11:46:17] 自旋算符进度: 40/100, 当前算符: S_0 · S_39, 耗时: 6.303s
[2025-07-25 11:46:23] 自旋算符进度: 41/100, 当前算符: S_0 · S_40, 耗时: 6.303s
[2025-07-25 11:46:29] 自旋算符进度: 42/100, 当前算符: S_0 · S_41, 耗时: 6.303s
[2025-07-25 11:46:36] 自旋算符进度: 43/100, 当前算符: S_0 · S_42, 耗时: 6.304s
[2025-07-25 11:46:42] 自旋算符进度: 44/100, 当前算符: S_0 · S_43, 耗时: 6.306s
[2025-07-25 11:46:48] 自旋算符进度: 45/100, 当前算符: S_0 · S_44, 耗时: 6.277s
[2025-07-25 11:46:54] 自旋算符进度: 46/100, 当前算符: S_0 · S_45, 耗时: 6.277s
[2025-07-25 11:47:01] 自旋算符进度: 47/100, 当前算符: S_0 · S_46, 耗时: 6.307s
[2025-07-25 11:47:07] 自旋算符进度: 48/100, 当前算符: S_0 · S_47, 耗时: 6.307s
[2025-07-25 11:47:13] 自旋算符进度: 49/100, 当前算符: S_0 · S_48, 耗时: 6.307s
[2025-07-25 11:47:20] 自旋算符进度: 50/100, 当前算符: S_0 · S_49, 耗时: 6.278s
[2025-07-25 11:47:26] 自旋算符进度: 51/100, 当前算符: S_0 · S_50, 耗时: 6.277s
[2025-07-25 11:47:32] 自旋算符进度: 52/100, 当前算符: S_0 · S_51, 耗时: 6.278s
[2025-07-25 11:47:39] 自旋算符进度: 53/100, 当前算符: S_0 · S_52, 耗时: 6.307s
[2025-07-25 11:47:45] 自旋算符进度: 54/100, 当前算符: S_0 · S_53, 耗时: 6.307s
[2025-07-25 11:47:51] 自旋算符进度: 55/100, 当前算符: S_0 · S_54, 耗时: 6.276s
[2025-07-25 11:47:57] 自旋算符进度: 56/100, 当前算符: S_0 · S_55, 耗时: 6.276s
[2025-07-25 11:48:04] 自旋算符进度: 57/100, 当前算符: S_0 · S_56, 耗时: 6.276s
[2025-07-25 11:48:10] 自旋算符进度: 58/100, 当前算符: S_0 · S_57, 耗时: 6.305s
[2025-07-25 11:48:16] 自旋算符进度: 59/100, 当前算符: S_0 · S_58, 耗时: 6.304s
[2025-07-25 11:48:23] 自旋算符进度: 60/100, 当前算符: S_0 · S_59, 耗时: 6.305s
[2025-07-25 11:48:29] 自旋算符进度: 61/100, 当前算符: S_0 · S_60, 耗时: 6.278s
[2025-07-25 11:48:35] 自旋算符进度: 62/100, 当前算符: S_0 · S_61, 耗时: 6.294s
[2025-07-25 11:48:41] 自旋算符进度: 63/100, 当前算符: S_0 · S_62, 耗时: 6.275s
[2025-07-25 11:48:48] 自旋算符进度: 64/100, 当前算符: S_0 · S_63, 耗时: 6.305s
[2025-07-25 11:48:54] 自旋算符进度: 65/100, 当前算符: S_0 · S_64, 耗时: 6.305s
[2025-07-25 11:49:00] 自旋算符进度: 66/100, 当前算符: S_0 · S_65, 耗时: 6.302s
[2025-07-25 11:49:07] 自旋算符进度: 67/100, 当前算符: S_0 · S_66, 耗时: 6.274s
[2025-07-25 11:49:13] 自旋算符进度: 68/100, 当前算符: S_0 · S_67, 耗时: 6.271s
[2025-07-25 11:49:19] 自旋算符进度: 69/100, 当前算符: S_0 · S_68, 耗时: 6.304s
[2025-07-25 11:49:25] 自旋算符进度: 70/100, 当前算符: S_0 · S_69, 耗时: 6.307s
[2025-07-25 11:49:32] 自旋算符进度: 71/100, 当前算符: S_0 · S_70, 耗时: 6.306s
[2025-07-25 11:49:38] 自旋算符进度: 72/100, 当前算符: S_0 · S_71, 耗时: 6.277s
[2025-07-25 11:49:44] 自旋算符进度: 73/100, 当前算符: S_0 · S_72, 耗时: 6.275s
[2025-07-25 11:49:51] 自旋算符进度: 74/100, 当前算符: S_0 · S_73, 耗时: 6.277s
[2025-07-25 11:49:57] 自旋算符进度: 75/100, 当前算符: S_0 · S_74, 耗时: 6.307s
[2025-07-25 11:50:03] 自旋算符进度: 76/100, 当前算符: S_0 · S_75, 耗时: 6.305s
[2025-07-25 11:50:10] 自旋算符进度: 77/100, 当前算符: S_0 · S_76, 耗时: 6.275s
[2025-07-25 11:50:16] 自旋算符进度: 78/100, 当前算符: S_0 · S_77, 耗时: 6.277s
[2025-07-25 11:50:22] 自旋算符进度: 79/100, 当前算符: S_0 · S_78, 耗时: 6.278s
[2025-07-25 11:50:28] 自旋算符进度: 80/100, 当前算符: S_0 · S_79, 耗时: 6.304s
[2025-07-25 11:50:35] 自旋算符进度: 81/100, 当前算符: S_0 · S_80, 耗时: 6.306s
[2025-07-25 11:50:41] 自旋算符进度: 82/100, 当前算符: S_0 · S_81, 耗时: 6.277s
[2025-07-25 11:50:47] 自旋算符进度: 83/100, 当前算符: S_0 · S_82, 耗时: 6.276s
[2025-07-25 11:50:54] 自旋算符进度: 84/100, 当前算符: S_0 · S_83, 耗时: 6.278s
[2025-07-25 11:51:00] 自旋算符进度: 85/100, 当前算符: S_0 · S_84, 耗时: 6.308s
[2025-07-25 11:51:06] 自旋算符进度: 86/100, 当前算符: S_0 · S_85, 耗时: 6.306s
[2025-07-25 11:51:12] 自旋算符进度: 87/100, 当前算符: S_0 · S_86, 耗时: 6.305s
[2025-07-25 11:51:19] 自旋算符进度: 88/100, 当前算符: S_0 · S_87, 耗时: 6.274s
[2025-07-25 11:51:25] 自旋算符进度: 89/100, 当前算符: S_0 · S_88, 耗时: 6.275s
[2025-07-25 11:51:31] 自旋算符进度: 90/100, 当前算符: S_0 · S_89, 耗时: 6.303s
[2025-07-25 11:51:38] 自旋算符进度: 91/100, 当前算符: S_0 · S_90, 耗时: 6.307s
[2025-07-25 11:51:44] 自旋算符进度: 92/100, 当前算符: S_0 · S_91, 耗时: 6.307s
[2025-07-25 11:51:50] 自旋算符进度: 93/100, 当前算符: S_0 · S_92, 耗时: 6.277s
[2025-07-25 11:51:56] 自旋算符进度: 94/100, 当前算符: S_0 · S_93, 耗时: 6.307s
[2025-07-25 11:52:03] 自旋算符进度: 95/100, 当前算符: S_0 · S_94, 耗时: 6.278s
[2025-07-25 11:52:09] 自旋算符进度: 96/100, 当前算符: S_0 · S_95, 耗时: 6.278s
[2025-07-25 11:52:15] 自旋算符进度: 97/100, 当前算符: S_0 · S_96, 耗时: 6.307s
[2025-07-25 11:52:22] 自旋算符进度: 98/100, 当前算符: S_0 · S_97, 耗时: 6.307s
[2025-07-25 11:52:28] 自旋算符进度: 99/100, 当前算符: S_0 · S_98, 耗时: 6.307s
[2025-07-25 11:52:34] 自旋算符进度: 100/100, 当前算符: S_0 · S_99, 耗时: 6.277s
[2025-07-25 11:52:34] 自旋相关函数计算完成,总耗时 638.59 秒
[2025-07-25 11:52:34] 计算傅里叶变换...
[2025-07-25 11:52:35] 自旋结构因子计算完成
[2025-07-25 11:52:36] 自旋相关函数平均误差: 0.000571
[2025-07-25 11:52:36] ================================================================================
[2025-07-25 11:52:36] 开始计算对角二聚体结构因子...
[2025-07-25 11:52:36] 识别所有对角二聚体...
[2025-07-25 11:52:36] 总共找到 100 个西北-东南方向对角二聚体和 100 个西南-东北方向对角二聚体
[2025-07-25 11:52:36] 预计算对角二聚体操作符...
[2025-07-25 11:52:38] 开始计算西北-东南方向对角二聚体相关函数 (100 个算符)...
[2025-07-25 11:52:45] NW-SE对角算符进度: 1/100, 当前算符: D_(0, 18) * D_(0, 18), 耗时: 6.322s
[2025-07-25 11:53:00] NW-SE对角算符进度: 2/100, 当前算符: D_(0, 18) * D_(1, 39), 耗时: 15.438s
[2025-07-25 11:53:11] NW-SE对角算符进度: 3/100, 当前算符: D_(0, 18) * D_(2, 20), 耗时: 10.611s
[2025-07-25 11:53:21] NW-SE对角算符进度: 4/100, 当前算符: D_(0, 18) * D_(3, 1), 耗时: 10.550s
[2025-07-25 11:53:32] NW-SE对角算符进度: 5/100, 当前算符: D_(0, 18) * D_(4, 2), 耗时: 10.552s
[2025-07-25 11:53:42] NW-SE对角算符进度: 6/100, 当前算符: D_(0, 18) * D_(5, 23), 耗时: 10.613s
[2025-07-25 11:53:53] NW-SE对角算符进度: 7/100, 当前算符: D_(0, 18) * D_(6, 24), 耗时: 10.552s
[2025-07-25 11:54:04] NW-SE对角算符进度: 8/100, 当前算符: D_(0, 18) * D_(7, 5), 耗时: 10.551s
[2025-07-25 11:54:14] NW-SE对角算符进度: 9/100, 当前算符: D_(0, 18) * D_(8, 6), 耗时: 10.551s
[2025-07-25 11:54:25] NW-SE对角算符进度: 10/100, 当前算符: D_(0, 18) * D_(9, 27), 耗时: 10.555s
[2025-07-25 11:54:35] NW-SE对角算符进度: 11/100, 当前算符: D_(0, 18) * D_(10, 28), 耗时: 10.553s
[2025-07-25 11:54:46] NW-SE对角算符进度: 12/100, 当前算符: D_(0, 18) * D_(11, 9), 耗时: 10.558s
[2025-07-25 11:54:56] NW-SE对角算符进度: 13/100, 当前算符: D_(0, 18) * D_(12, 10), 耗时: 10.611s
[2025-07-25 11:55:07] NW-SE对角算符进度: 14/100, 当前算符: D_(0, 18) * D_(13, 31), 耗时: 10.610s
[2025-07-25 11:55:18] NW-SE对角算符进度: 15/100, 当前算符: D_(0, 18) * D_(14, 32), 耗时: 10.556s
[2025-07-25 11:55:28] NW-SE对角算符进度: 16/100, 当前算符: D_(0, 18) * D_(15, 13), 耗时: 10.554s
[2025-07-25 11:55:39] NW-SE对角算符进度: 17/100, 当前算符: D_(0, 18) * D_(16, 14), 耗时: 10.551s
[2025-07-25 11:55:49] NW-SE对角算符进度: 18/100, 当前算符: D_(0, 18) * D_(17, 35), 耗时: 10.611s
[2025-07-25 11:56:07] NW-SE对角算符进度: 19/100, 当前算符: D_(0, 18) * D_(18, 36), 耗时: 17.678s
[2025-07-25 11:56:18] NW-SE对角算符进度: 20/100, 当前算符: D_(0, 18) * D_(19, 17), 耗时: 10.592s
[2025-07-25 11:56:28] NW-SE对角算符进度: 21/100, 当前算符: D_(0, 18) * D_(20, 38), 耗时: 10.528s
[2025-07-25 11:56:39] NW-SE对角算符进度: 22/100, 当前算符: D_(0, 18) * D_(21, 59), 耗时: 10.530s
[2025-07-25 11:56:49] NW-SE对角算符进度: 23/100, 当前算符: D_(0, 18) * D_(22, 40), 耗时: 10.587s
[2025-07-25 11:57:00] NW-SE对角算符进度: 24/100, 当前算符: D_(0, 18) * D_(23, 21), 耗时: 10.591s
[2025-07-25 11:57:10] NW-SE对角算符进度: 25/100, 当前算符: D_(0, 18) * D_(24, 22), 耗时: 10.587s
[2025-07-25 11:57:21] NW-SE对角算符进度: 26/100, 当前算符: D_(0, 18) * D_(25, 43), 耗时: 10.598s
[2025-07-25 11:57:32] NW-SE对角算符进度: 27/100, 当前算符: D_(0, 18) * D_(26, 44), 耗时: 10.537s
[2025-07-25 11:57:42] NW-SE对角算符进度: 28/100, 当前算符: D_(0, 18) * D_(27, 25), 耗时: 10.538s
[2025-07-25 11:57:53] NW-SE对角算符进度: 29/100, 当前算符: D_(0, 18) * D_(28, 26), 耗时: 10.663s
[2025-07-25 11:58:03] NW-SE对角算符进度: 30/100, 当前算符: D_(0, 18) * D_(29, 47), 耗时: 10.593s
[2025-07-25 11:58:14] NW-SE对角算符进度: 31/100, 当前算符: D_(0, 18) * D_(30, 48), 耗时: 10.594s
[2025-07-25 11:58:24] NW-SE对角算符进度: 32/100, 当前算符: D_(0, 18) * D_(31, 29), 耗时: 10.530s
[2025-07-25 11:58:35] NW-SE对角算符进度: 33/100, 当前算符: D_(0, 18) * D_(32, 30), 耗时: 10.538s
[2025-07-25 11:58:46] NW-SE对角算符进度: 34/100, 当前算符: D_(0, 18) * D_(33, 51), 耗时: 10.530s
[2025-07-25 11:58:56] NW-SE对角算符进度: 35/100, 当前算符: D_(0, 18) * D_(34, 52), 耗时: 10.593s
[2025-07-25 11:59:07] NW-SE对角算符进度: 36/100, 当前算符: D_(0, 18) * D_(35, 33), 耗时: 10.590s
[2025-07-25 11:59:17] NW-SE对角算符进度: 37/100, 当前算符: D_(0, 18) * D_(36, 34), 耗时: 10.529s
[2025-07-25 11:59:28] NW-SE对角算符进度: 38/100, 当前算符: D_(0, 18) * D_(37, 55), 耗时: 10.534s
[2025-07-25 11:59:38] NW-SE对角算符进度: 39/100, 当前算符: D_(0, 18) * D_(38, 56), 耗时: 10.590s
[2025-07-25 11:59:49] NW-SE对角算符进度: 40/100, 当前算符: D_(0, 18) * D_(39, 37), 耗时: 10.588s
[2025-07-25 12:00:00] NW-SE对角算符进度: 41/100, 当前算符: D_(0, 18) * D_(40, 58), 耗时: 10.589s
[2025-07-25 12:00:10] NW-SE对角算符进度: 42/100, 当前算符: D_(0, 18) * D_(41, 79), 耗时: 10.584s
[2025-07-25 12:00:21] NW-SE对角算符进度: 43/100, 当前算符: D_(0, 18) * D_(42, 60), 耗时: 10.585s
[2025-07-25 12:00:31] NW-SE对角算符进度: 44/100, 当前算符: D_(0, 18) * D_(43, 41), 耗时: 10.531s
[2025-07-25 12:00:42] NW-SE对角算符进度: 45/100, 当前算符: D_(0, 18) * D_(44, 42), 耗时: 10.527s
[2025-07-25 12:00:52] NW-SE对角算符进度: 46/100, 当前算符: D_(0, 18) * D_(45, 63), 耗时: 10.530s
[2025-07-25 12:01:03] NW-SE对角算符进度: 47/100, 当前算符: D_(0, 18) * D_(46, 64), 耗时: 10.595s
[2025-07-25 12:01:14] NW-SE对角算符进度: 48/100, 当前算符: D_(0, 18) * D_(47, 45), 耗时: 10.597s
[2025-07-25 12:01:24] NW-SE对角算符进度: 49/100, 当前算符: D_(0, 18) * D_(48, 46), 耗时: 10.534s
[2025-07-25 12:01:35] NW-SE对角算符进度: 50/100, 当前算符: D_(0, 18) * D_(49, 67), 耗时: 10.529s
[2025-07-25 12:01:45] NW-SE对角算符进度: 51/100, 当前算符: D_(0, 18) * D_(50, 68), 耗时: 10.543s
[2025-07-25 12:01:56] NW-SE对角算符进度: 52/100, 当前算符: D_(0, 18) * D_(51, 49), 耗时: 10.595s
[2025-07-25 12:02:06] NW-SE对角算符进度: 53/100, 当前算符: D_(0, 18) * D_(52, 50), 耗时: 10.591s
[2025-07-25 12:02:17] NW-SE对角算符进度: 54/100, 当前算符: D_(0, 18) * D_(53, 71), 耗时: 10.593s
[2025-07-25 12:02:27] NW-SE对角算符进度: 55/100, 当前算符: D_(0, 18) * D_(54, 72), 耗时: 10.533s
[2025-07-25 12:02:38] NW-SE对角算符进度: 56/100, 当前算符: D_(0, 18) * D_(55, 53), 耗时: 10.527s
[2025-07-25 12:02:49] NW-SE对角算符进度: 57/100, 当前算符: D_(0, 18) * D_(56, 54), 耗时: 10.587s
[2025-07-25 12:02:59] NW-SE对角算符进度: 58/100, 当前算符: D_(0, 18) * D_(57, 75), 耗时: 10.585s
[2025-07-25 12:03:10] NW-SE对角算符进度: 59/100, 当前算符: D_(0, 18) * D_(58, 76), 耗时: 10.588s
[2025-07-25 12:03:20] NW-SE对角算符进度: 60/100, 当前算符: D_(0, 18) * D_(59, 57), 耗时: 10.526s
[2025-07-25 12:03:31] NW-SE对角算符进度: 61/100, 当前算符: D_(0, 18) * D_(60, 78), 耗时: 10.529s
[2025-07-25 12:03:41] NW-SE对角算符进度: 62/100, 当前算符: D_(0, 18) * D_(61, 99), 耗时: 10.586s
[2025-07-25 12:03:52] NW-SE对角算符进度: 63/100, 当前算符: D_(0, 18) * D_(62, 80), 耗时: 10.585s
[2025-07-25 12:04:03] NW-SE对角算符进度: 64/100, 当前算符: D_(0, 18) * D_(63, 61), 耗时: 10.587s
[2025-07-25 12:04:13] NW-SE对角算符进度: 65/100, 当前算符: D_(0, 18) * D_(64, 62), 耗时: 10.525s
[2025-07-25 12:04:24] NW-SE对角算符进度: 66/100, 当前算符: D_(0, 18) * D_(65, 83), 耗时: 10.532s
[2025-07-25 12:04:34] NW-SE对角算符进度: 67/100, 当前算符: D_(0, 18) * D_(66, 84), 耗时: 10.525s
[2025-07-25 12:04:45] NW-SE对角算符进度: 68/100, 当前算符: D_(0, 18) * D_(67, 65), 耗时: 10.584s
[2025-07-25 12:04:55] NW-SE对角算符进度: 69/100, 当前算符: D_(0, 18) * D_(68, 66), 耗时: 10.586s
[2025-07-25 12:05:06] NW-SE对角算符进度: 70/100, 当前算符: D_(0, 18) * D_(69, 87), 耗时: 10.530s
[2025-07-25 12:05:16] NW-SE对角算符进度: 71/100, 当前算符: D_(0, 18) * D_(70, 88), 耗时: 10.530s
[2025-07-25 12:05:27] NW-SE对角算符进度: 72/100, 当前算符: D_(0, 18) * D_(71, 69), 耗时: 10.531s
[2025-07-25 12:05:37] NW-SE对角算符进度: 73/100, 当前算符: D_(0, 18) * D_(72, 70), 耗时: 10.592s
[2025-07-25 12:05:48] NW-SE对角算符进度: 74/100, 当前算符: D_(0, 18) * D_(73, 91), 耗时: 10.591s
[2025-07-25 12:05:59] NW-SE对角算符进度: 75/100, 当前算符: D_(0, 18) * D_(74, 92), 耗时: 10.594s
[2025-07-25 12:06:09] NW-SE对角算符进度: 76/100, 当前算符: D_(0, 18) * D_(75, 73), 耗时: 10.529s
[2025-07-25 12:06:20] NW-SE对角算符进度: 77/100, 当前算符: D_(0, 18) * D_(76, 74), 耗时: 10.532s
[2025-07-25 12:06:30] NW-SE对角算符进度: 78/100, 当前算符: D_(0, 18) * D_(77, 95), 耗时: 10.530s
[2025-07-25 12:06:41] NW-SE对角算符进度: 79/100, 当前算符: D_(0, 18) * D_(78, 96), 耗时: 10.532s
[2025-07-25 12:06:51] NW-SE对角算符进度: 80/100, 当前算符: D_(0, 18) * D_(79, 77), 耗时: 10.587s
[2025-07-25 12:07:02] NW-SE对角算符进度: 81/100, 当前算符: D_(0, 18) * D_(80, 98), 耗时: 10.588s
[2025-07-25 12:07:13] NW-SE对角算符进度: 82/100, 当前算符: D_(0, 18) * D_(81, 19), 耗时: 10.592s
[2025-07-25 12:07:21] NW-SE对角算符进度: 83/100, 当前算符: D_(0, 18) * D_(82, 0), 耗时: 8.828s
[2025-07-25 12:07:32] NW-SE对角算符进度: 84/100, 当前算符: D_(0, 18) * D_(83, 81), 耗时: 10.539s
[2025-07-25 12:07:43] NW-SE对角算符进度: 85/100, 当前算符: D_(0, 18) * D_(84, 82), 耗时: 10.594s
[2025-07-25 12:07:53] NW-SE对角算符进度: 86/100, 当前算符: D_(0, 18) * D_(85, 3), 耗时: 10.600s
[2025-07-25 12:08:04] NW-SE对角算符进度: 87/100, 当前算符: D_(0, 18) * D_(86, 4), 耗时: 10.601s
[2025-07-25 12:08:14] NW-SE对角算符进度: 88/100, 当前算符: D_(0, 18) * D_(87, 85), 耗时: 10.537s
[2025-07-25 12:08:25] NW-SE对角算符进度: 89/100, 当前算符: D_(0, 18) * D_(88, 86), 耗时: 10.539s
[2025-07-25 12:08:35] NW-SE对角算符进度: 90/100, 当前算符: D_(0, 18) * D_(89, 7), 耗时: 10.538s
[2025-07-25 12:08:46] NW-SE对角算符进度: 91/100, 当前算符: D_(0, 18) * D_(90, 8), 耗时: 10.598s
[2025-07-25 12:08:57] NW-SE对角算符进度: 92/100, 当前算符: D_(0, 18) * D_(91, 89), 耗时: 10.592s
[2025-07-25 12:09:07] NW-SE对角算符进度: 93/100, 当前算符: D_(0, 18) * D_(92, 90), 耗时: 10.596s
[2025-07-25 12:09:18] NW-SE对角算符进度: 94/100, 当前算符: D_(0, 18) * D_(93, 11), 耗时: 10.596s
[2025-07-25 12:09:28] NW-SE对角算符进度: 95/100, 当前算符: D_(0, 18) * D_(94, 12), 耗时: 10.534s
[2025-07-25 12:09:39] NW-SE对角算符进度: 96/100, 当前算符: D_(0, 18) * D_(95, 93), 耗时: 10.535s
[2025-07-25 12:09:49] NW-SE对角算符进度: 97/100, 当前算符: D_(0, 18) * D_(96, 94), 耗时: 10.532s
[2025-07-25 12:10:00] NW-SE对角算符进度: 98/100, 当前算符: D_(0, 18) * D_(97, 15), 耗时: 10.592s
[2025-07-25 12:10:11] NW-SE对角算符进度: 99/100, 当前算符: D_(0, 18) * D_(98, 16), 耗时: 10.592s
[2025-07-25 12:10:21] NW-SE对角算符进度: 100/100, 当前算符: D_(0, 18) * D_(99, 97), 耗时: 10.587s
[2025-07-25 12:10:21] 西北-东南方向对角二聚体相关函数计算完成,耗时: 1062.73 秒
[2025-07-25 12:10:21] ================================================================================
[2025-07-25 12:10:21] 开始计算西南-东北方向对角二聚体相关函数 (100 个算符)...
[2025-07-25 12:10:27] SW-NE对角算符进度: 1/100, 当前算符: D_(0, 2) * D_(0, 2), 耗时: 6.301s
[2025-07-25 12:10:38] SW-NE对角算符进度: 2/100, 当前算符: D_(0, 2) * D_(1, 23), 耗时: 10.597s
[2025-07-25 12:10:47] SW-NE对角算符进度: 3/100, 当前算符: D_(0, 2) * D_(2, 24), 耗时: 8.826s
[2025-07-25 12:10:57] SW-NE对角算符进度: 4/100, 当前算符: D_(0, 2) * D_(3, 5), 耗时: 10.541s
[2025-07-25 12:11:08] SW-NE对角算符进度: 5/100, 当前算符: D_(0, 2) * D_(4, 6), 耗时: 10.538s
[2025-07-25 12:11:19] SW-NE对角算符进度: 6/100, 当前算符: D_(0, 2) * D_(5, 27), 耗时: 10.592s
[2025-07-25 12:11:29] SW-NE对角算符进度: 7/100, 当前算符: D_(0, 2) * D_(6, 28), 耗时: 10.599s
[2025-07-25 12:11:40] SW-NE对角算符进度: 8/100, 当前算符: D_(0, 2) * D_(7, 9), 耗时: 10.600s
[2025-07-25 12:11:50] SW-NE对角算符进度: 9/100, 当前算符: D_(0, 2) * D_(8, 10), 耗时: 10.539s
[2025-07-25 12:12:01] SW-NE对角算符进度: 10/100, 当前算符: D_(0, 2) * D_(9, 31), 耗时: 10.594s
[2025-07-25 12:12:11] SW-NE对角算符进度: 11/100, 当前算符: D_(0, 2) * D_(10, 32), 耗时: 10.534s
[2025-07-25 12:12:22] SW-NE对角算符进度: 12/100, 当前算符: D_(0, 2) * D_(11, 13), 耗时: 10.541s
[2025-07-25 12:12:32] SW-NE对角算符进度: 13/100, 当前算符: D_(0, 2) * D_(12, 14), 耗时: 10.539s
[2025-07-25 12:12:43] SW-NE对角算符进度: 14/100, 当前算符: D_(0, 2) * D_(13, 35), 耗时: 10.599s
[2025-07-25 12:12:54] SW-NE对角算符进度: 15/100, 当前算符: D_(0, 2) * D_(14, 36), 耗时: 10.594s
[2025-07-25 12:13:04] SW-NE对角算符进度: 16/100, 当前算符: D_(0, 2) * D_(15, 17), 耗时: 10.543s
[2025-07-25 12:13:15] SW-NE对角算符进度: 17/100, 当前算符: D_(0, 2) * D_(16, 18), 耗时: 10.544s
[2025-07-25 12:13:25] SW-NE对角算符进度: 18/100, 当前算符: D_(0, 2) * D_(17, 39), 耗时: 10.539s
[2025-07-25 12:13:36] SW-NE对角算符进度: 19/100, 当前算符: D_(0, 2) * D_(18, 20), 耗时: 10.597s
[2025-07-25 12:13:46] SW-NE对角算符进度: 20/100, 当前算符: D_(0, 2) * D_(19, 1), 耗时: 10.595s
[2025-07-25 12:13:57] SW-NE对角算符进度: 21/100, 当前算符: D_(0, 2) * D_(20, 22), 耗时: 10.595s
[2025-07-25 12:14:08] SW-NE对角算符进度: 22/100, 当前算符: D_(0, 2) * D_(21, 43), 耗时: 10.537s
[2025-07-25 12:14:18] SW-NE对角算符进度: 23/100, 当前算符: D_(0, 2) * D_(22, 44), 耗时: 10.537s
[2025-07-25 12:14:29] SW-NE对角算符进度: 24/100, 当前算符: D_(0, 2) * D_(23, 25), 耗时: 10.597s
[2025-07-25 12:14:39] SW-NE对角算符进度: 25/100, 当前算符: D_(0, 2) * D_(24, 26), 耗时: 10.596s
[2025-07-25 12:14:50] SW-NE对角算符进度: 26/100, 当前算符: D_(0, 2) * D_(25, 47), 耗时: 10.593s
[2025-07-25 12:15:01] SW-NE对角算符进度: 27/100, 当前算符: D_(0, 2) * D_(26, 48), 耗时: 10.596s
[2025-07-25 12:15:11] SW-NE对角算符进度: 28/100, 当前算符: D_(0, 2) * D_(27, 29), 耗时: 10.597s
[2025-07-25 12:15:22] SW-NE对角算符进度: 29/100, 当前算符: D_(0, 2) * D_(28, 30), 耗时: 10.539s
[2025-07-25 12:15:32] SW-NE对角算符进度: 30/100, 当前算符: D_(0, 2) * D_(29, 51), 耗时: 10.534s
[2025-07-25 12:15:43] SW-NE对角算符进度: 31/100, 当前算符: D_(0, 2) * D_(30, 52), 耗时: 10.543s
[2025-07-25 12:15:53] SW-NE对角算符进度: 32/100, 当前算符: D_(0, 2) * D_(31, 33), 耗时: 10.594s
[2025-07-25 12:16:04] SW-NE对角算符进度: 33/100, 当前算符: D_(0, 2) * D_(32, 34), 耗时: 10.592s
[2025-07-25 12:16:15] SW-NE对角算符进度: 34/100, 当前算符: D_(0, 2) * D_(33, 55), 耗时: 10.596s
[2025-07-25 12:16:25] SW-NE对角算符进度: 35/100, 当前算符: D_(0, 2) * D_(34, 56), 耗时: 10.537s
[2025-07-25 12:16:36] SW-NE对角算符进度: 36/100, 当前算符: D_(0, 2) * D_(35, 37), 耗时: 10.540s
[2025-07-25 12:16:46] SW-NE对角算符进度: 37/100, 当前算符: D_(0, 2) * D_(36, 38), 耗时: 10.598s
[2025-07-25 12:16:57] SW-NE对角算符进度: 38/100, 当前算符: D_(0, 2) * D_(37, 59), 耗时: 10.598s
[2025-07-25 12:17:07] SW-NE对角算符进度: 39/100, 当前算符: D_(0, 2) * D_(38, 40), 耗时: 10.595s
[2025-07-25 12:17:18] SW-NE对角算符进度: 40/100, 当前算符: D_(0, 2) * D_(39, 21), 耗时: 10.537s
[2025-07-25 12:17:29] SW-NE对角算符进度: 41/100, 当前算符: D_(0, 2) * D_(40, 42), 耗时: 10.544s
[2025-07-25 12:17:39] SW-NE对角算符进度: 42/100, 当前算符: D_(0, 2) * D_(41, 63), 耗时: 10.535s
[2025-07-25 12:17:50] SW-NE对角算符进度: 43/100, 当前算符: D_(0, 2) * D_(42, 64), 耗时: 10.600s
[2025-07-25 12:18:00] SW-NE对角算符进度: 44/100, 当前算符: D_(0, 2) * D_(43, 45), 耗时: 10.596s
[2025-07-25 12:18:11] SW-NE对角算符进度: 45/100, 当前算符: D_(0, 2) * D_(44, 46), 耗时: 10.540s
[2025-07-25 12:18:21] SW-NE对角算符进度: 46/100, 当前算符: D_(0, 2) * D_(45, 67), 耗时: 10.542s
[2025-07-25 12:18:32] SW-NE对角算符进度: 47/100, 当前算符: D_(0, 2) * D_(46, 68), 耗时: 10.542s
[2025-07-25 12:18:42] SW-NE对角算符进度: 48/100, 当前算符: D_(0, 2) * D_(47, 49), 耗时: 10.543s
[2025-07-25 12:18:53] SW-NE对角算符进度: 49/100, 当前算符: D_(0, 2) * D_(48, 50), 耗时: 10.536s
[2025-07-25 12:19:03] SW-NE对角算符进度: 50/100, 当前算符: D_(0, 2) * D_(49, 71), 耗时: 10.540s
[2025-07-25 12:19:14] SW-NE对角算符进度: 51/100, 当前算符: D_(0, 2) * D_(50, 72), 耗时: 10.599s
[2025-07-25 12:19:25] SW-NE对角算符进度: 52/100, 当前算符: D_(0, 2) * D_(51, 53), 耗时: 10.604s
[2025-07-25 12:19:35] SW-NE对角算符进度: 53/100, 当前算符: D_(0, 2) * D_(52, 54), 耗时: 10.543s
[2025-07-25 12:19:46] SW-NE对角算符进度: 54/100, 当前算符: D_(0, 2) * D_(53, 75), 耗时: 10.550s
[2025-07-25 12:19:56] SW-NE对角算符进度: 55/100, 当前算符: D_(0, 2) * D_(54, 76), 耗时: 10.541s
[2025-07-25 12:20:07] SW-NE对角算符进度: 56/100, 当前算符: D_(0, 2) * D_(55, 57), 耗时: 10.607s
[2025-07-25 12:20:18] SW-NE对角算符进度: 57/100, 当前算符: D_(0, 2) * D_(56, 58), 耗时: 10.596s
[2025-07-25 12:20:28] SW-NE对角算符进度: 58/100, 当前算符: D_(0, 2) * D_(57, 79), 耗时: 10.595s
[2025-07-25 12:20:39] SW-NE对角算符进度: 59/100, 当前算符: D_(0, 2) * D_(58, 60), 耗时: 10.543s
[2025-07-25 12:20:49] SW-NE对角算符进度: 60/100, 当前算符: D_(0, 2) * D_(59, 41), 耗时: 10.544s
[2025-07-25 12:21:00] SW-NE对角算符进度: 61/100, 当前算符: D_(0, 2) * D_(60, 62), 耗时: 10.605s
[2025-07-25 12:21:10] SW-NE对角算符进度: 62/100, 当前算符: D_(0, 2) * D_(61, 83), 耗时: 10.595s
[2025-07-25 12:21:21] SW-NE对角算符进度: 63/100, 当前算符: D_(0, 2) * D_(62, 84), 耗时: 10.595s
[2025-07-25 12:21:32] SW-NE对角算符进度: 64/100, 当前算符: D_(0, 2) * D_(63, 65), 耗时: 10.537s
[2025-07-25 12:21:42] SW-NE对角算符进度: 65/100, 当前算符: D_(0, 2) * D_(64, 66), 耗时: 10.534s
[2025-07-25 12:21:53] SW-NE对角算符进度: 66/100, 当前算符: D_(0, 2) * D_(65, 87), 耗时: 10.539s
[2025-07-25 12:22:03] SW-NE对角算符进度: 67/100, 当前算符: D_(0, 2) * D_(66, 88), 耗时: 10.600s
[2025-07-25 12:22:14] SW-NE对角算符进度: 68/100, 当前算符: D_(0, 2) * D_(67, 69), 耗时: 10.597s
[2025-07-25 12:22:24] SW-NE对角算符进度: 69/100, 当前算符: D_(0, 2) * D_(68, 70), 耗时: 10.543s
[2025-07-25 12:22:35] SW-NE对角算符进度: 70/100, 当前算符: D_(0, 2) * D_(69, 91), 耗时: 10.536s
[2025-07-25 12:22:45] SW-NE对角算符进度: 71/100, 当前算符: D_(0, 2) * D_(70, 92), 耗时: 10.535s
[2025-07-25 12:22:56] SW-NE对角算符进度: 72/100, 当前算符: D_(0, 2) * D_(71, 73), 耗时: 10.596s
[2025-07-25 12:23:07] SW-NE对角算符进度: 73/100, 当前算符: D_(0, 2) * D_(72, 74), 耗时: 10.593s
[2025-07-25 12:23:17] SW-NE对角算符进度: 74/100, 当前算符: D_(0, 2) * D_(73, 95), 耗时: 10.595s
[2025-07-25 12:23:28] SW-NE对角算符进度: 75/100, 当前算符: D_(0, 2) * D_(74, 96), 耗时: 10.540s
[2025-07-25 12:23:38] SW-NE对角算符进度: 76/100, 当前算符: D_(0, 2) * D_(75, 77), 耗时: 10.537s
[2025-07-25 12:23:49] SW-NE对角算符进度: 77/100, 当前算符: D_(0, 2) * D_(76, 78), 耗时: 10.600s
[2025-07-25 12:24:00] SW-NE对角算符进度: 78/100, 当前算符: D_(0, 2) * D_(77, 99), 耗时: 10.605s
[2025-07-25 12:24:10] SW-NE对角算符进度: 79/100, 当前算符: D_(0, 2) * D_(78, 80), 耗时: 10.617s
[2025-07-25 12:24:21] SW-NE对角算符进度: 80/100, 当前算符: D_(0, 2) * D_(79, 61), 耗时: 10.545s
[2025-07-25 12:24:31] SW-NE对角算符进度: 81/100, 当前算符: D_(0, 2) * D_(80, 82), 耗时: 10.546s
[2025-07-25 12:24:42] SW-NE对角算符进度: 82/100, 当前算符: D_(0, 2) * D_(81, 3), 耗时: 10.542s
[2025-07-25 12:24:52] SW-NE对角算符进度: 83/100, 当前算符: D_(0, 2) * D_(82, 4), 耗时: 10.601s
[2025-07-25 12:25:03] SW-NE对角算符进度: 84/100, 当前算符: D_(0, 2) * D_(83, 85), 耗时: 10.606s
[2025-07-25 12:25:14] SW-NE对角算符进度: 85/100, 当前算符: D_(0, 2) * D_(84, 86), 耗时: 10.544s
[2025-07-25 12:25:24] SW-NE对角算符进度: 86/100, 当前算符: D_(0, 2) * D_(85, 7), 耗时: 10.543s
[2025-07-25 12:25:35] SW-NE对角算符进度: 87/100, 当前算符: D_(0, 2) * D_(86, 8), 耗时: 10.542s
[2025-07-25 12:25:45] SW-NE对角算符进度: 88/100, 当前算符: D_(0, 2) * D_(87, 89), 耗时: 10.604s
[2025-07-25 12:25:56] SW-NE对角算符进度: 89/100, 当前算符: D_(0, 2) * D_(88, 90), 耗时: 10.601s
[2025-07-25 12:26:06] SW-NE对角算符进度: 90/100, 当前算符: D_(0, 2) * D_(89, 11), 耗时: 10.596s
[2025-07-25 12:26:17] SW-NE对角算符进度: 91/100, 当前算符: D_(0, 2) * D_(90, 12), 耗时: 10.597s
[2025-07-25 12:26:28] SW-NE对角算符进度: 92/100, 当前算符: D_(0, 2) * D_(91, 93), 耗时: 10.597s
[2025-07-25 12:26:38] SW-NE对角算符进度: 93/100, 当前算符: D_(0, 2) * D_(92, 94), 耗时: 10.538s
[2025-07-25 12:26:49] SW-NE对角算符进度: 94/100, 当前算符: D_(0, 2) * D_(93, 15), 耗时: 10.541s
[2025-07-25 12:26:59] SW-NE对角算符进度: 95/100, 当前算符: D_(0, 2) * D_(94, 16), 耗时: 10.542s
[2025-07-25 12:27:10] SW-NE对角算符进度: 96/100, 当前算符: D_(0, 2) * D_(95, 97), 耗时: 10.599s
[2025-07-25 12:27:20] SW-NE对角算符进度: 97/100, 当前算符: D_(0, 2) * D_(96, 98), 耗时: 10.608s
[2025-07-25 12:27:31] SW-NE对角算符进度: 98/100, 当前算符: D_(0, 2) * D_(97, 19), 耗时: 10.609s
[2025-07-25 12:27:40] SW-NE对角算符进度: 99/100, 当前算符: D_(0, 2) * D_(98, 0), 耗时: 8.788s
[2025-07-25 12:27:50] SW-NE对角算符进度: 100/100, 当前算符: D_(0, 2) * D_(99, 81), 耗时: 10.544s
[2025-07-25 12:27:50] 西南-东北方向对角二聚体相关函数计算完成,耗时: 1049.27 秒
[2025-07-25 12:27:51] 计算傅里叶变换...
[2025-07-25 12:27:51] 对角二聚体结构因子计算完成
[2025-07-25 12:27:52] 对角二聚体相关函数平均误差: 0.000120
