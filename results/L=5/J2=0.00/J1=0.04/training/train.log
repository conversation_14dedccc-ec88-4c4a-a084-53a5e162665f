[2025-07-30 04:37:17] ==================================================
[2025-07-30 04:37:17] GCNN for Shastry-Sutherland Model
[2025-07-30 04:37:17] ==================================================
[2025-07-30 04:37:17] System parameters:
[2025-07-30 04:37:17]   - System size: L=5, N=100
[2025-07-30 04:37:17]   - System parameters: J1=0.04, J2=0.0, Q=1.0
[2025-07-30 04:37:17] --------------------------------------------------
[2025-07-30 04:37:17] Model parameters:
[2025-07-30 04:37:17]   - Number of layers = 4
[2025-07-30 04:37:17]   - Number of features = 4
[2025-07-30 04:37:17]   - Total parameters = 19628
[2025-07-30 04:37:17] --------------------------------------------------
[2025-07-30 04:37:17] Training parameters:
[2025-07-30 04:37:17]   - Learning rate: 0.015
[2025-07-30 04:37:17]   - Total iterations: 4650
[2025-07-30 04:37:17]   - Annealing cycles: 5
[2025-07-30 04:37:17]   - Initial period: 150
[2025-07-30 04:37:17]   - Period multiplier: 2.0
[2025-07-30 04:37:17]   - Temperature range: 0.0-1.0
[2025-07-30 04:37:17]   - Samples: 16384
[2025-07-30 04:37:17]   - Discarded samples: 0
[2025-07-30 04:37:17]   - Chunk size: 2048
[2025-07-30 04:37:17]   - Diagonal shift: 0.2
[2025-07-30 04:37:17]   - Gradient clipping: 1.0
[2025-07-30 04:37:17]   - Checkpoint enabled: interval=500
[2025-07-30 04:37:17]   - Checkpoint directory: results/L=5/J2=0.00/J1=0.04/training/checkpoints
[2025-07-30 04:37:17] --------------------------------------------------
[2025-07-30 04:37:17] Device status:
[2025-07-30 04:37:17]   - Devices model: A100
[2025-07-30 04:37:17]   - Number of devices: 4
[2025-07-30 04:37:17]   - Sharding: True
[2025-07-30 04:37:17] ============================================================
[2025-07-30 04:37:57] [Iter 1/4650] R0[0/150], Temp: 1.0000, Energy: 1.999644-0.000182j
[2025-07-30 04:38:12] [Iter 2/4650] R0[1/150], Temp: 0.9999, Energy: 1.999716+0.000389j
[2025-07-30 04:38:26] [Iter 3/4650] R0[2/150], Temp: 0.9996, Energy: 1.999313+0.000101j
[2025-07-30 04:38:41] [Iter 4/4650] R0[3/150], Temp: 0.9990, Energy: 1.999303-0.000106j
[2025-07-30 04:38:55] [Iter 5/4650] R0[4/150], Temp: 0.9982, Energy: 1.999632+0.000412j
[2025-07-30 04:39:09] [Iter 6/4650] R0[5/150], Temp: 0.9973, Energy: 1.999802+0.000352j
[2025-07-30 04:39:24] [Iter 7/4650] R0[6/150], Temp: 0.9961, Energy: 1.999155+0.000078j
[2025-07-30 04:39:38] [Iter 8/4650] R0[7/150], Temp: 0.9946, Energy: 1.999438-0.000107j
[2025-07-30 04:39:53] [Iter 9/4650] R0[8/150], Temp: 0.9930, Energy: 1.999661+0.000445j
[2025-07-30 04:40:07] [Iter 10/4650] R0[9/150], Temp: 0.9911, Energy: 1.999490-0.000057j
[2025-07-30 04:40:22] [Iter 11/4650] R0[10/150], Temp: 0.9891, Energy: 1.999189+0.000059j
[2025-07-30 04:40:36] [Iter 12/4650] R0[11/150], Temp: 0.9868, Energy: 1.999167-0.000219j
[2025-07-30 04:40:51] [Iter 13/4650] R0[12/150], Temp: 0.9843, Energy: 1.998851-0.000521j
[2025-07-30 04:41:05] [Iter 14/4650] R0[13/150], Temp: 0.9816, Energy: 1.999171-0.000100j
[2025-07-30 04:41:20] [Iter 15/4650] R0[14/150], Temp: 0.9787, Energy: 1.999471+0.000203j
[2025-07-30 04:41:34] [Iter 16/4650] R0[15/150], Temp: 0.9755, Energy: 1.998986-0.000151j
[2025-07-30 04:41:49] [Iter 17/4650] R0[16/150], Temp: 0.9722, Energy: 1.998769-0.000102j
[2025-07-30 04:42:03] [Iter 18/4650] R0[17/150], Temp: 0.9686, Energy: 1.998632-0.000369j
[2025-07-30 04:42:18] [Iter 19/4650] R0[18/150], Temp: 0.9649, Energy: 1.999161-0.000087j
[2025-07-30 04:42:33] [Iter 20/4650] R0[19/150], Temp: 0.9609, Energy: 1.999149+0.000038j
[2025-07-30 04:42:48] [Iter 21/4650] R0[20/150], Temp: 0.9568, Energy: 1.999486+0.000489j
[2025-07-30 04:43:03] [Iter 22/4650] R0[21/150], Temp: 0.9524, Energy: 1.998591-0.000197j
[2025-07-30 04:43:18] [Iter 23/4650] R0[22/150], Temp: 0.9479, Energy: 1.998746-0.000269j
[2025-07-30 04:43:33] [Iter 24/4650] R0[23/150], Temp: 0.9431, Energy: 1.998772+0.000179j
[2025-07-30 04:43:47] [Iter 25/4650] R0[24/150], Temp: 0.9382, Energy: 1.998659-0.000002j
[2025-07-30 04:44:02] [Iter 26/4650] R0[25/150], Temp: 0.9330, Energy: 1.998313+0.000099j
[2025-07-30 04:44:17] [Iter 27/4650] R0[26/150], Temp: 0.9277, Energy: 1.998821+0.000337j
[2025-07-30 04:44:32] [Iter 28/4650] R0[27/150], Temp: 0.9222, Energy: 1.998128-0.000493j
[2025-07-30 04:44:46] [Iter 29/4650] R0[28/150], Temp: 0.9165, Energy: 1.998810+0.000924j
[2025-07-30 04:45:01] [Iter 30/4650] R0[29/150], Temp: 0.9106, Energy: 1.998785+0.000054j
[2025-07-30 04:45:16] [Iter 31/4650] R0[30/150], Temp: 0.9045, Energy: 1.998373+0.000407j
[2025-07-30 04:45:30] [Iter 32/4650] R0[31/150], Temp: 0.8983, Energy: 1.997909-0.000267j
[2025-07-30 04:45:45] [Iter 33/4650] R0[32/150], Temp: 0.8918, Energy: 1.997697+0.000488j
[2025-07-30 04:46:00] [Iter 34/4650] R0[33/150], Temp: 0.8853, Energy: 1.997643-0.000206j
[2025-07-30 04:46:14] [Iter 35/4650] R0[34/150], Temp: 0.8785, Energy: 1.996695-0.000833j
[2025-07-30 04:46:29] [Iter 36/4650] R0[35/150], Temp: 0.8716, Energy: 1.998047+0.000831j
[2025-07-30 04:46:43] [Iter 37/4650] R0[36/150], Temp: 0.8645, Energy: 1.996523-0.000418j
[2025-07-30 04:46:58] [Iter 38/4650] R0[37/150], Temp: 0.8572, Energy: 1.997679+0.000518j
[2025-07-30 04:47:13] [Iter 39/4650] R0[38/150], Temp: 0.8498, Energy: 1.996447-0.000536j
[2025-07-30 04:47:28] [Iter 40/4650] R0[39/150], Temp: 0.8423, Energy: 1.996363+0.000030j
[2025-07-30 04:47:43] [Iter 41/4650] R0[40/150], Temp: 0.8346, Energy: 1.996132-0.000360j
[2025-07-30 04:47:58] [Iter 42/4650] R0[41/150], Temp: 0.8267, Energy: 1.996600+0.000230j
[2025-07-30 04:48:13] [Iter 43/4650] R0[42/150], Temp: 0.8187, Energy: 1.995825+0.000154j
[2025-07-30 04:48:28] [Iter 44/4650] R0[43/150], Temp: 0.8106, Energy: 1.995037-0.000360j
[2025-07-30 04:48:42] [Iter 45/4650] R0[44/150], Temp: 0.8023, Energy: 1.994461-0.000011j
[2025-07-30 04:48:57] [Iter 46/4650] R0[45/150], Temp: 0.7939, Energy: 1.996039+0.001343j
[2025-07-30 04:49:12] [Iter 47/4650] R0[46/150], Temp: 0.7854, Energy: 1.994393+0.000311j
[2025-07-30 04:49:27] [Iter 48/4650] R0[47/150], Temp: 0.7767, Energy: 1.993595-0.000767j
[2025-07-30 04:49:41] [Iter 49/4650] R0[48/150], Temp: 0.7679, Energy: 1.994262+0.000995j
[2025-07-30 04:49:56] [Iter 50/4650] R0[49/150], Temp: 0.7590, Energy: 1.993810+0.000682j
[2025-07-30 04:50:11] [Iter 51/4650] R0[50/150], Temp: 0.7500, Energy: 1.993025+0.000440j
[2025-07-30 04:50:26] [Iter 52/4650] R0[51/150], Temp: 0.7409, Energy: 1.992414+0.000542j
[2025-07-30 04:50:40] [Iter 53/4650] R0[52/150], Temp: 0.7316, Energy: 1.990809+0.000292j
[2025-07-30 04:50:55] [Iter 54/4650] R0[53/150], Temp: 0.7223, Energy: 1.990197-0.000562j
[2025-07-30 04:51:10] [Iter 55/4650] R0[54/150], Temp: 0.7129, Energy: 1.989294-0.000051j
[2025-07-30 04:51:24] [Iter 56/4650] R0[55/150], Temp: 0.7034, Energy: 1.990224+0.001563j
[2025-07-30 04:51:39] [Iter 57/4650] R0[56/150], Temp: 0.6938, Energy: 1.986926-0.000591j
[2025-07-30 04:51:54] [Iter 58/4650] R0[57/150], Temp: 0.6841, Energy: 1.985827-0.001386j
[2025-07-30 04:52:09] [Iter 59/4650] R0[58/150], Temp: 0.6743, Energy: 1.985700+0.000502j
[2025-07-30 04:52:23] [Iter 60/4650] R0[59/150], Temp: 0.6644, Energy: 1.987633+0.003906j
[2025-07-30 04:52:38] [Iter 61/4650] R0[60/150], Temp: 0.6545, Energy: 1.982343-0.000376j
[2025-07-30 04:52:53] [Iter 62/4650] R0[61/150], Temp: 0.6445, Energy: 1.980908+0.000117j
[2025-07-30 04:53:08] [Iter 63/4650] R0[62/150], Temp: 0.6345, Energy: 1.979538+0.000515j
[2025-07-30 04:53:23] [Iter 64/4650] R0[63/150], Temp: 0.6243, Energy: 1.978179+0.000660j
[2025-07-30 04:53:37] [Iter 65/4650] R0[64/150], Temp: 0.6142, Energy: 1.975124-0.000802j
[2025-07-30 04:53:52] [Iter 66/4650] R0[65/150], Temp: 0.6040, Energy: 1.974334+0.000648j
[2025-07-30 04:54:07] [Iter 67/4650] R0[66/150], Temp: 0.5937, Energy: 1.971158-0.000111j
[2025-07-30 04:54:22] [Iter 68/4650] R0[67/150], Temp: 0.5834, Energy: 1.968464+0.000388j
[2025-07-30 04:54:37] [Iter 69/4650] R0[68/150], Temp: 0.5730, Energy: 1.965805+0.001200j
[2025-07-30 04:54:52] [Iter 70/4650] R0[69/150], Temp: 0.5627, Energy: 1.963087+0.000458j
[2025-07-30 04:55:06] [Iter 71/4650] R0[70/150], Temp: 0.5523, Energy: 1.958248+0.000553j
[2025-07-30 04:55:21] [Iter 72/4650] R0[71/150], Temp: 0.5418, Energy: 1.952442-0.000877j
[2025-07-30 04:55:36] [Iter 73/4650] R0[72/150], Temp: 0.5314, Energy: 1.949649+0.000348j
[2025-07-30 04:55:51] [Iter 74/4650] R0[73/150], Temp: 0.5209, Energy: 1.942594-0.001201j
[2025-07-30 04:56:06] [Iter 75/4650] R0[74/150], Temp: 0.5105, Energy: 1.938613+0.001827j
[2025-07-30 04:56:21] [Iter 76/4650] R0[75/150], Temp: 0.5000, Energy: 1.935135+0.005055j
[2025-07-30 04:56:36] [Iter 77/4650] R0[76/150], Temp: 0.4895, Energy: 1.923932-0.000843j
[2025-07-30 04:56:51] [Iter 78/4650] R0[77/150], Temp: 0.4791, Energy: 1.915060-0.000362j
[2025-07-30 04:57:06] [Iter 79/4650] R0[78/150], Temp: 0.4686, Energy: 1.910069+0.003442j
[2025-07-30 04:57:21] [Iter 80/4650] R0[79/150], Temp: 0.4582, Energy: 1.896218-0.001496j
[2025-07-30 04:57:35] [Iter 81/4650] R0[80/150], Temp: 0.4477, Energy: 1.886475-0.000474j
[2025-07-30 04:57:50] [Iter 82/4650] R0[81/150], Temp: 0.4373, Energy: 1.877824+0.001934j
[2025-07-30 04:58:05] [Iter 83/4650] R0[82/150], Temp: 0.4270, Energy: 1.860439-0.000979j
[2025-07-30 04:58:19] [Iter 84/4650] R0[83/150], Temp: 0.4166, Energy: 1.845469-0.001814j
[2025-07-30 04:58:34] [Iter 85/4650] R0[84/150], Temp: 0.4063, Energy: 1.829724+0.000376j
[2025-07-30 04:58:49] [Iter 86/4650] R0[85/150], Temp: 0.3960, Energy: 1.804992-0.007341j
[2025-07-30 04:59:04] [Iter 87/4650] R0[86/150], Temp: 0.3858, Energy: 1.799879+0.008714j
[2025-07-30 04:59:19] [Iter 88/4650] R0[87/150], Temp: 0.3757, Energy: 1.765537-0.003044j
[2025-07-30 04:59:34] [Iter 89/4650] R0[88/150], Temp: 0.3655, Energy: 1.742375+0.002953j
[2025-07-30 04:59:49] [Iter 90/4650] R0[89/150], Temp: 0.3555, Energy: 1.705945-0.005983j
[2025-07-30 05:00:04] [Iter 91/4650] R0[90/150], Temp: 0.3455, Energy: 1.673025-0.006086j
[2025-07-30 05:00:18] [Iter 92/4650] R0[91/150], Temp: 0.3356, Energy: 1.647791+0.000777j
[2025-07-30 05:00:33] [Iter 93/4650] R0[92/150], Temp: 0.3257, Energy: 1.616184+0.007010j
[2025-07-30 05:00:48] [Iter 94/4650] R0[93/150], Temp: 0.3159, Energy: 1.566979+0.000064j
[2025-07-30 05:01:03] [Iter 95/4650] R0[94/150], Temp: 0.3062, Energy: 1.516178+0.000094j
[2025-07-30 05:01:18] [Iter 96/4650] R0[95/150], Temp: 0.2966, Energy: 1.470696+0.006734j
[2025-07-30 05:01:32] [Iter 97/4650] R0[96/150], Temp: 0.2871, Energy: 1.418365+0.006659j
[2025-07-30 05:01:47] [Iter 98/4650] R0[97/150], Temp: 0.2777, Energy: 1.355483+0.009238j
[2025-07-30 05:02:02] [Iter 99/4650] R0[98/150], Temp: 0.2684, Energy: 1.279253+0.002214j
[2025-07-30 05:02:17] [Iter 100/4650] R0[99/150], Temp: 0.2591, Energy: 1.202774-0.006413j
[2025-07-30 05:02:32] [Iter 101/4650] R0[100/150], Temp: 0.2500, Energy: 1.105475-0.000437j
[2025-07-30 05:02:47] [Iter 102/4650] R0[101/150], Temp: 0.2410, Energy: 1.023905+0.004267j
[2025-07-30 05:03:02] [Iter 103/4650] R0[102/150], Temp: 0.2321, Energy: 0.907217+0.004552j
[2025-07-30 05:03:17] [Iter 104/4650] R0[103/150], Temp: 0.2233, Energy: 0.775180+0.002482j
[2025-07-30 05:03:32] [Iter 105/4650] R0[104/150], Temp: 0.2146, Energy: 0.658292+0.017507j
[2025-07-30 05:03:46] [Iter 106/4650] R0[105/150], Temp: 0.2061, Energy: 0.510798-0.002918j
[2025-07-30 05:04:02] [Iter 107/4650] R0[106/150], Temp: 0.1977, Energy: 0.355377+0.004334j
[2025-07-30 05:04:16] [Iter 108/4650] R0[107/150], Temp: 0.1894, Energy: 0.162513-0.008742j
[2025-07-30 05:04:31] [Iter 109/4650] R0[108/150], Temp: 0.1813, Energy: -0.011768+0.005662j
[2025-07-30 05:04:45] [Iter 110/4650] R0[109/150], Temp: 0.1733, Energy: -0.253531-0.000028j
[2025-07-30 05:04:59] [Iter 111/4650] R0[110/150], Temp: 0.1654, Energy: -0.447737+0.033682j
[2025-07-30 05:05:14] [Iter 112/4650] R0[111/150], Temp: 0.1577, Energy: -0.728830+0.009620j
[2025-07-30 05:05:29] [Iter 113/4650] R0[112/150], Temp: 0.1502, Energy: -0.970443+0.030296j
[2025-07-30 05:05:43] [Iter 114/4650] R0[113/150], Temp: 0.1428, Energy: -1.305934-0.004914j
[2025-07-30 05:05:58] [Iter 115/4650] R0[114/150], Temp: 0.1355, Energy: -1.626858+0.006474j
[2025-07-30 05:06:13] [Iter 116/4650] R0[115/150], Temp: 0.1284, Energy: -1.994138-0.029961j
[2025-07-30 05:06:28] [Iter 117/4650] R0[116/150], Temp: 0.1215, Energy: -2.359128+0.005716j
[2025-07-30 05:06:42] [Iter 118/4650] R0[117/150], Temp: 0.1147, Energy: -2.764768+0.020932j
[2025-07-30 05:06:57] [Iter 119/4650] R0[118/150], Temp: 0.1082, Energy: -3.222626+0.013071j
[2025-07-30 05:07:12] [Iter 120/4650] R0[119/150], Temp: 0.1017, Energy: -3.745952+0.029608j
[2025-07-30 05:07:27] [Iter 121/4650] R0[120/150], Temp: 0.0955, Energy: -4.217276+0.023886j
[2025-07-30 05:07:41] [Iter 122/4650] R0[121/150], Temp: 0.0894, Energy: -4.735596-0.002986j
[2025-07-30 05:07:56] [Iter 123/4650] R0[122/150], Temp: 0.0835, Energy: -5.311972+0.017486j
[2025-07-30 05:08:11] [Iter 124/4650] R0[123/150], Temp: 0.0778, Energy: -5.892938+0.047122j
[2025-07-30 05:08:25] [Iter 125/4650] R0[124/150], Temp: 0.0723, Energy: -6.593244+0.040264j
[2025-07-30 05:08:40] [Iter 126/4650] R0[125/150], Temp: 0.0670, Energy: -7.245402+0.012470j
[2025-07-30 05:08:55] [Iter 127/4650] R0[126/150], Temp: 0.0618, Energy: -7.968135+0.027204j
[2025-07-30 05:09:09] [Iter 128/4650] R0[127/150], Temp: 0.0569, Energy: -8.718386+0.073618j
[2025-07-30 05:09:24] [Iter 129/4650] R0[128/150], Temp: 0.0521, Energy: -9.525279+0.028272j
[2025-07-30 05:09:39] [Iter 130/4650] R0[129/150], Temp: 0.0476, Energy: -10.333615+0.060724j
[2025-07-30 05:09:53] [Iter 131/4650] R0[130/150], Temp: 0.0432, Energy: -11.268452+0.059752j
[2025-07-30 05:10:08] [Iter 132/4650] R0[131/150], Temp: 0.0391, Energy: -12.100615+0.110270j
[2025-07-30 05:10:23] [Iter 133/4650] R0[132/150], Temp: 0.0351, Energy: -13.131450+0.043265j
[2025-07-30 05:10:37] [Iter 134/4650] R0[133/150], Temp: 0.0314, Energy: -14.163927+0.098267j
[2025-07-30 05:10:52] [Iter 135/4650] R0[134/150], Temp: 0.0278, Energy: -15.293319+0.096443j
[2025-07-30 05:11:07] [Iter 136/4650] R0[135/150], Temp: 0.0245, Energy: -16.381952+0.086576j
[2025-07-30 05:11:21] [Iter 137/4650] R0[136/150], Temp: 0.0213, Energy: -17.540756+0.087663j
[2025-07-30 05:11:36] [Iter 138/4650] R0[137/150], Temp: 0.0184, Energy: -18.752138+0.183265j
[2025-07-30 05:11:51] [Iter 139/4650] R0[138/150], Temp: 0.0157, Energy: -19.893400+0.131229j
[2025-07-30 05:12:05] [Iter 140/4650] R0[139/150], Temp: 0.0132, Energy: -21.242352+0.185798j
[2025-07-30 05:12:20] [Iter 141/4650] R0[140/150], Temp: 0.0109, Energy: -22.446707+0.164162j
[2025-07-30 05:12:35] [Iter 142/4650] R0[141/150], Temp: 0.0089, Energy: -23.727734+0.333459j
[2025-07-30 05:12:49] [Iter 143/4650] R0[142/150], Temp: 0.0070, Energy: -24.991487+0.272436j
[2025-07-30 05:13:04] [Iter 144/4650] R0[143/150], Temp: 0.0054, Energy: -26.206281+0.170615j
[2025-07-30 05:13:19] [Iter 145/4650] R0[144/150], Temp: 0.0039, Energy: -27.426406+0.296431j
[2025-07-30 05:13:34] [Iter 146/4650] R0[145/150], Temp: 0.0027, Energy: -28.619666+0.344491j
[2025-07-30 05:13:49] [Iter 147/4650] R0[146/150], Temp: 0.0018, Energy: -29.838003+0.363934j
[2025-07-30 05:14:04] [Iter 148/4650] R0[147/150], Temp: 0.0010, Energy: -30.997183+0.303557j
[2025-07-30 05:14:19] [Iter 149/4650] R0[148/150], Temp: 0.0004, Energy: -32.086455+0.372044j
[2025-07-30 05:14:33] [Iter 150/4650] R0[149/150], Temp: 0.0001, Energy: -33.164700+0.305268j
[2025-07-30 05:14:33] RESTART #1 | Period: 300
[2025-07-30 05:14:48] [Iter 151/4650] R1[0/300], Temp: 1.0000, Energy: -34.308872+0.325733j
[2025-07-30 05:15:02] [Iter 152/4650] R1[1/300], Temp: 1.0000, Energy: -35.493818+0.304426j
[2025-07-30 05:15:17] [Iter 153/4650] R1[2/300], Temp: 0.9999, Energy: -36.549831+0.365464j
[2025-07-30 05:15:31] [Iter 154/4650] R1[3/300], Temp: 0.9998, Energy: -37.703512+0.335129j
[2025-07-30 05:15:46] [Iter 155/4650] R1[4/300], Temp: 0.9996, Energy: -38.849778+0.408329j
[2025-07-30 05:16:00] [Iter 156/4650] R1[5/300], Temp: 0.9993, Energy: -39.945528+0.545545j
[2025-07-30 05:16:15] [Iter 157/4650] R1[6/300], Temp: 0.9990, Energy: -41.205580+0.526415j
[2025-07-30 05:16:30] [Iter 158/4650] R1[7/300], Temp: 0.9987, Energy: -42.626518+0.574626j
[2025-07-30 05:16:45] [Iter 159/4650] R1[8/300], Temp: 0.9982, Energy: -44.141511+0.625200j
[2025-07-30 05:16:59] [Iter 160/4650] R1[9/300], Temp: 0.9978, Energy: -45.797365+0.595439j
[2025-07-30 05:17:14] [Iter 161/4650] R1[10/300], Temp: 0.9973, Energy: -47.748944+0.798460j
[2025-07-30 05:17:29] [Iter 162/4650] R1[11/300], Temp: 0.9967, Energy: -49.947339+1.007704j
[2025-07-30 05:17:43] [Iter 163/4650] R1[12/300], Temp: 0.9961, Energy: -52.263567+1.185865j
[2025-07-30 05:17:59] [Iter 164/4650] R1[13/300], Temp: 0.9954, Energy: -54.923682+1.357551j
[2025-07-30 05:18:13] [Iter 165/4650] R1[14/300], Temp: 0.9946, Energy: -57.206140+1.576500j
[2025-07-30 05:18:28] [Iter 166/4650] R1[15/300], Temp: 0.9938, Energy: -59.554722+1.552809j
[2025-07-30 05:18:43] [Iter 167/4650] R1[16/300], Temp: 0.9930, Energy: -61.786215+1.433254j
[2025-07-30 05:18:58] [Iter 168/4650] R1[17/300], Temp: 0.9921, Energy: -63.920957+1.239477j
[2025-07-30 05:19:12] [Iter 169/4650] R1[18/300], Temp: 0.9911, Energy: -65.954617+0.992722j
[2025-07-30 05:19:27] [Iter 170/4650] R1[19/300], Temp: 0.9901, Energy: -67.681772+0.915759j
[2025-07-30 05:19:42] [Iter 171/4650] R1[20/300], Temp: 0.9891, Energy: -69.601665+0.776842j
[2025-07-30 05:19:57] [Iter 172/4650] R1[21/300], Temp: 0.9880, Energy: -70.842977+0.631853j
[2025-07-30 05:20:11] [Iter 173/4650] R1[22/300], Temp: 0.9868, Energy: -71.947330+0.532786j
[2025-07-30 05:20:26] [Iter 174/4650] R1[23/300], Temp: 0.9856, Energy: -73.069336+0.487609j
[2025-07-30 05:20:41] [Iter 175/4650] R1[24/300], Temp: 0.9843, Energy: -73.925620+0.525996j
[2025-07-30 05:20:56] [Iter 176/4650] R1[25/300], Temp: 0.9830, Energy: -74.645068+0.440364j
[2025-07-30 05:21:11] [Iter 177/4650] R1[26/300], Temp: 0.9816, Energy: -75.418797+0.443964j
[2025-07-30 05:21:26] [Iter 178/4650] R1[27/300], Temp: 0.9801, Energy: -75.996056+0.313293j
[2025-07-30 05:21:41] [Iter 179/4650] R1[28/300], Temp: 0.9787, Energy: -76.349573+0.254676j
[2025-07-30 05:21:56] [Iter 180/4650] R1[29/300], Temp: 0.9771, Energy: -76.761062+0.152298j
[2025-07-30 05:22:11] [Iter 181/4650] R1[30/300], Temp: 0.9755, Energy: -77.124713+0.046184j
[2025-07-30 05:22:25] [Iter 182/4650] R1[31/300], Temp: 0.9739, Energy: -77.506611+0.006244j
[2025-07-30 05:22:40] [Iter 183/4650] R1[32/300], Temp: 0.9722, Energy: -77.762618-0.007224j
[2025-07-30 05:22:55] [Iter 184/4650] R1[33/300], Temp: 0.9704, Energy: -78.044276-0.055526j
[2025-07-30 05:23:09] [Iter 185/4650] R1[34/300], Temp: 0.9686, Energy: -78.335127-0.086593j
[2025-07-30 05:23:24] [Iter 186/4650] R1[35/300], Temp: 0.9668, Energy: -78.517598-0.018954j
[2025-07-30 05:23:39] [Iter 187/4650] R1[36/300], Temp: 0.9649, Energy: -78.806532-0.110545j
[2025-07-30 05:23:54] [Iter 188/4650] R1[37/300], Temp: 0.9629, Energy: -78.930948-0.045069j
[2025-07-30 05:24:08] [Iter 189/4650] R1[38/300], Temp: 0.9609, Energy: -79.083634-0.059217j
[2025-07-30 05:24:23] [Iter 190/4650] R1[39/300], Temp: 0.9589, Energy: -79.161004-0.044129j
[2025-07-30 05:24:37] [Iter 191/4650] R1[40/300], Temp: 0.9568, Energy: -79.358501+0.004062j
[2025-07-30 05:24:52] [Iter 192/4650] R1[41/300], Temp: 0.9546, Energy: -79.525512-0.019290j
[2025-07-30 05:25:07] [Iter 193/4650] R1[42/300], Temp: 0.9524, Energy: -79.620056-0.016139j
[2025-07-30 05:25:22] [Iter 194/4650] R1[43/300], Temp: 0.9502, Energy: -79.779247-0.026562j
[2025-07-30 05:25:37] [Iter 195/4650] R1[44/300], Temp: 0.9479, Energy: -79.873652-0.052125j
[2025-07-30 05:25:51] [Iter 196/4650] R1[45/300], Temp: 0.9455, Energy: -79.957163-0.036320j
[2025-07-30 05:26:06] [Iter 197/4650] R1[46/300], Temp: 0.9431, Energy: -80.001205-0.015807j
[2025-07-30 05:26:21] [Iter 198/4650] R1[47/300], Temp: 0.9407, Energy: -80.097095-0.024915j
[2025-07-30 05:26:36] [Iter 199/4650] R1[48/300], Temp: 0.9382, Energy: -80.173797-0.010877j
[2025-07-30 05:26:50] [Iter 200/4650] R1[49/300], Temp: 0.9356, Energy: -80.317086-0.022387j
[2025-07-30 05:27:05] [Iter 201/4650] R1[50/300], Temp: 0.9330, Energy: -80.318129-0.027436j
[2025-07-30 05:27:20] [Iter 202/4650] R1[51/300], Temp: 0.9304, Energy: -80.414480-0.035594j
[2025-07-30 05:27:35] [Iter 203/4650] R1[52/300], Temp: 0.9277, Energy: -80.510609-0.030313j
[2025-07-30 05:27:49] [Iter 204/4650] R1[53/300], Temp: 0.9249, Energy: -80.561256-0.034938j
[2025-07-30 05:28:04] [Iter 205/4650] R1[54/300], Temp: 0.9222, Energy: -80.722101-0.036483j
[2025-07-30 05:28:18] [Iter 206/4650] R1[55/300], Temp: 0.9193, Energy: -80.759975-0.014356j
[2025-07-30 05:28:33] [Iter 207/4650] R1[56/300], Temp: 0.9165, Energy: -80.797084-0.023889j
[2025-07-30 05:28:47] [Iter 208/4650] R1[57/300], Temp: 0.9135, Energy: -80.907485-0.036712j
[2025-07-30 05:29:02] [Iter 209/4650] R1[58/300], Temp: 0.9106, Energy: -80.937670-0.056013j
[2025-07-30 05:29:16] [Iter 210/4650] R1[59/300], Temp: 0.9076, Energy: -80.993560-0.062611j
[2025-07-30 05:29:31] [Iter 211/4650] R1[60/300], Temp: 0.9045, Energy: -81.081951-0.031867j
[2025-07-30 05:29:46] [Iter 212/4650] R1[61/300], Temp: 0.9014, Energy: -81.128763-0.000467j
[2025-07-30 05:30:00] [Iter 213/4650] R1[62/300], Temp: 0.8983, Energy: -81.215636-0.024215j
[2025-07-30 05:30:15] [Iter 214/4650] R1[63/300], Temp: 0.8951, Energy: -81.292103-0.005275j
[2025-07-30 05:30:30] [Iter 215/4650] R1[64/300], Temp: 0.8918, Energy: -81.346496-0.022865j
[2025-07-30 05:30:45] [Iter 216/4650] R1[65/300], Temp: 0.8886, Energy: -81.431268-0.028315j
[2025-07-30 05:31:00] [Iter 217/4650] R1[66/300], Temp: 0.8853, Energy: -81.396913-0.023769j
[2025-07-30 05:31:15] [Iter 218/4650] R1[67/300], Temp: 0.8819, Energy: -81.463135-0.016045j
[2025-07-30 05:31:30] [Iter 219/4650] R1[68/300], Temp: 0.8785, Energy: -81.514273-0.001676j
[2025-07-30 05:31:45] [Iter 220/4650] R1[69/300], Temp: 0.8751, Energy: -81.565450-0.052719j
[2025-07-30 05:31:59] [Iter 221/4650] R1[70/300], Temp: 0.8716, Energy: -81.567976-0.050118j
[2025-07-30 05:32:14] [Iter 222/4650] R1[71/300], Temp: 0.8680, Energy: -81.672507-0.054055j
[2025-07-30 05:32:29] [Iter 223/4650] R1[72/300], Temp: 0.8645, Energy: -81.683683-0.037757j
[2025-07-30 05:32:44] [Iter 224/4650] R1[73/300], Temp: 0.8609, Energy: -81.795485-0.030462j
[2025-07-30 05:32:58] [Iter 225/4650] R1[74/300], Temp: 0.8572, Energy: -81.826416-0.002950j
[2025-07-30 05:33:13] [Iter 226/4650] R1[75/300], Temp: 0.8536, Energy: -81.884197-0.032269j
[2025-07-30 05:33:27] [Iter 227/4650] R1[76/300], Temp: 0.8498, Energy: -81.957735-0.014909j
[2025-07-30 05:33:42] [Iter 228/4650] R1[77/300], Temp: 0.8461, Energy: -81.990658-0.029299j
[2025-07-30 05:33:57] [Iter 229/4650] R1[78/300], Temp: 0.8423, Energy: -82.042344-0.026050j
[2025-07-30 05:34:12] [Iter 230/4650] R1[79/300], Temp: 0.8384, Energy: -82.074304-0.036579j
[2025-07-30 05:34:26] [Iter 231/4650] R1[80/300], Temp: 0.8346, Energy: -82.110219-0.018292j
[2025-07-30 05:34:41] [Iter 232/4650] R1[81/300], Temp: 0.8307, Energy: -82.152641-0.041347j
[2025-07-30 05:34:55] [Iter 233/4650] R1[82/300], Temp: 0.8267, Energy: -82.244156-0.027429j
[2025-07-30 05:35:10] [Iter 234/4650] R1[83/300], Temp: 0.8227, Energy: -82.268979-0.027646j
[2025-07-30 05:35:25] [Iter 235/4650] R1[84/300], Temp: 0.8187, Energy: -82.303940-0.016923j
[2025-07-30 05:35:40] [Iter 236/4650] R1[85/300], Temp: 0.8147, Energy: -82.379479-0.020891j
[2025-07-30 05:35:55] [Iter 237/4650] R1[86/300], Temp: 0.8106, Energy: -82.387865-0.016848j
[2025-07-30 05:36:10] [Iter 238/4650] R1[87/300], Temp: 0.8065, Energy: -82.403410-0.018406j
[2025-07-30 05:36:24] [Iter 239/4650] R1[88/300], Temp: 0.8023, Energy: -82.507218-0.015662j
[2025-07-30 05:36:39] [Iter 240/4650] R1[89/300], Temp: 0.7981, Energy: -82.466499-0.004466j
[2025-07-30 05:36:54] [Iter 241/4650] R1[90/300], Temp: 0.7939, Energy: -82.491018-0.013843j
[2025-07-30 05:37:09] [Iter 242/4650] R1[91/300], Temp: 0.7896, Energy: -82.582211-0.011548j
[2025-07-30 05:37:24] [Iter 243/4650] R1[92/300], Temp: 0.7854, Energy: -82.634324-0.016658j
[2025-07-30 05:37:39] [Iter 244/4650] R1[93/300], Temp: 0.7810, Energy: -82.628893-0.015409j
[2025-07-30 05:37:53] [Iter 245/4650] R1[94/300], Temp: 0.7767, Energy: -82.689299-0.017895j
[2025-07-30 05:38:08] [Iter 246/4650] R1[95/300], Temp: 0.7723, Energy: -82.687632-0.014630j
[2025-07-30 05:38:22] [Iter 247/4650] R1[96/300], Temp: 0.7679, Energy: -82.731896-0.023119j
[2025-07-30 05:38:37] [Iter 248/4650] R1[97/300], Temp: 0.7635, Energy: -82.739689-0.007889j
[2025-07-30 05:38:52] [Iter 249/4650] R1[98/300], Temp: 0.7590, Energy: -82.752074-0.008451j
[2025-07-30 05:39:06] [Iter 250/4650] R1[99/300], Temp: 0.7545, Energy: -82.810755-0.026061j
[2025-07-30 05:39:21] [Iter 251/4650] R1[100/300], Temp: 0.7500, Energy: -82.851493-0.024388j
[2025-07-30 05:39:35] [Iter 252/4650] R1[101/300], Temp: 0.7455, Energy: -82.872282-0.024301j
[2025-07-30 05:39:50] [Iter 253/4650] R1[102/300], Temp: 0.7409, Energy: -82.908443-0.024487j
[2025-07-30 05:40:04] [Iter 254/4650] R1[103/300], Temp: 0.7363, Energy: -82.936130-0.027483j
[2025-07-30 05:40:18] [Iter 255/4650] R1[104/300], Temp: 0.7316, Energy: -82.998493-0.022175j
[2025-07-30 05:40:33] [Iter 256/4650] R1[105/300], Temp: 0.7270, Energy: -83.022962-0.022428j
[2025-07-30 05:40:48] [Iter 257/4650] R1[106/300], Temp: 0.7223, Energy: -83.017088-0.015926j
[2025-07-30 05:41:02] [Iter 258/4650] R1[107/300], Temp: 0.7176, Energy: -83.068580-0.012812j
[2025-07-30 05:41:17] [Iter 259/4650] R1[108/300], Temp: 0.7129, Energy: -83.047121-0.016329j
[2025-07-30 05:41:31] [Iter 260/4650] R1[109/300], Temp: 0.7081, Energy: -83.085982+0.004259j
[2025-07-30 05:41:46] [Iter 261/4650] R1[110/300], Temp: 0.7034, Energy: -83.068548-0.008200j
[2025-07-30 05:42:01] [Iter 262/4650] R1[111/300], Temp: 0.6986, Energy: -83.123612-0.013686j
[2025-07-30 05:42:16] [Iter 263/4650] R1[112/300], Temp: 0.6938, Energy: -83.184106-0.010424j
[2025-07-30 05:42:31] [Iter 264/4650] R1[113/300], Temp: 0.6889, Energy: -83.281945-0.013406j
[2025-07-30 05:42:46] [Iter 265/4650] R1[114/300], Temp: 0.6841, Energy: -83.316058-0.026579j
[2025-07-30 05:43:01] [Iter 266/4650] R1[115/300], Temp: 0.6792, Energy: -83.333194-0.014830j
[2025-07-30 05:43:16] [Iter 267/4650] R1[116/300], Temp: 0.6743, Energy: -83.341036-0.015298j
[2025-07-30 05:43:30] [Iter 268/4650] R1[117/300], Temp: 0.6694, Energy: -83.385840-0.017023j
[2025-07-30 05:43:45] [Iter 269/4650] R1[118/300], Temp: 0.6644, Energy: -83.329312-0.005322j
[2025-07-30 05:44:00] [Iter 270/4650] R1[119/300], Temp: 0.6595, Energy: -83.373676-0.008326j
[2025-07-30 05:44:14] [Iter 271/4650] R1[120/300], Temp: 0.6545, Energy: -83.398880-0.002219j
[2025-07-30 05:44:29] [Iter 272/4650] R1[121/300], Temp: 0.6495, Energy: -83.340900+0.006740j
[2025-07-30 05:44:43] [Iter 273/4650] R1[122/300], Temp: 0.6445, Energy: -83.342897-0.008681j
[2025-07-30 05:44:59] [Iter 274/4650] R1[123/300], Temp: 0.6395, Energy: -83.436492-0.002992j
[2025-07-30 05:45:13] [Iter 275/4650] R1[124/300], Temp: 0.6345, Energy: -83.510387-0.011872j
[2025-07-30 05:45:27] [Iter 276/4650] R1[125/300], Temp: 0.6294, Energy: -83.434457-0.003070j
[2025-07-30 05:45:42] [Iter 277/4650] R1[126/300], Temp: 0.6243, Energy: -83.398094+0.003901j
[2025-07-30 05:45:57] [Iter 278/4650] R1[127/300], Temp: 0.6193, Energy: -83.470244-0.011808j
[2025-07-30 05:46:11] [Iter 279/4650] R1[128/300], Temp: 0.6142, Energy: -83.460688-0.006574j
[2025-07-30 05:46:26] [Iter 280/4650] R1[129/300], Temp: 0.6091, Energy: -83.490016+0.004492j
[2025-07-30 05:46:40] [Iter 281/4650] R1[130/300], Temp: 0.6040, Energy: -83.424900-0.002731j
[2025-07-30 05:46:55] [Iter 282/4650] R1[131/300], Temp: 0.5988, Energy: -83.433827-0.007948j
[2025-07-30 05:47:10] [Iter 283/4650] R1[132/300], Temp: 0.5937, Energy: -83.485199+0.013757j
[2025-07-30 05:47:25] [Iter 284/4650] R1[133/300], Temp: 0.5885, Energy: -83.487409-0.009700j
[2025-07-30 05:47:40] [Iter 285/4650] R1[134/300], Temp: 0.5834, Energy: -83.467576+0.000959j
[2025-07-30 05:47:55] [Iter 286/4650] R1[135/300], Temp: 0.5782, Energy: -83.513865-0.011969j
[2025-07-30 05:48:10] [Iter 287/4650] R1[136/300], Temp: 0.5730, Energy: -83.515947-0.025322j
[2025-07-30 05:48:25] [Iter 288/4650] R1[137/300], Temp: 0.5679, Energy: -83.507611-0.036811j
[2025-07-30 05:48:39] [Iter 289/4650] R1[138/300], Temp: 0.5627, Energy: -83.549195-0.032403j
[2025-07-30 05:48:54] [Iter 290/4650] R1[139/300], Temp: 0.5575, Energy: -83.530816-0.021406j
[2025-07-30 05:49:09] [Iter 291/4650] R1[140/300], Temp: 0.5523, Energy: -83.632714-0.043867j
[2025-07-30 05:49:23] [Iter 292/4650] R1[141/300], Temp: 0.5471, Energy: -83.623008-0.031420j
[2025-07-30 05:49:37] [Iter 293/4650] R1[142/300], Temp: 0.5418, Energy: -83.624137-0.029024j
[2025-07-30 05:49:52] [Iter 294/4650] R1[143/300], Temp: 0.5366, Energy: -83.660648-0.024205j
[2025-07-30 05:50:07] [Iter 295/4650] R1[144/300], Temp: 0.5314, Energy: -83.651780-0.023749j
[2025-07-30 05:50:21] [Iter 296/4650] R1[145/300], Temp: 0.5262, Energy: -83.672295-0.012034j
[2025-07-30 05:50:35] [Iter 297/4650] R1[146/300], Temp: 0.5209, Energy: -83.599742-0.023090j
[2025-07-30 05:50:50] [Iter 298/4650] R1[147/300], Temp: 0.5157, Energy: -83.684645-0.008244j
[2025-07-30 05:51:05] [Iter 299/4650] R1[148/300], Temp: 0.5105, Energy: -83.664946-0.031557j
[2025-07-30 05:51:20] [Iter 300/4650] R1[149/300], Temp: 0.5052, Energy: -83.681710-0.017844j
[2025-07-30 05:51:35] [Iter 301/4650] R1[150/300], Temp: 0.5000, Energy: -83.660911-0.024664j
[2025-07-30 05:51:50] [Iter 302/4650] R1[151/300], Temp: 0.4948, Energy: -83.713644-0.035532j
[2025-07-30 05:52:04] [Iter 303/4650] R1[152/300], Temp: 0.4895, Energy: -83.658609-0.009444j
[2025-07-30 05:52:19] [Iter 304/4650] R1[153/300], Temp: 0.4843, Energy: -83.763707-0.048083j
[2025-07-30 05:52:34] [Iter 305/4650] R1[154/300], Temp: 0.4791, Energy: -83.758001-0.042172j
[2025-07-30 05:52:49] [Iter 306/4650] R1[155/300], Temp: 0.4738, Energy: -83.715368-0.052520j
[2025-07-30 05:53:04] [Iter 307/4650] R1[156/300], Temp: 0.4686, Energy: -83.711339-0.033543j
[2025-07-30 05:53:19] [Iter 308/4650] R1[157/300], Temp: 0.4634, Energy: -83.688378-0.019565j
[2025-07-30 05:53:33] [Iter 309/4650] R1[158/300], Temp: 0.4582, Energy: -83.776252-0.025519j
[2025-07-30 05:53:48] [Iter 310/4650] R1[159/300], Temp: 0.4529, Energy: -83.737373-0.019812j
[2025-07-30 05:54:02] [Iter 311/4650] R1[160/300], Temp: 0.4477, Energy: -83.774264-0.017593j
[2025-07-30 05:54:17] [Iter 312/4650] R1[161/300], Temp: 0.4425, Energy: -83.781322-0.005446j
[2025-07-30 05:54:32] [Iter 313/4650] R1[162/300], Temp: 0.4373, Energy: -83.741499-0.013855j
[2025-07-30 05:54:47] [Iter 314/4650] R1[163/300], Temp: 0.4321, Energy: -83.749170+0.001807j
[2025-07-30 05:55:01] [Iter 315/4650] R1[164/300], Temp: 0.4270, Energy: -83.804542+0.002841j
[2025-07-30 05:55:16] [Iter 316/4650] R1[165/300], Temp: 0.4218, Energy: -83.813220-0.015000j
[2025-07-30 05:55:31] [Iter 317/4650] R1[166/300], Temp: 0.4166, Energy: -83.803572-0.008267j
[2025-07-30 05:55:46] [Iter 318/4650] R1[167/300], Temp: 0.4115, Energy: -83.827025+0.022669j
[2025-07-30 05:56:01] [Iter 319/4650] R1[168/300], Temp: 0.4063, Energy: -83.776271-0.012463j
[2025-07-30 05:56:15] [Iter 320/4650] R1[169/300], Temp: 0.4012, Energy: -83.843707-0.000551j
[2025-07-30 05:56:30] [Iter 321/4650] R1[170/300], Temp: 0.3960, Energy: -83.885860-0.005541j
[2025-07-30 05:56:45] [Iter 322/4650] R1[171/300], Temp: 0.3909, Energy: -83.888278-0.015696j
[2025-07-30 05:56:59] [Iter 323/4650] R1[172/300], Temp: 0.3858, Energy: -83.877657-0.004107j
[2025-07-30 05:57:14] [Iter 324/4650] R1[173/300], Temp: 0.3807, Energy: -83.826065-0.002377j
[2025-07-30 05:57:29] [Iter 325/4650] R1[174/300], Temp: 0.3757, Energy: -83.856493-0.015063j
[2025-07-30 05:57:44] [Iter 326/4650] R1[175/300], Temp: 0.3706, Energy: -83.842158+0.008089j
[2025-07-30 05:57:59] [Iter 327/4650] R1[176/300], Temp: 0.3655, Energy: -83.927496+0.000024j
[2025-07-30 05:58:13] [Iter 328/4650] R1[177/300], Temp: 0.3605, Energy: -83.910517-0.002683j
[2025-07-30 05:58:27] [Iter 329/4650] R1[178/300], Temp: 0.3555, Energy: -83.838666-0.001476j
[2025-07-30 05:58:41] [Iter 330/4650] R1[179/300], Temp: 0.3505, Energy: -83.835797+0.029711j
[2025-07-30 05:58:56] [Iter 331/4650] R1[180/300], Temp: 0.3455, Energy: -83.782784+0.006076j
[2025-07-30 05:59:11] [Iter 332/4650] R1[181/300], Temp: 0.3405, Energy: -83.813231+0.000650j
[2025-07-30 05:59:27] [Iter 333/4650] R1[182/300], Temp: 0.3356, Energy: -83.868579-0.025275j
[2025-07-30 05:59:41] [Iter 334/4650] R1[183/300], Temp: 0.3306, Energy: -83.743462-0.028718j
[2025-07-30 05:59:56] [Iter 335/4650] R1[184/300], Temp: 0.3257, Energy: -83.876879+0.000961j
[2025-07-30 06:00:11] [Iter 336/4650] R1[185/300], Temp: 0.3208, Energy: -83.864167-0.010835j
[2025-07-30 06:00:25] [Iter 337/4650] R1[186/300], Temp: 0.3159, Energy: -83.868362+0.009233j
[2025-07-30 06:00:40] [Iter 338/4650] R1[187/300], Temp: 0.3111, Energy: -83.887841-0.012760j
[2025-07-30 06:00:55] [Iter 339/4650] R1[188/300], Temp: 0.3062, Energy: -83.828866+0.004992j
[2025-07-30 06:01:10] [Iter 340/4650] R1[189/300], Temp: 0.3014, Energy: -83.905606+0.029826j
[2025-07-30 06:01:25] [Iter 341/4650] R1[190/300], Temp: 0.2966, Energy: -83.896790+0.013734j
[2025-07-30 06:01:39] [Iter 342/4650] R1[191/300], Temp: 0.2919, Energy: -83.785858+0.029589j
[2025-07-30 06:01:53] [Iter 343/4650] R1[192/300], Temp: 0.2871, Energy: -83.816448+0.002876j
[2025-07-30 06:02:08] [Iter 344/4650] R1[193/300], Temp: 0.2824, Energy: -83.769644-0.012236j
[2025-07-30 06:02:23] [Iter 345/4650] R1[194/300], Temp: 0.2777, Energy: -83.815144-0.014747j
[2025-07-30 06:02:37] [Iter 346/4650] R1[195/300], Temp: 0.2730, Energy: -83.791058-0.008911j
[2025-07-30 06:02:52] [Iter 347/4650] R1[196/300], Temp: 0.2684, Energy: -83.826540+0.002974j
[2025-07-30 06:03:07] [Iter 348/4650] R1[197/300], Temp: 0.2637, Energy: -83.876212+0.000506j
[2025-07-30 06:03:22] [Iter 349/4650] R1[198/300], Temp: 0.2591, Energy: -83.863863+0.002225j
[2025-07-30 06:03:36] [Iter 350/4650] R1[199/300], Temp: 0.2545, Energy: -83.870343-0.012319j
[2025-07-30 06:03:51] [Iter 351/4650] R1[200/300], Temp: 0.2500, Energy: -83.911874+0.001973j
[2025-07-30 06:04:06] [Iter 352/4650] R1[201/300], Temp: 0.2455, Energy: -83.909480-0.003386j
[2025-07-30 06:04:20] [Iter 353/4650] R1[202/300], Temp: 0.2410, Energy: -83.914646-0.006185j
[2025-07-30 06:04:35] [Iter 354/4650] R1[203/300], Temp: 0.2365, Energy: -83.882486-0.003931j
[2025-07-30 06:04:50] [Iter 355/4650] R1[204/300], Temp: 0.2321, Energy: -83.877842-0.002995j
[2025-07-30 06:05:05] [Iter 356/4650] R1[205/300], Temp: 0.2277, Energy: -84.002667-0.004028j
[2025-07-30 06:05:20] [Iter 357/4650] R1[206/300], Temp: 0.2233, Energy: -83.968996+0.005193j
[2025-07-30 06:05:35] [Iter 358/4650] R1[207/300], Temp: 0.2190, Energy: -84.006722-0.004199j
[2025-07-30 06:05:49] [Iter 359/4650] R1[208/300], Temp: 0.2146, Energy: -83.993718-0.010658j
[2025-07-30 06:06:04] [Iter 360/4650] R1[209/300], Temp: 0.2104, Energy: -83.956785+0.001155j
[2025-07-30 06:06:19] [Iter 361/4650] R1[210/300], Temp: 0.2061, Energy: -84.015046-0.002477j
[2025-07-30 06:06:34] [Iter 362/4650] R1[211/300], Temp: 0.2019, Energy: -83.941646+0.000456j
[2025-07-30 06:06:48] [Iter 363/4650] R1[212/300], Temp: 0.1977, Energy: -83.947910-0.000725j
[2025-07-30 06:07:04] [Iter 364/4650] R1[213/300], Temp: 0.1935, Energy: -83.930270-0.002951j
[2025-07-30 06:07:19] [Iter 365/4650] R1[214/300], Temp: 0.1894, Energy: -83.947343+0.000638j
[2025-07-30 06:07:33] [Iter 366/4650] R1[215/300], Temp: 0.1853, Energy: -83.952029-0.003206j
[2025-07-30 06:07:48] [Iter 367/4650] R1[216/300], Temp: 0.1813, Energy: -83.976492-0.002288j
[2025-07-30 06:08:02] [Iter 368/4650] R1[217/300], Temp: 0.1773, Energy: -84.008341+0.001117j
[2025-07-30 06:08:16] [Iter 369/4650] R1[218/300], Temp: 0.1733, Energy: -83.989546-0.007500j
[2025-07-30 06:08:31] [Iter 370/4650] R1[219/300], Temp: 0.1693, Energy: -83.972836-0.003038j
[2025-07-30 06:08:45] [Iter 371/4650] R1[220/300], Temp: 0.1654, Energy: -83.970707-0.003738j
[2025-07-30 06:09:00] [Iter 372/4650] R1[221/300], Temp: 0.1616, Energy: -83.961366-0.003305j
[2025-07-30 06:09:14] [Iter 373/4650] R1[222/300], Temp: 0.1577, Energy: -84.028669-0.004571j
[2025-07-30 06:09:29] [Iter 374/4650] R1[223/300], Temp: 0.1539, Energy: -84.009269+0.002798j
[2025-07-30 06:09:43] [Iter 375/4650] R1[224/300], Temp: 0.1502, Energy: -84.012739+0.001593j
[2025-07-30 06:09:57] [Iter 376/4650] R1[225/300], Temp: 0.1464, Energy: -84.116005-0.004490j
[2025-07-30 06:10:12] [Iter 377/4650] R1[226/300], Temp: 0.1428, Energy: -84.051730+0.001330j
[2025-07-30 06:10:27] [Iter 378/4650] R1[227/300], Temp: 0.1391, Energy: -83.983396+0.001227j
[2025-07-30 06:10:41] [Iter 379/4650] R1[228/300], Temp: 0.1355, Energy: -84.005707-0.004584j
[2025-07-30 06:10:56] [Iter 380/4650] R1[229/300], Temp: 0.1320, Energy: -84.033280+0.001653j
[2025-07-30 06:11:10] [Iter 381/4650] R1[230/300], Temp: 0.1284, Energy: -84.064171-0.002498j
[2025-07-30 06:11:25] [Iter 382/4650] R1[231/300], Temp: 0.1249, Energy: -84.038997+0.018809j
[2025-07-30 06:11:40] [Iter 383/4650] R1[232/300], Temp: 0.1215, Energy: -84.031494-0.021218j
[2025-07-30 06:11:54] [Iter 384/4650] R1[233/300], Temp: 0.1181, Energy: -84.005547-0.031429j
[2025-07-30 06:12:09] [Iter 385/4650] R1[234/300], Temp: 0.1147, Energy: -83.937677-0.021193j
[2025-07-30 06:12:23] [Iter 386/4650] R1[235/300], Temp: 0.1114, Energy: -83.975994-0.010764j
[2025-07-30 06:12:38] [Iter 387/4650] R1[236/300], Temp: 0.1082, Energy: -83.985433-0.018482j
[2025-07-30 06:12:53] [Iter 388/4650] R1[237/300], Temp: 0.1049, Energy: -84.007642-0.005741j
[2025-07-30 06:13:07] [Iter 389/4650] R1[238/300], Temp: 0.1017, Energy: -84.069555-0.006586j
[2025-07-30 06:13:22] [Iter 390/4650] R1[239/300], Temp: 0.0986, Energy: -84.052847-0.008057j
[2025-07-30 06:13:37] [Iter 391/4650] R1[240/300], Temp: 0.0955, Energy: -84.102311-0.008680j
[2025-07-30 06:13:52] [Iter 392/4650] R1[241/300], Temp: 0.0924, Energy: -84.093416+0.005796j
[2025-07-30 06:14:07] [Iter 393/4650] R1[242/300], Temp: 0.0894, Energy: -84.054128-0.002060j
[2025-07-30 06:14:22] [Iter 394/4650] R1[243/300], Temp: 0.0865, Energy: -84.095705-0.002638j
[2025-07-30 06:14:37] [Iter 395/4650] R1[244/300], Temp: 0.0835, Energy: -84.125722-0.009696j
[2025-07-30 06:14:51] [Iter 396/4650] R1[245/300], Temp: 0.0807, Energy: -84.167811+0.001313j
[2025-07-30 06:15:06] [Iter 397/4650] R1[246/300], Temp: 0.0778, Energy: -83.846436-0.044654j
[2025-07-30 06:15:21] [Iter 398/4650] R1[247/300], Temp: 0.0751, Energy: -84.020947-0.066977j
[2025-07-30 06:15:35] [Iter 399/4650] R1[248/300], Temp: 0.0723, Energy: -84.016861-0.076549j
[2025-07-30 06:15:49] [Iter 400/4650] R1[249/300], Temp: 0.0696, Energy: -84.057223-0.071473j
[2025-07-30 06:16:04] [Iter 401/4650] R1[250/300], Temp: 0.0670, Energy: -84.078746-0.047222j
[2025-07-30 06:16:19] [Iter 402/4650] R1[251/300], Temp: 0.0644, Energy: -84.129860-0.033129j
[2025-07-30 06:16:33] [Iter 403/4650] R1[252/300], Temp: 0.0618, Energy: -84.091210-0.020984j
[2025-07-30 06:16:48] [Iter 404/4650] R1[253/300], Temp: 0.0593, Energy: -84.107370-0.016760j
[2025-07-30 06:17:03] [Iter 405/4650] R1[254/300], Temp: 0.0569, Energy: -84.095187-0.008402j
[2025-07-30 06:17:18] [Iter 406/4650] R1[255/300], Temp: 0.0545, Energy: -84.112664-0.034157j
[2025-07-30 06:17:32] [Iter 407/4650] R1[256/300], Temp: 0.0521, Energy: -84.149132-0.021344j
[2025-07-30 06:17:47] [Iter 408/4650] R1[257/300], Temp: 0.0498, Energy: -84.140543-0.023652j
[2025-07-30 06:18:02] [Iter 409/4650] R1[258/300], Temp: 0.0476, Energy: -84.151992-0.014983j
[2025-07-30 06:18:17] [Iter 410/4650] R1[259/300], Temp: 0.0454, Energy: -84.186554-0.014057j
[2025-07-30 06:18:32] [Iter 411/4650] R1[260/300], Temp: 0.0432, Energy: -84.216757-0.036201j
[2025-07-30 06:18:47] [Iter 412/4650] R1[261/300], Temp: 0.0411, Energy: -84.160855-0.027481j
[2025-07-30 06:19:02] [Iter 413/4650] R1[262/300], Temp: 0.0391, Energy: -84.163667-0.032452j
[2025-07-30 06:19:17] [Iter 414/4650] R1[263/300], Temp: 0.0371, Energy: -84.213868-0.019018j
[2025-07-30 06:19:31] [Iter 415/4650] R1[264/300], Temp: 0.0351, Energy: -84.302208-0.029929j
[2025-07-30 06:19:46] [Iter 416/4650] R1[265/300], Temp: 0.0332, Energy: -84.292736-0.028448j
[2025-07-30 06:20:00] [Iter 417/4650] R1[266/300], Temp: 0.0314, Energy: -84.251437-0.011334j
[2025-07-30 06:20:15] [Iter 418/4650] R1[267/300], Temp: 0.0296, Energy: -84.268298-0.013974j
[2025-07-30 06:20:30] [Iter 419/4650] R1[268/300], Temp: 0.0278, Energy: -84.313923-0.014413j
[2025-07-30 06:20:45] [Iter 420/4650] R1[269/300], Temp: 0.0261, Energy: -84.317227-0.027328j
[2025-07-30 06:20:59] [Iter 421/4650] R1[270/300], Temp: 0.0245, Energy: -84.346166-0.012827j
[2025-07-30 06:21:14] [Iter 422/4650] R1[271/300], Temp: 0.0229, Energy: -84.316254-0.026186j
[2025-07-30 06:21:28] [Iter 423/4650] R1[272/300], Temp: 0.0213, Energy: -84.288139-0.004905j
[2025-07-30 06:21:43] [Iter 424/4650] R1[273/300], Temp: 0.0199, Energy: -84.363962-0.013284j
[2025-07-30 06:21:58] [Iter 425/4650] R1[274/300], Temp: 0.0184, Energy: -84.253938-0.013346j
[2025-07-30 06:22:13] [Iter 426/4650] R1[275/300], Temp: 0.0170, Energy: -84.288791-0.009579j
[2025-07-30 06:22:28] [Iter 427/4650] R1[276/300], Temp: 0.0157, Energy: -84.194288-0.003301j
[2025-07-30 06:22:42] [Iter 428/4650] R1[277/300], Temp: 0.0144, Energy: -84.184614-0.007307j
[2025-07-30 06:22:57] [Iter 429/4650] R1[278/300], Temp: 0.0132, Energy: -84.180449-0.024226j
[2025-07-30 06:23:12] [Iter 430/4650] R1[279/300], Temp: 0.0120, Energy: -84.167044-0.029340j
[2025-07-30 06:23:27] [Iter 431/4650] R1[280/300], Temp: 0.0109, Energy: -84.212281-0.002115j
[2025-07-30 06:23:42] [Iter 432/4650] R1[281/300], Temp: 0.0099, Energy: -84.266475-0.002614j
[2025-07-30 06:23:57] [Iter 433/4650] R1[282/300], Temp: 0.0089, Energy: -84.320153-0.020829j
[2025-07-30 06:24:12] [Iter 434/4650] R1[283/300], Temp: 0.0079, Energy: -84.283950-0.021013j
[2025-07-30 06:24:27] [Iter 435/4650] R1[284/300], Temp: 0.0070, Energy: -84.315448-0.003158j
[2025-07-30 06:24:42] [Iter 436/4650] R1[285/300], Temp: 0.0062, Energy: -84.266410-0.060987j
[2025-07-30 06:24:57] [Iter 437/4650] R1[286/300], Temp: 0.0054, Energy: -84.189201-0.060507j
[2025-07-30 06:25:12] [Iter 438/4650] R1[287/300], Temp: 0.0046, Energy: -84.240132-0.037254j
[2025-07-30 06:25:27] [Iter 439/4650] R1[288/300], Temp: 0.0039, Energy: -84.216905-0.016065j
[2025-07-30 06:25:41] [Iter 440/4650] R1[289/300], Temp: 0.0033, Energy: -84.287297-0.057218j
[2025-07-30 06:25:56] [Iter 441/4650] R1[290/300], Temp: 0.0027, Energy: -84.224383-0.048843j
[2025-07-30 06:26:11] [Iter 442/4650] R1[291/300], Temp: 0.0022, Energy: -84.152162-0.070789j
[2025-07-30 06:26:26] [Iter 443/4650] R1[292/300], Temp: 0.0018, Energy: -84.247816-0.075912j
[2025-07-30 06:26:41] [Iter 444/4650] R1[293/300], Temp: 0.0013, Energy: -84.187132-0.109519j
[2025-07-30 06:26:56] [Iter 445/4650] R1[294/300], Temp: 0.0010, Energy: -84.178323-0.092540j
[2025-07-30 06:27:10] [Iter 446/4650] R1[295/300], Temp: 0.0007, Energy: -84.053596-0.130673j
[2025-07-30 06:27:25] [Iter 447/4650] R1[296/300], Temp: 0.0004, Energy: -84.021781-0.074526j
[2025-07-30 06:27:40] [Iter 448/4650] R1[297/300], Temp: 0.0002, Energy: -83.987078-0.153098j
[2025-07-30 06:27:54] [Iter 449/4650] R1[298/300], Temp: 0.0001, Energy: -83.947883-0.186118j
[2025-07-30 06:28:09] [Iter 450/4650] R1[299/300], Temp: 0.0000, Energy: -83.961740-0.149551j
[2025-07-30 06:28:09] RESTART #2 | Period: 600
[2025-07-30 06:28:24] [Iter 451/4650] R2[0/600], Temp: 1.0000, Energy: -83.861194-0.041649j
[2025-07-30 06:28:38] [Iter 452/4650] R2[1/600], Temp: 1.0000, Energy: -83.827795+0.031999j
[2025-07-30 06:28:53] [Iter 453/4650] R2[2/600], Temp: 1.0000, Energy: -83.773846-0.025242j
[2025-07-30 06:29:07] [Iter 454/4650] R2[3/600], Temp: 0.9999, Energy: -83.875153-0.021852j
[2025-07-30 06:29:22] [Iter 455/4650] R2[4/600], Temp: 0.9999, Energy: -83.530651+0.002843j
[2025-07-30 06:29:36] [Iter 456/4650] R2[5/600], Temp: 0.9998, Energy: -83.626086+0.249564j
[2025-07-30 06:29:51] [Iter 457/4650] R2[6/600], Temp: 0.9998, Energy: -83.432937+0.376868j
[2025-07-30 06:30:05] [Iter 458/4650] R2[7/600], Temp: 0.9997, Energy: -83.500216+0.686757j
[2025-07-30 06:30:20] [Iter 459/4650] R2[8/600], Temp: 0.9996, Energy: -83.191333+0.863719j
[2025-07-30 06:30:36] [Iter 460/4650] R2[9/600], Temp: 0.9994, Energy: -82.922797+0.921524j
[2025-07-30 06:30:50] [Iter 461/4650] R2[10/600], Temp: 0.9993, Energy: -82.791708+1.058297j
[2025-07-30 06:31:04] [Iter 462/4650] R2[11/600], Temp: 0.9992, Energy: -82.750926+0.867129j
[2025-07-30 06:31:18] [Iter 463/4650] R2[12/600], Temp: 0.9990, Energy: -82.751347+0.729215j
[2025-07-30 06:31:33] [Iter 464/4650] R2[13/600], Temp: 0.9988, Energy: -82.903461+0.647771j
[2025-07-30 06:31:48] [Iter 465/4650] R2[14/600], Temp: 0.9987, Energy: -83.036500+0.473405j
[2025-07-30 06:32:02] [Iter 466/4650] R2[15/600], Temp: 0.9985, Energy: -83.090589+0.450992j
[2025-07-30 06:32:17] [Iter 467/4650] R2[16/600], Temp: 0.9982, Energy: -83.193654+0.402379j
[2025-07-30 06:32:32] [Iter 468/4650] R2[17/600], Temp: 0.9980, Energy: -83.296481+0.405280j
[2025-07-30 06:32:47] [Iter 469/4650] R2[18/600], Temp: 0.9978, Energy: -83.391507+0.382123j
[2025-07-30 06:33:02] [Iter 470/4650] R2[19/600], Temp: 0.9975, Energy: -83.505882+0.330937j
[2025-07-30 06:33:17] [Iter 471/4650] R2[20/600], Temp: 0.9973, Energy: -83.654772+0.305142j
[2025-07-30 06:33:32] [Iter 472/4650] R2[21/600], Temp: 0.9970, Energy: -83.631322+0.235029j
[2025-07-30 06:33:47] [Iter 473/4650] R2[22/600], Temp: 0.9967, Energy: -83.714303+0.173534j
[2025-07-30 06:34:01] [Iter 474/4650] R2[23/600], Temp: 0.9964, Energy: -83.603706+0.174110j
[2025-07-30 06:34:16] [Iter 475/4650] R2[24/600], Temp: 0.9961, Energy: -83.684665+0.145262j
[2025-07-30 06:34:30] [Iter 476/4650] R2[25/600], Temp: 0.9957, Energy: -83.670678+0.110665j
[2025-07-30 06:34:45] [Iter 477/4650] R2[26/600], Temp: 0.9954, Energy: -83.596466+0.093635j
[2025-07-30 06:34:59] [Iter 478/4650] R2[27/600], Temp: 0.9950, Energy: -83.603050+0.082491j
[2025-07-30 06:35:14] [Iter 479/4650] R2[28/600], Temp: 0.9946, Energy: -83.650931-0.005596j
[2025-07-30 06:35:29] [Iter 480/4650] R2[29/600], Temp: 0.9942, Energy: -83.638771-0.040813j
[2025-07-30 06:35:43] [Iter 481/4650] R2[30/600], Temp: 0.9938, Energy: -83.676057-0.043896j
[2025-07-30 06:35:59] [Iter 482/4650] R2[31/600], Temp: 0.9934, Energy: -83.745293-0.084968j
[2025-07-30 06:36:13] [Iter 483/4650] R2[32/600], Temp: 0.9930, Energy: -83.765951-0.107236j
[2025-07-30 06:36:28] [Iter 484/4650] R2[33/600], Temp: 0.9926, Energy: -83.812045-0.101883j
[2025-07-30 06:36:42] [Iter 485/4650] R2[34/600], Temp: 0.9921, Energy: -83.820026-0.127108j
[2025-07-30 06:36:57] [Iter 486/4650] R2[35/600], Temp: 0.9916, Energy: -83.794144-0.094302j
[2025-07-30 06:37:12] [Iter 487/4650] R2[36/600], Temp: 0.9911, Energy: -83.797507-0.107557j
[2025-07-30 06:37:27] [Iter 488/4650] R2[37/600], Temp: 0.9906, Energy: -83.769841-0.147237j
[2025-07-30 06:37:42] [Iter 489/4650] R2[38/600], Temp: 0.9901, Energy: -83.833620-0.134715j
[2025-07-30 06:37:57] [Iter 490/4650] R2[39/600], Temp: 0.9896, Energy: -83.873006-0.133214j
[2025-07-30 06:38:12] [Iter 491/4650] R2[40/600], Temp: 0.9891, Energy: -83.874003-0.130317j
[2025-07-30 06:38:26] [Iter 492/4650] R2[41/600], Temp: 0.9885, Energy: -83.949846-0.154184j
[2025-07-30 06:38:41] [Iter 493/4650] R2[42/600], Temp: 0.9880, Energy: -84.006749-0.133933j
[2025-07-30 06:38:55] [Iter 494/4650] R2[43/600], Temp: 0.9874, Energy: -84.004267-0.120912j
[2025-07-30 06:39:09] [Iter 495/4650] R2[44/600], Temp: 0.9868, Energy: -84.021282-0.110630j
[2025-07-30 06:39:24] [Iter 496/4650] R2[45/600], Temp: 0.9862, Energy: -84.040849-0.137707j
[2025-07-30 06:39:39] [Iter 497/4650] R2[46/600], Temp: 0.9856, Energy: -84.042119-0.146244j
[2025-07-30 06:39:54] [Iter 498/4650] R2[47/600], Temp: 0.9849, Energy: -84.083302-0.138092j
[2025-07-30 06:40:09] [Iter 499/4650] R2[48/600], Temp: 0.9843, Energy: -84.098217-0.130644j
[2025-07-30 06:40:24] [Iter 500/4650] R2[49/600], Temp: 0.9836, Energy: -84.066901-0.146862j
[2025-07-30 06:40:24] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-07-30 06:40:38] [Iter 501/4650] R2[50/600], Temp: 0.9830, Energy: -84.164502-0.132594j
[2025-07-30 06:40:53] [Iter 502/4650] R2[51/600], Temp: 0.9823, Energy: -84.229891-0.121661j
[2025-07-30 06:41:08] [Iter 503/4650] R2[52/600], Temp: 0.9816, Energy: -84.216347-0.105242j
[2025-07-30 06:41:23] [Iter 504/4650] R2[53/600], Temp: 0.9809, Energy: -84.181134-0.121642j
[2025-07-30 06:41:37] [Iter 505/4650] R2[54/600], Temp: 0.9801, Energy: -84.224665-0.115775j
[2025-07-30 06:41:52] [Iter 506/4650] R2[55/600], Temp: 0.9794, Energy: -84.302586-0.112042j
[2025-07-30 06:42:07] [Iter 507/4650] R2[56/600], Temp: 0.9787, Energy: -84.298954-0.143426j
[2025-07-30 06:42:21] [Iter 508/4650] R2[57/600], Temp: 0.9779, Energy: -84.283894-0.151750j
[2025-07-30 06:42:36] [Iter 509/4650] R2[58/600], Temp: 0.9771, Energy: -84.280616-0.124413j
[2025-07-30 06:42:51] [Iter 510/4650] R2[59/600], Temp: 0.9763, Energy: -84.288272-0.115548j
[2025-07-30 06:43:06] [Iter 511/4650] R2[60/600], Temp: 0.9755, Energy: -84.272015-0.127361j
[2025-07-30 06:43:21] [Iter 512/4650] R2[61/600], Temp: 0.9747, Energy: -84.263634-0.157707j
[2025-07-30 06:43:35] [Iter 513/4650] R2[62/600], Temp: 0.9739, Energy: -84.251070-0.199662j
[2025-07-30 06:43:50] [Iter 514/4650] R2[63/600], Temp: 0.9730, Energy: -84.341014-0.123070j
[2025-07-30 06:44:05] [Iter 515/4650] R2[64/600], Temp: 0.9722, Energy: -84.302708-0.161311j
[2025-07-30 06:44:20] [Iter 516/4650] R2[65/600], Temp: 0.9713, Energy: -84.290459-0.129501j
[2025-07-30 06:44:34] [Iter 517/4650] R2[66/600], Temp: 0.9704, Energy: -84.235152-0.119830j
[2025-07-30 06:44:49] [Iter 518/4650] R2[67/600], Temp: 0.9695, Energy: -84.212892-0.153199j
[2025-07-30 06:45:03] [Iter 519/4650] R2[68/600], Temp: 0.9686, Energy: -84.188145-0.104252j
[2025-07-30 06:45:18] [Iter 520/4650] R2[69/600], Temp: 0.9677, Energy: -84.109987-0.163112j
[2025-07-30 06:45:33] [Iter 521/4650] R2[70/600], Temp: 0.9668, Energy: -84.158889-0.238943j
[2025-07-30 06:45:48] [Iter 522/4650] R2[71/600], Temp: 0.9658, Energy: -84.031335-0.224448j
[2025-07-30 06:46:03] [Iter 523/4650] R2[72/600], Temp: 0.9649, Energy: -83.879910-0.204596j
[2025-07-30 06:46:17] [Iter 524/4650] R2[73/600], Temp: 0.9639, Energy: -83.744956-0.298028j
[2025-07-30 06:46:32] [Iter 525/4650] R2[74/600], Temp: 0.9629, Energy: -83.540617-0.103398j
[2025-07-30 06:46:46] [Iter 526/4650] R2[75/600], Temp: 0.9619, Energy: -83.401733-0.171941j
[2025-07-30 06:47:01] [Iter 527/4650] R2[76/600], Temp: 0.9609, Energy: -82.887938-0.255854j
[2025-07-30 06:47:16] [Iter 528/4650] R2[77/600], Temp: 0.9599, Energy: -83.346089-0.276031j
[2025-07-30 06:47:31] [Iter 529/4650] R2[78/600], Temp: 0.9589, Energy: -82.698476-0.489790j
[2025-07-30 06:47:45] [Iter 530/4650] R2[79/600], Temp: 0.9578, Energy: -82.629443+0.252013j
[2025-07-30 06:47:59] [Iter 531/4650] R2[80/600], Temp: 0.9568, Energy: -82.407255+0.316331j
[2025-07-30 06:48:14] [Iter 532/4650] R2[81/600], Temp: 0.9557, Energy: -80.814209-0.205583j
[2025-07-30 06:48:29] [Iter 533/4650] R2[82/600], Temp: 0.9546, Energy: -80.351654-0.202687j
[2025-07-30 06:48:44] [Iter 534/4650] R2[83/600], Temp: 0.9535, Energy: -79.030835+0.310505j
[2025-07-30 06:48:58] [Iter 535/4650] R2[84/600], Temp: 0.9524, Energy: -76.327897+1.447593j
[2025-07-30 06:49:12] [Iter 536/4650] R2[85/600], Temp: 0.9513, Energy: -75.429747+1.615555j
[2025-07-30 06:49:28] [Iter 537/4650] R2[86/600], Temp: 0.9502, Energy: -74.728201+1.224227j
[2025-07-30 06:49:42] [Iter 538/4650] R2[87/600], Temp: 0.9490, Energy: -74.711640+1.115597j
[2025-07-30 06:49:57] [Iter 539/4650] R2[88/600], Temp: 0.9479, Energy: -74.487060+0.638244j
[2025-07-30 06:50:12] [Iter 540/4650] R2[89/600], Temp: 0.9467, Energy: -75.077414+0.515274j
[2025-07-30 06:50:27] [Iter 541/4650] R2[90/600], Temp: 0.9455, Energy: -74.616483-0.167392j
[2025-07-30 06:50:42] [Iter 542/4650] R2[91/600], Temp: 0.9443, Energy: -75.200467-0.377281j
[2025-07-30 06:50:56] [Iter 543/4650] R2[92/600], Temp: 0.9431, Energy: -75.260598-1.117227j
[2025-07-30 06:51:11] [Iter 544/4650] R2[93/600], Temp: 0.9419, Energy: -75.460094-0.618997j
[2025-07-30 06:51:25] [Iter 545/4650] R2[94/600], Temp: 0.9407, Energy: -75.280801-0.103486j
[2025-07-30 06:51:40] [Iter 546/4650] R2[95/600], Temp: 0.9394, Energy: -76.637373-0.461324j
[2025-07-30 06:51:54] [Iter 547/4650] R2[96/600], Temp: 0.9382, Energy: -77.934105+1.313813j
[2025-07-30 06:52:09] [Iter 548/4650] R2[97/600], Temp: 0.9369, Energy: -77.894828+0.043159j
[2025-07-30 06:52:24] [Iter 549/4650] R2[98/600], Temp: 0.9356, Energy: -79.294658-5.484832j
[2025-07-30 06:52:38] [Iter 550/4650] R2[99/600], Temp: 0.9343, Energy: -52.145719-6.433105j
[2025-07-30 06:52:53] [Iter 551/4650] R2[100/600], Temp: 0.9330, Energy: -61.993106-11.521442j
[2025-07-30 06:53:08] [Iter 552/4650] R2[101/600], Temp: 0.9317, Energy: -77.038638-0.626348j
[2025-07-30 06:53:23] [Iter 553/4650] R2[102/600], Temp: 0.9304, Energy: -75.479109-1.735076j
[2025-07-30 06:53:38] [Iter 554/4650] R2[103/600], Temp: 0.9290, Energy: -77.662022-1.414749j
[2025-07-30 06:53:53] [Iter 555/4650] R2[104/600], Temp: 0.9277, Energy: -78.744601-1.474163j
[2025-07-30 06:54:07] [Iter 556/4650] R2[105/600], Temp: 0.9263, Energy: -79.814618-1.506072j
[2025-07-30 06:54:22] [Iter 557/4650] R2[106/600], Temp: 0.9249, Energy: -80.369199-1.349792j
[2025-07-30 06:54:37] [Iter 558/4650] R2[107/600], Temp: 0.9236, Energy: -80.893609-1.407867j
[2025-07-30 06:54:52] [Iter 559/4650] R2[108/600], Temp: 0.9222, Energy: -81.264374-1.306427j
[2025-07-30 06:55:06] [Iter 560/4650] R2[109/600], Temp: 0.9208, Energy: -81.517308-1.322905j
[2025-07-30 06:55:21] [Iter 561/4650] R2[110/600], Temp: 0.9193, Energy: -81.725075-1.111970j
[2025-07-30 06:55:36] [Iter 562/4650] R2[111/600], Temp: 0.9179, Energy: -81.981439-1.053050j
[2025-07-30 06:55:50] [Iter 563/4650] R2[112/600], Temp: 0.9165, Energy: -82.145788-1.015569j
[2025-07-30 06:56:05] [Iter 564/4650] R2[113/600], Temp: 0.9150, Energy: -82.234965-0.943956j
[2025-07-30 06:56:19] [Iter 565/4650] R2[114/600], Temp: 0.9135, Energy: -82.361546-0.867376j
[2025-07-30 06:56:34] [Iter 566/4650] R2[115/600], Temp: 0.9121, Energy: -82.318299-0.875270j
[2025-07-30 06:56:49] [Iter 567/4650] R2[116/600], Temp: 0.9106, Energy: -82.345805-0.795223j
[2025-07-30 06:57:04] [Iter 568/4650] R2[117/600], Temp: 0.9091, Energy: -82.293223-0.732552j
[2025-07-30 06:57:19] [Iter 569/4650] R2[118/600], Temp: 0.9076, Energy: -82.218168-0.647351j
[2025-07-30 06:57:34] [Iter 570/4650] R2[119/600], Temp: 0.9060, Energy: -82.060484-0.265508j
[2025-07-30 06:57:49] [Iter 571/4650] R2[120/600], Temp: 0.9045, Energy: -82.152199+0.145465j
[2025-07-30 06:58:03] [Iter 572/4650] R2[121/600], Temp: 0.9030, Energy: -82.755883+0.854279j
[2025-07-30 06:58:18] [Iter 573/4650] R2[122/600], Temp: 0.9014, Energy: -82.941912+0.872852j
[2025-07-30 06:58:32] [Iter 574/4650] R2[123/600], Temp: 0.8998, Energy: -83.261858+0.783473j
[2025-07-30 06:58:47] [Iter 575/4650] R2[124/600], Temp: 0.8983, Energy: -82.697179+0.535743j
[2025-07-30 06:59:01] [Iter 576/4650] R2[125/600], Temp: 0.8967, Energy: -82.286912+0.233298j
[2025-07-30 06:59:16] [Iter 577/4650] R2[126/600], Temp: 0.8951, Energy: -81.241428-0.239825j
[2025-07-30 06:59:31] [Iter 578/4650] R2[127/600], Temp: 0.8935, Energy: -81.271688-0.500502j
[2025-07-30 06:59:46] [Iter 579/4650] R2[128/600], Temp: 0.8918, Energy: -80.665108-0.292885j
[2025-07-30 07:00:01] [Iter 580/4650] R2[129/600], Temp: 0.8902, Energy: -80.777601-0.534035j
[2025-07-30 07:00:15] [Iter 581/4650] R2[130/600], Temp: 0.8886, Energy: -80.958355-0.755797j
[2025-07-30 07:00:30] [Iter 582/4650] R2[131/600], Temp: 0.8869, Energy: -81.513317-1.009103j
[2025-07-30 07:00:44] [Iter 583/4650] R2[132/600], Temp: 0.8853, Energy: -81.603400-1.458186j
[2025-07-30 07:00:58] [Iter 584/4650] R2[133/600], Temp: 0.8836, Energy: -82.115283-1.330754j
[2025-07-30 07:01:13] [Iter 585/4650] R2[134/600], Temp: 0.8819, Energy: -82.508321-1.488517j
[2025-07-30 07:01:27] [Iter 586/4650] R2[135/600], Temp: 0.8802, Energy: -83.095420-1.423343j
[2025-07-30 07:01:42] [Iter 587/4650] R2[136/600], Temp: 0.8785, Energy: -83.339421-1.450374j
[2025-07-30 07:01:56] [Iter 588/4650] R2[137/600], Temp: 0.8768, Energy: -83.928517-1.341006j
[2025-07-30 07:02:10] [Iter 589/4650] R2[138/600], Temp: 0.8751, Energy: -84.128551-1.187029j
[2025-07-30 07:02:25] [Iter 590/4650] R2[139/600], Temp: 0.8733, Energy: -84.593445-0.997969j
[2025-07-30 07:02:40] [Iter 591/4650] R2[140/600], Temp: 0.8716, Energy: -84.632241-0.860589j
[2025-07-30 07:02:55] [Iter 592/4650] R2[141/600], Temp: 0.8698, Energy: -84.665323-0.701165j
[2025-07-30 07:03:10] [Iter 593/4650] R2[142/600], Temp: 0.8680, Energy: -84.616519-0.562427j
[2025-07-30 07:03:25] [Iter 594/4650] R2[143/600], Temp: 0.8663, Energy: -84.524590-0.485141j
[2025-07-30 07:03:40] [Iter 595/4650] R2[144/600], Temp: 0.8645, Energy: -84.499854-0.368219j
[2025-07-30 07:03:54] [Iter 596/4650] R2[145/600], Temp: 0.8627, Energy: -84.466343-0.280064j
[2025-07-30 07:04:09] [Iter 597/4650] R2[146/600], Temp: 0.8609, Energy: -84.398923-0.231794j
[2025-07-30 07:04:24] [Iter 598/4650] R2[147/600], Temp: 0.8591, Energy: -84.312369-0.160526j
[2025-07-30 07:04:38] [Iter 599/4650] R2[148/600], Temp: 0.8572, Energy: -84.288723-0.133382j
[2025-07-30 07:04:53] [Iter 600/4650] R2[149/600], Temp: 0.8554, Energy: -84.253349-0.113932j
[2025-07-30 07:05:08] [Iter 601/4650] R2[150/600], Temp: 0.8536, Energy: -84.228058-0.096309j
[2025-07-30 07:05:23] [Iter 602/4650] R2[151/600], Temp: 0.8517, Energy: -84.155728-0.066842j
[2025-07-30 07:05:38] [Iter 603/4650] R2[152/600], Temp: 0.8498, Energy: -84.137706-0.052191j
[2025-07-30 07:05:52] [Iter 604/4650] R2[153/600], Temp: 0.8480, Energy: -84.167496-0.040885j
[2025-07-30 07:06:07] [Iter 605/4650] R2[154/600], Temp: 0.8461, Energy: -84.209624-0.015784j
[2025-07-30 07:06:21] [Iter 606/4650] R2[155/600], Temp: 0.8442, Energy: -84.157763-0.005491j
[2025-07-30 07:06:36] [Iter 607/4650] R2[156/600], Temp: 0.8423, Energy: -84.146771-0.003671j
[2025-07-30 07:06:50] [Iter 608/4650] R2[157/600], Temp: 0.8404, Energy: -84.086058-0.004145j
[2025-07-30 07:07:04] [Iter 609/4650] R2[158/600], Temp: 0.8384, Energy: -84.103739+0.026038j
[2025-07-30 07:07:19] [Iter 610/4650] R2[159/600], Temp: 0.8365, Energy: -84.226715+0.010976j
[2025-07-30 07:07:33] [Iter 611/4650] R2[160/600], Temp: 0.8346, Energy: -84.224096+0.025893j
[2025-07-30 07:07:48] [Iter 612/4650] R2[161/600], Temp: 0.8326, Energy: -84.156682+0.029169j
[2025-07-30 07:08:03] [Iter 613/4650] R2[162/600], Temp: 0.8307, Energy: -84.180855+0.027697j
[2025-07-30 07:08:17] [Iter 614/4650] R2[163/600], Temp: 0.8287, Energy: -84.180370+0.038856j
[2025-07-30 07:08:32] [Iter 615/4650] R2[164/600], Temp: 0.8267, Energy: -84.146339+0.030024j
[2025-07-30 07:08:46] [Iter 616/4650] R2[165/600], Temp: 0.8247, Energy: -84.217154+0.018402j
[2025-07-30 07:09:01] [Iter 617/4650] R2[166/600], Temp: 0.8227, Energy: -84.223842+0.029300j
[2025-07-30 07:09:16] [Iter 618/4650] R2[167/600], Temp: 0.8207, Energy: -84.206318+0.049041j
[2025-07-30 07:09:31] [Iter 619/4650] R2[168/600], Temp: 0.8187, Energy: -84.185394+0.031970j
[2025-07-30 07:09:45] [Iter 620/4650] R2[169/600], Temp: 0.8167, Energy: -84.211330+0.015584j
[2025-07-30 07:10:00] [Iter 621/4650] R2[170/600], Temp: 0.8147, Energy: -84.167916+0.020938j
[2025-07-30 07:10:14] [Iter 622/4650] R2[171/600], Temp: 0.8126, Energy: -84.246156+0.022820j
[2025-07-30 07:10:29] [Iter 623/4650] R2[172/600], Temp: 0.8106, Energy: -84.298736+0.027814j
[2025-07-30 07:10:43] [Iter 624/4650] R2[173/600], Temp: 0.8085, Energy: -84.277141+0.013368j
[2025-07-30 07:10:58] [Iter 625/4650] R2[174/600], Temp: 0.8065, Energy: -84.283603+0.012676j
[2025-07-30 07:11:12] [Iter 626/4650] R2[175/600], Temp: 0.8044, Energy: -84.287979+0.036494j
[2025-07-30 07:11:27] [Iter 627/4650] R2[176/600], Temp: 0.8023, Energy: -84.268591+0.020436j
[2025-07-30 07:11:42] [Iter 628/4650] R2[177/600], Temp: 0.8002, Energy: -84.336691+0.031993j
[2025-07-30 07:11:57] [Iter 629/4650] R2[178/600], Temp: 0.7981, Energy: -84.283368+0.024610j
[2025-07-30 07:12:11] [Iter 630/4650] R2[179/600], Temp: 0.7960, Energy: -84.309860+0.033749j
[2025-07-30 07:12:26] [Iter 631/4650] R2[180/600], Temp: 0.7939, Energy: -84.321365+0.017466j
[2025-07-30 07:12:41] [Iter 632/4650] R2[181/600], Temp: 0.7918, Energy: -84.320850+0.034967j
[2025-07-30 07:12:56] [Iter 633/4650] R2[182/600], Temp: 0.7896, Energy: -84.275507-0.015750j
[2025-07-30 07:13:11] [Iter 634/4650] R2[183/600], Temp: 0.7875, Energy: -84.341366-0.006740j
[2025-07-30 07:13:25] [Iter 635/4650] R2[184/600], Temp: 0.7854, Energy: -84.390787+0.018178j
[2025-07-30 07:13:39] [Iter 636/4650] R2[185/600], Temp: 0.7832, Energy: -84.345882-0.004111j
[2025-07-30 07:13:54] [Iter 637/4650] R2[186/600], Temp: 0.7810, Energy: -84.380578-0.024712j
[2025-07-30 07:14:09] [Iter 638/4650] R2[187/600], Temp: 0.7789, Energy: -84.442445-0.003942j
[2025-07-30 07:14:24] [Iter 639/4650] R2[188/600], Temp: 0.7767, Energy: -84.475118-0.006743j
[2025-07-30 07:14:39] [Iter 640/4650] R2[189/600], Temp: 0.7745, Energy: -84.489155+0.006277j
[2025-07-30 07:14:53] [Iter 641/4650] R2[190/600], Temp: 0.7723, Energy: -84.467682+0.001147j
[2025-07-30 07:15:08] [Iter 642/4650] R2[191/600], Temp: 0.7701, Energy: -84.382861-0.029621j
[2025-07-30 07:15:23] [Iter 643/4650] R2[192/600], Temp: 0.7679, Energy: -84.371224-0.048640j
[2025-07-30 07:15:37] [Iter 644/4650] R2[193/600], Temp: 0.7657, Energy: -84.340726-0.039523j
[2025-07-30 07:15:52] [Iter 645/4650] R2[194/600], Temp: 0.7635, Energy: -84.476295-0.029181j
[2025-07-30 07:16:07] [Iter 646/4650] R2[195/600], Temp: 0.7612, Energy: -84.510631-0.063398j
[2025-07-30 07:16:21] [Iter 647/4650] R2[196/600], Temp: 0.7590, Energy: -84.463615-0.039156j
[2025-07-30 07:16:36] [Iter 648/4650] R2[197/600], Temp: 0.7568, Energy: -84.483886-0.065447j
[2025-07-30 07:16:51] [Iter 649/4650] R2[198/600], Temp: 0.7545, Energy: -84.521029-0.062343j
[2025-07-30 07:17:06] [Iter 650/4650] R2[199/600], Temp: 0.7523, Energy: -84.571081-0.068289j
[2025-07-30 07:17:20] [Iter 651/4650] R2[200/600], Temp: 0.7500, Energy: -84.608569-0.032565j
[2025-07-30 07:17:35] [Iter 652/4650] R2[201/600], Temp: 0.7477, Energy: -84.601228-0.075291j
[2025-07-30 07:17:50] [Iter 653/4650] R2[202/600], Temp: 0.7455, Energy: -84.580291-0.043040j
[2025-07-30 07:18:05] [Iter 654/4650] R2[203/600], Temp: 0.7432, Energy: -84.577858-0.056811j
[2025-07-30 07:18:19] [Iter 655/4650] R2[204/600], Temp: 0.7409, Energy: -84.514805-0.075204j
[2025-07-30 07:18:34] [Iter 656/4650] R2[205/600], Temp: 0.7386, Energy: -84.496504-0.070264j
[2025-07-30 07:18:49] [Iter 657/4650] R2[206/600], Temp: 0.7363, Energy: -84.452852-0.059503j
[2025-07-30 07:19:03] [Iter 658/4650] R2[207/600], Temp: 0.7340, Energy: -84.551355-0.078395j
[2025-07-30 07:19:18] [Iter 659/4650] R2[208/600], Temp: 0.7316, Energy: -84.514654-0.070347j
[2025-07-30 07:19:32] [Iter 660/4650] R2[209/600], Temp: 0.7293, Energy: -84.527592-0.051692j
[2025-07-30 07:19:47] [Iter 661/4650] R2[210/600], Temp: 0.7270, Energy: -84.499117-0.080504j
[2025-07-30 07:20:02] [Iter 662/4650] R2[211/600], Temp: 0.7247, Energy: -84.516950-0.068702j
[2025-07-30 07:20:17] [Iter 663/4650] R2[212/600], Temp: 0.7223, Energy: -84.507614-0.053533j
[2025-07-30 07:20:32] [Iter 664/4650] R2[213/600], Temp: 0.7200, Energy: -84.575038-0.057541j
[2025-07-30 07:20:46] [Iter 665/4650] R2[214/600], Temp: 0.7176, Energy: -84.522668-0.090044j
[2025-07-30 07:21:01] [Iter 666/4650] R2[215/600], Temp: 0.7153, Energy: -84.480325-0.103572j
[2025-07-30 07:21:15] [Iter 667/4650] R2[216/600], Temp: 0.7129, Energy: -84.485093-0.078428j
[2025-07-30 07:21:30] [Iter 668/4650] R2[217/600], Temp: 0.7105, Energy: -84.509168-0.090887j
[2025-07-30 07:21:44] [Iter 669/4650] R2[218/600], Temp: 0.7081, Energy: -84.476944-0.141656j
[2025-07-30 07:21:59] [Iter 670/4650] R2[219/600], Temp: 0.7058, Energy: -84.468466-0.084143j
[2025-07-30 07:22:14] [Iter 671/4650] R2[220/600], Temp: 0.7034, Energy: -84.515537-0.083633j
[2025-07-30 07:22:29] [Iter 672/4650] R2[221/600], Temp: 0.7010, Energy: -84.557163-0.117019j
[2025-07-30 07:22:43] [Iter 673/4650] R2[222/600], Temp: 0.6986, Energy: -84.524510-0.093560j
[2025-07-30 07:22:58] [Iter 674/4650] R2[223/600], Temp: 0.6962, Energy: -84.527113-0.158934j
[2025-07-30 07:23:13] [Iter 675/4650] R2[224/600], Temp: 0.6938, Energy: -84.546288-0.140092j
[2025-07-30 07:23:28] [Iter 676/4650] R2[225/600], Temp: 0.6913, Energy: -84.522946-0.180380j
[2025-07-30 07:23:43] [Iter 677/4650] R2[226/600], Temp: 0.6889, Energy: -84.452123-0.169794j
[2025-07-30 07:23:58] [Iter 678/4650] R2[227/600], Temp: 0.6865, Energy: -84.386310-0.211102j
[2025-07-30 07:24:13] [Iter 679/4650] R2[228/600], Temp: 0.6841, Energy: -84.306411-0.173678j
[2025-07-30 07:24:27] [Iter 680/4650] R2[229/600], Temp: 0.6816, Energy: -84.393861-0.187190j
[2025-07-30 07:24:42] [Iter 681/4650] R2[230/600], Temp: 0.6792, Energy: -84.248705-0.202315j
[2025-07-30 07:24:57] [Iter 682/4650] R2[231/600], Temp: 0.6767, Energy: -84.274843-0.208452j
[2025-07-30 07:25:12] [Iter 683/4650] R2[232/600], Temp: 0.6743, Energy: -84.177104-0.298905j
[2025-07-30 07:25:26] [Iter 684/4650] R2[233/600], Temp: 0.6718, Energy: -84.326488-0.201891j
[2025-07-30 07:25:41] [Iter 685/4650] R2[234/600], Temp: 0.6694, Energy: -84.233532-0.239217j
[2025-07-30 07:25:56] [Iter 686/4650] R2[235/600], Temp: 0.6669, Energy: -84.330720-0.240518j
[2025-07-30 07:26:11] [Iter 687/4650] R2[236/600], Temp: 0.6644, Energy: -84.354424-0.229548j
[2025-07-30 07:26:25] [Iter 688/4650] R2[237/600], Temp: 0.6620, Energy: -84.247530-0.180760j
[2025-07-30 07:26:40] [Iter 689/4650] R2[238/600], Temp: 0.6595, Energy: -84.333240-0.149728j
[2025-07-30 07:26:55] [Iter 690/4650] R2[239/600], Temp: 0.6570, Energy: -84.240888-0.130451j
[2025-07-30 07:27:10] [Iter 691/4650] R2[240/600], Temp: 0.6545, Energy: -84.265029-0.128993j
[2025-07-30 07:27:25] [Iter 692/4650] R2[241/600], Temp: 0.6520, Energy: -84.253299-0.123933j
[2025-07-30 07:27:40] [Iter 693/4650] R2[242/600], Temp: 0.6495, Energy: -84.284320-0.133991j
[2025-07-30 07:27:54] [Iter 694/4650] R2[243/600], Temp: 0.6470, Energy: -84.301668-0.101444j
[2025-07-30 07:28:08] [Iter 695/4650] R2[244/600], Temp: 0.6445, Energy: -84.211877-0.147942j
[2025-07-30 07:28:22] [Iter 696/4650] R2[245/600], Temp: 0.6420, Energy: -84.281387-0.113549j
[2025-07-30 07:28:37] [Iter 697/4650] R2[246/600], Temp: 0.6395, Energy: -84.258048-0.125693j
[2025-07-30 07:28:52] [Iter 698/4650] R2[247/600], Temp: 0.6370, Energy: -84.216923-0.104704j
[2025-07-30 07:29:07] [Iter 699/4650] R2[248/600], Temp: 0.6345, Energy: -84.162981-0.110847j
[2025-07-30 07:29:22] [Iter 700/4650] R2[249/600], Temp: 0.6319, Energy: -84.100121-0.112778j
[2025-07-30 07:29:36] [Iter 701/4650] R2[250/600], Temp: 0.6294, Energy: -84.097726-0.083811j
[2025-07-30 07:29:51] [Iter 702/4650] R2[251/600], Temp: 0.6269, Energy: -83.748424-0.012061j
[2025-07-30 07:30:05] [Iter 703/4650] R2[252/600], Temp: 0.6243, Energy: -83.805979-0.158362j
[2025-07-30 07:30:19] [Iter 704/4650] R2[253/600], Temp: 0.6218, Energy: -84.025929-0.177902j
[2025-07-30 07:30:34] [Iter 705/4650] R2[254/600], Temp: 0.6193, Energy: -84.061859-0.300661j
[2025-07-30 07:30:48] [Iter 706/4650] R2[255/600], Temp: 0.6167, Energy: -83.783316-0.353533j
[2025-07-30 07:31:03] [Iter 707/4650] R2[256/600], Temp: 0.6142, Energy: -83.849826-0.283738j
[2025-07-30 07:31:17] [Iter 708/4650] R2[257/600], Temp: 0.6116, Energy: -83.846061-0.290871j
[2025-07-30 07:31:32] [Iter 709/4650] R2[258/600], Temp: 0.6091, Energy: -83.727597-0.393967j
[2025-07-30 07:31:46] [Iter 710/4650] R2[259/600], Temp: 0.6065, Energy: -83.557428-0.458962j
[2025-07-30 07:32:01] [Iter 711/4650] R2[260/600], Temp: 0.6040, Energy: -83.458090-0.599979j
[2025-07-30 07:32:16] [Iter 712/4650] R2[261/600], Temp: 0.6014, Energy: -83.447835-0.583317j
[2025-07-30 07:32:30] [Iter 713/4650] R2[262/600], Temp: 0.5988, Energy: -83.342041-0.718500j
[2025-07-30 07:32:45] [Iter 714/4650] R2[263/600], Temp: 0.5963, Energy: -83.192114-0.608343j
[2025-07-30 07:32:59] [Iter 715/4650] R2[264/600], Temp: 0.5937, Energy: -83.129151-0.704034j
[2025-07-30 07:33:14] [Iter 716/4650] R2[265/600], Temp: 0.5911, Energy: -83.281268-0.538569j
[2025-07-30 07:33:29] [Iter 717/4650] R2[266/600], Temp: 0.5885, Energy: -83.346009-0.594621j
[2025-07-30 07:33:43] [Iter 718/4650] R2[267/600], Temp: 0.5860, Energy: -83.428710-0.544617j
[2025-07-30 07:33:58] [Iter 719/4650] R2[268/600], Temp: 0.5834, Energy: -83.453548-0.555832j
[2025-07-30 07:34:13] [Iter 720/4650] R2[269/600], Temp: 0.5808, Energy: -83.528690-0.610933j
[2025-07-30 07:34:27] [Iter 721/4650] R2[270/600], Temp: 0.5782, Energy: -83.571609-0.553727j
[2025-07-30 07:34:42] [Iter 722/4650] R2[271/600], Temp: 0.5756, Energy: -83.747045-0.623956j
[2025-07-30 07:34:56] [Iter 723/4650] R2[272/600], Temp: 0.5730, Energy: -83.778313-0.626162j
[2025-07-30 07:35:11] [Iter 724/4650] R2[273/600], Temp: 0.5705, Energy: -84.001328-0.592308j
[2025-07-30 07:35:26] [Iter 725/4650] R2[274/600], Temp: 0.5679, Energy: -84.052443-0.518788j
[2025-07-30 07:35:41] [Iter 726/4650] R2[275/600], Temp: 0.5653, Energy: -84.162248-0.485660j
[2025-07-30 07:35:55] [Iter 727/4650] R2[276/600], Temp: 0.5627, Energy: -84.160082-0.351216j
[2025-07-30 07:36:10] [Iter 728/4650] R2[277/600], Temp: 0.5601, Energy: -84.153951-0.377294j
[2025-07-30 07:36:24] [Iter 729/4650] R2[278/600], Temp: 0.5575, Energy: -84.138585-0.467396j
[2025-07-30 07:36:38] [Iter 730/4650] R2[279/600], Temp: 0.5549, Energy: -84.197803-0.317935j
[2025-07-30 07:36:53] [Iter 731/4650] R2[280/600], Temp: 0.5523, Energy: -83.964903-0.294344j
[2025-07-30 07:37:07] [Iter 732/4650] R2[281/600], Temp: 0.5497, Energy: -83.934308-0.309780j
[2025-07-30 07:37:22] [Iter 733/4650] R2[282/600], Temp: 0.5471, Energy: -83.827127-0.195682j
[2025-07-30 07:37:36] [Iter 734/4650] R2[283/600], Temp: 0.5444, Energy: -84.257332-0.031694j
[2025-07-30 07:37:51] [Iter 735/4650] R2[284/600], Temp: 0.5418, Energy: -84.277661+0.032182j
[2025-07-30 07:38:06] [Iter 736/4650] R2[285/600], Temp: 0.5392, Energy: -84.314766+0.016553j
[2025-07-30 07:38:22] [Iter 737/4650] R2[286/600], Temp: 0.5366, Energy: -84.412790-0.012105j
[2025-07-30 07:38:36] [Iter 738/4650] R2[287/600], Temp: 0.5340, Energy: -84.329403+0.022679j
[2025-07-30 07:38:51] [Iter 739/4650] R2[288/600], Temp: 0.5314, Energy: -84.318277-0.013407j
[2025-07-30 07:39:06] [Iter 740/4650] R2[289/600], Temp: 0.5288, Energy: -84.425396+0.031913j
[2025-07-30 07:39:20] [Iter 741/4650] R2[290/600], Temp: 0.5262, Energy: -84.404174+0.061073j
[2025-07-30 07:39:35] [Iter 742/4650] R2[291/600], Temp: 0.5236, Energy: -84.338470+0.039022j
[2025-07-30 07:39:50] [Iter 743/4650] R2[292/600], Temp: 0.5209, Energy: -84.185703+0.052704j
[2025-07-30 07:40:05] [Iter 744/4650] R2[293/600], Temp: 0.5183, Energy: -83.664325-0.001523j
[2025-07-30 07:40:20] [Iter 745/4650] R2[294/600], Temp: 0.5157, Energy: -83.767498-0.043086j
[2025-07-30 07:40:35] [Iter 746/4650] R2[295/600], Temp: 0.5131, Energy: -83.523948-0.110910j
[2025-07-30 07:40:49] [Iter 747/4650] R2[296/600], Temp: 0.5105, Energy: -83.247515-0.057142j
[2025-07-30 07:41:04] [Iter 748/4650] R2[297/600], Temp: 0.5079, Energy: -83.205669-0.017733j
[2025-07-30 07:41:18] [Iter 749/4650] R2[298/600], Temp: 0.5052, Energy: -83.326561+0.067767j
[2025-07-30 07:41:32] [Iter 750/4650] R2[299/600], Temp: 0.5026, Energy: -83.269025+0.050805j
[2025-07-30 07:41:47] [Iter 751/4650] R2[300/600], Temp: 0.5000, Energy: -82.892373+0.128967j
[2025-07-30 07:42:02] [Iter 752/4650] R2[301/600], Temp: 0.4974, Energy: -83.585671+0.108929j
[2025-07-30 07:42:17] [Iter 753/4650] R2[302/600], Temp: 0.4948, Energy: -83.738766+0.123718j
[2025-07-30 07:42:31] [Iter 754/4650] R2[303/600], Temp: 0.4921, Energy: -83.975802+0.075204j
[2025-07-30 07:42:46] [Iter 755/4650] R2[304/600], Temp: 0.4895, Energy: -84.058524+0.073368j
[2025-07-30 07:43:01] [Iter 756/4650] R2[305/600], Temp: 0.4869, Energy: -84.133733+0.059192j
[2025-07-30 07:43:16] [Iter 757/4650] R2[306/600], Temp: 0.4843, Energy: -84.163552+0.045828j
[2025-07-30 07:43:31] [Iter 758/4650] R2[307/600], Temp: 0.4817, Energy: -84.329359+0.039485j
[2025-07-30 07:43:46] [Iter 759/4650] R2[308/600], Temp: 0.4791, Energy: -84.397212+0.041233j
[2025-07-30 07:44:00] [Iter 760/4650] R2[309/600], Temp: 0.4764, Energy: -84.369718+0.018894j
[2025-07-30 07:44:15] [Iter 761/4650] R2[310/600], Temp: 0.4738, Energy: -84.394461+0.001047j
[2025-07-30 07:44:29] [Iter 762/4650] R2[311/600], Temp: 0.4712, Energy: -84.354411-0.011482j
[2025-07-30 07:44:44] [Iter 763/4650] R2[312/600], Temp: 0.4686, Energy: -84.480654-0.015098j
[2025-07-30 07:44:59] [Iter 764/4650] R2[313/600], Temp: 0.4660, Energy: -84.550259-0.020476j
[2025-07-30 07:45:13] [Iter 765/4650] R2[314/600], Temp: 0.4634, Energy: -84.523105-0.018375j
[2025-07-30 07:45:28] [Iter 766/4650] R2[315/600], Temp: 0.4608, Energy: -84.528619-0.016977j
[2025-07-30 07:45:42] [Iter 767/4650] R2[316/600], Temp: 0.4582, Energy: -84.566234-0.005722j
[2025-07-30 07:45:57] [Iter 768/4650] R2[317/600], Temp: 0.4556, Energy: -84.547288-0.014941j
[2025-07-30 07:46:12] [Iter 769/4650] R2[318/600], Temp: 0.4529, Energy: -84.604351-0.020762j
[2025-07-30 07:46:27] [Iter 770/4650] R2[319/600], Temp: 0.4503, Energy: -84.546564-0.031527j
[2025-07-30 07:46:42] [Iter 771/4650] R2[320/600], Temp: 0.4477, Energy: -84.588127-0.028759j
[2025-07-30 07:46:57] [Iter 772/4650] R2[321/600], Temp: 0.4451, Energy: -84.538913-0.024667j
[2025-07-30 07:47:11] [Iter 773/4650] R2[322/600], Temp: 0.4425, Energy: -84.592043-0.045845j
[2025-07-30 07:47:26] [Iter 774/4650] R2[323/600], Temp: 0.4399, Energy: -84.527514-0.049456j
[2025-07-30 07:47:41] [Iter 775/4650] R2[324/600], Temp: 0.4373, Energy: -84.471160-0.085928j
[2025-07-30 07:47:55] [Iter 776/4650] R2[325/600], Temp: 0.4347, Energy: -84.310954-0.079032j
[2025-07-30 07:48:10] [Iter 777/4650] R2[326/600], Temp: 0.4321, Energy: -84.450346-0.085994j
[2025-07-30 07:48:25] [Iter 778/4650] R2[327/600], Temp: 0.4295, Energy: -84.514657-0.047317j
[2025-07-30 07:48:39] [Iter 779/4650] R2[328/600], Temp: 0.4270, Energy: -84.530347-0.067410j
[2025-07-30 07:48:54] [Iter 780/4650] R2[329/600], Temp: 0.4244, Energy: -84.546780-0.050283j
[2025-07-30 07:49:09] [Iter 781/4650] R2[330/600], Temp: 0.4218, Energy: -84.489765-0.030765j
[2025-07-30 07:49:24] [Iter 782/4650] R2[331/600], Temp: 0.4192, Energy: -84.468282-0.020124j
[2025-07-30 07:49:39] [Iter 783/4650] R2[332/600], Temp: 0.4166, Energy: -84.437994-0.080831j
[2025-07-30 07:49:54] [Iter 784/4650] R2[333/600], Temp: 0.4140, Energy: -84.369343-0.047233j
[2025-07-30 07:50:08] [Iter 785/4650] R2[334/600], Temp: 0.4115, Energy: -84.323676-0.047612j
[2025-07-30 07:50:23] [Iter 786/4650] R2[335/600], Temp: 0.4089, Energy: -84.185331-0.006184j
[2025-07-30 07:50:38] [Iter 787/4650] R2[336/600], Temp: 0.4063, Energy: -84.150532-0.008791j
[2025-07-30 07:50:53] [Iter 788/4650] R2[337/600], Temp: 0.4037, Energy: -84.143952-0.014954j
[2025-07-30 07:51:08] [Iter 789/4650] R2[338/600], Temp: 0.4012, Energy: -83.782928-0.086941j
[2025-07-30 07:51:23] [Iter 790/4650] R2[339/600], Temp: 0.3986, Energy: -84.205604-0.041228j
[2025-07-30 07:51:37] [Iter 791/4650] R2[340/600], Temp: 0.3960, Energy: -84.008905-0.067400j
[2025-07-30 07:51:51] [Iter 792/4650] R2[341/600], Temp: 0.3935, Energy: -84.211850+0.026352j
[2025-07-30 07:52:06] [Iter 793/4650] R2[342/600], Temp: 0.3909, Energy: -84.200536-0.031378j
[2025-07-30 07:52:21] [Iter 794/4650] R2[343/600], Temp: 0.3884, Energy: -84.028826-0.011074j
[2025-07-30 07:52:36] [Iter 795/4650] R2[344/600], Temp: 0.3858, Energy: -84.159718-0.033013j
[2025-07-30 07:52:51] [Iter 796/4650] R2[345/600], Temp: 0.3833, Energy: -84.180403-0.012412j
[2025-07-30 07:53:06] [Iter 797/4650] R2[346/600], Temp: 0.3807, Energy: -84.069505-0.003519j
[2025-07-30 07:53:21] [Iter 798/4650] R2[347/600], Temp: 0.3782, Energy: -84.230048+0.015197j
[2025-07-30 07:53:35] [Iter 799/4650] R2[348/600], Temp: 0.3757, Energy: -84.336683+0.051537j
[2025-07-30 07:53:50] [Iter 800/4650] R2[349/600], Temp: 0.3731, Energy: -84.396628+0.092706j
[2025-07-30 07:54:05] [Iter 801/4650] R2[350/600], Temp: 0.3706, Energy: -84.440039+0.087008j
[2025-07-30 07:54:20] [Iter 802/4650] R2[351/600], Temp: 0.3681, Energy: -84.465189+0.078591j
[2025-07-30 07:54:35] [Iter 803/4650] R2[352/600], Temp: 0.3655, Energy: -84.491776+0.039037j
[2025-07-30 07:54:49] [Iter 804/4650] R2[353/600], Temp: 0.3630, Energy: -84.443330+0.042475j
[2025-07-30 07:55:04] [Iter 805/4650] R2[354/600], Temp: 0.3605, Energy: -84.448073+0.065105j
[2025-07-30 07:55:19] [Iter 806/4650] R2[355/600], Temp: 0.3580, Energy: -84.488481+0.035551j
[2025-07-30 07:55:33] [Iter 807/4650] R2[356/600], Temp: 0.3555, Energy: -84.448567-0.015871j
[2025-07-30 07:55:48] [Iter 808/4650] R2[357/600], Temp: 0.3530, Energy: -84.460044-0.030965j
[2025-07-30 07:56:03] [Iter 809/4650] R2[358/600], Temp: 0.3505, Energy: -84.405733-0.039133j
[2025-07-30 07:56:18] [Iter 810/4650] R2[359/600], Temp: 0.3480, Energy: -84.465576-0.015602j
[2025-07-30 07:56:33] [Iter 811/4650] R2[360/600], Temp: 0.3455, Energy: -84.560906+0.021412j
[2025-07-30 07:56:48] [Iter 812/4650] R2[361/600], Temp: 0.3430, Energy: -84.523066+0.025121j
[2025-07-30 07:57:03] [Iter 813/4650] R2[362/600], Temp: 0.3405, Energy: -84.482157+0.042949j
[2025-07-30 07:57:17] [Iter 814/4650] R2[363/600], Temp: 0.3380, Energy: -84.556076+0.022684j
[2025-07-30 07:57:32] [Iter 815/4650] R2[364/600], Temp: 0.3356, Energy: -84.553167+0.024527j
[2025-07-30 07:57:47] [Iter 816/4650] R2[365/600], Temp: 0.3331, Energy: -84.592525+0.037116j
[2025-07-30 07:58:02] [Iter 817/4650] R2[366/600], Temp: 0.3306, Energy: -84.598021+0.018224j
[2025-07-30 07:58:17] [Iter 818/4650] R2[367/600], Temp: 0.3282, Energy: -84.539383+0.020760j
[2025-07-30 07:58:31] [Iter 819/4650] R2[368/600], Temp: 0.3257, Energy: -84.558030+0.017171j
[2025-07-30 07:58:46] [Iter 820/4650] R2[369/600], Temp: 0.3233, Energy: -84.641750+0.022761j
[2025-07-30 07:59:01] [Iter 821/4650] R2[370/600], Temp: 0.3208, Energy: -84.622098+0.012336j
[2025-07-30 07:59:15] [Iter 822/4650] R2[371/600], Temp: 0.3184, Energy: -84.616538+0.025960j
[2025-07-30 07:59:30] [Iter 823/4650] R2[372/600], Temp: 0.3159, Energy: -84.587864+0.011316j
[2025-07-30 07:59:44] [Iter 824/4650] R2[373/600], Temp: 0.3135, Energy: -84.684131+0.017463j
[2025-07-30 07:59:58] [Iter 825/4650] R2[374/600], Temp: 0.3111, Energy: -84.616607+0.007297j
[2025-07-30 08:00:13] [Iter 826/4650] R2[375/600], Temp: 0.3087, Energy: -84.626510+0.009408j
[2025-07-30 08:00:27] [Iter 827/4650] R2[376/600], Temp: 0.3062, Energy: -84.676742+0.011429j
[2025-07-30 08:00:42] [Iter 828/4650] R2[377/600], Temp: 0.3038, Energy: -84.642706-0.004582j
[2025-07-30 08:00:57] [Iter 829/4650] R2[378/600], Temp: 0.3014, Energy: -84.665233-0.007243j
[2025-07-30 08:01:11] [Iter 830/4650] R2[379/600], Temp: 0.2990, Energy: -84.818443+0.014973j
[2025-07-30 08:01:26] [Iter 831/4650] R2[380/600], Temp: 0.2966, Energy: -84.803247+0.004170j
[2025-07-30 08:01:41] [Iter 832/4650] R2[381/600], Temp: 0.2942, Energy: -84.755406-0.006919j
[2025-07-30 08:01:56] [Iter 833/4650] R2[382/600], Temp: 0.2919, Energy: -84.858667+0.001217j
[2025-07-30 08:02:11] [Iter 834/4650] R2[383/600], Temp: 0.2895, Energy: -84.831249+0.006817j
[2025-07-30 08:02:25] [Iter 835/4650] R2[384/600], Temp: 0.2871, Energy: -84.819529-0.008030j
[2025-07-30 08:02:40] [Iter 836/4650] R2[385/600], Temp: 0.2847, Energy: -84.851903+0.015029j
[2025-07-30 08:02:55] [Iter 837/4650] R2[386/600], Temp: 0.2824, Energy: -84.835013-0.006627j
[2025-07-30 08:03:10] [Iter 838/4650] R2[387/600], Temp: 0.2800, Energy: -84.736885+0.004701j
[2025-07-30 08:03:25] [Iter 839/4650] R2[388/600], Temp: 0.2777, Energy: -84.750432-0.001292j
[2025-07-30 08:03:40] [Iter 840/4650] R2[389/600], Temp: 0.2753, Energy: -84.792853-0.010544j
[2025-07-30 08:03:54] [Iter 841/4650] R2[390/600], Temp: 0.2730, Energy: -84.812793-0.002951j
[2025-07-30 08:04:09] [Iter 842/4650] R2[391/600], Temp: 0.2707, Energy: -84.768179-0.017128j
[2025-07-30 08:04:24] [Iter 843/4650] R2[392/600], Temp: 0.2684, Energy: -84.741453+0.001413j
[2025-07-30 08:04:39] [Iter 844/4650] R2[393/600], Temp: 0.2660, Energy: -84.849366+0.017494j
[2025-07-30 08:04:53] [Iter 845/4650] R2[394/600], Temp: 0.2637, Energy: -84.732996-0.001251j
[2025-07-30 08:05:08] [Iter 846/4650] R2[395/600], Temp: 0.2614, Energy: -84.776852+0.009178j
[2025-07-30 08:05:23] [Iter 847/4650] R2[396/600], Temp: 0.2591, Energy: -84.570759-0.002175j
[2025-07-30 08:05:38] [Iter 848/4650] R2[397/600], Temp: 0.2568, Energy: -84.756454-0.020674j
[2025-07-30 08:05:53] [Iter 849/4650] R2[398/600], Temp: 0.2545, Energy: -84.697310-0.018730j
[2025-07-30 08:06:08] [Iter 850/4650] R2[399/600], Temp: 0.2523, Energy: -84.665600+0.007090j
[2025-07-30 08:06:23] [Iter 851/4650] R2[400/600], Temp: 0.2500, Energy: -84.495296+0.020267j
[2025-07-30 08:06:38] [Iter 852/4650] R2[401/600], Temp: 0.2477, Energy: -84.541847+0.013070j
[2025-07-30 08:06:52] [Iter 853/4650] R2[402/600], Temp: 0.2455, Energy: -84.767401-0.005800j
[2025-07-30 08:07:07] [Iter 854/4650] R2[403/600], Temp: 0.2432, Energy: -84.696049-0.008239j
[2025-07-30 08:07:22] [Iter 855/4650] R2[404/600], Temp: 0.2410, Energy: -84.732298-0.012576j
[2025-07-30 08:07:37] [Iter 856/4650] R2[405/600], Temp: 0.2388, Energy: -84.789027-0.006586j
[2025-07-30 08:07:52] [Iter 857/4650] R2[406/600], Temp: 0.2365, Energy: -84.778191-0.000030j
[2025-07-30 08:08:06] [Iter 858/4650] R2[407/600], Temp: 0.2343, Energy: -84.704397+0.010563j
[2025-07-30 08:08:21] [Iter 859/4650] R2[408/600], Temp: 0.2321, Energy: -84.703288-0.003203j
[2025-07-30 08:08:36] [Iter 860/4650] R2[409/600], Temp: 0.2299, Energy: -84.719856-0.002776j
[2025-07-30 08:08:51] [Iter 861/4650] R2[410/600], Temp: 0.2277, Energy: -84.753137-0.004706j
[2025-07-30 08:09:05] [Iter 862/4650] R2[411/600], Temp: 0.2255, Energy: -84.721851+0.004351j
[2025-07-30 08:09:19] [Iter 863/4650] R2[412/600], Temp: 0.2233, Energy: -84.785731+0.000073j
[2025-07-30 08:09:34] [Iter 864/4650] R2[413/600], Temp: 0.2211, Energy: -84.736467+0.007944j
[2025-07-30 08:09:49] [Iter 865/4650] R2[414/600], Temp: 0.2190, Energy: -84.676007+0.002582j
[2025-07-30 08:10:04] [Iter 866/4650] R2[415/600], Temp: 0.2168, Energy: -84.692718+0.006163j
[2025-07-30 08:10:18] [Iter 867/4650] R2[416/600], Temp: 0.2146, Energy: -84.718566+0.011273j
[2025-07-30 08:10:33] [Iter 868/4650] R2[417/600], Temp: 0.2125, Energy: -84.762557+0.004707j
[2025-07-30 08:10:48] [Iter 869/4650] R2[418/600], Temp: 0.2104, Energy: -84.747212+0.007652j
[2025-07-30 08:11:03] [Iter 870/4650] R2[419/600], Temp: 0.2082, Energy: -84.748034+0.009035j
[2025-07-30 08:11:17] [Iter 871/4650] R2[420/600], Temp: 0.2061, Energy: -84.847119+0.011943j
[2025-07-30 08:11:32] [Iter 872/4650] R2[421/600], Temp: 0.2040, Energy: -84.784353+0.002272j
[2025-07-30 08:11:47] [Iter 873/4650] R2[422/600], Temp: 0.2019, Energy: -84.792194+0.022971j
[2025-07-30 08:12:01] [Iter 874/4650] R2[423/600], Temp: 0.1998, Energy: -84.736018+0.003116j
[2025-07-30 08:12:16] [Iter 875/4650] R2[424/600], Temp: 0.1977, Energy: -84.797728-0.000133j
[2025-07-30 08:12:31] [Iter 876/4650] R2[425/600], Temp: 0.1956, Energy: -84.853553-0.001644j
[2025-07-30 08:12:45] [Iter 877/4650] R2[426/600], Temp: 0.1935, Energy: -84.801195+0.000693j
[2025-07-30 08:13:00] [Iter 878/4650] R2[427/600], Temp: 0.1915, Energy: -84.847007+0.017412j
[2025-07-30 08:13:15] [Iter 879/4650] R2[428/600], Temp: 0.1894, Energy: -84.757587+0.007394j
[2025-07-30 08:13:30] [Iter 880/4650] R2[429/600], Temp: 0.1874, Energy: -84.740402+0.008997j
[2025-07-30 08:13:44] [Iter 881/4650] R2[430/600], Temp: 0.1853, Energy: -84.709113+0.013713j
[2025-07-30 08:13:59] [Iter 882/4650] R2[431/600], Temp: 0.1833, Energy: -84.752609-0.002355j
[2025-07-30 08:14:13] [Iter 883/4650] R2[432/600], Temp: 0.1813, Energy: -84.806730+0.002204j
[2025-07-30 08:14:28] [Iter 884/4650] R2[433/600], Temp: 0.1793, Energy: -84.838103+0.002445j
[2025-07-30 08:14:43] [Iter 885/4650] R2[434/600], Temp: 0.1773, Energy: -84.873980-0.007644j
[2025-07-30 08:14:57] [Iter 886/4650] R2[435/600], Temp: 0.1753, Energy: -84.819591-0.006364j
[2025-07-30 08:15:12] [Iter 887/4650] R2[436/600], Temp: 0.1733, Energy: -84.877991+0.003101j
[2025-07-30 08:15:27] [Iter 888/4650] R2[437/600], Temp: 0.1713, Energy: -84.904532-0.005924j
[2025-07-30 08:15:42] [Iter 889/4650] R2[438/600], Temp: 0.1693, Energy: -84.881218-0.001683j
[2025-07-30 08:15:57] [Iter 890/4650] R2[439/600], Temp: 0.1674, Energy: -84.836074-0.012904j
[2025-07-30 08:16:12] [Iter 891/4650] R2[440/600], Temp: 0.1654, Energy: -84.762660+0.004934j
[2025-07-30 08:16:26] [Iter 892/4650] R2[441/600], Temp: 0.1635, Energy: -84.794483-0.011213j
[2025-07-30 08:16:41] [Iter 893/4650] R2[442/600], Temp: 0.1616, Energy: -84.790134-0.013762j
[2025-07-30 08:16:56] [Iter 894/4650] R2[443/600], Temp: 0.1596, Energy: -84.810189-0.005365j
[2025-07-30 08:17:11] [Iter 895/4650] R2[444/600], Temp: 0.1577, Energy: -84.779424-0.003009j
[2025-07-30 08:17:25] [Iter 896/4650] R2[445/600], Temp: 0.1558, Energy: -84.759340-0.008327j
[2025-07-30 08:17:40] [Iter 897/4650] R2[446/600], Temp: 0.1539, Energy: -84.787108-0.007777j
[2025-07-30 08:17:55] [Iter 898/4650] R2[447/600], Temp: 0.1520, Energy: -84.791188+0.001940j
[2025-07-30 08:18:10] [Iter 899/4650] R2[448/600], Temp: 0.1502, Energy: -84.798640-0.003559j
[2025-07-30 08:18:25] [Iter 900/4650] R2[449/600], Temp: 0.1483, Energy: -84.836647-0.005439j
[2025-07-30 08:18:39] [Iter 901/4650] R2[450/600], Temp: 0.1464, Energy: -84.757882-0.003240j
[2025-07-30 08:18:54] [Iter 902/4650] R2[451/600], Temp: 0.1446, Energy: -84.667482+0.010039j
[2025-07-30 08:19:09] [Iter 903/4650] R2[452/600], Temp: 0.1428, Energy: -84.694930-0.019785j
[2025-07-30 08:19:24] [Iter 904/4650] R2[453/600], Temp: 0.1409, Energy: -84.711579-0.012369j
[2025-07-30 08:19:39] [Iter 905/4650] R2[454/600], Temp: 0.1391, Energy: -84.683162-0.008571j
[2025-07-30 08:19:54] [Iter 906/4650] R2[455/600], Temp: 0.1373, Energy: -84.710260-0.007015j
[2025-07-30 08:20:09] [Iter 907/4650] R2[456/600], Temp: 0.1355, Energy: -84.616453-0.075822j
[2025-07-30 08:20:24] [Iter 908/4650] R2[457/600], Temp: 0.1337, Energy: -84.666118-0.012218j
[2025-07-30 08:20:39] [Iter 909/4650] R2[458/600], Temp: 0.1320, Energy: -84.643084-0.015241j
[2025-07-30 08:20:53] [Iter 910/4650] R2[459/600], Temp: 0.1302, Energy: -84.725356-0.024448j
[2025-07-30 08:21:08] [Iter 911/4650] R2[460/600], Temp: 0.1284, Energy: -84.667930-0.054344j
[2025-07-30 08:21:23] [Iter 912/4650] R2[461/600], Temp: 0.1267, Energy: -84.731563-0.027175j
[2025-07-30 08:21:38] [Iter 913/4650] R2[462/600], Temp: 0.1249, Energy: -84.684354-0.007376j
[2025-07-30 08:21:53] [Iter 914/4650] R2[463/600], Temp: 0.1232, Energy: -84.751518-0.012834j
[2025-07-30 08:22:08] [Iter 915/4650] R2[464/600], Temp: 0.1215, Energy: -84.750001-0.003919j
[2025-07-30 08:22:23] [Iter 916/4650] R2[465/600], Temp: 0.1198, Energy: -84.735637+0.001810j
[2025-07-30 08:22:38] [Iter 917/4650] R2[466/600], Temp: 0.1181, Energy: -84.753675+0.015943j
[2025-07-30 08:22:53] [Iter 918/4650] R2[467/600], Temp: 0.1164, Energy: -84.778515+0.011307j
[2025-07-30 08:23:08] [Iter 919/4650] R2[468/600], Temp: 0.1147, Energy: -84.768780+0.009383j
[2025-07-30 08:23:23] [Iter 920/4650] R2[469/600], Temp: 0.1131, Energy: -84.766067+0.011978j
[2025-07-30 08:23:37] [Iter 921/4650] R2[470/600], Temp: 0.1114, Energy: -84.782699+0.000456j
[2025-07-30 08:23:52] [Iter 922/4650] R2[471/600], Temp: 0.1098, Energy: -84.863231-0.008929j
[2025-07-30 08:24:07] [Iter 923/4650] R2[472/600], Temp: 0.1082, Energy: -84.832019-0.001476j
[2025-07-30 08:24:22] [Iter 924/4650] R2[473/600], Temp: 0.1065, Energy: -84.876492-0.001832j
[2025-07-30 08:24:37] [Iter 925/4650] R2[474/600], Temp: 0.1049, Energy: -84.795866-0.008921j
[2025-07-30 08:24:51] [Iter 926/4650] R2[475/600], Temp: 0.1033, Energy: -84.861648+0.003085j
[2025-07-30 08:25:06] [Iter 927/4650] R2[476/600], Temp: 0.1017, Energy: -84.779350+0.003528j
[2025-07-30 08:25:21] [Iter 928/4650] R2[477/600], Temp: 0.1002, Energy: -84.804017-0.004564j
[2025-07-30 08:25:36] [Iter 929/4650] R2[478/600], Temp: 0.0986, Energy: -84.842779-0.004657j
[2025-07-30 08:25:50] [Iter 930/4650] R2[479/600], Temp: 0.0970, Energy: -84.786100+0.004965j
[2025-07-30 08:26:05] [Iter 931/4650] R2[480/600], Temp: 0.0955, Energy: -84.767034+0.011130j
[2025-07-30 08:26:19] [Iter 932/4650] R2[481/600], Temp: 0.0940, Energy: -84.666610-0.006644j
[2025-07-30 08:26:34] [Iter 933/4650] R2[482/600], Temp: 0.0924, Energy: -84.806559-0.013439j
[2025-07-30 08:26:49] [Iter 934/4650] R2[483/600], Temp: 0.0909, Energy: -84.721249+0.023333j
[2025-07-30 08:27:03] [Iter 935/4650] R2[484/600], Temp: 0.0894, Energy: -84.831949+0.010926j
[2025-07-30 08:27:18] [Iter 936/4650] R2[485/600], Temp: 0.0879, Energy: -84.744463-0.004237j
[2025-07-30 08:27:33] [Iter 937/4650] R2[486/600], Temp: 0.0865, Energy: -84.600002+0.062001j
[2025-07-30 08:27:48] [Iter 938/4650] R2[487/600], Temp: 0.0850, Energy: -84.768802+0.003400j
[2025-07-30 08:28:03] [Iter 939/4650] R2[488/600], Temp: 0.0835, Energy: -84.687815+0.002456j
[2025-07-30 08:28:18] [Iter 940/4650] R2[489/600], Temp: 0.0821, Energy: -84.767959+0.023125j
[2025-07-30 08:28:33] [Iter 941/4650] R2[490/600], Temp: 0.0807, Energy: -84.653621-0.010463j
[2025-07-30 08:28:47] [Iter 942/4650] R2[491/600], Temp: 0.0792, Energy: -84.722471+0.011433j
[2025-07-30 08:29:02] [Iter 943/4650] R2[492/600], Temp: 0.0778, Energy: -84.687912-0.056894j
[2025-07-30 08:29:17] [Iter 944/4650] R2[493/600], Temp: 0.0764, Energy: -84.717144+0.010376j
[2025-07-30 08:29:32] [Iter 945/4650] R2[494/600], Temp: 0.0751, Energy: -84.744860-0.013261j
[2025-07-30 08:29:46] [Iter 946/4650] R2[495/600], Temp: 0.0737, Energy: -84.819533-0.035867j
[2025-07-30 08:30:01] [Iter 947/4650] R2[496/600], Temp: 0.0723, Energy: -84.754378-0.062096j
[2025-07-30 08:30:16] [Iter 948/4650] R2[497/600], Temp: 0.0710, Energy: -84.679677+0.002147j
[2025-07-30 08:30:31] [Iter 949/4650] R2[498/600], Temp: 0.0696, Energy: -84.756254-0.001422j
[2025-07-30 08:30:46] [Iter 950/4650] R2[499/600], Temp: 0.0683, Energy: -84.779249-0.014394j
[2025-07-30 08:31:01] [Iter 951/4650] R2[500/600], Temp: 0.0670, Energy: -84.723703+0.003244j
[2025-07-30 08:31:16] [Iter 952/4650] R2[501/600], Temp: 0.0657, Energy: -84.736604-0.000226j
[2025-07-30 08:31:31] [Iter 953/4650] R2[502/600], Temp: 0.0644, Energy: -84.746854+0.004568j
[2025-07-30 08:31:46] [Iter 954/4650] R2[503/600], Temp: 0.0631, Energy: -84.778726+0.008832j
[2025-07-30 08:32:01] [Iter 955/4650] R2[504/600], Temp: 0.0618, Energy: -84.832250+0.003642j
[2025-07-30 08:32:15] [Iter 956/4650] R2[505/600], Temp: 0.0606, Energy: -84.885238-0.001695j
[2025-07-30 08:32:30] [Iter 957/4650] R2[506/600], Temp: 0.0593, Energy: -84.763342+0.000940j
[2025-07-30 08:32:45] [Iter 958/4650] R2[507/600], Temp: 0.0581, Energy: -84.783126+0.001512j
[2025-07-30 08:33:00] [Iter 959/4650] R2[508/600], Temp: 0.0569, Energy: -84.833601+0.004580j
[2025-07-30 08:33:15] [Iter 960/4650] R2[509/600], Temp: 0.0557, Energy: -84.747674+0.009960j
[2025-07-30 08:33:30] [Iter 961/4650] R2[510/600], Temp: 0.0545, Energy: -84.723014+0.009190j
[2025-07-30 08:33:44] [Iter 962/4650] R2[511/600], Temp: 0.0533, Energy: -84.577863+0.009703j
[2025-07-30 08:33:59] [Iter 963/4650] R2[512/600], Temp: 0.0521, Energy: -84.772337-0.000224j
[2025-07-30 08:34:14] [Iter 964/4650] R2[513/600], Temp: 0.0510, Energy: -84.740370+0.026270j
[2025-07-30 08:34:29] [Iter 965/4650] R2[514/600], Temp: 0.0498, Energy: -84.703942+0.033345j
[2025-07-30 08:34:44] [Iter 966/4650] R2[515/600], Temp: 0.0487, Energy: -84.772246+0.031594j
[2025-07-30 08:34:58] [Iter 967/4650] R2[516/600], Temp: 0.0476, Energy: -84.803431+0.016246j
[2025-07-30 08:35:13] [Iter 968/4650] R2[517/600], Temp: 0.0465, Energy: -84.803863+0.020276j
[2025-07-30 08:35:28] [Iter 969/4650] R2[518/600], Temp: 0.0454, Energy: -84.727467+0.009073j
[2025-07-30 08:35:43] [Iter 970/4650] R2[519/600], Temp: 0.0443, Energy: -84.827469+0.018103j
[2025-07-30 08:35:58] [Iter 971/4650] R2[520/600], Temp: 0.0432, Energy: -84.810096-0.005242j
[2025-07-30 08:36:12] [Iter 972/4650] R2[521/600], Temp: 0.0422, Energy: -84.739785+0.004243j
[2025-07-30 08:36:27] [Iter 973/4650] R2[522/600], Temp: 0.0411, Energy: -84.751760+0.007048j
[2025-07-30 08:36:42] [Iter 974/4650] R2[523/600], Temp: 0.0401, Energy: -84.633375+0.000176j
[2025-07-30 08:36:56] [Iter 975/4650] R2[524/600], Temp: 0.0391, Energy: -84.815259-0.010670j
[2025-07-30 08:37:11] [Iter 976/4650] R2[525/600], Temp: 0.0381, Energy: -84.829562+0.031578j
[2025-07-30 08:37:25] [Iter 977/4650] R2[526/600], Temp: 0.0371, Energy: -84.832929+0.002063j
[2025-07-30 08:37:40] [Iter 978/4650] R2[527/600], Temp: 0.0361, Energy: -84.851391+0.010377j
[2025-07-30 08:37:55] [Iter 979/4650] R2[528/600], Temp: 0.0351, Energy: -84.764326-0.019123j
[2025-07-30 08:38:10] [Iter 980/4650] R2[529/600], Temp: 0.0342, Energy: -84.754765+0.023463j
[2025-07-30 08:38:25] [Iter 981/4650] R2[530/600], Temp: 0.0332, Energy: -84.729835-0.012467j
[2025-07-30 08:38:40] [Iter 982/4650] R2[531/600], Temp: 0.0323, Energy: -84.708643+0.000811j
[2025-07-30 08:38:55] [Iter 983/4650] R2[532/600], Temp: 0.0314, Energy: -84.748088-0.019697j
[2025-07-30 08:39:10] [Iter 984/4650] R2[533/600], Temp: 0.0305, Energy: -84.797809+0.005544j
[2025-07-30 08:39:24] [Iter 985/4650] R2[534/600], Temp: 0.0296, Energy: -84.755610-0.010656j
[2025-07-30 08:39:40] [Iter 986/4650] R2[535/600], Temp: 0.0287, Energy: -84.805038+0.005467j
[2025-07-30 08:39:54] [Iter 987/4650] R2[536/600], Temp: 0.0278, Energy: -84.751844-0.015029j
[2025-07-30 08:40:09] [Iter 988/4650] R2[537/600], Temp: 0.0270, Energy: -84.799751-0.001682j
[2025-07-30 08:40:24] [Iter 989/4650] R2[538/600], Temp: 0.0261, Energy: -84.806158+0.000404j
[2025-07-30 08:40:39] [Iter 990/4650] R2[539/600], Temp: 0.0253, Energy: -84.860959-0.006401j
[2025-07-30 08:40:54] [Iter 991/4650] R2[540/600], Temp: 0.0245, Energy: -84.860823-0.005256j
[2025-07-30 08:41:09] [Iter 992/4650] R2[541/600], Temp: 0.0237, Energy: -84.832001-0.006664j
[2025-07-30 08:41:23] [Iter 993/4650] R2[542/600], Temp: 0.0229, Energy: -84.839480+0.002448j
[2025-07-30 08:41:38] [Iter 994/4650] R2[543/600], Temp: 0.0221, Energy: -84.783287-0.017166j
[2025-07-30 08:41:52] [Iter 995/4650] R2[544/600], Temp: 0.0213, Energy: -84.774364-0.000036j
[2025-07-30 08:42:06] [Iter 996/4650] R2[545/600], Temp: 0.0206, Energy: -84.887913+0.010685j
[2025-07-30 08:42:21] [Iter 997/4650] R2[546/600], Temp: 0.0199, Energy: -84.857870+0.013638j
[2025-07-30 08:42:36] [Iter 998/4650] R2[547/600], Temp: 0.0191, Energy: -84.786443-0.007938j
[2025-07-30 08:42:51] [Iter 999/4650] R2[548/600], Temp: 0.0184, Energy: -84.756552+0.007580j
[2025-07-30 08:43:06] [Iter 1000/4650] R2[549/600], Temp: 0.0177, Energy: -84.691286-0.005437j
[2025-07-30 08:43:06] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-07-30 08:43:21] [Iter 1001/4650] R2[550/600], Temp: 0.0170, Energy: -84.773037+0.004642j
[2025-07-30 08:43:35] [Iter 1002/4650] R2[551/600], Temp: 0.0164, Energy: -84.780420+0.015390j
[2025-07-30 08:43:50] [Iter 1003/4650] R2[552/600], Temp: 0.0157, Energy: -84.768299+0.012744j
[2025-07-30 08:44:05] [Iter 1004/4650] R2[553/600], Temp: 0.0151, Energy: -84.746087+0.006534j
[2025-07-30 08:44:20] [Iter 1005/4650] R2[554/600], Temp: 0.0144, Energy: -84.802203+0.014389j
[2025-07-30 08:44:35] [Iter 1006/4650] R2[555/600], Temp: 0.0138, Energy: -84.780860+0.006273j
[2025-07-30 08:44:50] [Iter 1007/4650] R2[556/600], Temp: 0.0132, Energy: -84.861243+0.009801j
[2025-07-30 08:45:05] [Iter 1008/4650] R2[557/600], Temp: 0.0126, Energy: -84.885645+0.017307j
[2025-07-30 08:45:20] [Iter 1009/4650] R2[558/600], Temp: 0.0120, Energy: -84.923151+0.000790j
[2025-07-30 08:45:35] [Iter 1010/4650] R2[559/600], Temp: 0.0115, Energy: -84.836928+0.017994j
[2025-07-30 08:45:49] [Iter 1011/4650] R2[560/600], Temp: 0.0109, Energy: -84.879490+0.013627j
[2025-07-30 08:46:04] [Iter 1012/4650] R2[561/600], Temp: 0.0104, Energy: -84.865249+0.012350j
[2025-07-30 08:46:19] [Iter 1013/4650] R2[562/600], Temp: 0.0099, Energy: -84.931980+0.018556j
[2025-07-30 08:46:34] [Iter 1014/4650] R2[563/600], Temp: 0.0094, Energy: -84.863452+0.001002j
[2025-07-30 08:46:49] [Iter 1015/4650] R2[564/600], Temp: 0.0089, Energy: -84.886542+0.006723j
[2025-07-30 08:47:03] [Iter 1016/4650] R2[565/600], Temp: 0.0084, Energy: -84.880364+0.000500j
[2025-07-30 08:47:18] [Iter 1017/4650] R2[566/600], Temp: 0.0079, Energy: -84.822734+0.007482j
[2025-07-30 08:47:33] [Iter 1018/4650] R2[567/600], Temp: 0.0074, Energy: -84.876897+0.003368j
[2025-07-30 08:47:47] [Iter 1019/4650] R2[568/600], Temp: 0.0070, Energy: -84.791436+0.022015j
[2025-07-30 08:48:02] [Iter 1020/4650] R2[569/600], Temp: 0.0066, Energy: -84.854358+0.023524j
[2025-07-30 08:48:17] [Iter 1021/4650] R2[570/600], Temp: 0.0062, Energy: -84.852959+0.012084j
[2025-07-30 08:48:31] [Iter 1022/4650] R2[571/600], Temp: 0.0058, Energy: -84.891152-0.000079j
[2025-07-30 08:48:46] [Iter 1023/4650] R2[572/600], Temp: 0.0054, Energy: -84.866281+0.000527j
[2025-07-30 08:49:01] [Iter 1024/4650] R2[573/600], Temp: 0.0050, Energy: -84.905431-0.008784j
[2025-07-30 08:49:16] [Iter 1025/4650] R2[574/600], Temp: 0.0046, Energy: -84.770356+0.002566j
[2025-07-30 08:49:31] [Iter 1026/4650] R2[575/600], Temp: 0.0043, Energy: -84.815568+0.004544j
[2025-07-30 08:49:45] [Iter 1027/4650] R2[576/600], Temp: 0.0039, Energy: -84.798262-0.008020j
[2025-07-30 08:50:00] [Iter 1028/4650] R2[577/600], Temp: 0.0036, Energy: -84.859099-0.017959j
[2025-07-30 08:50:15] [Iter 1029/4650] R2[578/600], Temp: 0.0033, Energy: -84.837192-0.018189j
[2025-07-30 08:50:31] [Iter 1030/4650] R2[579/600], Temp: 0.0030, Energy: -84.886773+0.000192j
[2025-07-30 08:50:45] [Iter 1031/4650] R2[580/600], Temp: 0.0027, Energy: -84.863954-0.004834j
[2025-07-30 08:51:00] [Iter 1032/4650] R2[581/600], Temp: 0.0025, Energy: -84.769042+0.000615j
[2025-07-30 08:51:14] [Iter 1033/4650] R2[582/600], Temp: 0.0022, Energy: -84.779007-0.000180j
[2025-07-30 08:51:29] [Iter 1034/4650] R2[583/600], Temp: 0.0020, Energy: -84.861818+0.011679j
[2025-07-30 08:51:45] [Iter 1035/4650] R2[584/600], Temp: 0.0018, Energy: -84.895599-0.002310j
[2025-07-30 08:51:59] [Iter 1036/4650] R2[585/600], Temp: 0.0015, Energy: -84.870980+0.001617j
[2025-07-30 08:52:14] [Iter 1037/4650] R2[586/600], Temp: 0.0013, Energy: -84.933920-0.015706j
[2025-07-30 08:52:29] [Iter 1038/4650] R2[587/600], Temp: 0.0012, Energy: -84.844657-0.014473j
[2025-07-30 08:52:44] [Iter 1039/4650] R2[588/600], Temp: 0.0010, Energy: -84.923846-0.001106j
[2025-07-30 08:52:58] [Iter 1040/4650] R2[589/600], Temp: 0.0008, Energy: -84.884274-0.002225j
[2025-07-30 08:53:13] [Iter 1041/4650] R2[590/600], Temp: 0.0007, Energy: -84.858780-0.000252j
[2025-07-30 08:53:27] [Iter 1042/4650] R2[591/600], Temp: 0.0006, Energy: -84.833747+0.011354j
[2025-07-30 08:53:42] [Iter 1043/4650] R2[592/600], Temp: 0.0004, Energy: -84.795724-0.004911j
[2025-07-30 08:53:56] [Iter 1044/4650] R2[593/600], Temp: 0.0003, Energy: -84.758851-0.010150j
[2025-07-30 08:54:11] [Iter 1045/4650] R2[594/600], Temp: 0.0002, Energy: -84.838201-0.023025j
[2025-07-30 08:54:25] [Iter 1046/4650] R2[595/600], Temp: 0.0002, Energy: -84.874534+0.000222j
[2025-07-30 08:54:40] [Iter 1047/4650] R2[596/600], Temp: 0.0001, Energy: -84.715534-0.008183j
[2025-07-30 08:54:55] [Iter 1048/4650] R2[597/600], Temp: 0.0001, Energy: -84.743394+0.030887j
[2025-07-30 08:55:10] [Iter 1049/4650] R2[598/600], Temp: 0.0000, Energy: -84.819390+0.000878j
[2025-07-30 08:55:25] [Iter 1050/4650] R2[599/600], Temp: 0.0000, Energy: -84.856969+0.010473j
[2025-07-30 08:55:25] RESTART #3 | Period: 1200
[2025-07-30 08:55:40] [Iter 1051/4650] R3[0/1200], Temp: 1.0000, Energy: -84.874244-0.001311j
[2025-07-30 08:55:55] [Iter 1052/4650] R3[1/1200], Temp: 1.0000, Energy: -84.848241+0.004831j
[2025-07-30 08:56:09] [Iter 1053/4650] R3[2/1200], Temp: 1.0000, Energy: -84.830806-0.000321j
[2025-07-30 08:56:23] [Iter 1054/4650] R3[3/1200], Temp: 1.0000, Energy: -84.757071-0.006598j
[2025-07-30 08:56:38] [Iter 1055/4650] R3[4/1200], Temp: 1.0000, Energy: -84.838614-0.027336j
[2025-07-30 08:56:53] [Iter 1056/4650] R3[5/1200], Temp: 1.0000, Energy: -84.840485-0.011983j
[2025-07-30 08:57:08] [Iter 1057/4650] R3[6/1200], Temp: 0.9999, Energy: -84.766024+0.012203j
[2025-07-30 08:57:22] [Iter 1058/4650] R3[7/1200], Temp: 0.9999, Energy: -84.706362-0.005519j
[2025-07-30 08:57:36] [Iter 1059/4650] R3[8/1200], Temp: 0.9999, Energy: -84.742201-0.008782j
[2025-07-30 08:57:51] [Iter 1060/4650] R3[9/1200], Temp: 0.9999, Energy: -84.734278+0.002969j
[2025-07-30 08:58:05] [Iter 1061/4650] R3[10/1200], Temp: 0.9998, Energy: -84.771357-0.015808j
[2025-07-30 08:58:20] [Iter 1062/4650] R3[11/1200], Temp: 0.9998, Energy: -84.844605+0.002348j
[2025-07-30 08:58:35] [Iter 1063/4650] R3[12/1200], Temp: 0.9998, Energy: -84.763805-0.008645j
[2025-07-30 08:58:50] [Iter 1064/4650] R3[13/1200], Temp: 0.9997, Energy: -84.800772-0.007024j
[2025-07-30 08:59:05] [Iter 1065/4650] R3[14/1200], Temp: 0.9997, Energy: -84.751640-0.011944j
[2025-07-30 08:59:19] [Iter 1066/4650] R3[15/1200], Temp: 0.9996, Energy: -84.818512-0.002619j
[2025-07-30 08:59:34] [Iter 1067/4650] R3[16/1200], Temp: 0.9996, Energy: -84.774713-0.001919j
[2025-07-30 08:59:49] [Iter 1068/4650] R3[17/1200], Temp: 0.9995, Energy: -84.748891+0.001464j
[2025-07-30 09:00:04] [Iter 1069/4650] R3[18/1200], Temp: 0.9994, Energy: -84.749275-0.012740j
[2025-07-30 09:00:18] [Iter 1070/4650] R3[19/1200], Temp: 0.9994, Energy: -84.731140+0.012621j
[2025-07-30 09:00:33] [Iter 1071/4650] R3[20/1200], Temp: 0.9993, Energy: -84.766400-0.009892j
[2025-07-30 09:00:47] [Iter 1072/4650] R3[21/1200], Temp: 0.9992, Energy: -84.808469-0.016886j
[2025-07-30 09:01:02] [Iter 1073/4650] R3[22/1200], Temp: 0.9992, Energy: -84.794977-0.011403j
[2025-07-30 09:01:17] [Iter 1074/4650] R3[23/1200], Temp: 0.9991, Energy: -84.796536+0.000902j
[2025-07-30 09:01:32] [Iter 1075/4650] R3[24/1200], Temp: 0.9990, Energy: -84.854960+0.008307j
[2025-07-30 09:01:47] [Iter 1076/4650] R3[25/1200], Temp: 0.9989, Energy: -84.884091+0.002749j
[2025-07-30 09:02:01] [Iter 1077/4650] R3[26/1200], Temp: 0.9988, Energy: -84.737449-0.004926j
[2025-07-30 09:02:15] [Iter 1078/4650] R3[27/1200], Temp: 0.9988, Energy: -84.799977+0.015157j
[2025-07-30 09:02:31] [Iter 1079/4650] R3[28/1200], Temp: 0.9987, Energy: -84.681791+0.039758j
[2025-07-30 09:02:45] [Iter 1080/4650] R3[29/1200], Temp: 0.9986, Energy: -84.708317+0.038105j
[2025-07-30 09:03:00] [Iter 1081/4650] R3[30/1200], Temp: 0.9985, Energy: -84.609779+0.012860j
[2025-07-30 09:03:14] [Iter 1082/4650] R3[31/1200], Temp: 0.9984, Energy: -84.709604+0.019825j
[2025-07-30 09:03:29] [Iter 1083/4650] R3[32/1200], Temp: 0.9982, Energy: -84.683881-0.003649j
[2025-07-30 09:03:43] [Iter 1084/4650] R3[33/1200], Temp: 0.9981, Energy: -84.677603+0.010674j
[2025-07-30 09:03:58] [Iter 1085/4650] R3[34/1200], Temp: 0.9980, Energy: -84.730317+0.015453j
[2025-07-30 09:04:13] [Iter 1086/4650] R3[35/1200], Temp: 0.9979, Energy: -84.692888+0.009634j
[2025-07-30 09:04:27] [Iter 1087/4650] R3[36/1200], Temp: 0.9978, Energy: -84.698934+0.005089j
[2025-07-30 09:04:42] [Iter 1088/4650] R3[37/1200], Temp: 0.9977, Energy: -84.602187+0.039036j
[2025-07-30 09:04:57] [Iter 1089/4650] R3[38/1200], Temp: 0.9975, Energy: -84.673751+0.004468j
[2025-07-30 09:05:11] [Iter 1090/4650] R3[39/1200], Temp: 0.9974, Energy: -84.628288+0.005600j
[2025-07-30 09:05:26] [Iter 1091/4650] R3[40/1200], Temp: 0.9973, Energy: -84.717041-0.004230j
[2025-07-30 09:05:40] [Iter 1092/4650] R3[41/1200], Temp: 0.9971, Energy: -84.675865-0.015496j
[2025-07-30 09:05:55] [Iter 1093/4650] R3[42/1200], Temp: 0.9970, Energy: -84.747307+0.003013j
[2025-07-30 09:06:10] [Iter 1094/4650] R3[43/1200], Temp: 0.9968, Energy: -84.743546+0.040791j
[2025-07-30 09:06:24] [Iter 1095/4650] R3[44/1200], Temp: 0.9967, Energy: -84.649742+0.009002j
[2025-07-30 09:06:39] [Iter 1096/4650] R3[45/1200], Temp: 0.9965, Energy: -84.628941+0.010110j
[2025-07-30 09:06:54] [Iter 1097/4650] R3[46/1200], Temp: 0.9964, Energy: -84.702885-0.003060j
[2025-07-30 09:07:09] [Iter 1098/4650] R3[47/1200], Temp: 0.9962, Energy: -84.739273+0.001476j
[2025-07-30 09:07:24] [Iter 1099/4650] R3[48/1200], Temp: 0.9961, Energy: -84.782126-0.001154j
[2025-07-30 09:07:38] [Iter 1100/4650] R3[49/1200], Temp: 0.9959, Energy: -84.755560+0.001767j
[2025-07-30 09:07:54] [Iter 1101/4650] R3[50/1200], Temp: 0.9957, Energy: -84.808343-0.004919j
[2025-07-30 09:08:08] [Iter 1102/4650] R3[51/1200], Temp: 0.9955, Energy: -84.788724-0.001944j
[2025-07-30 09:08:22] [Iter 1103/4650] R3[52/1200], Temp: 0.9954, Energy: -84.836437-0.003899j
[2025-07-30 09:08:37] [Iter 1104/4650] R3[53/1200], Temp: 0.9952, Energy: -84.804870+0.007469j
[2025-07-30 09:08:52] [Iter 1105/4650] R3[54/1200], Temp: 0.9950, Energy: -84.794839-0.003766j
[2025-07-30 09:09:06] [Iter 1106/4650] R3[55/1200], Temp: 0.9948, Energy: -84.739922+0.001109j
[2025-07-30 09:09:21] [Iter 1107/4650] R3[56/1200], Temp: 0.9946, Energy: -84.862690-0.001553j
[2025-07-30 09:09:35] [Iter 1108/4650] R3[57/1200], Temp: 0.9944, Energy: -84.840738-0.004989j
[2025-07-30 09:09:50] [Iter 1109/4650] R3[58/1200], Temp: 0.9942, Energy: -84.798594+0.004110j
[2025-07-30 09:10:05] [Iter 1110/4650] R3[59/1200], Temp: 0.9940, Energy: -84.873111-0.001224j
[2025-07-30 09:10:20] [Iter 1111/4650] R3[60/1200], Temp: 0.9938, Energy: -84.821075-0.002751j
[2025-07-30 09:10:34] [Iter 1112/4650] R3[61/1200], Temp: 0.9936, Energy: -84.883025-0.008964j
[2025-07-30 09:10:49] [Iter 1113/4650] R3[62/1200], Temp: 0.9934, Energy: -84.846941-0.018878j
[2025-07-30 09:11:03] [Iter 1114/4650] R3[63/1200], Temp: 0.9932, Energy: -84.833004-0.011098j
[2025-07-30 09:11:19] [Iter 1115/4650] R3[64/1200], Temp: 0.9930, Energy: -84.790694-0.000962j
[2025-07-30 09:11:34] [Iter 1116/4650] R3[65/1200], Temp: 0.9928, Energy: -84.803435-0.003883j
[2025-07-30 09:11:49] [Iter 1117/4650] R3[66/1200], Temp: 0.9926, Energy: -84.885947-0.024074j
[2025-07-30 09:12:03] [Iter 1118/4650] R3[67/1200], Temp: 0.9923, Energy: -84.858060-0.007434j
[2025-07-30 09:12:18] [Iter 1119/4650] R3[68/1200], Temp: 0.9921, Energy: -84.865326-0.000576j
[2025-07-30 09:12:32] [Iter 1120/4650] R3[69/1200], Temp: 0.9919, Energy: -84.823399-0.011583j
[2025-07-30 09:12:47] [Iter 1121/4650] R3[70/1200], Temp: 0.9916, Energy: -84.888836-0.001839j
[2025-07-30 09:13:01] [Iter 1122/4650] R3[71/1200], Temp: 0.9914, Energy: -84.947005+0.008714j
[2025-07-30 09:13:16] [Iter 1123/4650] R3[72/1200], Temp: 0.9911, Energy: -84.989877-0.003131j
[2025-07-30 09:13:31] [Iter 1124/4650] R3[73/1200], Temp: 0.9909, Energy: -84.849462+0.010316j
[2025-07-30 09:13:46] [Iter 1125/4650] R3[74/1200], Temp: 0.9906, Energy: -84.898407+0.006992j
[2025-07-30 09:14:01] [Iter 1126/4650] R3[75/1200], Temp: 0.9904, Energy: -84.863648-0.029123j
[2025-07-30 09:14:16] [Iter 1127/4650] R3[76/1200], Temp: 0.9901, Energy: -84.816821-0.015036j
[2025-07-30 09:14:31] [Iter 1128/4650] R3[77/1200], Temp: 0.9899, Energy: -84.935259-0.007919j
[2025-07-30 09:14:46] [Iter 1129/4650] R3[78/1200], Temp: 0.9896, Energy: -84.940995-0.016028j
[2025-07-30 09:15:01] [Iter 1130/4650] R3[79/1200], Temp: 0.9893, Energy: -84.938055-0.019642j
[2025-07-30 09:15:15] [Iter 1131/4650] R3[80/1200], Temp: 0.9891, Energy: -84.993438-0.015370j
[2025-07-30 09:15:30] [Iter 1132/4650] R3[81/1200], Temp: 0.9888, Energy: -84.938468-0.004751j
[2025-07-30 09:15:45] [Iter 1133/4650] R3[82/1200], Temp: 0.9885, Energy: -84.969883-0.000936j
[2025-07-30 09:16:00] [Iter 1134/4650] R3[83/1200], Temp: 0.9882, Energy: -84.908885-0.010185j
[2025-07-30 09:16:14] [Iter 1135/4650] R3[84/1200], Temp: 0.9880, Energy: -84.995391-0.010786j
[2025-07-30 09:16:29] [Iter 1136/4650] R3[85/1200], Temp: 0.9877, Energy: -84.888229-0.011943j
[2025-07-30 09:16:44] [Iter 1137/4650] R3[86/1200], Temp: 0.9874, Energy: -84.901568-0.003519j
[2025-07-30 09:16:58] [Iter 1138/4650] R3[87/1200], Temp: 0.9871, Energy: -84.941489+0.007410j
[2025-07-30 09:17:12] [Iter 1139/4650] R3[88/1200], Temp: 0.9868, Energy: -84.992452+0.006930j
[2025-07-30 09:17:27] [Iter 1140/4650] R3[89/1200], Temp: 0.9865, Energy: -84.966668-0.007783j
[2025-07-30 09:17:42] [Iter 1141/4650] R3[90/1200], Temp: 0.9862, Energy: -85.023700+0.013556j
[2025-07-30 09:17:57] [Iter 1142/4650] R3[91/1200], Temp: 0.9859, Energy: -84.899000+0.019439j
[2025-07-30 09:18:12] [Iter 1143/4650] R3[92/1200], Temp: 0.9856, Energy: -84.902377+0.014497j
[2025-07-30 09:18:26] [Iter 1144/4650] R3[93/1200], Temp: 0.9853, Energy: -84.901828+0.018984j
[2025-07-30 09:18:40] [Iter 1145/4650] R3[94/1200], Temp: 0.9849, Energy: -84.996750+0.012409j
[2025-07-30 09:18:55] [Iter 1146/4650] R3[95/1200], Temp: 0.9846, Energy: -84.926708-0.003651j
[2025-07-30 09:19:09] [Iter 1147/4650] R3[96/1200], Temp: 0.9843, Energy: -84.904200-0.001972j
[2025-07-30 09:19:23] [Iter 1148/4650] R3[97/1200], Temp: 0.9840, Energy: -84.899771-0.013972j
[2025-07-30 09:19:38] [Iter 1149/4650] R3[98/1200], Temp: 0.9836, Energy: -84.943377+0.004014j
[2025-07-30 09:19:53] [Iter 1150/4650] R3[99/1200], Temp: 0.9833, Energy: -84.883169-0.008552j
[2025-07-30 09:20:07] [Iter 1151/4650] R3[100/1200], Temp: 0.9830, Energy: -84.882876+0.009032j
[2025-07-30 09:20:22] [Iter 1152/4650] R3[101/1200], Temp: 0.9826, Energy: -84.909319+0.005155j
[2025-07-30 09:20:37] [Iter 1153/4650] R3[102/1200], Temp: 0.9823, Energy: -84.881804+0.004976j
[2025-07-30 09:20:52] [Iter 1154/4650] R3[103/1200], Temp: 0.9819, Energy: -84.865948-0.006538j
[2025-07-30 09:21:07] [Iter 1155/4650] R3[104/1200], Temp: 0.9816, Energy: -84.893393-0.002046j
[2025-07-30 09:21:21] [Iter 1156/4650] R3[105/1200], Temp: 0.9812, Energy: -84.946492-0.007303j
[2025-07-30 09:21:36] [Iter 1157/4650] R3[106/1200], Temp: 0.9809, Energy: -84.851208-0.005809j
[2025-07-30 09:21:51] [Iter 1158/4650] R3[107/1200], Temp: 0.9805, Energy: -84.842099+0.003593j
[2025-07-30 09:22:06] [Iter 1159/4650] R3[108/1200], Temp: 0.9801, Energy: -84.836363-0.006525j
[2025-07-30 09:22:21] [Iter 1160/4650] R3[109/1200], Temp: 0.9798, Energy: -84.850250+0.002486j
[2025-07-30 09:22:35] [Iter 1161/4650] R3[110/1200], Temp: 0.9794, Energy: -84.879066-0.006509j
[2025-07-30 09:22:49] [Iter 1162/4650] R3[111/1200], Temp: 0.9790, Energy: -84.824543-0.000410j
[2025-07-30 09:23:04] [Iter 1163/4650] R3[112/1200], Temp: 0.9787, Energy: -84.881060-0.000758j
[2025-07-30 09:23:19] [Iter 1164/4650] R3[113/1200], Temp: 0.9783, Energy: -84.930053+0.000497j
[2025-07-30 09:23:34] [Iter 1165/4650] R3[114/1200], Temp: 0.9779, Energy: -84.962705-0.002763j
[2025-07-30 09:23:49] [Iter 1166/4650] R3[115/1200], Temp: 0.9775, Energy: -84.845992-0.013367j
[2025-07-30 09:24:03] [Iter 1167/4650] R3[116/1200], Temp: 0.9771, Energy: -84.911673-0.001428j
[2025-07-30 09:24:17] [Iter 1168/4650] R3[117/1200], Temp: 0.9767, Energy: -84.907885-0.000552j
[2025-07-30 09:24:32] [Iter 1169/4650] R3[118/1200], Temp: 0.9763, Energy: -84.892545-0.005222j
[2025-07-30 09:24:47] [Iter 1170/4650] R3[119/1200], Temp: 0.9759, Energy: -85.017585-0.012371j
[2025-07-30 09:25:01] [Iter 1171/4650] R3[120/1200], Temp: 0.9755, Energy: -84.952638-0.006014j
[2025-07-30 09:25:16] [Iter 1172/4650] R3[121/1200], Temp: 0.9751, Energy: -84.867780-0.007036j
[2025-07-30 09:25:31] [Iter 1173/4650] R3[122/1200], Temp: 0.9747, Energy: -84.917347+0.005652j
[2025-07-30 09:25:45] [Iter 1174/4650] R3[123/1200], Temp: 0.9743, Energy: -84.767062+0.002011j
[2025-07-30 09:26:00] [Iter 1175/4650] R3[124/1200], Temp: 0.9739, Energy: -84.877183+0.010167j
[2025-07-30 09:26:15] [Iter 1176/4650] R3[125/1200], Temp: 0.9735, Energy: -84.825983-0.010052j
[2025-07-30 09:26:30] [Iter 1177/4650] R3[126/1200], Temp: 0.9730, Energy: -84.981374-0.003754j
[2025-07-30 09:26:44] [Iter 1178/4650] R3[127/1200], Temp: 0.9726, Energy: -84.921463-0.005163j
[2025-07-30 09:26:59] [Iter 1179/4650] R3[128/1200], Temp: 0.9722, Energy: -85.040080+0.001112j
[2025-07-30 09:27:15] [Iter 1180/4650] R3[129/1200], Temp: 0.9718, Energy: -84.991135-0.000054j
[2025-07-30 09:27:30] [Iter 1181/4650] R3[130/1200], Temp: 0.9713, Energy: -85.030637+0.004909j
[2025-07-30 09:27:45] [Iter 1182/4650] R3[131/1200], Temp: 0.9709, Energy: -84.872455+0.006364j
[2025-07-30 09:28:00] [Iter 1183/4650] R3[132/1200], Temp: 0.9704, Energy: -84.873250+0.009137j
[2025-07-30 09:28:14] [Iter 1184/4650] R3[133/1200], Temp: 0.9700, Energy: -84.842412+0.002868j
[2025-07-30 09:28:29] [Iter 1185/4650] R3[134/1200], Temp: 0.9695, Energy: -84.902047-0.000967j
[2025-07-30 09:28:44] [Iter 1186/4650] R3[135/1200], Temp: 0.9691, Energy: -84.885527-0.020056j
[2025-07-30 09:28:58] [Iter 1187/4650] R3[136/1200], Temp: 0.9686, Energy: -84.864179+0.005606j
[2025-07-30 09:29:13] [Iter 1188/4650] R3[137/1200], Temp: 0.9682, Energy: -84.906248-0.000403j
[2025-07-30 09:29:27] [Iter 1189/4650] R3[138/1200], Temp: 0.9677, Energy: -84.851190-0.002872j
[2025-07-30 09:29:42] [Iter 1190/4650] R3[139/1200], Temp: 0.9673, Energy: -84.919743-0.020136j
[2025-07-30 09:29:56] [Iter 1191/4650] R3[140/1200], Temp: 0.9668, Energy: -84.883336-0.005203j
[2025-07-30 09:30:11] [Iter 1192/4650] R3[141/1200], Temp: 0.9663, Energy: -84.918053-0.009854j
[2025-07-30 09:30:25] [Iter 1193/4650] R3[142/1200], Temp: 0.9658, Energy: -84.955427+0.000891j
[2025-07-30 09:30:40] [Iter 1194/4650] R3[143/1200], Temp: 0.9654, Energy: -84.991216-0.001807j
[2025-07-30 09:30:55] [Iter 1195/4650] R3[144/1200], Temp: 0.9649, Energy: -84.944829-0.001665j
[2025-07-30 09:31:10] [Iter 1196/4650] R3[145/1200], Temp: 0.9644, Energy: -84.982940-0.019027j
[2025-07-30 09:31:24] [Iter 1197/4650] R3[146/1200], Temp: 0.9639, Energy: -84.927742-0.005817j
[2025-07-30 09:31:39] [Iter 1198/4650] R3[147/1200], Temp: 0.9634, Energy: -84.943879-0.016295j
[2025-07-30 09:31:54] [Iter 1199/4650] R3[148/1200], Temp: 0.9629, Energy: -84.981570-0.009477j
[2025-07-30 09:32:08] [Iter 1200/4650] R3[149/1200], Temp: 0.9624, Energy: -84.904543-0.005582j
[2025-07-30 09:32:23] [Iter 1201/4650] R3[150/1200], Temp: 0.9619, Energy: -84.985685-0.002317j
[2025-07-30 09:32:38] [Iter 1202/4650] R3[151/1200], Temp: 0.9614, Energy: -85.003921-0.009344j
[2025-07-30 09:32:53] [Iter 1203/4650] R3[152/1200], Temp: 0.9609, Energy: -84.976959-0.009798j
[2025-07-30 09:33:08] [Iter 1204/4650] R3[153/1200], Temp: 0.9604, Energy: -85.031578-0.014450j
[2025-07-30 09:33:22] [Iter 1205/4650] R3[154/1200], Temp: 0.9599, Energy: -85.005463-0.000245j
[2025-07-30 09:33:37] [Iter 1206/4650] R3[155/1200], Temp: 0.9594, Energy: -84.955538-0.003483j
[2025-07-30 09:33:51] [Iter 1207/4650] R3[156/1200], Temp: 0.9589, Energy: -85.039757-0.002976j
[2025-07-30 09:34:06] [Iter 1208/4650] R3[157/1200], Temp: 0.9584, Energy: -85.003613-0.000384j
[2025-07-30 09:34:21] [Iter 1209/4650] R3[158/1200], Temp: 0.9578, Energy: -84.950331-0.005851j
[2025-07-30 09:34:36] [Iter 1210/4650] R3[159/1200], Temp: 0.9573, Energy: -84.939246-0.005405j
[2025-07-30 09:34:51] [Iter 1211/4650] R3[160/1200], Temp: 0.9568, Energy: -84.910212+0.002187j
[2025-07-30 09:35:06] [Iter 1212/4650] R3[161/1200], Temp: 0.9562, Energy: -84.982325-0.005519j
[2025-07-30 09:35:20] [Iter 1213/4650] R3[162/1200], Temp: 0.9557, Energy: -84.915866-0.007443j
[2025-07-30 09:35:35] [Iter 1214/4650] R3[163/1200], Temp: 0.9552, Energy: -84.952965-0.000412j
[2025-07-30 09:35:50] [Iter 1215/4650] R3[164/1200], Temp: 0.9546, Energy: -84.975404-0.002339j
[2025-07-30 09:36:04] [Iter 1216/4650] R3[165/1200], Temp: 0.9541, Energy: -85.032494-0.002938j
[2025-07-30 09:36:19] [Iter 1217/4650] R3[166/1200], Temp: 0.9535, Energy: -85.003332+0.002417j
[2025-07-30 09:36:34] [Iter 1218/4650] R3[167/1200], Temp: 0.9530, Energy: -85.019565-0.003981j
[2025-07-30 09:36:49] [Iter 1219/4650] R3[168/1200], Temp: 0.9524, Energy: -85.007759-0.002917j
[2025-07-30 09:37:04] [Iter 1220/4650] R3[169/1200], Temp: 0.9519, Energy: -84.948266+0.004250j
[2025-07-30 09:37:19] [Iter 1221/4650] R3[170/1200], Temp: 0.9513, Energy: -84.985143+0.002652j
[2025-07-30 09:37:33] [Iter 1222/4650] R3[171/1200], Temp: 0.9507, Energy: -85.010289+0.003999j
[2025-07-30 09:37:47] [Iter 1223/4650] R3[172/1200], Temp: 0.9502, Energy: -84.950565+0.007137j
[2025-07-30 09:38:02] [Iter 1224/4650] R3[173/1200], Temp: 0.9496, Energy: -85.042394+0.004028j
[2025-07-30 09:38:17] [Iter 1225/4650] R3[174/1200], Temp: 0.9490, Energy: -84.994101+0.003467j
[2025-07-30 09:38:32] [Iter 1226/4650] R3[175/1200], Temp: 0.9484, Energy: -84.948545-0.010749j
[2025-07-30 09:38:47] [Iter 1227/4650] R3[176/1200], Temp: 0.9479, Energy: -85.047842+0.002415j
[2025-07-30 09:39:02] [Iter 1228/4650] R3[177/1200], Temp: 0.9473, Energy: -84.953990-0.012902j
[2025-07-30 09:39:16] [Iter 1229/4650] R3[178/1200], Temp: 0.9467, Energy: -84.933116-0.000061j
[2025-07-30 09:39:31] [Iter 1230/4650] R3[179/1200], Temp: 0.9461, Energy: -85.048742+0.008576j
[2025-07-30 09:39:46] [Iter 1231/4650] R3[180/1200], Temp: 0.9455, Energy: -84.979871+0.009954j
[2025-07-30 09:40:01] [Iter 1232/4650] R3[181/1200], Temp: 0.9449, Energy: -84.950428-0.013266j
[2025-07-30 09:40:16] [Iter 1233/4650] R3[182/1200], Temp: 0.9443, Energy: -84.889242+0.016749j
[2025-07-30 09:40:31] [Iter 1234/4650] R3[183/1200], Temp: 0.9437, Energy: -84.936397-0.001182j
[2025-07-30 09:40:46] [Iter 1235/4650] R3[184/1200], Temp: 0.9431, Energy: -84.838281-0.011586j
[2025-07-30 09:41:01] [Iter 1236/4650] R3[185/1200], Temp: 0.9425, Energy: -84.938948+0.003721j
[2025-07-30 09:41:16] [Iter 1237/4650] R3[186/1200], Temp: 0.9419, Energy: -84.976941+0.018451j
[2025-07-30 09:41:31] [Iter 1238/4650] R3[187/1200], Temp: 0.9413, Energy: -84.888868+0.000958j
[2025-07-30 09:41:46] [Iter 1239/4650] R3[188/1200], Temp: 0.9407, Energy: -84.918448+0.021250j
[2025-07-30 09:42:01] [Iter 1240/4650] R3[189/1200], Temp: 0.9400, Energy: -84.867964+0.009282j
[2025-07-30 09:42:16] [Iter 1241/4650] R3[190/1200], Temp: 0.9394, Energy: -84.807956+0.029669j
[2025-07-30 09:42:31] [Iter 1242/4650] R3[191/1200], Temp: 0.9388, Energy: -84.858455+0.014269j
[2025-07-30 09:42:46] [Iter 1243/4650] R3[192/1200], Temp: 0.9382, Energy: -84.865301+0.024115j
[2025-07-30 09:43:00] [Iter 1244/4650] R3[193/1200], Temp: 0.9375, Energy: -84.939111+0.020412j
[2025-07-30 09:43:15] [Iter 1245/4650] R3[194/1200], Temp: 0.9369, Energy: -84.915652+0.002317j
[2025-07-30 09:43:30] [Iter 1246/4650] R3[195/1200], Temp: 0.9362, Energy: -84.945635+0.003620j
[2025-07-30 09:43:45] [Iter 1247/4650] R3[196/1200], Temp: 0.9356, Energy: -84.932037+0.006045j
[2025-07-30 09:43:59] [Iter 1248/4650] R3[197/1200], Temp: 0.9350, Energy: -84.994548-0.004074j
[2025-07-30 09:44:13] [Iter 1249/4650] R3[198/1200], Temp: 0.9343, Energy: -84.938954-0.001920j
[2025-07-30 09:44:28] [Iter 1250/4650] R3[199/1200], Temp: 0.9337, Energy: -85.014549-0.007109j
[2025-07-30 09:44:43] [Iter 1251/4650] R3[200/1200], Temp: 0.9330, Energy: -84.997358+0.000909j
[2025-07-30 09:44:57] [Iter 1252/4650] R3[201/1200], Temp: 0.9324, Energy: -84.973712-0.012735j
[2025-07-30 09:45:12] [Iter 1253/4650] R3[202/1200], Temp: 0.9317, Energy: -84.973856-0.010065j
[2025-07-30 09:45:26] [Iter 1254/4650] R3[203/1200], Temp: 0.9310, Energy: -84.969083-0.000189j
[2025-07-30 09:45:41] [Iter 1255/4650] R3[204/1200], Temp: 0.9304, Energy: -84.950442-0.004455j
[2025-07-30 09:45:56] [Iter 1256/4650] R3[205/1200], Temp: 0.9297, Energy: -84.928778-0.005739j
[2025-07-30 09:46:11] [Iter 1257/4650] R3[206/1200], Temp: 0.9290, Energy: -84.950390+0.000046j
[2025-07-30 09:46:26] [Iter 1258/4650] R3[207/1200], Temp: 0.9284, Energy: -84.948669+0.002677j
[2025-07-30 09:46:41] [Iter 1259/4650] R3[208/1200], Temp: 0.9277, Energy: -84.944048-0.002524j
[2025-07-30 09:46:56] [Iter 1260/4650] R3[209/1200], Temp: 0.9270, Energy: -84.909453+0.007182j
[2025-07-30 09:47:10] [Iter 1261/4650] R3[210/1200], Temp: 0.9263, Energy: -84.906143+0.001484j
[2025-07-30 09:47:25] [Iter 1262/4650] R3[211/1200], Temp: 0.9256, Energy: -84.856162-0.002841j
[2025-07-30 09:47:40] [Iter 1263/4650] R3[212/1200], Temp: 0.9249, Energy: -84.843614+0.000677j
[2025-07-30 09:47:55] [Iter 1264/4650] R3[213/1200], Temp: 0.9243, Energy: -84.879874+0.013457j
[2025-07-30 09:48:10] [Iter 1265/4650] R3[214/1200], Temp: 0.9236, Energy: -84.841873+0.000062j
[2025-07-30 09:48:25] [Iter 1266/4650] R3[215/1200], Temp: 0.9229, Energy: -84.915226-0.005584j
[2025-07-30 09:48:40] [Iter 1267/4650] R3[216/1200], Temp: 0.9222, Energy: -84.851072-0.007075j
[2025-07-30 09:48:54] [Iter 1268/4650] R3[217/1200], Temp: 0.9215, Energy: -84.887633+0.002394j
[2025-07-30 09:49:09] [Iter 1269/4650] R3[218/1200], Temp: 0.9208, Energy: -84.964240+0.005311j
[2025-07-30 09:49:24] [Iter 1270/4650] R3[219/1200], Temp: 0.9200, Energy: -84.930438+0.002316j
[2025-07-30 09:49:38] [Iter 1271/4650] R3[220/1200], Temp: 0.9193, Energy: -84.921286-0.010042j
[2025-07-30 09:49:53] [Iter 1272/4650] R3[221/1200], Temp: 0.9186, Energy: -84.846878-0.025423j
[2025-07-30 09:50:08] [Iter 1273/4650] R3[222/1200], Temp: 0.9179, Energy: -84.942454+0.020729j
[2025-07-30 09:50:22] [Iter 1274/4650] R3[223/1200], Temp: 0.9172, Energy: -84.941646-0.008750j
[2025-07-30 09:50:37] [Iter 1275/4650] R3[224/1200], Temp: 0.9165, Energy: -84.915841-0.000377j
[2025-07-30 09:50:51] [Iter 1276/4650] R3[225/1200], Temp: 0.9157, Energy: -84.895492-0.011201j
[2025-07-30 09:51:06] [Iter 1277/4650] R3[226/1200], Temp: 0.9150, Energy: -84.944152+0.008927j
[2025-07-30 09:51:20] [Iter 1278/4650] R3[227/1200], Temp: 0.9143, Energy: -84.845905-0.015389j
[2025-07-30 09:51:35] [Iter 1279/4650] R3[228/1200], Temp: 0.9135, Energy: -84.883453-0.009649j
[2025-07-30 09:51:50] [Iter 1280/4650] R3[229/1200], Temp: 0.9128, Energy: -84.939111-0.002583j
[2025-07-30 09:52:04] [Iter 1281/4650] R3[230/1200], Temp: 0.9121, Energy: -84.927329-0.012037j
[2025-07-30 09:52:18] [Iter 1282/4650] R3[231/1200], Temp: 0.9113, Energy: -84.945725-0.009905j
[2025-07-30 09:52:33] [Iter 1283/4650] R3[232/1200], Temp: 0.9106, Energy: -84.958385-0.006028j
[2025-07-30 09:52:48] [Iter 1284/4650] R3[233/1200], Temp: 0.9098, Energy: -85.007757-0.001233j
[2025-07-30 09:53:03] [Iter 1285/4650] R3[234/1200], Temp: 0.9091, Energy: -84.952303-0.001915j
[2025-07-30 09:53:18] [Iter 1286/4650] R3[235/1200], Temp: 0.9083, Energy: -84.938113+0.002805j
[2025-07-30 09:53:33] [Iter 1287/4650] R3[236/1200], Temp: 0.9076, Energy: -84.934198-0.002226j
[2025-07-30 09:53:48] [Iter 1288/4650] R3[237/1200], Temp: 0.9068, Energy: -84.859988-0.007916j
[2025-07-30 09:54:03] [Iter 1289/4650] R3[238/1200], Temp: 0.9060, Energy: -84.841879+0.006062j
[2025-07-30 09:54:17] [Iter 1290/4650] R3[239/1200], Temp: 0.9053, Energy: -84.846741+0.002058j
[2025-07-30 09:54:32] [Iter 1291/4650] R3[240/1200], Temp: 0.9045, Energy: -84.930850+0.002089j
[2025-07-30 09:54:47] [Iter 1292/4650] R3[241/1200], Temp: 0.9037, Energy: -84.841189-0.000800j
[2025-07-30 09:55:01] [Iter 1293/4650] R3[242/1200], Temp: 0.9030, Energy: -84.902477+0.000974j
[2025-07-30 09:55:16] [Iter 1294/4650] R3[243/1200], Temp: 0.9022, Energy: -84.916812-0.006310j
[2025-07-30 09:55:31] [Iter 1295/4650] R3[244/1200], Temp: 0.9014, Energy: -84.903385-0.005138j
[2025-07-30 09:55:46] [Iter 1296/4650] R3[245/1200], Temp: 0.9006, Energy: -84.949717-0.005559j
[2025-07-30 09:56:01] [Iter 1297/4650] R3[246/1200], Temp: 0.8998, Energy: -84.957836+0.004947j
[2025-07-30 09:56:15] [Iter 1298/4650] R3[247/1200], Temp: 0.8991, Energy: -84.961014-0.004388j
[2025-07-30 09:56:30] [Iter 1299/4650] R3[248/1200], Temp: 0.8983, Energy: -84.884650-0.002847j
[2025-07-30 09:56:44] [Iter 1300/4650] R3[249/1200], Temp: 0.8975, Energy: -84.912590-0.011933j
[2025-07-30 09:56:59] [Iter 1301/4650] R3[250/1200], Temp: 0.8967, Energy: -85.035254-0.006597j
[2025-07-30 09:57:14] [Iter 1302/4650] R3[251/1200], Temp: 0.8959, Energy: -84.957312-0.008690j
[2025-07-30 09:57:28] [Iter 1303/4650] R3[252/1200], Temp: 0.8951, Energy: -84.960460-0.002559j
[2025-07-30 09:57:43] [Iter 1304/4650] R3[253/1200], Temp: 0.8943, Energy: -85.020534+0.003280j
[2025-07-30 09:57:57] [Iter 1305/4650] R3[254/1200], Temp: 0.8935, Energy: -84.989013+0.011232j
[2025-07-30 09:58:12] [Iter 1306/4650] R3[255/1200], Temp: 0.8927, Energy: -85.028869-0.001885j
[2025-07-30 09:58:27] [Iter 1307/4650] R3[256/1200], Temp: 0.8918, Energy: -84.957224+0.014078j
[2025-07-30 09:58:41] [Iter 1308/4650] R3[257/1200], Temp: 0.8910, Energy: -84.990577+0.011360j
[2025-07-30 09:58:56] [Iter 1309/4650] R3[258/1200], Temp: 0.8902, Energy: -84.937444+0.008844j
[2025-07-30 09:59:11] [Iter 1310/4650] R3[259/1200], Temp: 0.8894, Energy: -84.932662+0.003399j
[2025-07-30 09:59:25] [Iter 1311/4650] R3[260/1200], Temp: 0.8886, Energy: -84.926948+0.007017j
[2025-07-30 09:59:40] [Iter 1312/4650] R3[261/1200], Temp: 0.8877, Energy: -84.967334+0.006380j
[2025-07-30 09:59:55] [Iter 1313/4650] R3[262/1200], Temp: 0.8869, Energy: -84.884743+0.008361j
[2025-07-30 10:00:09] [Iter 1314/4650] R3[263/1200], Temp: 0.8861, Energy: -84.889221+0.007813j
[2025-07-30 10:00:24] [Iter 1315/4650] R3[264/1200], Temp: 0.8853, Energy: -84.850012-0.005806j
[2025-07-30 10:00:38] [Iter 1316/4650] R3[265/1200], Temp: 0.8844, Energy: -84.933479-0.006737j
[2025-07-30 10:00:53] [Iter 1317/4650] R3[266/1200], Temp: 0.8836, Energy: -84.902001-0.002492j
[2025-07-30 10:01:08] [Iter 1318/4650] R3[267/1200], Temp: 0.8827, Energy: -84.874221+0.006370j
[2025-07-30 10:01:23] [Iter 1319/4650] R3[268/1200], Temp: 0.8819, Energy: -84.888613+0.001179j
[2025-07-30 10:01:38] [Iter 1320/4650] R3[269/1200], Temp: 0.8811, Energy: -84.872054+0.002822j
[2025-07-30 10:01:53] [Iter 1321/4650] R3[270/1200], Temp: 0.8802, Energy: -84.883073+0.010624j
[2025-07-30 10:02:08] [Iter 1322/4650] R3[271/1200], Temp: 0.8794, Energy: -84.944791+0.006207j
[2025-07-30 10:02:22] [Iter 1323/4650] R3[272/1200], Temp: 0.8785, Energy: -84.928422-0.001341j
[2025-07-30 10:02:37] [Iter 1324/4650] R3[273/1200], Temp: 0.8776, Energy: -84.956386+0.011592j
[2025-07-30 10:02:51] [Iter 1325/4650] R3[274/1200], Temp: 0.8768, Energy: -84.971369+0.015243j
[2025-07-30 10:03:06] [Iter 1326/4650] R3[275/1200], Temp: 0.8759, Energy: -85.020510+0.004483j
[2025-07-30 10:03:21] [Iter 1327/4650] R3[276/1200], Temp: 0.8751, Energy: -84.945978+0.005663j
[2025-07-30 10:03:35] [Iter 1328/4650] R3[277/1200], Temp: 0.8742, Energy: -85.015511+0.003995j
[2025-07-30 10:03:50] [Iter 1329/4650] R3[278/1200], Temp: 0.8733, Energy: -85.020885+0.003325j
[2025-07-30 10:04:05] [Iter 1330/4650] R3[279/1200], Temp: 0.8724, Energy: -85.007527-0.005592j
[2025-07-30 10:04:19] [Iter 1331/4650] R3[280/1200], Temp: 0.8716, Energy: -85.130365-0.006391j
[2025-07-30 10:04:34] [Iter 1332/4650] R3[281/1200], Temp: 0.8707, Energy: -84.987202-0.012108j
[2025-07-30 10:04:49] [Iter 1333/4650] R3[282/1200], Temp: 0.8698, Energy: -84.989689-0.024750j
[2025-07-30 10:05:03] [Iter 1334/4650] R3[283/1200], Temp: 0.8689, Energy: -84.920857-0.006967j
[2025-07-30 10:05:18] [Iter 1335/4650] R3[284/1200], Temp: 0.8680, Energy: -84.982611-0.022556j
[2025-07-30 10:05:32] [Iter 1336/4650] R3[285/1200], Temp: 0.8672, Energy: -85.035737-0.023766j
[2025-07-30 10:05:47] [Iter 1337/4650] R3[286/1200], Temp: 0.8663, Energy: -85.005377-0.008052j
[2025-07-30 10:06:02] [Iter 1338/4650] R3[287/1200], Temp: 0.8654, Energy: -84.991098-0.002939j
[2025-07-30 10:06:17] [Iter 1339/4650] R3[288/1200], Temp: 0.8645, Energy: -84.988252+0.004284j
[2025-07-30 10:06:32] [Iter 1340/4650] R3[289/1200], Temp: 0.8636, Energy: -85.015650-0.006817j
[2025-07-30 10:06:46] [Iter 1341/4650] R3[290/1200], Temp: 0.8627, Energy: -85.044271-0.001285j
[2025-07-30 10:07:01] [Iter 1342/4650] R3[291/1200], Temp: 0.8618, Energy: -85.013069-0.003109j
[2025-07-30 10:07:16] [Iter 1343/4650] R3[292/1200], Temp: 0.8609, Energy: -85.024039+0.001944j
[2025-07-30 10:07:31] [Iter 1344/4650] R3[293/1200], Temp: 0.8600, Energy: -84.993759+0.003487j
[2025-07-30 10:07:46] [Iter 1345/4650] R3[294/1200], Temp: 0.8591, Energy: -85.015871+0.003740j
[2025-07-30 10:08:01] [Iter 1346/4650] R3[295/1200], Temp: 0.8582, Energy: -84.965478+0.005219j
[2025-07-30 10:08:15] [Iter 1347/4650] R3[296/1200], Temp: 0.8572, Energy: -85.035783+0.002225j
[2025-07-30 10:08:30] [Iter 1348/4650] R3[297/1200], Temp: 0.8563, Energy: -84.994504+0.026421j
[2025-07-30 10:08:45] [Iter 1349/4650] R3[298/1200], Temp: 0.8554, Energy: -84.963201+0.009540j
[2025-07-30 10:09:00] [Iter 1350/4650] R3[299/1200], Temp: 0.8545, Energy: -84.908605+0.005325j
[2025-07-30 10:09:15] [Iter 1351/4650] R3[300/1200], Temp: 0.8536, Energy: -84.914734-0.001205j
[2025-07-30 10:09:30] [Iter 1352/4650] R3[301/1200], Temp: 0.8526, Energy: -84.952727-0.001436j
[2025-07-30 10:09:44] [Iter 1353/4650] R3[302/1200], Temp: 0.8517, Energy: -84.954492-0.001500j
[2025-07-30 10:09:59] [Iter 1354/4650] R3[303/1200], Temp: 0.8508, Energy: -85.006033-0.005442j
[2025-07-30 10:10:13] [Iter 1355/4650] R3[304/1200], Temp: 0.8498, Energy: -84.962814+0.004098j
[2025-07-30 10:10:28] [Iter 1356/4650] R3[305/1200], Temp: 0.8489, Energy: -84.869076-0.005034j
[2025-07-30 10:10:43] [Iter 1357/4650] R3[306/1200], Temp: 0.8480, Energy: -84.921659-0.005787j
[2025-07-30 10:10:58] [Iter 1358/4650] R3[307/1200], Temp: 0.8470, Energy: -84.822513+0.008075j
[2025-07-30 10:11:13] [Iter 1359/4650] R3[308/1200], Temp: 0.8461, Energy: -84.781411+0.000464j
[2025-07-30 10:11:27] [Iter 1360/4650] R3[309/1200], Temp: 0.8451, Energy: -84.782928-0.001760j
[2025-07-30 10:11:42] [Iter 1361/4650] R3[310/1200], Temp: 0.8442, Energy: -84.887342-0.001204j
[2025-07-30 10:11:57] [Iter 1362/4650] R3[311/1200], Temp: 0.8432, Energy: -84.858615-0.014450j
[2025-07-30 10:12:12] [Iter 1363/4650] R3[312/1200], Temp: 0.8423, Energy: -84.932893-0.003080j
[2025-07-30 10:12:26] [Iter 1364/4650] R3[313/1200], Temp: 0.8413, Energy: -84.932817+0.000158j
[2025-07-30 10:12:41] [Iter 1365/4650] R3[314/1200], Temp: 0.8404, Energy: -84.951690-0.008966j
[2025-07-30 10:12:56] [Iter 1366/4650] R3[315/1200], Temp: 0.8394, Energy: -84.953413-0.002834j
[2025-07-30 10:13:11] [Iter 1367/4650] R3[316/1200], Temp: 0.8384, Energy: -84.930661-0.010074j
[2025-07-30 10:13:26] [Iter 1368/4650] R3[317/1200], Temp: 0.8375, Energy: -84.931846-0.014147j
[2025-07-30 10:13:41] [Iter 1369/4650] R3[318/1200], Temp: 0.8365, Energy: -84.930840+0.001791j
[2025-07-30 10:13:55] [Iter 1370/4650] R3[319/1200], Temp: 0.8355, Energy: -84.963651-0.009192j
[2025-07-30 10:14:10] [Iter 1371/4650] R3[320/1200], Temp: 0.8346, Energy: -84.950768+0.000925j
[2025-07-30 10:14:25] [Iter 1372/4650] R3[321/1200], Temp: 0.8336, Energy: -85.011612+0.013422j
[2025-07-30 10:14:40] [Iter 1373/4650] R3[322/1200], Temp: 0.8326, Energy: -84.990251+0.002932j
[2025-07-30 10:14:54] [Iter 1374/4650] R3[323/1200], Temp: 0.8316, Energy: -84.918830+0.013662j
[2025-07-30 10:15:09] [Iter 1375/4650] R3[324/1200], Temp: 0.8307, Energy: -84.997063+0.004003j
[2025-07-30 10:15:24] [Iter 1376/4650] R3[325/1200], Temp: 0.8297, Energy: -84.978089+0.001952j
[2025-07-30 10:15:39] [Iter 1377/4650] R3[326/1200], Temp: 0.8287, Energy: -85.003630+0.007290j
[2025-07-30 10:15:54] [Iter 1378/4650] R3[327/1200], Temp: 0.8277, Energy: -85.021310-0.000805j
[2025-07-30 10:16:08] [Iter 1379/4650] R3[328/1200], Temp: 0.8267, Energy: -84.975845+0.002338j
[2025-07-30 10:16:23] [Iter 1380/4650] R3[329/1200], Temp: 0.8257, Energy: -84.976683-0.023382j
[2025-07-30 10:16:38] [Iter 1381/4650] R3[330/1200], Temp: 0.8247, Energy: -84.906797-0.012013j
[2025-07-30 10:16:52] [Iter 1382/4650] R3[331/1200], Temp: 0.8237, Energy: -84.961479-0.020421j
[2025-07-30 10:17:07] [Iter 1383/4650] R3[332/1200], Temp: 0.8227, Energy: -84.981930-0.002024j
[2025-07-30 10:17:22] [Iter 1384/4650] R3[333/1200], Temp: 0.8217, Energy: -85.022022-0.007299j
[2025-07-30 10:17:36] [Iter 1385/4650] R3[334/1200], Temp: 0.8207, Energy: -85.042348+0.007357j
[2025-07-30 10:17:51] [Iter 1386/4650] R3[335/1200], Temp: 0.8197, Energy: -85.064997-0.002886j
[2025-07-30 10:18:06] [Iter 1387/4650] R3[336/1200], Temp: 0.8187, Energy: -85.066235-0.004360j
[2025-07-30 10:18:20] [Iter 1388/4650] R3[337/1200], Temp: 0.8177, Energy: -84.991463-0.001447j
[2025-07-30 10:18:35] [Iter 1389/4650] R3[338/1200], Temp: 0.8167, Energy: -84.961391+0.004876j
[2025-07-30 10:18:50] [Iter 1390/4650] R3[339/1200], Temp: 0.8157, Energy: -84.908214+0.005531j
[2025-07-30 10:19:04] [Iter 1391/4650] R3[340/1200], Temp: 0.8147, Energy: -84.953318-0.002364j
[2025-07-30 10:19:19] [Iter 1392/4650] R3[341/1200], Temp: 0.8136, Energy: -84.905017+0.004230j
[2025-07-30 10:19:34] [Iter 1393/4650] R3[342/1200], Temp: 0.8126, Energy: -85.005868-0.002630j
[2025-07-30 10:19:48] [Iter 1394/4650] R3[343/1200], Temp: 0.8116, Energy: -84.951687-0.002875j
[2025-07-30 10:20:03] [Iter 1395/4650] R3[344/1200], Temp: 0.8106, Energy: -84.923161+0.003389j
[2025-07-30 10:20:18] [Iter 1396/4650] R3[345/1200], Temp: 0.8095, Energy: -84.949669+0.001729j
[2025-07-30 10:20:32] [Iter 1397/4650] R3[346/1200], Temp: 0.8085, Energy: -84.940433+0.000417j
[2025-07-30 10:20:47] [Iter 1398/4650] R3[347/1200], Temp: 0.8075, Energy: -84.844415+0.010748j
[2025-07-30 10:21:02] [Iter 1399/4650] R3[348/1200], Temp: 0.8065, Energy: -84.895210+0.013393j
[2025-07-30 10:21:16] [Iter 1400/4650] R3[349/1200], Temp: 0.8054, Energy: -84.927306+0.012545j
[2025-07-30 10:21:31] [Iter 1401/4650] R3[350/1200], Temp: 0.8044, Energy: -84.914203+0.006567j
[2025-07-30 10:21:46] [Iter 1402/4650] R3[351/1200], Temp: 0.8033, Energy: -84.973257-0.017306j
[2025-07-30 10:22:01] [Iter 1403/4650] R3[352/1200], Temp: 0.8023, Energy: -85.018463-0.006144j
[2025-07-30 10:22:16] [Iter 1404/4650] R3[353/1200], Temp: 0.8013, Energy: -85.047927-0.007298j
[2025-07-30 10:22:31] [Iter 1405/4650] R3[354/1200], Temp: 0.8002, Energy: -85.011978-0.000469j
[2025-07-30 10:22:45] [Iter 1406/4650] R3[355/1200], Temp: 0.7992, Energy: -84.868409-0.035661j
[2025-07-30 10:23:00] [Iter 1407/4650] R3[356/1200], Temp: 0.7981, Energy: -84.917339-0.000708j
[2025-07-30 10:23:14] [Iter 1408/4650] R3[357/1200], Temp: 0.7971, Energy: -84.966224-0.012550j
[2025-07-30 10:23:29] [Iter 1409/4650] R3[358/1200], Temp: 0.7960, Energy: -85.073327-0.004411j
[2025-07-30 10:23:43] [Iter 1410/4650] R3[359/1200], Temp: 0.7950, Energy: -84.972918-0.001617j
[2025-07-30 10:23:58] [Iter 1411/4650] R3[360/1200], Temp: 0.7939, Energy: -84.961747-0.000101j
[2025-07-30 10:24:13] [Iter 1412/4650] R3[361/1200], Temp: 0.7928, Energy: -84.922378-0.002568j
[2025-07-30 10:24:27] [Iter 1413/4650] R3[362/1200], Temp: 0.7918, Energy: -84.918670+0.002656j
[2025-07-30 10:24:42] [Iter 1414/4650] R3[363/1200], Temp: 0.7907, Energy: -84.913341+0.008713j
[2025-07-30 10:24:56] [Iter 1415/4650] R3[364/1200], Temp: 0.7896, Energy: -84.923558+0.006765j
[2025-07-30 10:25:11] [Iter 1416/4650] R3[365/1200], Temp: 0.7886, Energy: -84.905708+0.009336j
[2025-07-30 10:25:26] [Iter 1417/4650] R3[366/1200], Temp: 0.7875, Energy: -84.975447+0.001715j
[2025-07-30 10:25:41] [Iter 1418/4650] R3[367/1200], Temp: 0.7864, Energy: -84.951772+0.003937j
[2025-07-30 10:25:56] [Iter 1419/4650] R3[368/1200], Temp: 0.7854, Energy: -84.999318+0.002646j
[2025-07-30 10:26:11] [Iter 1420/4650] R3[369/1200], Temp: 0.7843, Energy: -84.996423+0.000810j
[2025-07-30 10:26:25] [Iter 1421/4650] R3[370/1200], Temp: 0.7832, Energy: -84.954604+0.002658j
[2025-07-30 10:26:39] [Iter 1422/4650] R3[371/1200], Temp: 0.7821, Energy: -85.011089-0.000718j
[2025-07-30 10:26:54] [Iter 1423/4650] R3[372/1200], Temp: 0.7810, Energy: -84.989211-0.004672j
[2025-07-30 10:27:09] [Iter 1424/4650] R3[373/1200], Temp: 0.7800, Energy: -84.987464-0.002274j
[2025-07-30 10:27:24] [Iter 1425/4650] R3[374/1200], Temp: 0.7789, Energy: -84.977288-0.008353j
[2025-07-30 10:27:39] [Iter 1426/4650] R3[375/1200], Temp: 0.7778, Energy: -85.000505+0.004479j
[2025-07-30 10:27:53] [Iter 1427/4650] R3[376/1200], Temp: 0.7767, Energy: -85.008461+0.000431j
[2025-07-30 10:28:08] [Iter 1428/4650] R3[377/1200], Temp: 0.7756, Energy: -84.910946-0.010825j
[2025-07-30 10:28:23] [Iter 1429/4650] R3[378/1200], Temp: 0.7745, Energy: -84.949431-0.000111j
[2025-07-30 10:28:38] [Iter 1430/4650] R3[379/1200], Temp: 0.7734, Energy: -84.979406-0.008790j
[2025-07-30 10:28:52] [Iter 1431/4650] R3[380/1200], Temp: 0.7723, Energy: -84.948966-0.004215j
[2025-07-30 10:29:07] [Iter 1432/4650] R3[381/1200], Temp: 0.7712, Energy: -84.980569+0.015816j
[2025-07-30 10:29:22] [Iter 1433/4650] R3[382/1200], Temp: 0.7701, Energy: -84.917724+0.023610j
[2025-07-30 10:29:36] [Iter 1434/4650] R3[383/1200], Temp: 0.7690, Energy: -84.903679-0.015494j
[2025-07-30 10:29:51] [Iter 1435/4650] R3[384/1200], Temp: 0.7679, Energy: -84.924919+0.032290j
[2025-07-30 10:30:06] [Iter 1436/4650] R3[385/1200], Temp: 0.7668, Energy: -84.875681+0.013015j
[2025-07-30 10:30:21] [Iter 1437/4650] R3[386/1200], Temp: 0.7657, Energy: -84.905104+0.016715j
[2025-07-30 10:30:35] [Iter 1438/4650] R3[387/1200], Temp: 0.7646, Energy: -84.923690+0.009328j
[2025-07-30 10:30:50] [Iter 1439/4650] R3[388/1200], Temp: 0.7635, Energy: -84.922190+0.008877j
[2025-07-30 10:31:05] [Iter 1440/4650] R3[389/1200], Temp: 0.7624, Energy: -84.865609-0.016082j
[2025-07-30 10:31:19] [Iter 1441/4650] R3[390/1200], Temp: 0.7612, Energy: -84.928267-0.000335j
[2025-07-30 10:31:34] [Iter 1442/4650] R3[391/1200], Temp: 0.7601, Energy: -84.933756-0.000969j
[2025-07-30 10:31:48] [Iter 1443/4650] R3[392/1200], Temp: 0.7590, Energy: -84.951215-0.008229j
[2025-07-30 10:32:03] [Iter 1444/4650] R3[393/1200], Temp: 0.7579, Energy: -84.971063-0.009946j
[2025-07-30 10:32:18] [Iter 1445/4650] R3[394/1200], Temp: 0.7568, Energy: -84.924059-0.000987j
[2025-07-30 10:32:32] [Iter 1446/4650] R3[395/1200], Temp: 0.7556, Energy: -84.946315-0.008749j
[2025-07-30 10:32:47] [Iter 1447/4650] R3[396/1200], Temp: 0.7545, Energy: -84.949107+0.018466j
[2025-07-30 10:33:02] [Iter 1448/4650] R3[397/1200], Temp: 0.7534, Energy: -84.923310+0.000261j
[2025-07-30 10:33:17] [Iter 1449/4650] R3[398/1200], Temp: 0.7523, Energy: -84.871263+0.007867j
[2025-07-30 10:33:32] [Iter 1450/4650] R3[399/1200], Temp: 0.7511, Energy: -84.931543+0.014762j
[2025-07-30 10:33:47] [Iter 1451/4650] R3[400/1200], Temp: 0.7500, Energy: -85.035622+0.004586j
[2025-07-30 10:34:02] [Iter 1452/4650] R3[401/1200], Temp: 0.7489, Energy: -84.952043+0.009435j
[2025-07-30 10:34:16] [Iter 1453/4650] R3[402/1200], Temp: 0.7477, Energy: -84.957409+0.001029j
[2025-07-30 10:34:31] [Iter 1454/4650] R3[403/1200], Temp: 0.7466, Energy: -84.927476+0.001238j
[2025-07-30 10:34:46] [Iter 1455/4650] R3[404/1200], Temp: 0.7455, Energy: -84.969371+0.013789j
[2025-07-30 10:35:00] [Iter 1456/4650] R3[405/1200], Temp: 0.7443, Energy: -84.966896+0.000341j
[2025-07-30 10:35:15] [Iter 1457/4650] R3[406/1200], Temp: 0.7432, Energy: -84.953159-0.000527j
[2025-07-30 10:35:30] [Iter 1458/4650] R3[407/1200], Temp: 0.7420, Energy: -84.938975+0.014483j
[2025-07-30 10:35:45] [Iter 1459/4650] R3[408/1200], Temp: 0.7409, Energy: -84.932191+0.008218j
[2025-07-30 10:35:59] [Iter 1460/4650] R3[409/1200], Temp: 0.7397, Energy: -84.902327+0.003857j
[2025-07-30 10:36:14] [Iter 1461/4650] R3[410/1200], Temp: 0.7386, Energy: -84.860581+0.017036j
[2025-07-30 10:36:29] [Iter 1462/4650] R3[411/1200], Temp: 0.7374, Energy: -84.771293+0.007053j
[2025-07-30 10:36:43] [Iter 1463/4650] R3[412/1200], Temp: 0.7363, Energy: -84.868758+0.023935j
[2025-07-30 10:36:58] [Iter 1464/4650] R3[413/1200], Temp: 0.7351, Energy: -84.938550+0.007240j
[2025-07-30 10:37:13] [Iter 1465/4650] R3[414/1200], Temp: 0.7340, Energy: -84.991962-0.007455j
[2025-07-30 10:37:27] [Iter 1466/4650] R3[415/1200], Temp: 0.7328, Energy: -85.055937+0.010076j
[2025-07-30 10:37:42] [Iter 1467/4650] R3[416/1200], Temp: 0.7316, Energy: -84.867932-0.000647j
[2025-07-30 10:37:57] [Iter 1468/4650] R3[417/1200], Temp: 0.7305, Energy: -84.897021-0.001250j
[2025-07-30 10:38:12] [Iter 1469/4650] R3[418/1200], Temp: 0.7293, Energy: -84.900058+0.000885j
[2025-07-30 10:38:27] [Iter 1470/4650] R3[419/1200], Temp: 0.7282, Energy: -84.912142-0.005244j
[2025-07-30 10:38:41] [Iter 1471/4650] R3[420/1200], Temp: 0.7270, Energy: -84.884028-0.013244j
[2025-07-30 10:38:55] [Iter 1472/4650] R3[421/1200], Temp: 0.7258, Energy: -84.946618+0.010653j
[2025-07-30 10:39:10] [Iter 1473/4650] R3[422/1200], Temp: 0.7247, Energy: -84.882018-0.000119j
[2025-07-30 10:39:25] [Iter 1474/4650] R3[423/1200], Temp: 0.7235, Energy: -84.856757-0.014538j
[2025-07-30 10:39:40] [Iter 1475/4650] R3[424/1200], Temp: 0.7223, Energy: -84.889982-0.004666j
[2025-07-30 10:39:55] [Iter 1476/4650] R3[425/1200], Temp: 0.7211, Energy: -84.926599-0.013792j
[2025-07-30 10:40:09] [Iter 1477/4650] R3[426/1200], Temp: 0.7200, Energy: -84.933801-0.011912j
[2025-07-30 10:40:24] [Iter 1478/4650] R3[427/1200], Temp: 0.7188, Energy: -84.956484-0.000118j
[2025-07-30 10:40:38] [Iter 1479/4650] R3[428/1200], Temp: 0.7176, Energy: -84.944091-0.000966j
[2025-07-30 10:40:53] [Iter 1480/4650] R3[429/1200], Temp: 0.7164, Energy: -84.951602-0.001136j
[2025-07-30 10:41:08] [Iter 1481/4650] R3[430/1200], Temp: 0.7153, Energy: -84.925209-0.003986j
[2025-07-30 10:41:23] [Iter 1482/4650] R3[431/1200], Temp: 0.7141, Energy: -84.929128-0.000129j
[2025-07-30 10:41:37] [Iter 1483/4650] R3[432/1200], Temp: 0.7129, Energy: -84.929507+0.002843j
[2025-07-30 10:41:52] [Iter 1484/4650] R3[433/1200], Temp: 0.7117, Energy: -84.952683-0.005923j
[2025-07-30 10:42:07] [Iter 1485/4650] R3[434/1200], Temp: 0.7105, Energy: -84.973011+0.003494j
[2025-07-30 10:42:22] [Iter 1486/4650] R3[435/1200], Temp: 0.7093, Energy: -84.995053-0.015877j
[2025-07-30 10:42:37] [Iter 1487/4650] R3[436/1200], Temp: 0.7081, Energy: -85.002850+0.014613j
[2025-07-30 10:42:51] [Iter 1488/4650] R3[437/1200], Temp: 0.7069, Energy: -84.986274-0.006396j
[2025-07-30 10:43:06] [Iter 1489/4650] R3[438/1200], Temp: 0.7058, Energy: -84.950861-0.012652j
[2025-07-30 10:43:21] [Iter 1490/4650] R3[439/1200], Temp: 0.7046, Energy: -84.998659+0.017194j
[2025-07-30 10:43:36] [Iter 1491/4650] R3[440/1200], Temp: 0.7034, Energy: -84.983762+0.004772j
[2025-07-30 10:43:51] [Iter 1492/4650] R3[441/1200], Temp: 0.7022, Energy: -84.929211+0.003444j
[2025-07-30 10:44:06] [Iter 1493/4650] R3[442/1200], Temp: 0.7010, Energy: -84.990372-0.002017j
[2025-07-30 10:44:20] [Iter 1494/4650] R3[443/1200], Temp: 0.6998, Energy: -84.933667-0.005324j
[2025-07-30 10:44:35] [Iter 1495/4650] R3[444/1200], Temp: 0.6986, Energy: -84.953411-0.007875j
[2025-07-30 10:44:50] [Iter 1496/4650] R3[445/1200], Temp: 0.6974, Energy: -84.885973-0.001077j
[2025-07-30 10:45:05] [Iter 1497/4650] R3[446/1200], Temp: 0.6962, Energy: -84.980448+0.003472j
[2025-07-30 10:45:20] [Iter 1498/4650] R3[447/1200], Temp: 0.6950, Energy: -84.898215+0.000224j
[2025-07-30 10:45:34] [Iter 1499/4650] R3[448/1200], Temp: 0.6938, Energy: -84.897948-0.006773j
[2025-07-30 10:45:49] [Iter 1500/4650] R3[449/1200], Temp: 0.6926, Energy: -84.886171-0.004628j
[2025-07-30 10:45:49] ✓ Checkpoint saved: checkpoint_iter_001500.pkl
[2025-07-30 10:46:04] [Iter 1501/4650] R3[450/1200], Temp: 0.6913, Energy: -84.838089-0.000911j
[2025-07-30 10:46:19] [Iter 1502/4650] R3[451/1200], Temp: 0.6901, Energy: -84.867994-0.008402j
[2025-07-30 10:46:34] [Iter 1503/4650] R3[452/1200], Temp: 0.6889, Energy: -84.828724-0.006101j
[2025-07-30 10:46:48] [Iter 1504/4650] R3[453/1200], Temp: 0.6877, Energy: -84.886079-0.001338j
[2025-07-30 10:47:03] [Iter 1505/4650] R3[454/1200], Temp: 0.6865, Energy: -84.918862-0.012324j
[2025-07-30 10:47:18] [Iter 1506/4650] R3[455/1200], Temp: 0.6853, Energy: -84.957663+0.002433j
[2025-07-30 10:47:33] [Iter 1507/4650] R3[456/1200], Temp: 0.6841, Energy: -84.933692-0.011772j
[2025-07-30 10:47:48] [Iter 1508/4650] R3[457/1200], Temp: 0.6828, Energy: -84.973396-0.000990j
[2025-07-30 10:48:02] [Iter 1509/4650] R3[458/1200], Temp: 0.6816, Energy: -84.953229-0.013519j
[2025-07-30 10:48:17] [Iter 1510/4650] R3[459/1200], Temp: 0.6804, Energy: -84.910900-0.010998j
[2025-07-30 10:48:31] [Iter 1511/4650] R3[460/1200], Temp: 0.6792, Energy: -84.929525+0.002700j
[2025-07-30 10:48:46] [Iter 1512/4650] R3[461/1200], Temp: 0.6780, Energy: -84.889816+0.000071j
[2025-07-30 10:49:01] [Iter 1513/4650] R3[462/1200], Temp: 0.6767, Energy: -84.928370+0.001243j
[2025-07-30 10:49:15] [Iter 1514/4650] R3[463/1200], Temp: 0.6755, Energy: -84.953392-0.001429j
[2025-07-30 10:49:30] [Iter 1515/4650] R3[464/1200], Temp: 0.6743, Energy: -84.894455+0.004931j
[2025-07-30 10:49:45] [Iter 1516/4650] R3[465/1200], Temp: 0.6731, Energy: -84.895861+0.005452j
[2025-07-30 10:49:59] [Iter 1517/4650] R3[466/1200], Temp: 0.6718, Energy: -84.922905-0.002731j
[2025-07-30 10:50:14] [Iter 1518/4650] R3[467/1200], Temp: 0.6706, Energy: -84.859958+0.014505j
[2025-07-30 10:50:29] [Iter 1519/4650] R3[468/1200], Temp: 0.6694, Energy: -84.889808+0.008220j
[2025-07-30 10:50:44] [Iter 1520/4650] R3[469/1200], Temp: 0.6681, Energy: -84.924825+0.016456j
[2025-07-30 10:50:59] [Iter 1521/4650] R3[470/1200], Temp: 0.6669, Energy: -84.849629+0.002694j
[2025-07-30 10:51:13] [Iter 1522/4650] R3[471/1200], Temp: 0.6657, Energy: -84.948696+0.000936j
[2025-07-30 10:51:27] [Iter 1523/4650] R3[472/1200], Temp: 0.6644, Energy: -84.908308-0.008850j
[2025-07-30 10:51:42] [Iter 1524/4650] R3[473/1200], Temp: 0.6632, Energy: -84.948252-0.018284j
[2025-07-30 10:51:57] [Iter 1525/4650] R3[474/1200], Temp: 0.6620, Energy: -85.027286-0.000156j
[2025-07-30 10:52:12] [Iter 1526/4650] R3[475/1200], Temp: 0.6607, Energy: -84.984677+0.007105j
[2025-07-30 10:52:27] [Iter 1527/4650] R3[476/1200], Temp: 0.6595, Energy: -84.945887-0.011830j
[2025-07-30 10:52:41] [Iter 1528/4650] R3[477/1200], Temp: 0.6582, Energy: -84.886412+0.009575j
[2025-07-30 10:52:56] [Iter 1529/4650] R3[478/1200], Temp: 0.6570, Energy: -84.942246-0.006199j
[2025-07-30 10:53:11] [Iter 1530/4650] R3[479/1200], Temp: 0.6558, Energy: -84.930186-0.001800j
[2025-07-30 10:53:26] [Iter 1531/4650] R3[480/1200], Temp: 0.6545, Energy: -84.848554-0.003251j
[2025-07-30 10:53:41] [Iter 1532/4650] R3[481/1200], Temp: 0.6533, Energy: -84.935597-0.000350j
[2025-07-30 10:53:56] [Iter 1533/4650] R3[482/1200], Temp: 0.6520, Energy: -84.967390+0.005939j
[2025-07-30 10:54:11] [Iter 1534/4650] R3[483/1200], Temp: 0.6508, Energy: -84.892540-0.003712j
[2025-07-30 10:54:25] [Iter 1535/4650] R3[484/1200], Temp: 0.6495, Energy: -84.899101-0.002647j
[2025-07-30 10:54:40] [Iter 1536/4650] R3[485/1200], Temp: 0.6483, Energy: -84.921142-0.000915j
[2025-07-30 10:54:55] [Iter 1537/4650] R3[486/1200], Temp: 0.6470, Energy: -84.864825+0.002287j
[2025-07-30 10:55:09] [Iter 1538/4650] R3[487/1200], Temp: 0.6458, Energy: -84.880002+0.002407j
[2025-07-30 10:55:24] [Iter 1539/4650] R3[488/1200], Temp: 0.6445, Energy: -84.891536+0.005401j
[2025-07-30 10:55:39] [Iter 1540/4650] R3[489/1200], Temp: 0.6433, Energy: -84.884091-0.006946j
[2025-07-30 10:55:54] [Iter 1541/4650] R3[490/1200], Temp: 0.6420, Energy: -84.891969+0.005110j
[2025-07-30 10:56:09] [Iter 1542/4650] R3[491/1200], Temp: 0.6408, Energy: -84.932488-0.000237j
[2025-07-30 10:56:23] [Iter 1543/4650] R3[492/1200], Temp: 0.6395, Energy: -84.894998-0.000159j
[2025-07-30 10:56:38] [Iter 1544/4650] R3[493/1200], Temp: 0.6382, Energy: -84.850541-0.002399j
[2025-07-30 10:56:53] [Iter 1545/4650] R3[494/1200], Temp: 0.6370, Energy: -84.932436+0.000082j
[2025-07-30 10:57:08] [Iter 1546/4650] R3[495/1200], Temp: 0.6357, Energy: -84.908241-0.003319j
[2025-07-30 10:57:22] [Iter 1547/4650] R3[496/1200], Temp: 0.6345, Energy: -84.891566-0.004504j
[2025-07-30 10:57:37] [Iter 1548/4650] R3[497/1200], Temp: 0.6332, Energy: -84.904706-0.013702j
[2025-07-30 10:57:51] [Iter 1549/4650] R3[498/1200], Temp: 0.6319, Energy: -84.896538-0.006255j
[2025-07-30 10:58:06] [Iter 1550/4650] R3[499/1200], Temp: 0.6307, Energy: -84.935395-0.004810j
[2025-07-30 10:58:21] [Iter 1551/4650] R3[500/1200], Temp: 0.6294, Energy: -84.956799-0.006505j
[2025-07-30 10:58:36] [Iter 1552/4650] R3[501/1200], Temp: 0.6281, Energy: -84.972944-0.007525j
[2025-07-30 10:58:50] [Iter 1553/4650] R3[502/1200], Temp: 0.6269, Energy: -84.926209-0.009758j
[2025-07-30 10:59:05] [Iter 1554/4650] R3[503/1200], Temp: 0.6256, Energy: -84.941144-0.017673j
[2025-07-30 10:59:19] [Iter 1555/4650] R3[504/1200], Temp: 0.6243, Energy: -84.941169-0.004280j
[2025-07-30 10:59:33] [Iter 1556/4650] R3[505/1200], Temp: 0.6231, Energy: -84.990041-0.012807j
[2025-07-30 10:59:48] [Iter 1557/4650] R3[506/1200], Temp: 0.6218, Energy: -85.034468-0.002263j
[2025-07-30 11:00:03] [Iter 1558/4650] R3[507/1200], Temp: 0.6205, Energy: -84.998604-0.001321j
[2025-07-30 11:00:17] [Iter 1559/4650] R3[508/1200], Temp: 0.6193, Energy: -85.005435-0.003452j
[2025-07-30 11:00:31] [Iter 1560/4650] R3[509/1200], Temp: 0.6180, Energy: -84.923861-0.015126j
[2025-07-30 11:00:46] [Iter 1561/4650] R3[510/1200], Temp: 0.6167, Energy: -84.907393-0.001107j
[2025-07-30 11:01:01] [Iter 1562/4650] R3[511/1200], Temp: 0.6154, Energy: -84.950802-0.004281j
[2025-07-30 11:01:15] [Iter 1563/4650] R3[512/1200], Temp: 0.6142, Energy: -84.949793+0.004643j
[2025-07-30 11:01:30] [Iter 1564/4650] R3[513/1200], Temp: 0.6129, Energy: -84.884137-0.011113j
[2025-07-30 11:01:45] [Iter 1565/4650] R3[514/1200], Temp: 0.6116, Energy: -84.918298-0.007815j
[2025-07-30 11:02:00] [Iter 1566/4650] R3[515/1200], Temp: 0.6103, Energy: -84.957805-0.004819j
[2025-07-30 11:02:15] [Iter 1567/4650] R3[516/1200], Temp: 0.6091, Energy: -84.941908-0.011876j
[2025-07-30 11:02:29] [Iter 1568/4650] R3[517/1200], Temp: 0.6078, Energy: -84.900579+0.010207j
[2025-07-30 11:02:44] [Iter 1569/4650] R3[518/1200], Temp: 0.6065, Energy: -84.870060+0.009527j
[2025-07-30 11:02:58] [Iter 1570/4650] R3[519/1200], Temp: 0.6052, Energy: -84.872247+0.015498j
[2025-07-30 11:03:13] [Iter 1571/4650] R3[520/1200], Temp: 0.6040, Energy: -84.900368+0.007524j
[2025-07-30 11:03:28] [Iter 1572/4650] R3[521/1200], Temp: 0.6027, Energy: -84.946402-0.001376j
[2025-07-30 11:03:42] [Iter 1573/4650] R3[522/1200], Temp: 0.6014, Energy: -84.949512+0.000355j
[2025-07-30 11:03:57] [Iter 1574/4650] R3[523/1200], Temp: 0.6001, Energy: -84.926038+0.000734j
[2025-07-30 11:04:12] [Iter 1575/4650] R3[524/1200], Temp: 0.5988, Energy: -84.954478+0.007601j
[2025-07-30 11:04:27] [Iter 1576/4650] R3[525/1200], Temp: 0.5975, Energy: -84.958158+0.006918j
[2025-07-30 11:04:41] [Iter 1577/4650] R3[526/1200], Temp: 0.5963, Energy: -84.863945+0.001433j
[2025-07-30 11:04:56] [Iter 1578/4650] R3[527/1200], Temp: 0.5950, Energy: -84.795511-0.007453j
[2025-07-30 11:05:11] [Iter 1579/4650] R3[528/1200], Temp: 0.5937, Energy: -84.888469+0.013155j
[2025-07-30 11:05:26] [Iter 1580/4650] R3[529/1200], Temp: 0.5924, Energy: -84.871341-0.013237j
[2025-07-30 11:05:41] [Iter 1581/4650] R3[530/1200], Temp: 0.5911, Energy: -84.863042-0.006390j
[2025-07-30 11:05:55] [Iter 1582/4650] R3[531/1200], Temp: 0.5898, Energy: -84.938344-0.003038j
[2025-07-30 11:06:10] [Iter 1583/4650] R3[532/1200], Temp: 0.5885, Energy: -84.972197+0.003443j
[2025-07-30 11:06:25] [Iter 1584/4650] R3[533/1200], Temp: 0.5873, Energy: -84.955586-0.029157j
[2025-07-30 11:06:40] [Iter 1585/4650] R3[534/1200], Temp: 0.5860, Energy: -85.066110+0.000471j
[2025-07-30 11:06:55] [Iter 1586/4650] R3[535/1200], Temp: 0.5847, Energy: -85.074491-0.026785j
[2025-07-30 11:07:10] [Iter 1587/4650] R3[536/1200], Temp: 0.5834, Energy: -85.059062-0.015669j
[2025-07-30 11:07:24] [Iter 1588/4650] R3[537/1200], Temp: 0.5821, Energy: -85.072060-0.003131j
[2025-07-30 11:07:39] [Iter 1589/4650] R3[538/1200], Temp: 0.5808, Energy: -85.065138-0.000427j
[2025-07-30 11:07:54] [Iter 1590/4650] R3[539/1200], Temp: 0.5795, Energy: -85.018897+0.003294j
[2025-07-30 11:08:09] [Iter 1591/4650] R3[540/1200], Temp: 0.5782, Energy: -85.057543-0.011067j
[2025-07-30 11:08:24] [Iter 1592/4650] R3[541/1200], Temp: 0.5769, Energy: -85.078766-0.004681j
[2025-07-30 11:08:39] [Iter 1593/4650] R3[542/1200], Temp: 0.5756, Energy: -84.994565+0.001178j
[2025-07-30 11:08:54] [Iter 1594/4650] R3[543/1200], Temp: 0.5743, Energy: -84.986612+0.009762j
[2025-07-30 11:09:09] [Iter 1595/4650] R3[544/1200], Temp: 0.5730, Energy: -84.989134+0.000650j
[2025-07-30 11:09:24] [Iter 1596/4650] R3[545/1200], Temp: 0.5717, Energy: -84.980676-0.001281j
[2025-07-30 11:09:38] [Iter 1597/4650] R3[546/1200], Temp: 0.5705, Energy: -85.038035+0.015603j
[2025-07-30 11:09:52] [Iter 1598/4650] R3[547/1200], Temp: 0.5692, Energy: -85.025852+0.000320j
[2025-07-30 11:10:07] [Iter 1599/4650] R3[548/1200], Temp: 0.5679, Energy: -85.052796+0.000293j
[2025-07-30 11:10:22] [Iter 1600/4650] R3[549/1200], Temp: 0.5666, Energy: -85.020417-0.024196j
[2025-07-30 11:10:36] [Iter 1601/4650] R3[550/1200], Temp: 0.5653, Energy: -85.021589+0.000318j
[2025-07-30 11:10:50] [Iter 1602/4650] R3[551/1200], Temp: 0.5640, Energy: -84.966325-0.005661j
[2025-07-30 11:11:05] [Iter 1603/4650] R3[552/1200], Temp: 0.5627, Energy: -85.020143-0.005307j
[2025-07-30 11:11:20] [Iter 1604/4650] R3[553/1200], Temp: 0.5614, Energy: -84.859312+0.001331j
[2025-07-30 11:11:35] [Iter 1605/4650] R3[554/1200], Temp: 0.5601, Energy: -84.886252+0.001477j
[2025-07-30 11:11:50] [Iter 1606/4650] R3[555/1200], Temp: 0.5588, Energy: -84.917636-0.000236j
[2025-07-30 11:12:04] [Iter 1607/4650] R3[556/1200], Temp: 0.5575, Energy: -84.936199+0.010318j
[2025-07-30 11:12:18] [Iter 1608/4650] R3[557/1200], Temp: 0.5562, Energy: -84.901295-0.004176j
[2025-07-30 11:12:33] [Iter 1609/4650] R3[558/1200], Temp: 0.5549, Energy: -84.954002-0.008947j
[2025-07-30 11:12:47] [Iter 1610/4650] R3[559/1200], Temp: 0.5536, Energy: -85.031634-0.011531j
[2025-07-30 11:13:02] [Iter 1611/4650] R3[560/1200], Temp: 0.5523, Energy: -85.037570-0.006607j
[2025-07-30 11:13:17] [Iter 1612/4650] R3[561/1200], Temp: 0.5510, Energy: -85.066573-0.012291j
[2025-07-30 11:13:31] [Iter 1613/4650] R3[562/1200], Temp: 0.5497, Energy: -85.025650-0.010314j
[2025-07-30 11:13:46] [Iter 1614/4650] R3[563/1200], Temp: 0.5484, Energy: -85.109809-0.013809j
[2025-07-30 11:14:01] [Iter 1615/4650] R3[564/1200], Temp: 0.5471, Energy: -85.048016+0.005844j
[2025-07-30 11:14:15] [Iter 1616/4650] R3[565/1200], Temp: 0.5458, Energy: -85.018684+0.001821j
[2025-07-30 11:14:31] [Iter 1617/4650] R3[566/1200], Temp: 0.5444, Energy: -85.052350-0.001273j
[2025-07-30 11:14:46] [Iter 1618/4650] R3[567/1200], Temp: 0.5431, Energy: -85.006743-0.009444j
[2025-07-30 11:15:01] [Iter 1619/4650] R3[568/1200], Temp: 0.5418, Energy: -85.058696-0.014331j
[2025-07-30 11:15:16] [Iter 1620/4650] R3[569/1200], Temp: 0.5405, Energy: -84.992179-0.008700j
[2025-07-30 11:15:31] [Iter 1621/4650] R3[570/1200], Temp: 0.5392, Energy: -84.936472-0.017228j
[2025-07-30 11:15:45] [Iter 1622/4650] R3[571/1200], Temp: 0.5379, Energy: -84.928839-0.004456j
[2025-07-30 11:16:00] [Iter 1623/4650] R3[572/1200], Temp: 0.5366, Energy: -84.926460-0.001413j
[2025-07-30 11:16:14] [Iter 1624/4650] R3[573/1200], Temp: 0.5353, Energy: -84.907807-0.003518j
[2025-07-30 11:16:28] [Iter 1625/4650] R3[574/1200], Temp: 0.5340, Energy: -84.943404-0.003667j
[2025-07-30 11:16:43] [Iter 1626/4650] R3[575/1200], Temp: 0.5327, Energy: -84.987058+0.001605j
[2025-07-30 11:16:58] [Iter 1627/4650] R3[576/1200], Temp: 0.5314, Energy: -84.946979-0.012019j
[2025-07-30 11:17:13] [Iter 1628/4650] R3[577/1200], Temp: 0.5301, Energy: -84.880834-0.000668j
[2025-07-30 11:17:28] [Iter 1629/4650] R3[578/1200], Temp: 0.5288, Energy: -84.992735+0.008967j
[2025-07-30 11:17:43] [Iter 1630/4650] R3[579/1200], Temp: 0.5275, Energy: -84.912913-0.003113j
[2025-07-30 11:17:58] [Iter 1631/4650] R3[580/1200], Temp: 0.5262, Energy: -84.877196+0.008462j
[2025-07-30 11:18:12] [Iter 1632/4650] R3[581/1200], Temp: 0.5249, Energy: -84.926887+0.003996j
[2025-07-30 11:18:27] [Iter 1633/4650] R3[582/1200], Temp: 0.5236, Energy: -84.977878-0.003523j
[2025-07-30 11:18:42] [Iter 1634/4650] R3[583/1200], Temp: 0.5222, Energy: -84.969729+0.001428j
[2025-07-30 11:18:57] [Iter 1635/4650] R3[584/1200], Temp: 0.5209, Energy: -84.956548-0.005811j
[2025-07-30 11:19:11] [Iter 1636/4650] R3[585/1200], Temp: 0.5196, Energy: -84.921769+0.002717j
[2025-07-30 11:19:27] [Iter 1637/4650] R3[586/1200], Temp: 0.5183, Energy: -84.991328+0.001609j
[2025-07-30 11:19:42] [Iter 1638/4650] R3[587/1200], Temp: 0.5170, Energy: -84.984731+0.005861j
[2025-07-30 11:19:57] [Iter 1639/4650] R3[588/1200], Temp: 0.5157, Energy: -84.979838+0.015151j
[2025-07-30 11:20:12] [Iter 1640/4650] R3[589/1200], Temp: 0.5144, Energy: -84.938762+0.010420j
[2025-07-30 11:20:26] [Iter 1641/4650] R3[590/1200], Temp: 0.5131, Energy: -84.939397+0.017378j
[2025-07-30 11:20:41] [Iter 1642/4650] R3[591/1200], Temp: 0.5118, Energy: -84.964815+0.007435j
[2025-07-30 11:20:56] [Iter 1643/4650] R3[592/1200], Temp: 0.5105, Energy: -84.943873-0.001205j
[2025-07-30 11:21:11] [Iter 1644/4650] R3[593/1200], Temp: 0.5092, Energy: -84.975975+0.002783j
[2025-07-30 11:21:26] [Iter 1645/4650] R3[594/1200], Temp: 0.5079, Energy: -84.973489+0.014476j
[2025-07-30 11:21:40] [Iter 1646/4650] R3[595/1200], Temp: 0.5065, Energy: -85.022833+0.010803j
[2025-07-30 11:21:55] [Iter 1647/4650] R3[596/1200], Temp: 0.5052, Energy: -84.975012+0.016979j
[2025-07-30 11:22:10] [Iter 1648/4650] R3[597/1200], Temp: 0.5039, Energy: -84.989523+0.007969j
[2025-07-30 11:22:24] [Iter 1649/4650] R3[598/1200], Temp: 0.5026, Energy: -85.004146+0.023499j
[2025-07-30 11:22:39] [Iter 1650/4650] R3[599/1200], Temp: 0.5013, Energy: -85.046555+0.021357j
[2025-07-30 11:22:54] [Iter 1651/4650] R3[600/1200], Temp: 0.5000, Energy: -84.961421+0.008777j
[2025-07-30 11:23:09] [Iter 1652/4650] R3[601/1200], Temp: 0.4987, Energy: -85.006736+0.007358j
[2025-07-30 11:23:23] [Iter 1653/4650] R3[602/1200], Temp: 0.4974, Energy: -84.960037+0.005350j
[2025-07-30 11:23:38] [Iter 1654/4650] R3[603/1200], Temp: 0.4961, Energy: -84.917870+0.003596j
[2025-07-30 11:23:53] [Iter 1655/4650] R3[604/1200], Temp: 0.4948, Energy: -84.946102-0.000547j
[2025-07-30 11:24:07] [Iter 1656/4650] R3[605/1200], Temp: 0.4935, Energy: -84.987511+0.004261j
[2025-07-30 11:24:22] [Iter 1657/4650] R3[606/1200], Temp: 0.4921, Energy: -84.971603-0.001687j
[2025-07-30 11:24:36] [Iter 1658/4650] R3[607/1200], Temp: 0.4908, Energy: -84.985679+0.006383j
[2025-07-30 11:24:51] [Iter 1659/4650] R3[608/1200], Temp: 0.4895, Energy: -84.977019+0.001540j
[2025-07-30 11:25:05] [Iter 1660/4650] R3[609/1200], Temp: 0.4882, Energy: -84.990932+0.003159j
[2025-07-30 11:25:20] [Iter 1661/4650] R3[610/1200], Temp: 0.4869, Energy: -84.984951+0.000123j
[2025-07-30 11:25:34] [Iter 1662/4650] R3[611/1200], Temp: 0.4856, Energy: -84.979198+0.005087j
[2025-07-30 11:25:49] [Iter 1663/4650] R3[612/1200], Temp: 0.4843, Energy: -84.926172-0.000488j
[2025-07-30 11:26:04] [Iter 1664/4650] R3[613/1200], Temp: 0.4830, Energy: -84.986800-0.001518j
[2025-07-30 11:26:18] [Iter 1665/4650] R3[614/1200], Temp: 0.4817, Energy: -84.928347+0.004193j
[2025-07-30 11:26:33] [Iter 1666/4650] R3[615/1200], Temp: 0.4804, Energy: -84.948081+0.004095j
[2025-07-30 11:26:48] [Iter 1667/4650] R3[616/1200], Temp: 0.4791, Energy: -84.995737+0.000368j
[2025-07-30 11:27:02] [Iter 1668/4650] R3[617/1200], Temp: 0.4778, Energy: -85.009955+0.008704j
[2025-07-30 11:27:17] [Iter 1669/4650] R3[618/1200], Temp: 0.4764, Energy: -85.050984+0.008855j
[2025-07-30 11:27:32] [Iter 1670/4650] R3[619/1200], Temp: 0.4751, Energy: -84.953992+0.001938j
[2025-07-30 11:27:46] [Iter 1671/4650] R3[620/1200], Temp: 0.4738, Energy: -84.955967+0.003167j
[2025-07-30 11:28:01] [Iter 1672/4650] R3[621/1200], Temp: 0.4725, Energy: -85.048000-0.004267j
[2025-07-30 11:28:16] [Iter 1673/4650] R3[622/1200], Temp: 0.4712, Energy: -85.031128-0.001962j
[2025-07-30 11:28:30] [Iter 1674/4650] R3[623/1200], Temp: 0.4699, Energy: -85.004642-0.000920j
[2025-07-30 11:28:45] [Iter 1675/4650] R3[624/1200], Temp: 0.4686, Energy: -85.039651-0.006094j
[2025-07-30 11:28:59] [Iter 1676/4650] R3[625/1200], Temp: 0.4673, Energy: -84.976316+0.004575j
[2025-07-30 11:29:14] [Iter 1677/4650] R3[626/1200], Temp: 0.4660, Energy: -84.991351+0.006961j
[2025-07-30 11:29:28] [Iter 1678/4650] R3[627/1200], Temp: 0.4647, Energy: -84.943720-0.001410j
[2025-07-30 11:29:43] [Iter 1679/4650] R3[628/1200], Temp: 0.4634, Energy: -85.015695+0.012477j
[2025-07-30 11:29:57] [Iter 1680/4650] R3[629/1200], Temp: 0.4621, Energy: -85.015977-0.000291j
[2025-07-30 11:30:12] [Iter 1681/4650] R3[630/1200], Temp: 0.4608, Energy: -85.038559+0.008484j
[2025-07-30 11:30:27] [Iter 1682/4650] R3[631/1200], Temp: 0.4595, Energy: -85.071998-0.000022j
[2025-07-30 11:30:42] [Iter 1683/4650] R3[632/1200], Temp: 0.4582, Energy: -85.023525-0.001468j
[2025-07-30 11:30:57] [Iter 1684/4650] R3[633/1200], Temp: 0.4569, Energy: -85.003851+0.012243j
[2025-07-30 11:31:11] [Iter 1685/4650] R3[634/1200], Temp: 0.4556, Energy: -84.949971+0.003885j
[2025-07-30 11:31:26] [Iter 1686/4650] R3[635/1200], Temp: 0.4542, Energy: -85.001284-0.007139j
[2025-07-30 11:31:40] [Iter 1687/4650] R3[636/1200], Temp: 0.4529, Energy: -84.961555+0.005124j
[2025-07-30 11:31:55] [Iter 1688/4650] R3[637/1200], Temp: 0.4516, Energy: -84.967445-0.002481j
[2025-07-30 11:32:10] [Iter 1689/4650] R3[638/1200], Temp: 0.4503, Energy: -84.909215+0.008236j
[2025-07-30 11:32:25] [Iter 1690/4650] R3[639/1200], Temp: 0.4490, Energy: -84.876129+0.008794j
[2025-07-30 11:32:39] [Iter 1691/4650] R3[640/1200], Temp: 0.4477, Energy: -84.911665+0.001800j
[2025-07-30 11:32:54] [Iter 1692/4650] R3[641/1200], Temp: 0.4464, Energy: -84.842800+0.002404j
[2025-07-30 11:33:09] [Iter 1693/4650] R3[642/1200], Temp: 0.4451, Energy: -84.941195+0.007882j
[2025-07-30 11:33:23] [Iter 1694/4650] R3[643/1200], Temp: 0.4438, Energy: -84.935782-0.006343j
[2025-07-30 11:33:38] [Iter 1695/4650] R3[644/1200], Temp: 0.4425, Energy: -84.943638+0.002644j
[2025-07-30 11:33:53] [Iter 1696/4650] R3[645/1200], Temp: 0.4412, Energy: -84.938386+0.002027j
[2025-07-30 11:34:08] [Iter 1697/4650] R3[646/1200], Temp: 0.4399, Energy: -84.971902+0.008024j
[2025-07-30 11:34:23] [Iter 1698/4650] R3[647/1200], Temp: 0.4386, Energy: -84.934210-0.008403j
[2025-07-30 11:34:38] [Iter 1699/4650] R3[648/1200], Temp: 0.4373, Energy: -84.988583-0.007310j
[2025-07-30 11:34:53] [Iter 1700/4650] R3[649/1200], Temp: 0.4360, Energy: -84.999055+0.000996j
[2025-07-30 11:35:07] [Iter 1701/4650] R3[650/1200], Temp: 0.4347, Energy: -85.008213+0.007056j
[2025-07-30 11:35:22] [Iter 1702/4650] R3[651/1200], Temp: 0.4334, Energy: -84.931479-0.003433j
[2025-07-30 11:35:36] [Iter 1703/4650] R3[652/1200], Temp: 0.4321, Energy: -84.898995+0.002021j
[2025-07-30 11:35:51] [Iter 1704/4650] R3[653/1200], Temp: 0.4308, Energy: -84.950742+0.009519j
[2025-07-30 11:36:06] [Iter 1705/4650] R3[654/1200], Temp: 0.4295, Energy: -85.009616-0.006383j
[2025-07-30 11:36:21] [Iter 1706/4650] R3[655/1200], Temp: 0.4283, Energy: -85.031235+0.001088j
[2025-07-30 11:36:36] [Iter 1707/4650] R3[656/1200], Temp: 0.4270, Energy: -84.990421+0.008447j
[2025-07-30 11:36:51] [Iter 1708/4650] R3[657/1200], Temp: 0.4257, Energy: -85.032806+0.003326j
[2025-07-30 11:37:05] [Iter 1709/4650] R3[658/1200], Temp: 0.4244, Energy: -85.005438-0.000360j
[2025-07-30 11:37:20] [Iter 1710/4650] R3[659/1200], Temp: 0.4231, Energy: -84.948073+0.003484j
[2025-07-30 11:37:35] [Iter 1711/4650] R3[660/1200], Temp: 0.4218, Energy: -84.977240-0.013882j
[2025-07-30 11:37:50] [Iter 1712/4650] R3[661/1200], Temp: 0.4205, Energy: -85.036745-0.009074j
[2025-07-30 11:38:04] [Iter 1713/4650] R3[662/1200], Temp: 0.4192, Energy: -85.042929-0.009653j
[2025-07-30 11:38:19] [Iter 1714/4650] R3[663/1200], Temp: 0.4179, Energy: -84.979311-0.007267j
[2025-07-30 11:38:34] [Iter 1715/4650] R3[664/1200], Temp: 0.4166, Energy: -84.984222-0.009662j
[2025-07-30 11:38:48] [Iter 1716/4650] R3[665/1200], Temp: 0.4153, Energy: -84.963621-0.013501j
[2025-07-30 11:39:03] [Iter 1717/4650] R3[666/1200], Temp: 0.4140, Energy: -84.927294-0.003842j
[2025-07-30 11:39:18] [Iter 1718/4650] R3[667/1200], Temp: 0.4127, Energy: -84.869950-0.000373j
[2025-07-30 11:39:33] [Iter 1719/4650] R3[668/1200], Temp: 0.4115, Energy: -84.864612-0.011083j
[2025-07-30 11:39:47] [Iter 1720/4650] R3[669/1200], Temp: 0.4102, Energy: -84.861034+0.002861j
[2025-07-30 11:40:02] [Iter 1721/4650] R3[670/1200], Temp: 0.4089, Energy: -84.894798-0.008783j
[2025-07-30 11:40:16] [Iter 1722/4650] R3[671/1200], Temp: 0.4076, Energy: -84.912383+0.000850j
[2025-07-30 11:40:31] [Iter 1723/4650] R3[672/1200], Temp: 0.4063, Energy: -84.945202+0.011211j
[2025-07-30 11:40:45] [Iter 1724/4650] R3[673/1200], Temp: 0.4050, Energy: -84.998904+0.003602j
[2025-07-30 11:41:00] [Iter 1725/4650] R3[674/1200], Temp: 0.4037, Energy: -85.019340-0.007929j
[2025-07-30 11:41:15] [Iter 1726/4650] R3[675/1200], Temp: 0.4025, Energy: -84.959367-0.004699j
[2025-07-30 11:41:30] [Iter 1727/4650] R3[676/1200], Temp: 0.4012, Energy: -84.974242-0.011673j
[2025-07-30 11:41:45] [Iter 1728/4650] R3[677/1200], Temp: 0.3999, Energy: -84.956205-0.006980j
[2025-07-30 11:42:00] [Iter 1729/4650] R3[678/1200], Temp: 0.3986, Energy: -84.912414-0.008853j
[2025-07-30 11:42:15] [Iter 1730/4650] R3[679/1200], Temp: 0.3973, Energy: -84.888889-0.002991j
[2025-07-30 11:42:30] [Iter 1731/4650] R3[680/1200], Temp: 0.3960, Energy: -84.925748+0.002508j
[2025-07-30 11:42:44] [Iter 1732/4650] R3[681/1200], Temp: 0.3948, Energy: -84.927386-0.002367j
[2025-07-30 11:42:58] [Iter 1733/4650] R3[682/1200], Temp: 0.3935, Energy: -84.899569+0.003928j
[2025-07-30 11:43:13] [Iter 1734/4650] R3[683/1200], Temp: 0.3922, Energy: -84.947606-0.000893j
[2025-07-30 11:43:28] [Iter 1735/4650] R3[684/1200], Temp: 0.3909, Energy: -84.927105+0.004427j
[2025-07-30 11:43:43] [Iter 1736/4650] R3[685/1200], Temp: 0.3897, Energy: -85.027343+0.002175j
[2025-07-30 11:43:58] [Iter 1737/4650] R3[686/1200], Temp: 0.3884, Energy: -84.955835-0.005976j
[2025-07-30 11:44:13] [Iter 1738/4650] R3[687/1200], Temp: 0.3871, Energy: -84.956486-0.004129j
[2025-07-30 11:44:28] [Iter 1739/4650] R3[688/1200], Temp: 0.3858, Energy: -84.946850-0.005768j
[2025-07-30 11:44:42] [Iter 1740/4650] R3[689/1200], Temp: 0.3846, Energy: -85.004946+0.000649j
[2025-07-30 11:44:57] [Iter 1741/4650] R3[690/1200], Temp: 0.3833, Energy: -84.998454-0.001996j
[2025-07-30 11:45:12] [Iter 1742/4650] R3[691/1200], Temp: 0.3820, Energy: -84.996342-0.004805j
[2025-07-30 11:45:27] [Iter 1743/4650] R3[692/1200], Temp: 0.3807, Energy: -84.907960-0.001440j
[2025-07-30 11:45:42] [Iter 1744/4650] R3[693/1200], Temp: 0.3795, Energy: -84.920421-0.005692j
[2025-07-30 11:45:57] [Iter 1745/4650] R3[694/1200], Temp: 0.3782, Energy: -84.944277-0.004360j
[2025-07-30 11:46:11] [Iter 1746/4650] R3[695/1200], Temp: 0.3769, Energy: -84.958296-0.009816j
[2025-07-30 11:46:26] [Iter 1747/4650] R3[696/1200], Temp: 0.3757, Energy: -84.962313-0.007587j
[2025-07-30 11:46:41] [Iter 1748/4650] R3[697/1200], Temp: 0.3744, Energy: -84.935975+0.007287j
[2025-07-30 11:46:56] [Iter 1749/4650] R3[698/1200], Temp: 0.3731, Energy: -84.912813+0.018237j
[2025-07-30 11:47:11] [Iter 1750/4650] R3[699/1200], Temp: 0.3719, Energy: -84.954845+0.015913j
[2025-07-30 11:47:25] [Iter 1751/4650] R3[700/1200], Temp: 0.3706, Energy: -85.002536+0.003286j
[2025-07-30 11:47:40] [Iter 1752/4650] R3[701/1200], Temp: 0.3693, Energy: -85.048026+0.001709j
[2025-07-30 11:47:54] [Iter 1753/4650] R3[702/1200], Temp: 0.3681, Energy: -85.013299+0.010731j
[2025-07-30 11:48:09] [Iter 1754/4650] R3[703/1200], Temp: 0.3668, Energy: -84.980663+0.011693j
[2025-07-30 11:48:24] [Iter 1755/4650] R3[704/1200], Temp: 0.3655, Energy: -84.943877+0.004021j
[2025-07-30 11:48:39] [Iter 1756/4650] R3[705/1200], Temp: 0.3643, Energy: -84.952311+0.000890j
[2025-07-30 11:48:54] [Iter 1757/4650] R3[706/1200], Temp: 0.3630, Energy: -84.948775+0.007878j
[2025-07-30 11:49:08] [Iter 1758/4650] R3[707/1200], Temp: 0.3618, Energy: -85.039333+0.000568j
[2025-07-30 11:49:23] [Iter 1759/4650] R3[708/1200], Temp: 0.3605, Energy: -85.078352-0.001164j
[2025-07-30 11:49:38] [Iter 1760/4650] R3[709/1200], Temp: 0.3592, Energy: -85.106759-0.005585j
[2025-07-30 11:49:52] [Iter 1761/4650] R3[710/1200], Temp: 0.3580, Energy: -85.015977+0.005754j
[2025-07-30 11:50:07] [Iter 1762/4650] R3[711/1200], Temp: 0.3567, Energy: -85.085186-0.004855j
[2025-07-30 11:50:22] [Iter 1763/4650] R3[712/1200], Temp: 0.3555, Energy: -85.043256-0.005686j
[2025-07-30 11:50:36] [Iter 1764/4650] R3[713/1200], Temp: 0.3542, Energy: -85.084689+0.001150j
[2025-07-30 11:50:51] [Iter 1765/4650] R3[714/1200], Temp: 0.3530, Energy: -85.024629+0.009046j
[2025-07-30 11:51:06] [Iter 1766/4650] R3[715/1200], Temp: 0.3517, Energy: -85.087029+0.001929j
[2025-07-30 11:51:20] [Iter 1767/4650] R3[716/1200], Temp: 0.3505, Energy: -84.992579-0.005536j
[2025-07-30 11:51:35] [Iter 1768/4650] R3[717/1200], Temp: 0.3492, Energy: -84.937744-0.000839j
[2025-07-30 11:51:50] [Iter 1769/4650] R3[718/1200], Temp: 0.3480, Energy: -84.955212+0.002289j
[2025-07-30 11:52:04] [Iter 1770/4650] R3[719/1200], Temp: 0.3467, Energy: -84.923791+0.009596j
[2025-07-30 11:52:19] [Iter 1771/4650] R3[720/1200], Temp: 0.3455, Energy: -84.967934+0.026636j
[2025-07-30 11:52:33] [Iter 1772/4650] R3[721/1200], Temp: 0.3442, Energy: -84.930738+0.003990j
[2025-07-30 11:52:48] [Iter 1773/4650] R3[722/1200], Temp: 0.3430, Energy: -84.989036+0.012756j
[2025-07-30 11:53:03] [Iter 1774/4650] R3[723/1200], Temp: 0.3418, Energy: -84.933126+0.008467j
[2025-07-30 11:53:18] [Iter 1775/4650] R3[724/1200], Temp: 0.3405, Energy: -84.944612-0.001439j
[2025-07-30 11:53:33] [Iter 1776/4650] R3[725/1200], Temp: 0.3393, Energy: -85.026351-0.001646j
[2025-07-30 11:53:47] [Iter 1777/4650] R3[726/1200], Temp: 0.3380, Energy: -85.053765+0.005103j
[2025-07-30 11:54:02] [Iter 1778/4650] R3[727/1200], Temp: 0.3368, Energy: -85.009921+0.004029j
[2025-07-30 11:54:16] [Iter 1779/4650] R3[728/1200], Temp: 0.3356, Energy: -84.995140-0.001107j
[2025-07-30 11:54:31] [Iter 1780/4650] R3[729/1200], Temp: 0.3343, Energy: -84.961994-0.001811j
[2025-07-30 11:54:46] [Iter 1781/4650] R3[730/1200], Temp: 0.3331, Energy: -84.997426+0.000291j
[2025-07-30 11:55:00] [Iter 1782/4650] R3[731/1200], Temp: 0.3319, Energy: -84.982658+0.000402j
[2025-07-30 11:55:15] [Iter 1783/4650] R3[732/1200], Temp: 0.3306, Energy: -85.011394+0.005315j
[2025-07-30 11:55:29] [Iter 1784/4650] R3[733/1200], Temp: 0.3294, Energy: -84.903174+0.006570j
[2025-07-30 11:55:44] [Iter 1785/4650] R3[734/1200], Temp: 0.3282, Energy: -84.957045+0.004386j
[2025-07-30 11:55:59] [Iter 1786/4650] R3[735/1200], Temp: 0.3269, Energy: -84.948072+0.009443j
[2025-07-30 11:56:13] [Iter 1787/4650] R3[736/1200], Temp: 0.3257, Energy: -84.892496-0.003591j
[2025-07-30 11:56:28] [Iter 1788/4650] R3[737/1200], Temp: 0.3245, Energy: -84.859690+0.003049j
[2025-07-30 11:56:43] [Iter 1789/4650] R3[738/1200], Temp: 0.3233, Energy: -84.899836-0.001500j
[2025-07-30 11:56:58] [Iter 1790/4650] R3[739/1200], Temp: 0.3220, Energy: -84.918449+0.011981j
[2025-07-30 11:57:13] [Iter 1791/4650] R3[740/1200], Temp: 0.3208, Energy: -84.884150-0.000861j
[2025-07-30 11:57:28] [Iter 1792/4650] R3[741/1200], Temp: 0.3196, Energy: -85.019580+0.004076j
[2025-07-30 11:57:42] [Iter 1793/4650] R3[742/1200], Temp: 0.3184, Energy: -84.987845+0.006939j
[2025-07-30 11:57:57] [Iter 1794/4650] R3[743/1200], Temp: 0.3172, Energy: -84.956413-0.000264j
[2025-07-30 11:58:11] [Iter 1795/4650] R3[744/1200], Temp: 0.3159, Energy: -84.979572-0.002165j
[2025-07-30 11:58:25] [Iter 1796/4650] R3[745/1200], Temp: 0.3147, Energy: -84.891742+0.006920j
[2025-07-30 11:58:40] [Iter 1797/4650] R3[746/1200], Temp: 0.3135, Energy: -84.963217+0.000203j
[2025-07-30 11:58:55] [Iter 1798/4650] R3[747/1200], Temp: 0.3123, Energy: -84.967365-0.007177j
[2025-07-30 11:59:10] [Iter 1799/4650] R3[748/1200], Temp: 0.3111, Energy: -85.064939-0.000336j
[2025-07-30 11:59:24] [Iter 1800/4650] R3[749/1200], Temp: 0.3099, Energy: -85.032868+0.000584j
[2025-07-30 11:59:39] [Iter 1801/4650] R3[750/1200], Temp: 0.3087, Energy: -85.037057+0.002973j
[2025-07-30 11:59:54] [Iter 1802/4650] R3[751/1200], Temp: 0.3074, Energy: -85.000492+0.002433j
[2025-07-30 12:00:09] [Iter 1803/4650] R3[752/1200], Temp: 0.3062, Energy: -84.976308+0.000636j
[2025-07-30 12:00:24] [Iter 1804/4650] R3[753/1200], Temp: 0.3050, Energy: -84.941701-0.003081j
[2025-07-30 12:00:38] [Iter 1805/4650] R3[754/1200], Temp: 0.3038, Energy: -84.925041-0.000065j
[2025-07-30 12:00:53] [Iter 1806/4650] R3[755/1200], Temp: 0.3026, Energy: -84.949733-0.006415j
[2025-07-30 12:01:08] [Iter 1807/4650] R3[756/1200], Temp: 0.3014, Energy: -85.027965+0.002051j
[2025-07-30 12:01:23] [Iter 1808/4650] R3[757/1200], Temp: 0.3002, Energy: -85.083133-0.010837j
[2025-07-30 12:01:38] [Iter 1809/4650] R3[758/1200], Temp: 0.2990, Energy: -85.084158-0.008383j
[2025-07-30 12:01:53] [Iter 1810/4650] R3[759/1200], Temp: 0.2978, Energy: -85.030197-0.000372j
[2025-07-30 12:02:07] [Iter 1811/4650] R3[760/1200], Temp: 0.2966, Energy: -85.089826-0.008011j
[2025-07-30 12:02:23] [Iter 1812/4650] R3[761/1200], Temp: 0.2954, Energy: -85.052844-0.008467j
[2025-07-30 12:02:37] [Iter 1813/4650] R3[762/1200], Temp: 0.2942, Energy: -85.009990+0.000611j
[2025-07-30 12:02:52] [Iter 1814/4650] R3[763/1200], Temp: 0.2931, Energy: -85.056822-0.002720j
[2025-07-30 12:03:07] [Iter 1815/4650] R3[764/1200], Temp: 0.2919, Energy: -84.931615+0.000362j
[2025-07-30 12:03:22] [Iter 1816/4650] R3[765/1200], Temp: 0.2907, Energy: -84.921752-0.004985j
[2025-07-30 12:03:36] [Iter 1817/4650] R3[766/1200], Temp: 0.2895, Energy: -84.929140-0.004656j
[2025-07-30 12:03:50] [Iter 1818/4650] R3[767/1200], Temp: 0.2883, Energy: -84.961425-0.005411j
[2025-07-30 12:04:05] [Iter 1819/4650] R3[768/1200], Temp: 0.2871, Energy: -84.998071-0.006400j
[2025-07-30 12:04:20] [Iter 1820/4650] R3[769/1200], Temp: 0.2859, Energy: -85.025522+0.002570j
[2025-07-30 12:04:35] [Iter 1821/4650] R3[770/1200], Temp: 0.2847, Energy: -84.979656-0.002140j
[2025-07-30 12:04:49] [Iter 1822/4650] R3[771/1200], Temp: 0.2836, Energy: -85.008929-0.002142j
[2025-07-30 12:05:04] [Iter 1823/4650] R3[772/1200], Temp: 0.2824, Energy: -84.923910-0.004848j
[2025-07-30 12:05:18] [Iter 1824/4650] R3[773/1200], Temp: 0.2812, Energy: -84.907738-0.000104j
[2025-07-30 12:05:33] [Iter 1825/4650] R3[774/1200], Temp: 0.2800, Energy: -84.916771-0.011497j
[2025-07-30 12:05:48] [Iter 1826/4650] R3[775/1200], Temp: 0.2789, Energy: -84.928866-0.008346j
[2025-07-30 12:06:02] [Iter 1827/4650] R3[776/1200], Temp: 0.2777, Energy: -84.925104-0.001974j
[2025-07-30 12:06:16] [Iter 1828/4650] R3[777/1200], Temp: 0.2765, Energy: -84.950934+0.001156j
[2025-07-30 12:06:31] [Iter 1829/4650] R3[778/1200], Temp: 0.2753, Energy: -84.957133-0.004486j
[2025-07-30 12:06:46] [Iter 1830/4650] R3[779/1200], Temp: 0.2742, Energy: -85.002710-0.004135j
[2025-07-30 12:07:01] [Iter 1831/4650] R3[780/1200], Temp: 0.2730, Energy: -84.997545-0.024872j
[2025-07-30 12:07:15] [Iter 1832/4650] R3[781/1200], Temp: 0.2718, Energy: -84.984751-0.000959j
[2025-07-30 12:07:29] [Iter 1833/4650] R3[782/1200], Temp: 0.2707, Energy: -84.969494-0.022458j
[2025-07-30 12:07:44] [Iter 1834/4650] R3[783/1200], Temp: 0.2695, Energy: -85.007758-0.006667j
[2025-07-30 12:07:59] [Iter 1835/4650] R3[784/1200], Temp: 0.2684, Energy: -84.981343-0.003070j
[2025-07-30 12:08:14] [Iter 1836/4650] R3[785/1200], Temp: 0.2672, Energy: -85.061970+0.001046j
[2025-07-30 12:08:28] [Iter 1837/4650] R3[786/1200], Temp: 0.2660, Energy: -85.069902+0.004645j
[2025-07-30 12:08:43] [Iter 1838/4650] R3[787/1200], Temp: 0.2649, Energy: -85.102204-0.005541j
[2025-07-30 12:08:57] [Iter 1839/4650] R3[788/1200], Temp: 0.2637, Energy: -85.052642+0.005602j
[2025-07-30 12:09:12] [Iter 1840/4650] R3[789/1200], Temp: 0.2626, Energy: -85.047147+0.001010j
[2025-07-30 12:09:27] [Iter 1841/4650] R3[790/1200], Temp: 0.2614, Energy: -85.074202-0.011560j
[2025-07-30 12:09:42] [Iter 1842/4650] R3[791/1200], Temp: 0.2603, Energy: -85.044958-0.005354j
[2025-07-30 12:09:57] [Iter 1843/4650] R3[792/1200], Temp: 0.2591, Energy: -85.081839-0.012003j
[2025-07-30 12:10:12] [Iter 1844/4650] R3[793/1200], Temp: 0.2580, Energy: -85.018508-0.007947j
[2025-07-30 12:10:26] [Iter 1845/4650] R3[794/1200], Temp: 0.2568, Energy: -85.043348-0.010154j
[2025-07-30 12:10:41] [Iter 1846/4650] R3[795/1200], Temp: 0.2557, Energy: -84.988032-0.000176j
[2025-07-30 12:10:55] [Iter 1847/4650] R3[796/1200], Temp: 0.2545, Energy: -84.944107-0.021941j
[2025-07-30 12:11:10] [Iter 1848/4650] R3[797/1200], Temp: 0.2534, Energy: -84.989970-0.008110j
[2025-07-30 12:11:24] [Iter 1849/4650] R3[798/1200], Temp: 0.2523, Energy: -84.978657-0.000280j
[2025-07-30 12:11:39] [Iter 1850/4650] R3[799/1200], Temp: 0.2511, Energy: -84.973168-0.007592j
[2025-07-30 12:11:54] [Iter 1851/4650] R3[800/1200], Temp: 0.2500, Energy: -84.932909-0.004490j
[2025-07-30 12:12:09] [Iter 1852/4650] R3[801/1200], Temp: 0.2489, Energy: -84.860282-0.004395j
[2025-07-30 12:12:24] [Iter 1853/4650] R3[802/1200], Temp: 0.2477, Energy: -84.987890-0.022581j
[2025-07-30 12:12:39] [Iter 1854/4650] R3[803/1200], Temp: 0.2466, Energy: -84.983628-0.002555j
[2025-07-30 12:12:54] [Iter 1855/4650] R3[804/1200], Temp: 0.2455, Energy: -84.977177+0.002923j
[2025-07-30 12:13:08] [Iter 1856/4650] R3[805/1200], Temp: 0.2444, Energy: -85.000281-0.002393j
[2025-07-30 12:13:23] [Iter 1857/4650] R3[806/1200], Temp: 0.2432, Energy: -84.990602-0.018715j
[2025-07-30 12:13:38] [Iter 1858/4650] R3[807/1200], Temp: 0.2421, Energy: -84.976223-0.000997j
[2025-07-30 12:13:52] [Iter 1859/4650] R3[808/1200], Temp: 0.2410, Energy: -85.002815-0.000105j
[2025-07-30 12:14:07] [Iter 1860/4650] R3[809/1200], Temp: 0.2399, Energy: -84.951079-0.002071j
[2025-07-30 12:14:21] [Iter 1861/4650] R3[810/1200], Temp: 0.2388, Energy: -84.919137-0.005308j
[2025-07-30 12:14:36] [Iter 1862/4650] R3[811/1200], Temp: 0.2376, Energy: -84.961026-0.003753j
[2025-07-30 12:14:51] [Iter 1863/4650] R3[812/1200], Temp: 0.2365, Energy: -84.935044-0.012336j
[2025-07-30 12:15:06] [Iter 1864/4650] R3[813/1200], Temp: 0.2354, Energy: -84.957151-0.008278j
[2025-07-30 12:15:21] [Iter 1865/4650] R3[814/1200], Temp: 0.2343, Energy: -85.037751+0.002247j
[2025-07-30 12:15:35] [Iter 1866/4650] R3[815/1200], Temp: 0.2332, Energy: -85.012407+0.001685j
[2025-07-30 12:15:50] [Iter 1867/4650] R3[816/1200], Temp: 0.2321, Energy: -85.055702-0.009239j
[2025-07-30 12:16:05] [Iter 1868/4650] R3[817/1200], Temp: 0.2310, Energy: -85.018264-0.004244j
[2025-07-30 12:16:19] [Iter 1869/4650] R3[818/1200], Temp: 0.2299, Energy: -84.985706-0.004313j
[2025-07-30 12:16:34] [Iter 1870/4650] R3[819/1200], Temp: 0.2288, Energy: -85.010859+0.003702j
[2025-07-30 12:16:49] [Iter 1871/4650] R3[820/1200], Temp: 0.2277, Energy: -84.962084+0.004299j
[2025-07-30 12:17:03] [Iter 1872/4650] R3[821/1200], Temp: 0.2266, Energy: -84.997181+0.005054j
[2025-07-30 12:17:18] [Iter 1873/4650] R3[822/1200], Temp: 0.2255, Energy: -85.064781+0.001013j
[2025-07-30 12:17:33] [Iter 1874/4650] R3[823/1200], Temp: 0.2244, Energy: -85.066413+0.005202j
[2025-07-30 12:17:47] [Iter 1875/4650] R3[824/1200], Temp: 0.2233, Energy: -85.067611+0.009009j
[2025-07-30 12:18:02] [Iter 1876/4650] R3[825/1200], Temp: 0.2222, Energy: -85.020344+0.004258j
[2025-07-30 12:18:16] [Iter 1877/4650] R3[826/1200], Temp: 0.2211, Energy: -84.993634+0.002129j
[2025-07-30 12:18:31] [Iter 1878/4650] R3[827/1200], Temp: 0.2200, Energy: -85.035998+0.010169j
[2025-07-30 12:18:46] [Iter 1879/4650] R3[828/1200], Temp: 0.2190, Energy: -84.990297+0.008228j
[2025-07-30 12:19:01] [Iter 1880/4650] R3[829/1200], Temp: 0.2179, Energy: -84.971030+0.006722j
[2025-07-30 12:19:16] [Iter 1881/4650] R3[830/1200], Temp: 0.2168, Energy: -84.953903+0.020532j
[2025-07-30 12:19:30] [Iter 1882/4650] R3[831/1200], Temp: 0.2157, Energy: -85.061243+0.011025j
[2025-07-30 12:19:45] [Iter 1883/4650] R3[832/1200], Temp: 0.2146, Energy: -85.074760-0.002250j
[2025-07-30 12:20:00] [Iter 1884/4650] R3[833/1200], Temp: 0.2136, Energy: -84.994992+0.005670j
[2025-07-30 12:20:15] [Iter 1885/4650] R3[834/1200], Temp: 0.2125, Energy: -85.048845+0.007971j
[2025-07-30 12:20:29] [Iter 1886/4650] R3[835/1200], Temp: 0.2114, Energy: -85.040535+0.003530j
[2025-07-30 12:20:44] [Iter 1887/4650] R3[836/1200], Temp: 0.2104, Energy: -85.041370+0.000523j
[2025-07-30 12:20:59] [Iter 1888/4650] R3[837/1200], Temp: 0.2093, Energy: -85.043131+0.005927j
[2025-07-30 12:21:13] [Iter 1889/4650] R3[838/1200], Temp: 0.2082, Energy: -85.013605-0.000637j
[2025-07-30 12:21:28] [Iter 1890/4650] R3[839/1200], Temp: 0.2072, Energy: -84.972946+0.005025j
[2025-07-30 12:21:43] [Iter 1891/4650] R3[840/1200], Temp: 0.2061, Energy: -84.919637-0.001941j
[2025-07-30 12:21:57] [Iter 1892/4650] R3[841/1200], Temp: 0.2050, Energy: -84.991750+0.001235j
[2025-07-30 12:22:12] [Iter 1893/4650] R3[842/1200], Temp: 0.2040, Energy: -85.022443-0.000173j
[2025-07-30 12:22:27] [Iter 1894/4650] R3[843/1200], Temp: 0.2029, Energy: -85.014466+0.002306j
[2025-07-30 12:22:42] [Iter 1895/4650] R3[844/1200], Temp: 0.2019, Energy: -84.975965+0.004969j
[2025-07-30 12:22:57] [Iter 1896/4650] R3[845/1200], Temp: 0.2008, Energy: -85.007304+0.002143j
[2025-07-30 12:23:11] [Iter 1897/4650] R3[846/1200], Temp: 0.1998, Energy: -85.011556+0.007341j
[2025-07-30 12:23:26] [Iter 1898/4650] R3[847/1200], Temp: 0.1987, Energy: -85.011413-0.005127j
[2025-07-30 12:23:41] [Iter 1899/4650] R3[848/1200], Temp: 0.1977, Energy: -85.002633+0.003699j
[2025-07-30 12:23:56] [Iter 1900/4650] R3[849/1200], Temp: 0.1967, Energy: -84.977435+0.000440j
[2025-07-30 12:24:10] [Iter 1901/4650] R3[850/1200], Temp: 0.1956, Energy: -85.030905+0.000298j
[2025-07-30 12:24:25] [Iter 1902/4650] R3[851/1200], Temp: 0.1946, Energy: -85.031954+0.008852j
[2025-07-30 12:24:40] [Iter 1903/4650] R3[852/1200], Temp: 0.1935, Energy: -85.028533+0.005567j
[2025-07-30 12:24:54] [Iter 1904/4650] R3[853/1200], Temp: 0.1925, Energy: -84.973034+0.011980j
[2025-07-30 12:25:09] [Iter 1905/4650] R3[854/1200], Temp: 0.1915, Energy: -84.998939+0.003983j
[2025-07-30 12:25:24] [Iter 1906/4650] R3[855/1200], Temp: 0.1905, Energy: -85.012625+0.002621j
[2025-07-30 12:25:38] [Iter 1907/4650] R3[856/1200], Temp: 0.1894, Energy: -84.946933+0.003317j
[2025-07-30 12:25:54] [Iter 1908/4650] R3[857/1200], Temp: 0.1884, Energy: -84.949159-0.012219j
[2025-07-30 12:26:08] [Iter 1909/4650] R3[858/1200], Temp: 0.1874, Energy: -84.967306-0.006909j
[2025-07-30 12:26:23] [Iter 1910/4650] R3[859/1200], Temp: 0.1864, Energy: -84.935130-0.000222j
[2025-07-30 12:26:38] [Iter 1911/4650] R3[860/1200], Temp: 0.1853, Energy: -84.922467-0.006429j
[2025-07-30 12:26:53] [Iter 1912/4650] R3[861/1200], Temp: 0.1843, Energy: -84.896067-0.007045j
[2025-07-30 12:27:07] [Iter 1913/4650] R3[862/1200], Temp: 0.1833, Energy: -84.951156-0.010981j
[2025-07-30 12:27:22] [Iter 1914/4650] R3[863/1200], Temp: 0.1823, Energy: -84.909039+0.004129j
[2025-07-30 12:27:37] [Iter 1915/4650] R3[864/1200], Temp: 0.1813, Energy: -84.986929+0.010186j
[2025-07-30 12:27:52] [Iter 1916/4650] R3[865/1200], Temp: 0.1803, Energy: -85.009631-0.013337j
[2025-07-30 12:28:07] [Iter 1917/4650] R3[866/1200], Temp: 0.1793, Energy: -85.067704-0.003881j
[2025-07-30 12:28:21] [Iter 1918/4650] R3[867/1200], Temp: 0.1783, Energy: -85.090620-0.002037j
[2025-07-30 12:28:36] [Iter 1919/4650] R3[868/1200], Temp: 0.1773, Energy: -85.085485+0.001223j
[2025-07-30 12:28:51] [Iter 1920/4650] R3[869/1200], Temp: 0.1763, Energy: -85.122051-0.009440j
[2025-07-30 12:29:06] [Iter 1921/4650] R3[870/1200], Temp: 0.1753, Energy: -85.069409-0.004457j
[2025-07-30 12:29:21] [Iter 1922/4650] R3[871/1200], Temp: 0.1743, Energy: -85.071879-0.005964j
[2025-07-30 12:29:36] [Iter 1923/4650] R3[872/1200], Temp: 0.1733, Energy: -85.038238-0.001713j
[2025-07-30 12:29:50] [Iter 1924/4650] R3[873/1200], Temp: 0.1723, Energy: -85.036289-0.004405j
[2025-07-30 12:30:05] [Iter 1925/4650] R3[874/1200], Temp: 0.1713, Energy: -85.022202-0.004248j
[2025-07-30 12:30:20] [Iter 1926/4650] R3[875/1200], Temp: 0.1703, Energy: -84.972586-0.010497j
[2025-07-30 12:30:34] [Iter 1927/4650] R3[876/1200], Temp: 0.1693, Energy: -84.970052-0.006057j
[2025-07-30 12:30:48] [Iter 1928/4650] R3[877/1200], Temp: 0.1684, Energy: -84.974016-0.004229j
[2025-07-30 12:31:03] [Iter 1929/4650] R3[878/1200], Temp: 0.1674, Energy: -84.983928+0.005049j
[2025-07-30 12:31:18] [Iter 1930/4650] R3[879/1200], Temp: 0.1664, Energy: -85.000204+0.003948j
[2025-07-30 12:31:33] [Iter 1931/4650] R3[880/1200], Temp: 0.1654, Energy: -85.045135-0.000843j
[2025-07-30 12:31:48] [Iter 1932/4650] R3[881/1200], Temp: 0.1645, Energy: -85.011116-0.000913j
[2025-07-30 12:32:02] [Iter 1933/4650] R3[882/1200], Temp: 0.1635, Energy: -85.029212+0.008185j
[2025-07-30 12:32:17] [Iter 1934/4650] R3[883/1200], Temp: 0.1625, Energy: -85.031129+0.001249j
[2025-07-30 12:32:31] [Iter 1935/4650] R3[884/1200], Temp: 0.1616, Energy: -85.051405+0.004112j
[2025-07-30 12:32:46] [Iter 1936/4650] R3[885/1200], Temp: 0.1606, Energy: -85.058461+0.005426j
[2025-07-30 12:33:01] [Iter 1937/4650] R3[886/1200], Temp: 0.1596, Energy: -85.023210-0.003297j
[2025-07-30 12:33:16] [Iter 1938/4650] R3[887/1200], Temp: 0.1587, Energy: -85.029917-0.002007j
[2025-07-30 12:33:30] [Iter 1939/4650] R3[888/1200], Temp: 0.1577, Energy: -84.937703+0.000136j
[2025-07-30 12:33:45] [Iter 1940/4650] R3[889/1200], Temp: 0.1568, Energy: -84.957084-0.001220j
[2025-07-30 12:34:00] [Iter 1941/4650] R3[890/1200], Temp: 0.1558, Energy: -84.963258-0.000560j
[2025-07-30 12:34:15] [Iter 1942/4650] R3[891/1200], Temp: 0.1549, Energy: -84.981188-0.002673j
[2025-07-30 12:34:30] [Iter 1943/4650] R3[892/1200], Temp: 0.1539, Energy: -84.973635-0.007889j
[2025-07-30 12:34:45] [Iter 1944/4650] R3[893/1200], Temp: 0.1530, Energy: -84.974990-0.008235j
[2025-07-30 12:35:00] [Iter 1945/4650] R3[894/1200], Temp: 0.1520, Energy: -84.969882+0.001012j
[2025-07-30 12:35:14] [Iter 1946/4650] R3[895/1200], Temp: 0.1511, Energy: -84.998627-0.003851j
[2025-07-30 12:35:29] [Iter 1947/4650] R3[896/1200], Temp: 0.1502, Energy: -84.973662-0.019166j
[2025-07-30 12:35:44] [Iter 1948/4650] R3[897/1200], Temp: 0.1492, Energy: -84.957215-0.001440j
[2025-07-30 12:35:59] [Iter 1949/4650] R3[898/1200], Temp: 0.1483, Energy: -85.033571+0.004372j
[2025-07-30 12:36:13] [Iter 1950/4650] R3[899/1200], Temp: 0.1474, Energy: -85.078400-0.001914j
[2025-07-30 12:36:28] [Iter 1951/4650] R3[900/1200], Temp: 0.1464, Energy: -85.020284-0.002558j
[2025-07-30 12:36:42] [Iter 1952/4650] R3[901/1200], Temp: 0.1455, Energy: -85.089687+0.003240j
[2025-07-30 12:36:57] [Iter 1953/4650] R3[902/1200], Temp: 0.1446, Energy: -85.117115-0.006605j
[2025-07-30 12:37:12] [Iter 1954/4650] R3[903/1200], Temp: 0.1437, Energy: -85.133798+0.002019j
[2025-07-30 12:37:27] [Iter 1955/4650] R3[904/1200], Temp: 0.1428, Energy: -85.036949+0.004240j
[2025-07-30 12:37:42] [Iter 1956/4650] R3[905/1200], Temp: 0.1418, Energy: -85.015383-0.005462j
[2025-07-30 12:37:56] [Iter 1957/4650] R3[906/1200], Temp: 0.1409, Energy: -85.054259-0.002528j
[2025-07-30 12:38:11] [Iter 1958/4650] R3[907/1200], Temp: 0.1400, Energy: -85.003435+0.001490j
[2025-07-30 12:38:26] [Iter 1959/4650] R3[908/1200], Temp: 0.1391, Energy: -84.979430+0.001724j
[2025-07-30 12:38:41] [Iter 1960/4650] R3[909/1200], Temp: 0.1382, Energy: -84.943811+0.004883j
[2025-07-30 12:38:55] [Iter 1961/4650] R3[910/1200], Temp: 0.1373, Energy: -84.967956+0.000902j
[2025-07-30 12:39:10] [Iter 1962/4650] R3[911/1200], Temp: 0.1364, Energy: -85.010685-0.004973j
[2025-07-30 12:39:25] [Iter 1963/4650] R3[912/1200], Temp: 0.1355, Energy: -85.040914-0.001101j
[2025-07-30 12:39:40] [Iter 1964/4650] R3[913/1200], Temp: 0.1346, Energy: -85.073052+0.007763j
[2025-07-30 12:39:54] [Iter 1965/4650] R3[914/1200], Temp: 0.1337, Energy: -84.973917+0.001648j
[2025-07-30 12:40:09] [Iter 1966/4650] R3[915/1200], Temp: 0.1328, Energy: -85.004936+0.002697j
[2025-07-30 12:40:24] [Iter 1967/4650] R3[916/1200], Temp: 0.1320, Energy: -84.971411+0.000325j
[2025-07-30 12:40:39] [Iter 1968/4650] R3[917/1200], Temp: 0.1311, Energy: -85.000198-0.006027j
[2025-07-30 12:40:54] [Iter 1969/4650] R3[918/1200], Temp: 0.1302, Energy: -84.982661-0.007619j
[2025-07-30 12:41:09] [Iter 1970/4650] R3[919/1200], Temp: 0.1293, Energy: -84.970615-0.016225j
[2025-07-30 12:41:24] [Iter 1971/4650] R3[920/1200], Temp: 0.1284, Energy: -85.010447-0.003077j
[2025-07-30 12:41:38] [Iter 1972/4650] R3[921/1200], Temp: 0.1276, Energy: -84.919750-0.003044j
[2025-07-30 12:41:53] [Iter 1973/4650] R3[922/1200], Temp: 0.1267, Energy: -84.876357+0.001836j
[2025-07-30 12:42:08] [Iter 1974/4650] R3[923/1200], Temp: 0.1258, Energy: -84.896892-0.001317j
[2025-07-30 12:42:22] [Iter 1975/4650] R3[924/1200], Temp: 0.1249, Energy: -84.998988+0.004038j
[2025-07-30 12:42:37] [Iter 1976/4650] R3[925/1200], Temp: 0.1241, Energy: -85.003155-0.009928j
[2025-07-30 12:42:51] [Iter 1977/4650] R3[926/1200], Temp: 0.1232, Energy: -84.989254-0.005700j
[2025-07-30 12:43:06] [Iter 1978/4650] R3[927/1200], Temp: 0.1224, Energy: -84.952627-0.002346j
[2025-07-30 12:43:21] [Iter 1979/4650] R3[928/1200], Temp: 0.1215, Energy: -84.983811-0.003572j
[2025-07-30 12:43:36] [Iter 1980/4650] R3[929/1200], Temp: 0.1206, Energy: -85.007422-0.005213j
[2025-07-30 12:43:51] [Iter 1981/4650] R3[930/1200], Temp: 0.1198, Energy: -85.012373-0.002154j
[2025-07-30 12:44:05] [Iter 1982/4650] R3[931/1200], Temp: 0.1189, Energy: -85.006278-0.008322j
[2025-07-30 12:44:20] [Iter 1983/4650] R3[932/1200], Temp: 0.1181, Energy: -85.054719+0.004945j
[2025-07-30 12:44:35] [Iter 1984/4650] R3[933/1200], Temp: 0.1173, Energy: -85.001681-0.008677j
[2025-07-30 12:44:50] [Iter 1985/4650] R3[934/1200], Temp: 0.1164, Energy: -84.993454-0.012797j
[2025-07-30 12:45:05] [Iter 1986/4650] R3[935/1200], Temp: 0.1156, Energy: -85.059004+0.002479j
[2025-07-30 12:45:20] [Iter 1987/4650] R3[936/1200], Temp: 0.1147, Energy: -84.968392-0.006226j
[2025-07-30 12:45:35] [Iter 1988/4650] R3[937/1200], Temp: 0.1139, Energy: -85.044668-0.000930j
[2025-07-30 12:45:49] [Iter 1989/4650] R3[938/1200], Temp: 0.1131, Energy: -85.073863+0.002972j
[2025-07-30 12:46:04] [Iter 1990/4650] R3[939/1200], Temp: 0.1123, Energy: -85.100901-0.000996j
[2025-07-30 12:46:19] [Iter 1991/4650] R3[940/1200], Temp: 0.1114, Energy: -85.130228-0.007777j
[2025-07-30 12:46:33] [Iter 1992/4650] R3[941/1200], Temp: 0.1106, Energy: -85.056824-0.005905j
[2025-07-30 12:46:48] [Iter 1993/4650] R3[942/1200], Temp: 0.1098, Energy: -85.103191+0.014167j
[2025-07-30 12:47:02] [Iter 1994/4650] R3[943/1200], Temp: 0.1090, Energy: -85.122570+0.007628j
[2025-07-30 12:47:17] [Iter 1995/4650] R3[944/1200], Temp: 0.1082, Energy: -85.100873+0.005112j
[2025-07-30 12:47:32] [Iter 1996/4650] R3[945/1200], Temp: 0.1073, Energy: -85.133928+0.000188j
[2025-07-30 12:47:47] [Iter 1997/4650] R3[946/1200], Temp: 0.1065, Energy: -85.066721-0.000981j
[2025-07-30 12:48:01] [Iter 1998/4650] R3[947/1200], Temp: 0.1057, Energy: -85.058163-0.003555j
[2025-07-30 12:48:16] [Iter 1999/4650] R3[948/1200], Temp: 0.1049, Energy: -85.094617-0.001890j
[2025-07-30 12:48:31] [Iter 2000/4650] R3[949/1200], Temp: 0.1041, Energy: -85.060573+0.000162j
[2025-07-30 12:48:31] ✓ Checkpoint saved: checkpoint_iter_002000.pkl
[2025-07-30 12:48:45] [Iter 2001/4650] R3[950/1200], Temp: 0.1033, Energy: -85.008883-0.002223j
[2025-07-30 12:49:00] [Iter 2002/4650] R3[951/1200], Temp: 0.1025, Energy: -84.977434-0.006185j
[2025-07-30 12:49:15] [Iter 2003/4650] R3[952/1200], Temp: 0.1017, Energy: -85.026275-0.000500j
[2025-07-30 12:49:29] [Iter 2004/4650] R3[953/1200], Temp: 0.1009, Energy: -85.005443+0.004762j
[2025-07-30 12:49:44] [Iter 2005/4650] R3[954/1200], Temp: 0.1002, Energy: -84.968770+0.006648j
[2025-07-30 12:49:59] [Iter 2006/4650] R3[955/1200], Temp: 0.0994, Energy: -85.030870+0.006907j
[2025-07-30 12:50:13] [Iter 2007/4650] R3[956/1200], Temp: 0.0986, Energy: -84.979861-0.000167j
[2025-07-30 12:50:28] [Iter 2008/4650] R3[957/1200], Temp: 0.0978, Energy: -85.016854+0.000905j
[2025-07-30 12:50:43] [Iter 2009/4650] R3[958/1200], Temp: 0.0970, Energy: -84.989657-0.003328j
[2025-07-30 12:50:57] [Iter 2010/4650] R3[959/1200], Temp: 0.0963, Energy: -84.970007-0.005192j
[2025-07-30 12:51:12] [Iter 2011/4650] R3[960/1200], Temp: 0.0955, Energy: -85.033667+0.008405j
[2025-07-30 12:51:27] [Iter 2012/4650] R3[961/1200], Temp: 0.0947, Energy: -85.025915+0.003741j
[2025-07-30 12:51:41] [Iter 2013/4650] R3[962/1200], Temp: 0.0940, Energy: -85.008044+0.001319j
[2025-07-30 12:51:55] [Iter 2014/4650] R3[963/1200], Temp: 0.0932, Energy: -85.007623-0.001590j
[2025-07-30 12:52:10] [Iter 2015/4650] R3[964/1200], Temp: 0.0924, Energy: -85.005403+0.004649j
[2025-07-30 12:52:25] [Iter 2016/4650] R3[965/1200], Temp: 0.0917, Energy: -84.963469+0.001410j
[2025-07-30 12:52:40] [Iter 2017/4650] R3[966/1200], Temp: 0.0909, Energy: -85.014238-0.000979j
[2025-07-30 12:52:54] [Iter 2018/4650] R3[967/1200], Temp: 0.0902, Energy: -84.991792-0.000077j
[2025-07-30 12:53:09] [Iter 2019/4650] R3[968/1200], Temp: 0.0894, Energy: -84.959110-0.000486j
[2025-07-30 12:53:24] [Iter 2020/4650] R3[969/1200], Temp: 0.0887, Energy: -84.953098+0.004661j
[2025-07-30 12:53:38] [Iter 2021/4650] R3[970/1200], Temp: 0.0879, Energy: -84.939886+0.004765j
[2025-07-30 12:53:53] [Iter 2022/4650] R3[971/1200], Temp: 0.0872, Energy: -84.997910+0.004275j
[2025-07-30 12:54:08] [Iter 2023/4650] R3[972/1200], Temp: 0.0865, Energy: -84.953224-0.008286j
[2025-07-30 12:54:23] [Iter 2024/4650] R3[973/1200], Temp: 0.0857, Energy: -84.972963-0.007405j
[2025-07-30 12:54:38] [Iter 2025/4650] R3[974/1200], Temp: 0.0850, Energy: -84.984664+0.008947j
[2025-07-30 12:54:53] [Iter 2026/4650] R3[975/1200], Temp: 0.0843, Energy: -84.995408+0.009878j
[2025-07-30 12:55:08] [Iter 2027/4650] R3[976/1200], Temp: 0.0835, Energy: -84.974035+0.009791j
[2025-07-30 12:55:22] [Iter 2028/4650] R3[977/1200], Temp: 0.0828, Energy: -84.945798+0.007100j
[2025-07-30 12:55:37] [Iter 2029/4650] R3[978/1200], Temp: 0.0821, Energy: -84.934057-0.000112j
[2025-07-30 12:55:52] [Iter 2030/4650] R3[979/1200], Temp: 0.0814, Energy: -84.946042+0.001833j
[2025-07-30 12:56:07] [Iter 2031/4650] R3[980/1200], Temp: 0.0807, Energy: -84.898565+0.008207j
[2025-07-30 12:56:22] [Iter 2032/4650] R3[981/1200], Temp: 0.0800, Energy: -84.951087-0.007434j
[2025-07-30 12:56:37] [Iter 2033/4650] R3[982/1200], Temp: 0.0792, Energy: -84.969165-0.005680j
[2025-07-30 12:56:52] [Iter 2034/4650] R3[983/1200], Temp: 0.0785, Energy: -84.928317+0.001573j
[2025-07-30 12:57:07] [Iter 2035/4650] R3[984/1200], Temp: 0.0778, Energy: -85.001649-0.003572j
[2025-07-30 12:57:21] [Iter 2036/4650] R3[985/1200], Temp: 0.0771, Energy: -85.000521+0.002864j
[2025-07-30 12:57:35] [Iter 2037/4650] R3[986/1200], Temp: 0.0764, Energy: -84.999072-0.001490j
[2025-07-30 12:57:50] [Iter 2038/4650] R3[987/1200], Temp: 0.0757, Energy: -85.067440+0.000748j
[2025-07-30 12:58:04] [Iter 2039/4650] R3[988/1200], Temp: 0.0751, Energy: -84.998626-0.008758j
[2025-07-30 12:58:19] [Iter 2040/4650] R3[989/1200], Temp: 0.0744, Energy: -84.990566-0.001038j
[2025-07-30 12:58:33] [Iter 2041/4650] R3[990/1200], Temp: 0.0737, Energy: -84.984385+0.010168j
[2025-07-30 12:58:48] [Iter 2042/4650] R3[991/1200], Temp: 0.0730, Energy: -85.034463+0.004464j
[2025-07-30 12:59:03] [Iter 2043/4650] R3[992/1200], Temp: 0.0723, Energy: -85.035572+0.009097j
[2025-07-30 12:59:18] [Iter 2044/4650] R3[993/1200], Temp: 0.0716, Energy: -85.030605+0.000846j
[2025-07-30 12:59:33] [Iter 2045/4650] R3[994/1200], Temp: 0.0710, Energy: -85.002932-0.008744j
[2025-07-30 12:59:47] [Iter 2046/4650] R3[995/1200], Temp: 0.0703, Energy: -84.928847+0.015333j
[2025-07-30 13:00:02] [Iter 2047/4650] R3[996/1200], Temp: 0.0696, Energy: -84.966134-0.001955j
[2025-07-30 13:00:17] [Iter 2048/4650] R3[997/1200], Temp: 0.0690, Energy: -84.955661-0.001879j
[2025-07-30 13:00:32] [Iter 2049/4650] R3[998/1200], Temp: 0.0683, Energy: -84.942954-0.001340j
[2025-07-30 13:00:46] [Iter 2050/4650] R3[999/1200], Temp: 0.0676, Energy: -84.913767-0.003501j
[2025-07-30 13:01:01] [Iter 2051/4650] R3[1000/1200], Temp: 0.0670, Energy: -84.982005+0.004322j
[2025-07-30 13:01:16] [Iter 2052/4650] R3[1001/1200], Temp: 0.0663, Energy: -85.039291+0.003481j
[2025-07-30 13:01:31] [Iter 2053/4650] R3[1002/1200], Temp: 0.0657, Energy: -85.049107+0.005754j
[2025-07-30 13:01:45] [Iter 2054/4650] R3[1003/1200], Temp: 0.0650, Energy: -84.923494+0.005596j
[2025-07-30 13:02:00] [Iter 2055/4650] R3[1004/1200], Temp: 0.0644, Energy: -85.002794+0.000127j
[2025-07-30 13:02:15] [Iter 2056/4650] R3[1005/1200], Temp: 0.0638, Energy: -85.009843-0.003711j
[2025-07-30 13:02:30] [Iter 2057/4650] R3[1006/1200], Temp: 0.0631, Energy: -85.059753-0.000840j
[2025-07-30 13:02:45] [Iter 2058/4650] R3[1007/1200], Temp: 0.0625, Energy: -85.022118+0.001676j
[2025-07-30 13:02:59] [Iter 2059/4650] R3[1008/1200], Temp: 0.0618, Energy: -85.014189-0.001503j
[2025-07-30 13:03:14] [Iter 2060/4650] R3[1009/1200], Temp: 0.0612, Energy: -85.068263-0.006114j
[2025-07-30 13:03:29] [Iter 2061/4650] R3[1010/1200], Temp: 0.0606, Energy: -85.021756+0.004212j
[2025-07-30 13:03:43] [Iter 2062/4650] R3[1011/1200], Temp: 0.0600, Energy: -85.057034-0.000438j
[2025-07-30 13:03:58] [Iter 2063/4650] R3[1012/1200], Temp: 0.0593, Energy: -85.097447+0.004131j
[2025-07-30 13:04:13] [Iter 2064/4650] R3[1013/1200], Temp: 0.0587, Energy: -85.134369+0.003688j
[2025-07-30 13:04:27] [Iter 2065/4650] R3[1014/1200], Temp: 0.0581, Energy: -85.036460+0.009093j
[2025-07-30 13:04:42] [Iter 2066/4650] R3[1015/1200], Temp: 0.0575, Energy: -85.030016+0.000644j
[2025-07-30 13:04:56] [Iter 2067/4650] R3[1016/1200], Temp: 0.0569, Energy: -85.014734+0.005932j
[2025-07-30 13:05:11] [Iter 2068/4650] R3[1017/1200], Temp: 0.0563, Energy: -84.966258-0.000045j
[2025-07-30 13:05:26] [Iter 2069/4650] R3[1018/1200], Temp: 0.0557, Energy: -85.020781-0.005808j
[2025-07-30 13:05:41] [Iter 2070/4650] R3[1019/1200], Temp: 0.0551, Energy: -84.989796-0.001603j
[2025-07-30 13:05:56] [Iter 2071/4650] R3[1020/1200], Temp: 0.0545, Energy: -85.012768+0.000629j
[2025-07-30 13:06:10] [Iter 2072/4650] R3[1021/1200], Temp: 0.0539, Energy: -84.991132+0.006298j
[2025-07-30 13:06:25] [Iter 2073/4650] R3[1022/1200], Temp: 0.0533, Energy: -84.971759-0.002770j
[2025-07-30 13:06:40] [Iter 2074/4650] R3[1023/1200], Temp: 0.0527, Energy: -85.009399-0.002066j
[2025-07-30 13:06:54] [Iter 2075/4650] R3[1024/1200], Temp: 0.0521, Energy: -85.044889+0.003865j
[2025-07-30 13:07:09] [Iter 2076/4650] R3[1025/1200], Temp: 0.0516, Energy: -85.025044-0.008179j
[2025-07-30 13:07:24] [Iter 2077/4650] R3[1026/1200], Temp: 0.0510, Energy: -85.028683-0.005082j
[2025-07-30 13:07:38] [Iter 2078/4650] R3[1027/1200], Temp: 0.0504, Energy: -85.010903+0.004817j
[2025-07-30 13:07:53] [Iter 2079/4650] R3[1028/1200], Temp: 0.0498, Energy: -84.976473+0.012111j
[2025-07-30 13:08:08] [Iter 2080/4650] R3[1029/1200], Temp: 0.0493, Energy: -84.971162+0.001594j
[2025-07-30 13:08:22] [Iter 2081/4650] R3[1030/1200], Temp: 0.0487, Energy: -84.961794-0.001873j
[2025-07-30 13:08:37] [Iter 2082/4650] R3[1031/1200], Temp: 0.0481, Energy: -85.010447-0.004105j
[2025-07-30 13:08:51] [Iter 2083/4650] R3[1032/1200], Temp: 0.0476, Energy: -84.983948-0.001817j
[2025-07-30 13:09:05] [Iter 2084/4650] R3[1033/1200], Temp: 0.0470, Energy: -85.001645+0.002286j
[2025-07-30 13:09:20] [Iter 2085/4650] R3[1034/1200], Temp: 0.0465, Energy: -85.077841-0.000071j
[2025-07-30 13:09:36] [Iter 2086/4650] R3[1035/1200], Temp: 0.0459, Energy: -85.055960+0.005706j
[2025-07-30 13:09:51] [Iter 2087/4650] R3[1036/1200], Temp: 0.0454, Energy: -85.141936-0.004970j
[2025-07-30 13:10:05] [Iter 2088/4650] R3[1037/1200], Temp: 0.0448, Energy: -85.077675-0.014147j
[2025-07-30 13:10:20] [Iter 2089/4650] R3[1038/1200], Temp: 0.0443, Energy: -85.111833-0.002894j
[2025-07-30 13:10:35] [Iter 2090/4650] R3[1039/1200], Temp: 0.0438, Energy: -85.063011-0.001158j
[2025-07-30 13:10:49] [Iter 2091/4650] R3[1040/1200], Temp: 0.0432, Energy: -85.028792-0.008490j
[2025-07-30 13:11:04] [Iter 2092/4650] R3[1041/1200], Temp: 0.0427, Energy: -84.988008-0.015643j
[2025-07-30 13:11:18] [Iter 2093/4650] R3[1042/1200], Temp: 0.0422, Energy: -85.028741-0.003190j
[2025-07-30 13:11:33] [Iter 2094/4650] R3[1043/1200], Temp: 0.0416, Energy: -85.003516-0.009807j
[2025-07-30 13:11:48] [Iter 2095/4650] R3[1044/1200], Temp: 0.0411, Energy: -84.982496+0.000135j
[2025-07-30 13:12:03] [Iter 2096/4650] R3[1045/1200], Temp: 0.0406, Energy: -84.960940+0.000216j
[2025-07-30 13:12:17] [Iter 2097/4650] R3[1046/1200], Temp: 0.0401, Energy: -85.007708+0.003722j
[2025-07-30 13:12:32] [Iter 2098/4650] R3[1047/1200], Temp: 0.0396, Energy: -84.979622-0.004520j
[2025-07-30 13:12:47] [Iter 2099/4650] R3[1048/1200], Temp: 0.0391, Energy: -85.003385+0.002536j
[2025-07-30 13:13:02] [Iter 2100/4650] R3[1049/1200], Temp: 0.0386, Energy: -84.931286+0.012043j
[2025-07-30 13:13:17] [Iter 2101/4650] R3[1050/1200], Temp: 0.0381, Energy: -85.009206+0.002522j
[2025-07-30 13:13:32] [Iter 2102/4650] R3[1051/1200], Temp: 0.0376, Energy: -85.006814+0.015080j
[2025-07-30 13:13:47] [Iter 2103/4650] R3[1052/1200], Temp: 0.0371, Energy: -84.988891+0.002180j
[2025-07-30 13:14:02] [Iter 2104/4650] R3[1053/1200], Temp: 0.0366, Energy: -84.931813+0.010752j
[2025-07-30 13:14:17] [Iter 2105/4650] R3[1054/1200], Temp: 0.0361, Energy: -84.946518-0.000401j
[2025-07-30 13:14:31] [Iter 2106/4650] R3[1055/1200], Temp: 0.0356, Energy: -84.937427+0.008458j
[2025-07-30 13:14:46] [Iter 2107/4650] R3[1056/1200], Temp: 0.0351, Energy: -84.946257+0.000002j
[2025-07-30 13:15:00] [Iter 2108/4650] R3[1057/1200], Temp: 0.0346, Energy: -84.973696+0.007555j
[2025-07-30 13:15:15] [Iter 2109/4650] R3[1058/1200], Temp: 0.0342, Energy: -84.962656+0.005898j
[2025-07-30 13:15:30] [Iter 2110/4650] R3[1059/1200], Temp: 0.0337, Energy: -84.961594+0.008238j
[2025-07-30 13:15:45] [Iter 2111/4650] R3[1060/1200], Temp: 0.0332, Energy: -84.928122+0.003025j
[2025-07-30 13:16:00] [Iter 2112/4650] R3[1061/1200], Temp: 0.0327, Energy: -84.954527+0.004668j
[2025-07-30 13:16:14] [Iter 2113/4650] R3[1062/1200], Temp: 0.0323, Energy: -84.993579+0.001654j
[2025-07-30 13:16:29] [Iter 2114/4650] R3[1063/1200], Temp: 0.0318, Energy: -84.961180-0.002338j
[2025-07-30 13:16:44] [Iter 2115/4650] R3[1064/1200], Temp: 0.0314, Energy: -84.997903-0.001208j
[2025-07-30 13:16:58] [Iter 2116/4650] R3[1065/1200], Temp: 0.0309, Energy: -85.059260+0.008053j
[2025-07-30 13:17:14] [Iter 2117/4650] R3[1066/1200], Temp: 0.0305, Energy: -85.026825+0.008426j
[2025-07-30 13:17:28] [Iter 2118/4650] R3[1067/1200], Temp: 0.0300, Energy: -84.945534-0.014248j
[2025-07-30 13:17:43] [Iter 2119/4650] R3[1068/1200], Temp: 0.0296, Energy: -84.924916+0.002868j
[2025-07-30 13:17:58] [Iter 2120/4650] R3[1069/1200], Temp: 0.0291, Energy: -84.917864-0.010868j
[2025-07-30 13:18:13] [Iter 2121/4650] R3[1070/1200], Temp: 0.0287, Energy: -84.929313-0.006772j
[2025-07-30 13:18:28] [Iter 2122/4650] R3[1071/1200], Temp: 0.0282, Energy: -84.968746+0.002506j
[2025-07-30 13:18:43] [Iter 2123/4650] R3[1072/1200], Temp: 0.0278, Energy: -84.977830-0.005596j
[2025-07-30 13:18:58] [Iter 2124/4650] R3[1073/1200], Temp: 0.0274, Energy: -85.017553-0.001969j
[2025-07-30 13:19:12] [Iter 2125/4650] R3[1074/1200], Temp: 0.0270, Energy: -84.984056-0.002737j
[2025-07-30 13:19:27] [Iter 2126/4650] R3[1075/1200], Temp: 0.0265, Energy: -85.043195-0.005938j
[2025-07-30 13:19:42] [Iter 2127/4650] R3[1076/1200], Temp: 0.0261, Energy: -84.973546+0.005173j
[2025-07-30 13:19:57] [Iter 2128/4650] R3[1077/1200], Temp: 0.0257, Energy: -85.047485+0.002614j
[2025-07-30 13:20:12] [Iter 2129/4650] R3[1078/1200], Temp: 0.0253, Energy: -85.070883-0.001563j
[2025-07-30 13:20:27] [Iter 2130/4650] R3[1079/1200], Temp: 0.0249, Energy: -85.036697+0.009739j
[2025-07-30 13:20:42] [Iter 2131/4650] R3[1080/1200], Temp: 0.0245, Energy: -85.067228-0.011202j
[2025-07-30 13:20:57] [Iter 2132/4650] R3[1081/1200], Temp: 0.0241, Energy: -84.987822-0.009401j
[2025-07-30 13:21:11] [Iter 2133/4650] R3[1082/1200], Temp: 0.0237, Energy: -84.976789+0.006484j
[2025-07-30 13:21:27] [Iter 2134/4650] R3[1083/1200], Temp: 0.0233, Energy: -85.010908-0.010486j
[2025-07-30 13:21:41] [Iter 2135/4650] R3[1084/1200], Temp: 0.0229, Energy: -85.020764-0.005666j
[2025-07-30 13:21:56] [Iter 2136/4650] R3[1085/1200], Temp: 0.0225, Energy: -85.042274-0.010542j
[2025-07-30 13:22:11] [Iter 2137/4650] R3[1086/1200], Temp: 0.0221, Energy: -85.024490-0.011388j
[2025-07-30 13:22:25] [Iter 2138/4650] R3[1087/1200], Temp: 0.0217, Energy: -85.020040-0.008301j
[2025-07-30 13:22:40] [Iter 2139/4650] R3[1088/1200], Temp: 0.0213, Energy: -85.016865-0.009184j
[2025-07-30 13:22:55] [Iter 2140/4650] R3[1089/1200], Temp: 0.0210, Energy: -85.036744+0.001260j
[2025-07-30 13:23:09] [Iter 2141/4650] R3[1090/1200], Temp: 0.0206, Energy: -84.995262-0.007973j
[2025-07-30 13:23:24] [Iter 2142/4650] R3[1091/1200], Temp: 0.0202, Energy: -85.024865-0.002003j
[2025-07-30 13:23:39] [Iter 2143/4650] R3[1092/1200], Temp: 0.0199, Energy: -85.007305-0.003212j
[2025-07-30 13:23:54] [Iter 2144/4650] R3[1093/1200], Temp: 0.0195, Energy: -84.980294-0.008335j
[2025-07-30 13:24:08] [Iter 2145/4650] R3[1094/1200], Temp: 0.0191, Energy: -84.976563+0.001742j
[2025-07-30 13:24:24] [Iter 2146/4650] R3[1095/1200], Temp: 0.0188, Energy: -85.063964+0.000390j
[2025-07-30 13:24:38] [Iter 2147/4650] R3[1096/1200], Temp: 0.0184, Energy: -85.005405-0.006193j
[2025-07-30 13:24:54] [Iter 2148/4650] R3[1097/1200], Temp: 0.0181, Energy: -85.036019+0.005780j
[2025-07-30 13:25:08] [Iter 2149/4650] R3[1098/1200], Temp: 0.0177, Energy: -85.019582-0.003187j
[2025-07-30 13:25:23] [Iter 2150/4650] R3[1099/1200], Temp: 0.0174, Energy: -84.958239+0.004877j
[2025-07-30 13:25:37] [Iter 2151/4650] R3[1100/1200], Temp: 0.0170, Energy: -85.000578-0.000924j
[2025-07-30 13:25:52] [Iter 2152/4650] R3[1101/1200], Temp: 0.0167, Energy: -84.987801+0.025687j
[2025-07-30 13:26:07] [Iter 2153/4650] R3[1102/1200], Temp: 0.0164, Energy: -84.987920-0.004108j
[2025-07-30 13:26:22] [Iter 2154/4650] R3[1103/1200], Temp: 0.0160, Energy: -84.938639+0.004929j
[2025-07-30 13:26:37] [Iter 2155/4650] R3[1104/1200], Temp: 0.0157, Energy: -85.031031+0.004186j
[2025-07-30 13:26:51] [Iter 2156/4650] R3[1105/1200], Temp: 0.0154, Energy: -85.025924-0.007740j
[2025-07-30 13:27:06] [Iter 2157/4650] R3[1106/1200], Temp: 0.0151, Energy: -85.011065+0.000443j
[2025-07-30 13:27:20] [Iter 2158/4650] R3[1107/1200], Temp: 0.0147, Energy: -85.009356-0.003944j
[2025-07-30 13:27:35] [Iter 2159/4650] R3[1108/1200], Temp: 0.0144, Energy: -84.954941-0.015998j
[2025-07-30 13:27:50] [Iter 2160/4650] R3[1109/1200], Temp: 0.0141, Energy: -85.035694+0.000768j
[2025-07-30 13:28:05] [Iter 2161/4650] R3[1110/1200], Temp: 0.0138, Energy: -85.031522-0.002932j
[2025-07-30 13:28:20] [Iter 2162/4650] R3[1111/1200], Temp: 0.0135, Energy: -84.957124-0.015106j
[2025-07-30 13:28:35] [Iter 2163/4650] R3[1112/1200], Temp: 0.0132, Energy: -84.978725-0.013036j
[2025-07-30 13:28:50] [Iter 2164/4650] R3[1113/1200], Temp: 0.0129, Energy: -84.977845+0.005420j
[2025-07-30 13:29:05] [Iter 2165/4650] R3[1114/1200], Temp: 0.0126, Energy: -84.978726+0.007814j
[2025-07-30 13:29:20] [Iter 2166/4650] R3[1115/1200], Temp: 0.0123, Energy: -84.948729-0.006253j
[2025-07-30 13:29:35] [Iter 2167/4650] R3[1116/1200], Temp: 0.0120, Energy: -84.977673-0.002660j
[2025-07-30 13:29:49] [Iter 2168/4650] R3[1117/1200], Temp: 0.0118, Energy: -84.966799+0.005041j
[2025-07-30 13:30:04] [Iter 2169/4650] R3[1118/1200], Temp: 0.0115, Energy: -85.023424+0.002741j
[2025-07-30 13:30:19] [Iter 2170/4650] R3[1119/1200], Temp: 0.0112, Energy: -84.968799-0.002283j
[2025-07-30 13:30:34] [Iter 2171/4650] R3[1120/1200], Temp: 0.0109, Energy: -84.984389-0.003671j
[2025-07-30 13:30:49] [Iter 2172/4650] R3[1121/1200], Temp: 0.0107, Energy: -84.968083+0.013456j
[2025-07-30 13:31:03] [Iter 2173/4650] R3[1122/1200], Temp: 0.0104, Energy: -85.042340-0.001931j
[2025-07-30 13:31:18] [Iter 2174/4650] R3[1123/1200], Temp: 0.0101, Energy: -84.987611+0.003249j
[2025-07-30 13:31:33] [Iter 2175/4650] R3[1124/1200], Temp: 0.0099, Energy: -84.929967+0.008132j
[2025-07-30 13:31:47] [Iter 2176/4650] R3[1125/1200], Temp: 0.0096, Energy: -84.964361-0.001525j
[2025-07-30 13:32:01] [Iter 2177/4650] R3[1126/1200], Temp: 0.0094, Energy: -84.990189+0.003357j
[2025-07-30 13:32:16] [Iter 2178/4650] R3[1127/1200], Temp: 0.0091, Energy: -85.026997-0.002364j
[2025-07-30 13:32:31] [Iter 2179/4650] R3[1128/1200], Temp: 0.0089, Energy: -84.960969-0.010302j
[2025-07-30 13:32:45] [Iter 2180/4650] R3[1129/1200], Temp: 0.0086, Energy: -84.943808+0.002957j
[2025-07-30 13:33:00] [Iter 2181/4650] R3[1130/1200], Temp: 0.0084, Energy: -85.010222+0.005071j
[2025-07-30 13:33:15] [Iter 2182/4650] R3[1131/1200], Temp: 0.0081, Energy: -84.981311-0.004298j
[2025-07-30 13:33:30] [Iter 2183/4650] R3[1132/1200], Temp: 0.0079, Energy: -84.947479-0.000958j
[2025-07-30 13:33:45] [Iter 2184/4650] R3[1133/1200], Temp: 0.0077, Energy: -84.956229-0.000576j
[2025-07-30 13:34:00] [Iter 2185/4650] R3[1134/1200], Temp: 0.0074, Energy: -84.991642+0.000919j
[2025-07-30 13:34:14] [Iter 2186/4650] R3[1135/1200], Temp: 0.0072, Energy: -85.161512-0.000188j
[2025-07-30 13:34:30] [Iter 2187/4650] R3[1136/1200], Temp: 0.0070, Energy: -85.126197+0.002809j
[2025-07-30 13:34:44] [Iter 2188/4650] R3[1137/1200], Temp: 0.0068, Energy: -85.105608-0.003746j
[2025-07-30 13:34:59] [Iter 2189/4650] R3[1138/1200], Temp: 0.0066, Energy: -84.994863-0.004912j
[2025-07-30 13:35:14] [Iter 2190/4650] R3[1139/1200], Temp: 0.0064, Energy: -85.016389-0.000947j
[2025-07-30 13:35:29] [Iter 2191/4650] R3[1140/1200], Temp: 0.0062, Energy: -85.052262-0.005522j
[2025-07-30 13:35:43] [Iter 2192/4650] R3[1141/1200], Temp: 0.0060, Energy: -85.043213-0.004847j
[2025-07-30 13:35:58] [Iter 2193/4650] R3[1142/1200], Temp: 0.0058, Energy: -84.967705-0.005022j
[2025-07-30 13:36:13] [Iter 2194/4650] R3[1143/1200], Temp: 0.0056, Energy: -85.015005+0.000379j
[2025-07-30 13:36:28] [Iter 2195/4650] R3[1144/1200], Temp: 0.0054, Energy: -84.959131+0.000936j
[2025-07-30 13:36:43] [Iter 2196/4650] R3[1145/1200], Temp: 0.0052, Energy: -84.973032+0.008757j
[2025-07-30 13:36:58] [Iter 2197/4650] R3[1146/1200], Temp: 0.0050, Energy: -85.038771+0.003390j
[2025-07-30 13:37:13] [Iter 2198/4650] R3[1147/1200], Temp: 0.0048, Energy: -85.051953+0.003104j
[2025-07-30 13:37:28] [Iter 2199/4650] R3[1148/1200], Temp: 0.0046, Energy: -85.050903+0.003878j
[2025-07-30 13:37:43] [Iter 2200/4650] R3[1149/1200], Temp: 0.0045, Energy: -85.050990-0.000772j
[2025-07-30 13:37:57] [Iter 2201/4650] R3[1150/1200], Temp: 0.0043, Energy: -85.043840+0.001246j
[2025-07-30 13:38:12] [Iter 2202/4650] R3[1151/1200], Temp: 0.0041, Energy: -85.005502+0.009125j
[2025-07-30 13:38:27] [Iter 2203/4650] R3[1152/1200], Temp: 0.0039, Energy: -84.973160+0.000542j
[2025-07-30 13:38:42] [Iter 2204/4650] R3[1153/1200], Temp: 0.0038, Energy: -85.061712+0.002952j
[2025-07-30 13:38:57] [Iter 2205/4650] R3[1154/1200], Temp: 0.0036, Energy: -85.092014-0.006038j
[2025-07-30 13:39:12] [Iter 2206/4650] R3[1155/1200], Temp: 0.0035, Energy: -85.121063+0.005960j
[2025-07-30 13:39:26] [Iter 2207/4650] R3[1156/1200], Temp: 0.0033, Energy: -85.041047-0.001223j
[2025-07-30 13:39:41] [Iter 2208/4650] R3[1157/1200], Temp: 0.0032, Energy: -85.058428+0.009991j
[2025-07-30 13:39:57] [Iter 2209/4650] R3[1158/1200], Temp: 0.0030, Energy: -85.071735-0.006874j
[2025-07-30 13:40:12] [Iter 2210/4650] R3[1159/1200], Temp: 0.0029, Energy: -85.147619-0.002737j
[2025-07-30 13:40:26] [Iter 2211/4650] R3[1160/1200], Temp: 0.0027, Energy: -85.079050-0.011534j
[2025-07-30 13:40:42] [Iter 2212/4650] R3[1161/1200], Temp: 0.0026, Energy: -85.043049-0.007438j
[2025-07-30 13:40:57] [Iter 2213/4650] R3[1162/1200], Temp: 0.0025, Energy: -85.084726+0.008052j
[2025-07-30 13:41:12] [Iter 2214/4650] R3[1163/1200], Temp: 0.0023, Energy: -85.100889-0.006548j
[2025-07-30 13:41:26] [Iter 2215/4650] R3[1164/1200], Temp: 0.0022, Energy: -85.105367-0.002612j
[2025-07-30 13:41:41] [Iter 2216/4650] R3[1165/1200], Temp: 0.0021, Energy: -85.085111-0.006868j
[2025-07-30 13:41:56] [Iter 2217/4650] R3[1166/1200], Temp: 0.0020, Energy: -85.101755-0.003092j
[2025-07-30 13:42:11] [Iter 2218/4650] R3[1167/1200], Temp: 0.0019, Energy: -85.061172-0.004387j
[2025-07-30 13:42:26] [Iter 2219/4650] R3[1168/1200], Temp: 0.0018, Energy: -85.080675+0.002033j
[2025-07-30 13:42:41] [Iter 2220/4650] R3[1169/1200], Temp: 0.0016, Energy: -85.075955-0.010971j
[2025-07-30 13:42:56] [Iter 2221/4650] R3[1170/1200], Temp: 0.0015, Energy: -85.074745+0.002688j
[2025-07-30 13:43:10] [Iter 2222/4650] R3[1171/1200], Temp: 0.0014, Energy: -85.112574-0.009802j
[2025-07-30 13:43:25] [Iter 2223/4650] R3[1172/1200], Temp: 0.0013, Energy: -85.097723-0.000733j
[2025-07-30 13:43:39] [Iter 2224/4650] R3[1173/1200], Temp: 0.0012, Energy: -85.111434+0.001544j
[2025-07-30 13:43:54] [Iter 2225/4650] R3[1174/1200], Temp: 0.0012, Energy: -84.988204+0.002566j
[2025-07-30 13:44:09] [Iter 2226/4650] R3[1175/1200], Temp: 0.0011, Energy: -84.996499-0.013017j
[2025-07-30 13:44:24] [Iter 2227/4650] R3[1176/1200], Temp: 0.0010, Energy: -85.009765-0.000079j
[2025-07-30 13:44:39] [Iter 2228/4650] R3[1177/1200], Temp: 0.0009, Energy: -85.066625+0.001908j
[2025-07-30 13:44:53] [Iter 2229/4650] R3[1178/1200], Temp: 0.0008, Energy: -85.005306+0.001796j
[2025-07-30 13:45:07] [Iter 2230/4650] R3[1179/1200], Temp: 0.0008, Energy: -85.032544-0.007008j
[2025-07-30 13:45:23] [Iter 2231/4650] R3[1180/1200], Temp: 0.0007, Energy: -85.031374+0.005031j
[2025-07-30 13:45:37] [Iter 2232/4650] R3[1181/1200], Temp: 0.0006, Energy: -85.045359-0.002681j
[2025-07-30 13:45:52] [Iter 2233/4650] R3[1182/1200], Temp: 0.0006, Energy: -85.054385+0.008175j
[2025-07-30 13:46:06] [Iter 2234/4650] R3[1183/1200], Temp: 0.0005, Energy: -85.034606-0.001163j
[2025-07-30 13:46:21] [Iter 2235/4650] R3[1184/1200], Temp: 0.0004, Energy: -85.036429-0.007076j
[2025-07-30 13:46:35] [Iter 2236/4650] R3[1185/1200], Temp: 0.0004, Energy: -85.080305-0.007487j
[2025-07-30 13:46:51] [Iter 2237/4650] R3[1186/1200], Temp: 0.0003, Energy: -85.081559+0.002274j
[2025-07-30 13:47:05] [Iter 2238/4650] R3[1187/1200], Temp: 0.0003, Energy: -85.029372-0.010421j
[2025-07-30 13:47:20] [Iter 2239/4650] R3[1188/1200], Temp: 0.0002, Energy: -85.049611+0.000225j
[2025-07-30 13:47:35] [Iter 2240/4650] R3[1189/1200], Temp: 0.0002, Energy: -85.071482+0.002545j
[2025-07-30 13:47:50] [Iter 2241/4650] R3[1190/1200], Temp: 0.0002, Energy: -85.064849-0.007356j
[2025-07-30 13:48:05] [Iter 2242/4650] R3[1191/1200], Temp: 0.0001, Energy: -85.087243+0.009987j
[2025-07-30 13:48:20] [Iter 2243/4650] R3[1192/1200], Temp: 0.0001, Energy: -85.154332-0.009127j
[2025-07-30 13:48:35] [Iter 2244/4650] R3[1193/1200], Temp: 0.0001, Energy: -85.079175+0.009361j
[2025-07-30 13:48:49] [Iter 2245/4650] R3[1194/1200], Temp: 0.0001, Energy: -85.097647-0.007391j
[2025-07-30 13:49:03] [Iter 2246/4650] R3[1195/1200], Temp: 0.0000, Energy: -85.063417-0.004431j
[2025-07-30 13:49:18] [Iter 2247/4650] R3[1196/1200], Temp: 0.0000, Energy: -85.159196-0.008505j
[2025-07-30 13:49:32] [Iter 2248/4650] R3[1197/1200], Temp: 0.0000, Energy: -85.061217-0.012796j
[2025-07-30 13:49:46] [Iter 2249/4650] R3[1198/1200], Temp: 0.0000, Energy: -85.035308-0.006096j
[2025-07-30 13:50:01] [Iter 2250/4650] R3[1199/1200], Temp: 0.0000, Energy: -85.026344-0.003781j
[2025-07-30 13:50:01] RESTART #4 | Period: 2400
[2025-07-30 13:50:16] [Iter 2251/4650] R4[0/2400], Temp: 1.0000, Energy: -85.047521+0.007665j
[2025-07-30 13:50:31] [Iter 2252/4650] R4[1/2400], Temp: 1.0000, Energy: -84.984151-0.005708j
[2025-07-30 13:50:45] [Iter 2253/4650] R4[2/2400], Temp: 1.0000, Energy: -84.906463-0.000562j
[2025-07-30 13:51:00] [Iter 2254/4650] R4[3/2400], Temp: 1.0000, Energy: -84.959479-0.000876j
[2025-07-30 13:51:15] [Iter 2255/4650] R4[4/2400], Temp: 1.0000, Energy: -84.915901+0.000916j
[2025-07-30 13:51:29] [Iter 2256/4650] R4[5/2400], Temp: 1.0000, Energy: -84.985233-0.016838j
[2025-07-30 13:51:43] [Iter 2257/4650] R4[6/2400], Temp: 1.0000, Energy: -85.008045-0.011302j
[2025-07-30 13:51:58] [Iter 2258/4650] R4[7/2400], Temp: 1.0000, Energy: -84.917965+0.006620j
[2025-07-30 13:52:13] [Iter 2259/4650] R4[8/2400], Temp: 1.0000, Energy: -84.909454+0.000357j
[2025-07-30 13:52:28] [Iter 2260/4650] R4[9/2400], Temp: 1.0000, Energy: -84.954197-0.002409j
[2025-07-30 13:52:43] [Iter 2261/4650] R4[10/2400], Temp: 1.0000, Energy: -85.005480-0.006225j
[2025-07-30 13:52:58] [Iter 2262/4650] R4[11/2400], Temp: 0.9999, Energy: -84.972240+0.004503j
[2025-07-30 13:53:13] [Iter 2263/4650] R4[12/2400], Temp: 0.9999, Energy: -84.970120-0.002477j
[2025-07-30 13:53:27] [Iter 2264/4650] R4[13/2400], Temp: 0.9999, Energy: -85.027174+0.002110j
[2025-07-30 13:53:42] [Iter 2265/4650] R4[14/2400], Temp: 0.9999, Energy: -85.001104-0.001416j
[2025-07-30 13:53:56] [Iter 2266/4650] R4[15/2400], Temp: 0.9999, Energy: -85.034058-0.006559j
[2025-07-30 13:54:11] [Iter 2267/4650] R4[16/2400], Temp: 0.9999, Energy: -84.992324+0.009545j
[2025-07-30 13:54:26] [Iter 2268/4650] R4[17/2400], Temp: 0.9999, Energy: -85.043345-0.010767j
[2025-07-30 13:54:40] [Iter 2269/4650] R4[18/2400], Temp: 0.9999, Energy: -85.033998-0.007374j
[2025-07-30 13:54:55] [Iter 2270/4650] R4[19/2400], Temp: 0.9998, Energy: -85.046287-0.006566j
[2025-07-30 13:55:10] [Iter 2271/4650] R4[20/2400], Temp: 0.9998, Energy: -85.058095-0.009844j
[2025-07-30 13:55:25] [Iter 2272/4650] R4[21/2400], Temp: 0.9998, Energy: -85.056524+0.009091j
[2025-07-30 13:55:40] [Iter 2273/4650] R4[22/2400], Temp: 0.9998, Energy: -85.036002+0.003926j
[2025-07-30 13:55:54] [Iter 2274/4650] R4[23/2400], Temp: 0.9998, Energy: -85.027589+0.006654j
[2025-07-30 13:56:09] [Iter 2275/4650] R4[24/2400], Temp: 0.9998, Energy: -85.031688-0.007003j
[2025-07-30 13:56:24] [Iter 2276/4650] R4[25/2400], Temp: 0.9997, Energy: -84.947415-0.006800j
[2025-07-30 13:56:38] [Iter 2277/4650] R4[26/2400], Temp: 0.9997, Energy: -84.968389+0.006719j
[2025-07-30 13:56:53] [Iter 2278/4650] R4[27/2400], Temp: 0.9997, Energy: -84.952232-0.000235j
[2025-07-30 13:57:08] [Iter 2279/4650] R4[28/2400], Temp: 0.9997, Energy: -84.953406-0.004051j
[2025-07-30 13:57:23] [Iter 2280/4650] R4[29/2400], Temp: 0.9996, Energy: -84.975008+0.008327j
[2025-07-30 13:57:38] [Iter 2281/4650] R4[30/2400], Temp: 0.9996, Energy: -84.924453-0.002709j
[2025-07-30 13:57:53] [Iter 2282/4650] R4[31/2400], Temp: 0.9996, Energy: -84.972015+0.005832j
[2025-07-30 13:58:08] [Iter 2283/4650] R4[32/2400], Temp: 0.9996, Energy: -84.942220+0.001253j
[2025-07-30 13:58:23] [Iter 2284/4650] R4[33/2400], Temp: 0.9995, Energy: -84.982417+0.012297j
[2025-07-30 13:58:37] [Iter 2285/4650] R4[34/2400], Temp: 0.9995, Energy: -84.957466+0.000404j
[2025-07-30 13:58:52] [Iter 2286/4650] R4[35/2400], Temp: 0.9995, Energy: -84.946092+0.000052j
[2025-07-30 13:59:07] [Iter 2287/4650] R4[36/2400], Temp: 0.9994, Energy: -84.918507+0.005077j
[2025-07-30 13:59:22] [Iter 2288/4650] R4[37/2400], Temp: 0.9994, Energy: -84.942304+0.005149j
[2025-07-30 13:59:36] [Iter 2289/4650] R4[38/2400], Temp: 0.9994, Energy: -85.010799+0.007297j
[2025-07-30 13:59:51] [Iter 2290/4650] R4[39/2400], Temp: 0.9993, Energy: -85.004542+0.013851j
[2025-07-30 14:00:06] [Iter 2291/4650] R4[40/2400], Temp: 0.9993, Energy: -84.974855+0.007432j
[2025-07-30 14:00:21] [Iter 2292/4650] R4[41/2400], Temp: 0.9993, Energy: -85.027570+0.005976j
[2025-07-30 14:00:35] [Iter 2293/4650] R4[42/2400], Temp: 0.9992, Energy: -85.018372+0.004715j
[2025-07-30 14:00:50] [Iter 2294/4650] R4[43/2400], Temp: 0.9992, Energy: -84.964199+0.006082j
[2025-07-30 14:01:05] [Iter 2295/4650] R4[44/2400], Temp: 0.9992, Energy: -84.963611+0.010238j
[2025-07-30 14:01:20] [Iter 2296/4650] R4[45/2400], Temp: 0.9991, Energy: -84.962621+0.005650j
[2025-07-30 14:01:35] [Iter 2297/4650] R4[46/2400], Temp: 0.9991, Energy: -84.974762+0.001978j
[2025-07-30 14:01:50] [Iter 2298/4650] R4[47/2400], Temp: 0.9991, Energy: -84.988415+0.002328j
[2025-07-30 14:02:05] [Iter 2299/4650] R4[48/2400], Temp: 0.9990, Energy: -84.966201-0.003131j
[2025-07-30 14:02:19] [Iter 2300/4650] R4[49/2400], Temp: 0.9990, Energy: -84.916396+0.004901j
[2025-07-30 14:02:34] [Iter 2301/4650] R4[50/2400], Temp: 0.9989, Energy: -84.989590+0.001976j
[2025-07-30 14:02:49] [Iter 2302/4650] R4[51/2400], Temp: 0.9989, Energy: -84.943463-0.002601j
[2025-07-30 14:03:04] [Iter 2303/4650] R4[52/2400], Temp: 0.9988, Energy: -84.904568+0.000231j
[2025-07-30 14:03:18] [Iter 2304/4650] R4[53/2400], Temp: 0.9988, Energy: -84.984893+0.002307j
[2025-07-30 14:03:33] [Iter 2305/4650] R4[54/2400], Temp: 0.9988, Energy: -84.954347+0.004120j
[2025-07-30 14:03:48] [Iter 2306/4650] R4[55/2400], Temp: 0.9987, Energy: -84.946508+0.000192j
[2025-07-30 14:04:03] [Iter 2307/4650] R4[56/2400], Temp: 0.9987, Energy: -85.023921+0.000904j
[2025-07-30 14:04:17] [Iter 2308/4650] R4[57/2400], Temp: 0.9986, Energy: -85.067618-0.004805j
[2025-07-30 14:04:32] [Iter 2309/4650] R4[58/2400], Temp: 0.9986, Energy: -85.074427+0.002462j
[2025-07-30 14:04:47] [Iter 2310/4650] R4[59/2400], Temp: 0.9985, Energy: -85.061869+0.004426j
[2025-07-30 14:05:02] [Iter 2311/4650] R4[60/2400], Temp: 0.9985, Energy: -85.013887+0.005910j
[2025-07-30 14:05:17] [Iter 2312/4650] R4[61/2400], Temp: 0.9984, Energy: -85.002694-0.010151j
[2025-07-30 14:05:32] [Iter 2313/4650] R4[62/2400], Temp: 0.9984, Energy: -85.033381-0.002151j
[2025-07-30 14:05:47] [Iter 2314/4650] R4[63/2400], Temp: 0.9983, Energy: -85.008539+0.008110j
[2025-07-30 14:06:02] [Iter 2315/4650] R4[64/2400], Temp: 0.9982, Energy: -85.100845+0.012405j
[2025-07-30 14:06:17] [Iter 2316/4650] R4[65/2400], Temp: 0.9982, Energy: -85.063913-0.006312j
[2025-07-30 14:06:32] [Iter 2317/4650] R4[66/2400], Temp: 0.9981, Energy: -85.045737-0.001008j
[2025-07-30 14:06:47] [Iter 2318/4650] R4[67/2400], Temp: 0.9981, Energy: -85.038617-0.013823j
[2025-07-30 14:07:02] [Iter 2319/4650] R4[68/2400], Temp: 0.9980, Energy: -85.005385-0.004074j
[2025-07-30 14:07:16] [Iter 2320/4650] R4[69/2400], Temp: 0.9980, Energy: -85.082071+0.016235j
[2025-07-30 14:07:31] [Iter 2321/4650] R4[70/2400], Temp: 0.9979, Energy: -85.059547-0.010227j
[2025-07-30 14:07:46] [Iter 2322/4650] R4[71/2400], Temp: 0.9978, Energy: -85.024155-0.002592j
[2025-07-30 14:08:01] [Iter 2323/4650] R4[72/2400], Temp: 0.9978, Energy: -85.055861+0.018620j
[2025-07-30 14:08:16] [Iter 2324/4650] R4[73/2400], Temp: 0.9977, Energy: -85.096336+0.000214j
[2025-07-30 14:08:30] [Iter 2325/4650] R4[74/2400], Temp: 0.9977, Energy: -85.113443+0.005138j
[2025-07-30 14:08:45] [Iter 2326/4650] R4[75/2400], Temp: 0.9976, Energy: -85.093349+0.004311j
[2025-07-30 14:09:00] [Iter 2327/4650] R4[76/2400], Temp: 0.9975, Energy: -85.070130+0.000321j
[2025-07-30 14:09:15] [Iter 2328/4650] R4[77/2400], Temp: 0.9975, Energy: -85.078679-0.002135j
[2025-07-30 14:09:30] [Iter 2329/4650] R4[78/2400], Temp: 0.9974, Energy: -85.046304+0.003703j
[2025-07-30 14:09:44] [Iter 2330/4650] R4[79/2400], Temp: 0.9973, Energy: -85.081250-0.001546j
[2025-07-30 14:10:00] [Iter 2331/4650] R4[80/2400], Temp: 0.9973, Energy: -85.123556+0.003670j
[2025-07-30 14:10:15] [Iter 2332/4650] R4[81/2400], Temp: 0.9972, Energy: -85.050176+0.001574j
[2025-07-30 14:10:29] [Iter 2333/4650] R4[82/2400], Temp: 0.9971, Energy: -84.995255-0.005511j
[2025-07-30 14:10:44] [Iter 2334/4650] R4[83/2400], Temp: 0.9971, Energy: -85.017736+0.005421j
[2025-07-30 14:10:59] [Iter 2335/4650] R4[84/2400], Temp: 0.9970, Energy: -84.961786-0.004302j
[2025-07-30 14:11:13] [Iter 2336/4650] R4[85/2400], Temp: 0.9969, Energy: -84.959799+0.000419j
[2025-07-30 14:11:28] [Iter 2337/4650] R4[86/2400], Temp: 0.9968, Energy: -84.984117-0.001131j
[2025-07-30 14:11:43] [Iter 2338/4650] R4[87/2400], Temp: 0.9968, Energy: -84.985195-0.014994j
[2025-07-30 14:11:58] [Iter 2339/4650] R4[88/2400], Temp: 0.9967, Energy: -85.051128-0.006081j
[2025-07-30 14:12:13] [Iter 2340/4650] R4[89/2400], Temp: 0.9966, Energy: -85.042268+0.001707j
[2025-07-30 14:12:28] [Iter 2341/4650] R4[90/2400], Temp: 0.9965, Energy: -85.090531-0.004734j
[2025-07-30 14:12:43] [Iter 2342/4650] R4[91/2400], Temp: 0.9965, Energy: -85.051833-0.000651j
[2025-07-30 14:12:58] [Iter 2343/4650] R4[92/2400], Temp: 0.9964, Energy: -84.969843-0.007496j
[2025-07-30 14:13:12] [Iter 2344/4650] R4[93/2400], Temp: 0.9963, Energy: -85.013152+0.000978j
[2025-07-30 14:13:27] [Iter 2345/4650] R4[94/2400], Temp: 0.9962, Energy: -84.976612-0.003848j
[2025-07-30 14:13:42] [Iter 2346/4650] R4[95/2400], Temp: 0.9961, Energy: -84.976322-0.002624j
[2025-07-30 14:13:57] [Iter 2347/4650] R4[96/2400], Temp: 0.9961, Energy: -85.029410-0.000903j
[2025-07-30 14:14:12] [Iter 2348/4650] R4[97/2400], Temp: 0.9960, Energy: -85.034598-0.001344j
[2025-07-30 14:14:27] [Iter 2349/4650] R4[98/2400], Temp: 0.9959, Energy: -85.004730-0.004022j
[2025-07-30 14:14:42] [Iter 2350/4650] R4[99/2400], Temp: 0.9958, Energy: -85.000531+0.000188j
[2025-07-30 14:14:57] [Iter 2351/4650] R4[100/2400], Temp: 0.9957, Energy: -85.005628-0.012168j
[2025-07-30 14:15:11] [Iter 2352/4650] R4[101/2400], Temp: 0.9956, Energy: -84.966897-0.002416j
[2025-07-30 14:15:26] [Iter 2353/4650] R4[102/2400], Temp: 0.9955, Energy: -85.019795-0.007686j
[2025-07-30 14:15:41] [Iter 2354/4650] R4[103/2400], Temp: 0.9955, Energy: -85.053124-0.002173j
[2025-07-30 14:15:56] [Iter 2355/4650] R4[104/2400], Temp: 0.9954, Energy: -85.040604-0.005341j
[2025-07-30 14:16:10] [Iter 2356/4650] R4[105/2400], Temp: 0.9953, Energy: -85.003366+0.002256j
[2025-07-30 14:16:26] [Iter 2357/4650] R4[106/2400], Temp: 0.9952, Energy: -85.051496-0.002504j
[2025-07-30 14:16:40] [Iter 2358/4650] R4[107/2400], Temp: 0.9951, Energy: -85.045223+0.004903j
[2025-07-30 14:16:55] [Iter 2359/4650] R4[108/2400], Temp: 0.9950, Energy: -85.004327+0.000694j
[2025-07-30 14:17:10] [Iter 2360/4650] R4[109/2400], Temp: 0.9949, Energy: -85.036255-0.004184j
[2025-07-30 14:17:25] [Iter 2361/4650] R4[110/2400], Temp: 0.9948, Energy: -85.145239-0.000085j
[2025-07-30 14:17:40] [Iter 2362/4650] R4[111/2400], Temp: 0.9947, Energy: -85.167389-0.002837j
[2025-07-30 14:17:55] [Iter 2363/4650] R4[112/2400], Temp: 0.9946, Energy: -85.051173-0.000363j
[2025-07-30 14:18:10] [Iter 2364/4650] R4[113/2400], Temp: 0.9945, Energy: -85.013432-0.004384j
[2025-07-30 14:18:25] [Iter 2365/4650] R4[114/2400], Temp: 0.9944, Energy: -85.001196+0.000651j
[2025-07-30 14:18:40] [Iter 2366/4650] R4[115/2400], Temp: 0.9943, Energy: -84.980887+0.003601j
[2025-07-30 14:18:54] [Iter 2367/4650] R4[116/2400], Temp: 0.9942, Energy: -84.996089-0.003419j
[2025-07-30 14:19:09] [Iter 2368/4650] R4[117/2400], Temp: 0.9941, Energy: -84.995964+0.001324j
[2025-07-30 14:19:24] [Iter 2369/4650] R4[118/2400], Temp: 0.9940, Energy: -84.979898-0.000760j
[2025-07-30 14:19:39] [Iter 2370/4650] R4[119/2400], Temp: 0.9939, Energy: -84.944584-0.005886j
[2025-07-30 14:19:54] [Iter 2371/4650] R4[120/2400], Temp: 0.9938, Energy: -84.960625-0.001796j
[2025-07-30 14:20:09] [Iter 2372/4650] R4[121/2400], Temp: 0.9937, Energy: -85.039851+0.015414j
[2025-07-30 14:20:24] [Iter 2373/4650] R4[122/2400], Temp: 0.9936, Energy: -84.974084-0.013533j
[2025-07-30 14:20:39] [Iter 2374/4650] R4[123/2400], Temp: 0.9935, Energy: -85.021746-0.003133j
[2025-07-30 14:20:53] [Iter 2375/4650] R4[124/2400], Temp: 0.9934, Energy: -84.985272-0.002837j
[2025-07-30 14:21:08] [Iter 2376/4650] R4[125/2400], Temp: 0.9933, Energy: -84.943564-0.012332j
[2025-07-30 14:21:23] [Iter 2377/4650] R4[126/2400], Temp: 0.9932, Energy: -84.938215-0.004371j
[2025-07-30 14:21:38] [Iter 2378/4650] R4[127/2400], Temp: 0.9931, Energy: -85.017469+0.003795j
[2025-07-30 14:21:53] [Iter 2379/4650] R4[128/2400], Temp: 0.9930, Energy: -85.033122-0.002257j
[2025-07-30 14:22:08] [Iter 2380/4650] R4[129/2400], Temp: 0.9929, Energy: -85.017221-0.018982j
[2025-07-30 14:22:23] [Iter 2381/4650] R4[130/2400], Temp: 0.9928, Energy: -85.075489+0.000384j
[2025-07-30 14:22:37] [Iter 2382/4650] R4[131/2400], Temp: 0.9927, Energy: -85.046589+0.001769j
[2025-07-30 14:22:52] [Iter 2383/4650] R4[132/2400], Temp: 0.9926, Energy: -85.081203-0.006454j
[2025-07-30 14:23:07] [Iter 2384/4650] R4[133/2400], Temp: 0.9924, Energy: -85.072855+0.005451j
[2025-07-30 14:23:22] [Iter 2385/4650] R4[134/2400], Temp: 0.9923, Energy: -85.027950+0.002207j
[2025-07-30 14:23:37] [Iter 2386/4650] R4[135/2400], Temp: 0.9922, Energy: -85.089144-0.001216j
[2025-07-30 14:23:52] [Iter 2387/4650] R4[136/2400], Temp: 0.9921, Energy: -85.069246-0.004235j
[2025-07-30 14:24:07] [Iter 2388/4650] R4[137/2400], Temp: 0.9920, Energy: -85.062321-0.002964j
[2025-07-30 14:24:22] [Iter 2389/4650] R4[138/2400], Temp: 0.9919, Energy: -85.021378-0.002763j
[2025-07-30 14:24:36] [Iter 2390/4650] R4[139/2400], Temp: 0.9917, Energy: -85.057791-0.005614j
[2025-07-30 14:24:51] [Iter 2391/4650] R4[140/2400], Temp: 0.9916, Energy: -85.065492-0.003755j
[2025-07-30 14:25:05] [Iter 2392/4650] R4[141/2400], Temp: 0.9915, Energy: -85.069593-0.001920j
[2025-07-30 14:25:20] [Iter 2393/4650] R4[142/2400], Temp: 0.9914, Energy: -85.051931-0.004398j
[2025-07-30 14:25:34] [Iter 2394/4650] R4[143/2400], Temp: 0.9913, Energy: -85.035189-0.001939j
[2025-07-30 14:25:49] [Iter 2395/4650] R4[144/2400], Temp: 0.9911, Energy: -85.059856-0.003654j
[2025-07-30 14:26:04] [Iter 2396/4650] R4[145/2400], Temp: 0.9910, Energy: -85.014811-0.015210j
[2025-07-30 14:26:19] [Iter 2397/4650] R4[146/2400], Temp: 0.9909, Energy: -85.079052+0.008771j
[2025-07-30 14:26:33] [Iter 2398/4650] R4[147/2400], Temp: 0.9908, Energy: -85.081223-0.001699j
[2025-07-30 14:26:48] [Iter 2399/4650] R4[148/2400], Temp: 0.9906, Energy: -85.040608+0.003491j
[2025-07-30 14:27:03] [Iter 2400/4650] R4[149/2400], Temp: 0.9905, Energy: -84.972958-0.001290j
[2025-07-30 14:27:17] [Iter 2401/4650] R4[150/2400], Temp: 0.9904, Energy: -84.986683-0.006476j
[2025-07-30 14:27:32] [Iter 2402/4650] R4[151/2400], Temp: 0.9903, Energy: -84.987660-0.005441j
[2025-07-30 14:27:46] [Iter 2403/4650] R4[152/2400], Temp: 0.9901, Energy: -84.958355-0.010099j
[2025-07-30 14:28:01] [Iter 2404/4650] R4[153/2400], Temp: 0.9900, Energy: -84.948083+0.007028j
[2025-07-30 14:28:16] [Iter 2405/4650] R4[154/2400], Temp: 0.9899, Energy: -84.991378-0.004230j
[2025-07-30 14:28:31] [Iter 2406/4650] R4[155/2400], Temp: 0.9897, Energy: -84.961091-0.002788j
[2025-07-30 14:28:45] [Iter 2407/4650] R4[156/2400], Temp: 0.9896, Energy: -84.973520-0.011108j
[2025-07-30 14:29:01] [Iter 2408/4650] R4[157/2400], Temp: 0.9895, Energy: -84.970119-0.007160j
[2025-07-30 14:29:15] [Iter 2409/4650] R4[158/2400], Temp: 0.9893, Energy: -84.987287+0.002127j
[2025-07-30 14:29:30] [Iter 2410/4650] R4[159/2400], Temp: 0.9892, Energy: -84.971260+0.001981j
[2025-07-30 14:29:45] [Iter 2411/4650] R4[160/2400], Temp: 0.9891, Energy: -84.923162-0.003309j
[2025-07-30 14:30:00] [Iter 2412/4650] R4[161/2400], Temp: 0.9889, Energy: -84.911201+0.007494j
[2025-07-30 14:30:14] [Iter 2413/4650] R4[162/2400], Temp: 0.9888, Energy: -84.930576+0.005083j
[2025-07-30 14:30:29] [Iter 2414/4650] R4[163/2400], Temp: 0.9887, Energy: -85.014332-0.002097j
[2025-07-30 14:30:44] [Iter 2415/4650] R4[164/2400], Temp: 0.9885, Energy: -84.966744+0.004933j
[2025-07-30 14:30:59] [Iter 2416/4650] R4[165/2400], Temp: 0.9884, Energy: -84.929368+0.001911j
[2025-07-30 14:31:13] [Iter 2417/4650] R4[166/2400], Temp: 0.9882, Energy: -85.091746-0.003067j
[2025-07-30 14:31:28] [Iter 2418/4650] R4[167/2400], Temp: 0.9881, Energy: -85.052672+0.002872j
[2025-07-30 14:31:43] [Iter 2419/4650] R4[168/2400], Temp: 0.9880, Energy: -85.067381-0.008107j
[2025-07-30 14:31:58] [Iter 2420/4650] R4[169/2400], Temp: 0.9878, Energy: -85.075699-0.003496j
[2025-07-30 14:32:13] [Iter 2421/4650] R4[170/2400], Temp: 0.9877, Energy: -85.072545-0.008418j
[2025-07-30 14:32:28] [Iter 2422/4650] R4[171/2400], Temp: 0.9875, Energy: -85.058182-0.015244j
[2025-07-30 14:32:43] [Iter 2423/4650] R4[172/2400], Temp: 0.9874, Energy: -85.082245-0.008636j
[2025-07-30 14:32:58] [Iter 2424/4650] R4[173/2400], Temp: 0.9872, Energy: -85.113314-0.003220j
[2025-07-30 14:33:13] [Iter 2425/4650] R4[174/2400], Temp: 0.9871, Energy: -85.070325-0.011346j
[2025-07-30 14:33:28] [Iter 2426/4650] R4[175/2400], Temp: 0.9869, Energy: -85.010670-0.006113j
[2025-07-30 14:33:43] [Iter 2427/4650] R4[176/2400], Temp: 0.9868, Energy: -85.064005-0.000828j
[2025-07-30 14:33:58] [Iter 2428/4650] R4[177/2400], Temp: 0.9866, Energy: -85.068547-0.002159j
[2025-07-30 14:34:12] [Iter 2429/4650] R4[178/2400], Temp: 0.9865, Energy: -85.095968-0.001179j
[2025-07-30 14:34:27] [Iter 2430/4650] R4[179/2400], Temp: 0.9863, Energy: -85.123545+0.005706j
[2025-07-30 14:34:42] [Iter 2431/4650] R4[180/2400], Temp: 0.9862, Energy: -85.095671-0.004584j
[2025-07-30 14:34:56] [Iter 2432/4650] R4[181/2400], Temp: 0.9860, Energy: -85.076120-0.002210j
[2025-07-30 14:35:11] [Iter 2433/4650] R4[182/2400], Temp: 0.9859, Energy: -85.027876+0.006429j
[2025-07-30 14:35:26] [Iter 2434/4650] R4[183/2400], Temp: 0.9857, Energy: -85.039753-0.007870j
[2025-07-30 14:35:41] [Iter 2435/4650] R4[184/2400], Temp: 0.9856, Energy: -85.099957-0.005363j
[2025-07-30 14:35:55] [Iter 2436/4650] R4[185/2400], Temp: 0.9854, Energy: -85.061191-0.003560j
[2025-07-30 14:36:10] [Iter 2437/4650] R4[186/2400], Temp: 0.9853, Energy: -85.034871-0.010649j
[2025-07-30 14:36:25] [Iter 2438/4650] R4[187/2400], Temp: 0.9851, Energy: -84.975391+0.006367j
[2025-07-30 14:36:40] [Iter 2439/4650] R4[188/2400], Temp: 0.9849, Energy: -85.039613+0.006041j
[2025-07-30 14:36:55] [Iter 2440/4650] R4[189/2400], Temp: 0.9848, Energy: -85.012910+0.005120j
[2025-07-30 14:37:10] [Iter 2441/4650] R4[190/2400], Temp: 0.9846, Energy: -84.995122+0.009246j
[2025-07-30 14:37:24] [Iter 2442/4650] R4[191/2400], Temp: 0.9845, Energy: -85.064775+0.000247j
[2025-07-30 14:37:38] [Iter 2443/4650] R4[192/2400], Temp: 0.9843, Energy: -85.071860+0.001998j
[2025-07-30 14:37:53] [Iter 2444/4650] R4[193/2400], Temp: 0.9841, Energy: -85.092908+0.008150j
[2025-07-30 14:38:08] [Iter 2445/4650] R4[194/2400], Temp: 0.9840, Energy: -85.034905-0.003361j
[2025-07-30 14:38:23] [Iter 2446/4650] R4[195/2400], Temp: 0.9838, Energy: -85.054507-0.006709j
[2025-07-30 14:38:38] [Iter 2447/4650] R4[196/2400], Temp: 0.9836, Energy: -85.073138+0.007028j
[2025-07-30 14:38:53] [Iter 2448/4650] R4[197/2400], Temp: 0.9835, Energy: -85.087978-0.013947j
[2025-07-30 14:39:08] [Iter 2449/4650] R4[198/2400], Temp: 0.9833, Energy: -85.038496+0.001205j
[2025-07-30 14:39:23] [Iter 2450/4650] R4[199/2400], Temp: 0.9831, Energy: -85.009484-0.000745j
[2025-07-30 14:39:38] [Iter 2451/4650] R4[200/2400], Temp: 0.9830, Energy: -85.002598+0.017065j
[2025-07-30 14:39:53] [Iter 2452/4650] R4[201/2400], Temp: 0.9828, Energy: -84.921388+0.011285j
[2025-07-30 14:40:07] [Iter 2453/4650] R4[202/2400], Temp: 0.9826, Energy: -85.031098-0.001331j
[2025-07-30 14:40:22] [Iter 2454/4650] R4[203/2400], Temp: 0.9825, Energy: -84.902958-0.008702j
[2025-07-30 14:40:37] [Iter 2455/4650] R4[204/2400], Temp: 0.9823, Energy: -84.882507+0.005198j
[2025-07-30 14:40:51] [Iter 2456/4650] R4[205/2400], Temp: 0.9821, Energy: -84.932759+0.010231j
[2025-07-30 14:41:06] [Iter 2457/4650] R4[206/2400], Temp: 0.9819, Energy: -84.942637+0.002146j
[2025-07-30 14:41:21] [Iter 2458/4650] R4[207/2400], Temp: 0.9818, Energy: -84.958308+0.003274j
[2025-07-30 14:41:36] [Iter 2459/4650] R4[208/2400], Temp: 0.9816, Energy: -85.024300+0.003478j
[2025-07-30 14:41:50] [Iter 2460/4650] R4[209/2400], Temp: 0.9814, Energy: -85.005960+0.002297j
[2025-07-30 14:42:05] [Iter 2461/4650] R4[210/2400], Temp: 0.9812, Energy: -84.973161+0.008359j
[2025-07-30 14:42:19] [Iter 2462/4650] R4[211/2400], Temp: 0.9810, Energy: -85.016356-0.002248j
[2025-07-30 14:42:34] [Iter 2463/4650] R4[212/2400], Temp: 0.9809, Energy: -85.050231+0.004115j
[2025-07-30 14:42:48] [Iter 2464/4650] R4[213/2400], Temp: 0.9807, Energy: -85.033503+0.001237j
[2025-07-30 14:43:03] [Iter 2465/4650] R4[214/2400], Temp: 0.9805, Energy: -85.003520+0.003499j
[2025-07-30 14:43:18] [Iter 2466/4650] R4[215/2400], Temp: 0.9803, Energy: -85.040181-0.004674j
[2025-07-30 14:43:33] [Iter 2467/4650] R4[216/2400], Temp: 0.9801, Energy: -84.975603-0.000525j
[2025-07-30 14:43:48] [Iter 2468/4650] R4[217/2400], Temp: 0.9800, Energy: -85.012727-0.003915j
[2025-07-30 14:44:03] [Iter 2469/4650] R4[218/2400], Temp: 0.9798, Energy: -85.020886+0.002393j
[2025-07-30 14:44:17] [Iter 2470/4650] R4[219/2400], Temp: 0.9796, Energy: -85.004599+0.001769j
[2025-07-30 14:44:32] [Iter 2471/4650] R4[220/2400], Temp: 0.9794, Energy: -84.992076+0.000114j
[2025-07-30 14:44:47] [Iter 2472/4650] R4[221/2400], Temp: 0.9792, Energy: -84.981822-0.000022j
[2025-07-30 14:45:02] [Iter 2473/4650] R4[222/2400], Temp: 0.9790, Energy: -85.011855-0.004943j
[2025-07-30 14:45:16] [Iter 2474/4650] R4[223/2400], Temp: 0.9788, Energy: -84.997643+0.006670j
[2025-07-30 14:45:32] [Iter 2475/4650] R4[224/2400], Temp: 0.9787, Energy: -84.978503+0.006536j
[2025-07-30 14:45:47] [Iter 2476/4650] R4[225/2400], Temp: 0.9785, Energy: -84.956391+0.000396j
[2025-07-30 14:46:01] [Iter 2477/4650] R4[226/2400], Temp: 0.9783, Energy: -84.990974+0.000682j
[2025-07-30 14:46:16] [Iter 2478/4650] R4[227/2400], Temp: 0.9781, Energy: -85.033912+0.000968j
[2025-07-30 14:46:31] [Iter 2479/4650] R4[228/2400], Temp: 0.9779, Energy: -85.044949+0.001754j
[2025-07-30 14:46:46] [Iter 2480/4650] R4[229/2400], Temp: 0.9777, Energy: -84.963663+0.000982j
[2025-07-30 14:47:00] [Iter 2481/4650] R4[230/2400], Temp: 0.9775, Energy: -84.926021+0.004707j
[2025-07-30 14:47:15] [Iter 2482/4650] R4[231/2400], Temp: 0.9773, Energy: -85.000714-0.002059j
[2025-07-30 14:47:30] [Iter 2483/4650] R4[232/2400], Temp: 0.9771, Energy: -84.973754-0.000941j
[2025-07-30 14:47:45] [Iter 2484/4650] R4[233/2400], Temp: 0.9769, Energy: -85.003361-0.002219j
[2025-07-30 14:48:00] [Iter 2485/4650] R4[234/2400], Temp: 0.9767, Energy: -84.973366-0.004187j
[2025-07-30 14:48:15] [Iter 2486/4650] R4[235/2400], Temp: 0.9765, Energy: -85.010944+0.009682j
[2025-07-30 14:48:30] [Iter 2487/4650] R4[236/2400], Temp: 0.9763, Energy: -84.954595+0.002376j
[2025-07-30 14:48:45] [Iter 2488/4650] R4[237/2400], Temp: 0.9761, Energy: -84.989583+0.000257j
[2025-07-30 14:48:59] [Iter 2489/4650] R4[238/2400], Temp: 0.9759, Energy: -85.052137+0.001734j
[2025-07-30 14:49:14] [Iter 2490/4650] R4[239/2400], Temp: 0.9757, Energy: -85.042066-0.000939j
[2025-07-30 14:49:29] [Iter 2491/4650] R4[240/2400], Temp: 0.9755, Energy: -84.973129+0.004024j
[2025-07-30 14:49:43] [Iter 2492/4650] R4[241/2400], Temp: 0.9753, Energy: -84.975322-0.010379j
[2025-07-30 14:49:58] [Iter 2493/4650] R4[242/2400], Temp: 0.9751, Energy: -84.969318-0.005557j
[2025-07-30 14:50:13] [Iter 2494/4650] R4[243/2400], Temp: 0.9749, Energy: -84.999324+0.002918j
[2025-07-30 14:50:28] [Iter 2495/4650] R4[244/2400], Temp: 0.9747, Energy: -85.003121-0.001978j
[2025-07-30 14:50:43] [Iter 2496/4650] R4[245/2400], Temp: 0.9745, Energy: -84.926817+0.001820j
[2025-07-30 14:50:58] [Iter 2497/4650] R4[246/2400], Temp: 0.9743, Energy: -84.920787+0.005826j
[2025-07-30 14:51:13] [Iter 2498/4650] R4[247/2400], Temp: 0.9741, Energy: -84.935989-0.010375j
[2025-07-30 14:51:28] [Iter 2499/4650] R4[248/2400], Temp: 0.9739, Energy: -85.012834+0.001345j
[2025-07-30 14:51:43] [Iter 2500/4650] R4[249/2400], Temp: 0.9737, Energy: -85.003585-0.002066j
[2025-07-30 14:51:43] ✓ Checkpoint saved: checkpoint_iter_002500.pkl
[2025-07-30 14:51:58] [Iter 2501/4650] R4[250/2400], Temp: 0.9735, Energy: -85.073315+0.001327j
[2025-07-30 14:52:13] [Iter 2502/4650] R4[251/2400], Temp: 0.9733, Energy: -85.079215+0.002603j
[2025-07-30 14:52:27] [Iter 2503/4650] R4[252/2400], Temp: 0.9730, Energy: -85.067174-0.004901j
[2025-07-30 14:52:42] [Iter 2504/4650] R4[253/2400], Temp: 0.9728, Energy: -84.968622+0.005670j
[2025-07-30 14:52:57] [Iter 2505/4650] R4[254/2400], Temp: 0.9726, Energy: -84.954683-0.011119j
[2025-07-30 14:53:12] [Iter 2506/4650] R4[255/2400], Temp: 0.9724, Energy: -84.846308+0.009883j
[2025-07-30 14:53:27] [Iter 2507/4650] R4[256/2400], Temp: 0.9722, Energy: -84.872272-0.005008j
[2025-07-30 14:53:42] [Iter 2508/4650] R4[257/2400], Temp: 0.9720, Energy: -84.914202+0.001658j
[2025-07-30 14:53:56] [Iter 2509/4650] R4[258/2400], Temp: 0.9718, Energy: -84.972370+0.002617j
[2025-07-30 14:54:11] [Iter 2510/4650] R4[259/2400], Temp: 0.9715, Energy: -85.019951+0.005113j
[2025-07-30 14:54:26] [Iter 2511/4650] R4[260/2400], Temp: 0.9713, Energy: -85.028932-0.001426j
[2025-07-30 14:54:41] [Iter 2512/4650] R4[261/2400], Temp: 0.9711, Energy: -85.079205-0.002762j
[2025-07-30 14:54:56] [Iter 2513/4650] R4[262/2400], Temp: 0.9709, Energy: -84.964983+0.000402j
[2025-07-30 14:55:10] [Iter 2514/4650] R4[263/2400], Temp: 0.9707, Energy: -84.981780+0.001293j
[2025-07-30 14:55:24] [Iter 2515/4650] R4[264/2400], Temp: 0.9704, Energy: -85.002366+0.001965j
[2025-07-30 14:55:39] [Iter 2516/4650] R4[265/2400], Temp: 0.9702, Energy: -85.002509+0.000142j
[2025-07-30 14:55:54] [Iter 2517/4650] R4[266/2400], Temp: 0.9700, Energy: -85.022878+0.003159j
[2025-07-30 14:56:08] [Iter 2518/4650] R4[267/2400], Temp: 0.9698, Energy: -85.031178-0.004699j
[2025-07-30 14:56:23] [Iter 2519/4650] R4[268/2400], Temp: 0.9695, Energy: -85.038934-0.002066j
[2025-07-30 14:56:37] [Iter 2520/4650] R4[269/2400], Temp: 0.9693, Energy: -85.098076+0.007232j
[2025-07-30 14:56:52] [Iter 2521/4650] R4[270/2400], Temp: 0.9691, Energy: -85.065777+0.005687j
[2025-07-30 14:57:06] [Iter 2522/4650] R4[271/2400], Temp: 0.9689, Energy: -85.027721-0.003123j
[2025-07-30 14:57:21] [Iter 2523/4650] R4[272/2400], Temp: 0.9686, Energy: -85.002007-0.002334j
[2025-07-30 14:57:35] [Iter 2524/4650] R4[273/2400], Temp: 0.9684, Energy: -85.034673-0.000865j
[2025-07-30 14:57:50] [Iter 2525/4650] R4[274/2400], Temp: 0.9682, Energy: -85.029400+0.006305j
[2025-07-30 14:58:04] [Iter 2526/4650] R4[275/2400], Temp: 0.9680, Energy: -85.061578-0.006436j
[2025-07-30 14:58:19] [Iter 2527/4650] R4[276/2400], Temp: 0.9677, Energy: -85.033659-0.004497j
[2025-07-30 14:58:33] [Iter 2528/4650] R4[277/2400], Temp: 0.9675, Energy: -85.017390-0.000324j
[2025-07-30 14:58:48] [Iter 2529/4650] R4[278/2400], Temp: 0.9673, Energy: -85.013512-0.002613j
[2025-07-30 14:59:02] [Iter 2530/4650] R4[279/2400], Temp: 0.9670, Energy: -84.961908-0.012376j
[2025-07-30 14:59:17] [Iter 2531/4650] R4[280/2400], Temp: 0.9668, Energy: -84.942768+0.002962j
[2025-07-30 14:59:32] [Iter 2532/4650] R4[281/2400], Temp: 0.9666, Energy: -84.970516-0.009492j
[2025-07-30 14:59:46] [Iter 2533/4650] R4[282/2400], Temp: 0.9663, Energy: -84.951847+0.008590j
[2025-07-30 15:00:01] [Iter 2534/4650] R4[283/2400], Temp: 0.9661, Energy: -84.964334+0.013794j
[2025-07-30 15:00:16] [Iter 2535/4650] R4[284/2400], Temp: 0.9658, Energy: -84.999724+0.001431j
[2025-07-30 15:00:31] [Iter 2536/4650] R4[285/2400], Temp: 0.9656, Energy: -85.010495+0.000098j
[2025-07-30 15:00:45] [Iter 2537/4650] R4[286/2400], Temp: 0.9654, Energy: -84.988812-0.007028j
[2025-07-30 15:01:00] [Iter 2538/4650] R4[287/2400], Temp: 0.9651, Energy: -84.973692-0.005894j
[2025-07-30 15:01:15] [Iter 2539/4650] R4[288/2400], Temp: 0.9649, Energy: -84.987513+0.004164j
[2025-07-30 15:01:30] [Iter 2540/4650] R4[289/2400], Temp: 0.9646, Energy: -84.953791+0.005820j
[2025-07-30 15:01:45] [Iter 2541/4650] R4[290/2400], Temp: 0.9644, Energy: -84.975771-0.004591j
[2025-07-30 15:01:59] [Iter 2542/4650] R4[291/2400], Temp: 0.9642, Energy: -84.948430-0.005223j
[2025-07-30 15:02:14] [Iter 2543/4650] R4[292/2400], Temp: 0.9639, Energy: -84.865558+0.002288j
[2025-07-30 15:02:28] [Iter 2544/4650] R4[293/2400], Temp: 0.9637, Energy: -84.908663-0.000457j
[2025-07-30 15:02:43] [Iter 2545/4650] R4[294/2400], Temp: 0.9634, Energy: -84.946198-0.003344j
[2025-07-30 15:02:58] [Iter 2546/4650] R4[295/2400], Temp: 0.9632, Energy: -84.980067+0.002834j
[2025-07-30 15:03:13] [Iter 2547/4650] R4[296/2400], Temp: 0.9629, Energy: -84.993965-0.001545j
[2025-07-30 15:03:28] [Iter 2548/4650] R4[297/2400], Temp: 0.9627, Energy: -84.943595+0.007487j
[2025-07-30 15:03:43] [Iter 2549/4650] R4[298/2400], Temp: 0.9624, Energy: -84.973491-0.002049j
[2025-07-30 15:03:58] [Iter 2550/4650] R4[299/2400], Temp: 0.9622, Energy: -84.982663-0.006008j
[2025-07-30 15:04:13] [Iter 2551/4650] R4[300/2400], Temp: 0.9619, Energy: -84.947813-0.005670j
[2025-07-30 15:04:28] [Iter 2552/4650] R4[301/2400], Temp: 0.9617, Energy: -84.981219+0.002053j
[2025-07-30 15:04:43] [Iter 2553/4650] R4[302/2400], Temp: 0.9614, Energy: -84.925209+0.003386j
[2025-07-30 15:04:57] [Iter 2554/4650] R4[303/2400], Temp: 0.9612, Energy: -84.971306-0.001652j
[2025-07-30 15:05:12] [Iter 2555/4650] R4[304/2400], Temp: 0.9609, Energy: -84.970102-0.001794j
[2025-07-30 15:05:26] [Iter 2556/4650] R4[305/2400], Temp: 0.9607, Energy: -85.018793+0.009652j
[2025-07-30 15:05:41] [Iter 2557/4650] R4[306/2400], Temp: 0.9604, Energy: -84.967297+0.007597j
[2025-07-30 15:05:56] [Iter 2558/4650] R4[307/2400], Temp: 0.9602, Energy: -84.896750-0.005343j
[2025-07-30 15:06:11] [Iter 2559/4650] R4[308/2400], Temp: 0.9599, Energy: -84.871408+0.011620j
[2025-07-30 15:06:26] [Iter 2560/4650] R4[309/2400], Temp: 0.9597, Energy: -84.970570+0.008001j
[2025-07-30 15:06:40] [Iter 2561/4650] R4[310/2400], Temp: 0.9594, Energy: -84.963471+0.003914j
[2025-07-30 15:06:55] [Iter 2562/4650] R4[311/2400], Temp: 0.9591, Energy: -84.960256-0.008470j
[2025-07-30 15:07:10] [Iter 2563/4650] R4[312/2400], Temp: 0.9589, Energy: -84.902919-0.011246j
[2025-07-30 15:07:25] [Iter 2564/4650] R4[313/2400], Temp: 0.9586, Energy: -84.929381+0.002099j
[2025-07-30 15:07:40] [Iter 2565/4650] R4[314/2400], Temp: 0.9584, Energy: -84.962630+0.003617j
[2025-07-30 15:07:55] [Iter 2566/4650] R4[315/2400], Temp: 0.9581, Energy: -84.938984+0.000619j
[2025-07-30 15:08:10] [Iter 2567/4650] R4[316/2400], Temp: 0.9578, Energy: -84.959011+0.000163j
[2025-07-30 15:08:24] [Iter 2568/4650] R4[317/2400], Temp: 0.9576, Energy: -84.958964-0.002896j
[2025-07-30 15:08:39] [Iter 2569/4650] R4[318/2400], Temp: 0.9573, Energy: -84.971494-0.001636j
[2025-07-30 15:08:53] [Iter 2570/4650] R4[319/2400], Temp: 0.9570, Energy: -84.985425-0.004778j
[2025-07-30 15:09:08] [Iter 2571/4650] R4[320/2400], Temp: 0.9568, Energy: -84.937903+0.008331j
[2025-07-30 15:09:23] [Iter 2572/4650] R4[321/2400], Temp: 0.9565, Energy: -85.032117-0.001038j
[2025-07-30 15:09:37] [Iter 2573/4650] R4[322/2400], Temp: 0.9562, Energy: -84.984879-0.004369j
[2025-07-30 15:09:51] [Iter 2574/4650] R4[323/2400], Temp: 0.9560, Energy: -84.954563-0.005429j
[2025-07-30 15:10:06] [Iter 2575/4650] R4[324/2400], Temp: 0.9557, Energy: -84.980422+0.004916j
[2025-07-30 15:10:21] [Iter 2576/4650] R4[325/2400], Temp: 0.9554, Energy: -84.948127-0.002151j
[2025-07-30 15:10:36] [Iter 2577/4650] R4[326/2400], Temp: 0.9552, Energy: -85.006082+0.001342j
[2025-07-30 15:10:51] [Iter 2578/4650] R4[327/2400], Temp: 0.9549, Energy: -85.028919-0.007309j
[2025-07-30 15:11:06] [Iter 2579/4650] R4[328/2400], Temp: 0.9546, Energy: -84.969120-0.000187j
[2025-07-30 15:11:21] [Iter 2580/4650] R4[329/2400], Temp: 0.9543, Energy: -85.006842+0.001377j
[2025-07-30 15:11:36] [Iter 2581/4650] R4[330/2400], Temp: 0.9541, Energy: -84.987290+0.000348j
[2025-07-30 15:11:50] [Iter 2582/4650] R4[331/2400], Temp: 0.9538, Energy: -85.067723+0.005124j
[2025-07-30 15:12:05] [Iter 2583/4650] R4[332/2400], Temp: 0.9535, Energy: -85.085264+0.000968j
[2025-07-30 15:12:19] [Iter 2584/4650] R4[333/2400], Temp: 0.9532, Energy: -85.011924-0.001834j
[2025-07-30 15:12:34] [Iter 2585/4650] R4[334/2400], Temp: 0.9530, Energy: -85.020659-0.002778j
[2025-07-30 15:12:49] [Iter 2586/4650] R4[335/2400], Temp: 0.9527, Energy: -85.018061-0.004758j
[2025-07-30 15:13:03] [Iter 2587/4650] R4[336/2400], Temp: 0.9524, Energy: -84.989906-0.008908j
[2025-07-30 15:13:18] [Iter 2588/4650] R4[337/2400], Temp: 0.9521, Energy: -85.009242-0.004785j
[2025-07-30 15:13:33] [Iter 2589/4650] R4[338/2400], Temp: 0.9519, Energy: -84.980450-0.005955j
[2025-07-30 15:13:48] [Iter 2590/4650] R4[339/2400], Temp: 0.9516, Energy: -84.963580-0.006511j
[2025-07-30 15:14:03] [Iter 2591/4650] R4[340/2400], Temp: 0.9513, Energy: -85.012340+0.000618j
[2025-07-30 15:14:17] [Iter 2592/4650] R4[341/2400], Temp: 0.9510, Energy: -85.030925-0.002233j
[2025-07-30 15:14:32] [Iter 2593/4650] R4[342/2400], Temp: 0.9507, Energy: -84.953156-0.001380j
[2025-07-30 15:14:47] [Iter 2594/4650] R4[343/2400], Temp: 0.9504, Energy: -84.929674-0.003266j
[2025-07-30 15:15:02] [Iter 2595/4650] R4[344/2400], Temp: 0.9502, Energy: -84.915596+0.003879j
[2025-07-30 15:15:16] [Iter 2596/4650] R4[345/2400], Temp: 0.9499, Energy: -84.981659-0.000149j
[2025-07-30 15:15:31] [Iter 2597/4650] R4[346/2400], Temp: 0.9496, Energy: -84.960013+0.003260j
[2025-07-30 15:15:46] [Iter 2598/4650] R4[347/2400], Temp: 0.9493, Energy: -84.993428+0.000010j
[2025-07-30 15:16:01] [Iter 2599/4650] R4[348/2400], Temp: 0.9490, Energy: -85.020057+0.001777j
[2025-07-30 15:16:16] [Iter 2600/4650] R4[349/2400], Temp: 0.9487, Energy: -85.053748+0.002970j
[2025-07-30 15:16:31] [Iter 2601/4650] R4[350/2400], Temp: 0.9484, Energy: -84.939202-0.000957j
[2025-07-30 15:16:46] [Iter 2602/4650] R4[351/2400], Temp: 0.9481, Energy: -85.025452-0.005382j
[2025-07-30 15:17:01] [Iter 2603/4650] R4[352/2400], Temp: 0.9479, Energy: -84.996872+0.002426j
[2025-07-30 15:17:16] [Iter 2604/4650] R4[353/2400], Temp: 0.9476, Energy: -84.957477-0.007152j
[2025-07-30 15:17:31] [Iter 2605/4650] R4[354/2400], Temp: 0.9473, Energy: -84.975105-0.008377j
[2025-07-30 15:17:46] [Iter 2606/4650] R4[355/2400], Temp: 0.9470, Energy: -85.044328-0.001402j
[2025-07-30 15:18:01] [Iter 2607/4650] R4[356/2400], Temp: 0.9467, Energy: -85.001765-0.009117j
[2025-07-30 15:18:15] [Iter 2608/4650] R4[357/2400], Temp: 0.9464, Energy: -85.001837-0.002729j
[2025-07-30 15:18:29] [Iter 2609/4650] R4[358/2400], Temp: 0.9461, Energy: -85.011210+0.002445j
[2025-07-30 15:18:44] [Iter 2610/4650] R4[359/2400], Temp: 0.9458, Energy: -85.042032-0.003781j
[2025-07-30 15:18:58] [Iter 2611/4650] R4[360/2400], Temp: 0.9455, Energy: -85.021767-0.001888j
[2025-07-30 15:19:13] [Iter 2612/4650] R4[361/2400], Temp: 0.9452, Energy: -85.097717-0.001479j
[2025-07-30 15:19:27] [Iter 2613/4650] R4[362/2400], Temp: 0.9449, Energy: -85.055355-0.002436j
[2025-07-30 15:19:42] [Iter 2614/4650] R4[363/2400], Temp: 0.9446, Energy: -85.052117-0.001640j
[2025-07-30 15:19:57] [Iter 2615/4650] R4[364/2400], Temp: 0.9443, Energy: -85.002615+0.000159j
[2025-07-30 15:20:11] [Iter 2616/4650] R4[365/2400], Temp: 0.9440, Energy: -85.068822-0.007372j
[2025-07-30 15:20:26] [Iter 2617/4650] R4[366/2400], Temp: 0.9437, Energy: -84.979728-0.010204j
[2025-07-30 15:20:40] [Iter 2618/4650] R4[367/2400], Temp: 0.9434, Energy: -85.008014-0.000136j
[2025-07-30 15:20:55] [Iter 2619/4650] R4[368/2400], Temp: 0.9431, Energy: -85.027029+0.001536j
[2025-07-30 15:21:10] [Iter 2620/4650] R4[369/2400], Temp: 0.9428, Energy: -85.092354-0.001722j
[2025-07-30 15:21:25] [Iter 2621/4650] R4[370/2400], Temp: 0.9425, Energy: -85.097269-0.002435j
[2025-07-30 15:21:40] [Iter 2622/4650] R4[371/2400], Temp: 0.9422, Energy: -85.048444-0.004346j
[2025-07-30 15:21:55] [Iter 2623/4650] R4[372/2400], Temp: 0.9419, Energy: -85.055375-0.007009j
[2025-07-30 15:22:09] [Iter 2624/4650] R4[373/2400], Temp: 0.9416, Energy: -85.036986-0.012541j
[2025-07-30 15:22:25] [Iter 2625/4650] R4[374/2400], Temp: 0.9413, Energy: -85.040953-0.015460j
[2025-07-30 15:22:39] [Iter 2626/4650] R4[375/2400], Temp: 0.9410, Energy: -85.025614+0.004108j
[2025-07-30 15:22:54] [Iter 2627/4650] R4[376/2400], Temp: 0.9407, Energy: -85.033912+0.000410j
[2025-07-30 15:23:08] [Iter 2628/4650] R4[377/2400], Temp: 0.9403, Energy: -85.053770+0.006345j
[2025-07-30 15:23:23] [Iter 2629/4650] R4[378/2400], Temp: 0.9400, Energy: -85.028961-0.003094j
[2025-07-30 15:23:37] [Iter 2630/4650] R4[379/2400], Temp: 0.9397, Energy: -85.009350+0.001789j
[2025-07-30 15:23:52] [Iter 2631/4650] R4[380/2400], Temp: 0.9394, Energy: -85.021959-0.007420j
[2025-07-30 15:24:07] [Iter 2632/4650] R4[381/2400], Temp: 0.9391, Energy: -84.965599+0.005783j
[2025-07-30 15:24:22] [Iter 2633/4650] R4[382/2400], Temp: 0.9388, Energy: -84.976075-0.013806j
[2025-07-30 15:24:37] [Iter 2634/4650] R4[383/2400], Temp: 0.9385, Energy: -84.934749-0.001615j
[2025-07-30 15:24:51] [Iter 2635/4650] R4[384/2400], Temp: 0.9382, Energy: -84.999923-0.005056j
[2025-07-30 15:25:06] [Iter 2636/4650] R4[385/2400], Temp: 0.9378, Energy: -85.013139+0.002484j
[2025-07-30 15:25:21] [Iter 2637/4650] R4[386/2400], Temp: 0.9375, Energy: -85.023870-0.000442j
[2025-07-30 15:25:35] [Iter 2638/4650] R4[387/2400], Temp: 0.9372, Energy: -84.988688+0.013832j
[2025-07-30 15:25:50] [Iter 2639/4650] R4[388/2400], Temp: 0.9369, Energy: -85.022512+0.000008j
[2025-07-30 15:26:05] [Iter 2640/4650] R4[389/2400], Temp: 0.9366, Energy: -85.003138+0.001070j
[2025-07-30 15:26:19] [Iter 2641/4650] R4[390/2400], Temp: 0.9362, Energy: -84.978797+0.010658j
[2025-07-30 15:26:33] [Iter 2642/4650] R4[391/2400], Temp: 0.9359, Energy: -84.947028+0.001263j
[2025-07-30 15:26:48] [Iter 2643/4650] R4[392/2400], Temp: 0.9356, Energy: -85.046447+0.005909j
[2025-07-30 15:27:03] [Iter 2644/4650] R4[393/2400], Temp: 0.9353, Energy: -85.031426-0.003066j
[2025-07-30 15:27:18] [Iter 2645/4650] R4[394/2400], Temp: 0.9350, Energy: -85.110887+0.006968j
[2025-07-30 15:27:32] [Iter 2646/4650] R4[395/2400], Temp: 0.9346, Energy: -85.096897-0.000603j
[2025-07-30 15:27:47] [Iter 2647/4650] R4[396/2400], Temp: 0.9343, Energy: -85.094152-0.002209j
[2025-07-30 15:28:02] [Iter 2648/4650] R4[397/2400], Temp: 0.9340, Energy: -85.100569-0.000323j
[2025-07-30 15:28:16] [Iter 2649/4650] R4[398/2400], Temp: 0.9337, Energy: -85.046577+0.002856j
[2025-07-30 15:28:31] [Iter 2650/4650] R4[399/2400], Temp: 0.9333, Energy: -85.080298-0.002764j
[2025-07-30 15:28:46] [Iter 2651/4650] R4[400/2400], Temp: 0.9330, Energy: -85.036634-0.004964j
[2025-07-30 15:29:00] [Iter 2652/4650] R4[401/2400], Temp: 0.9327, Energy: -85.014100+0.004091j
[2025-07-30 15:29:15] [Iter 2653/4650] R4[402/2400], Temp: 0.9324, Energy: -85.031259-0.002535j
[2025-07-30 15:29:30] [Iter 2654/4650] R4[403/2400], Temp: 0.9320, Energy: -85.016717+0.001666j
[2025-07-30 15:29:44] [Iter 2655/4650] R4[404/2400], Temp: 0.9317, Energy: -85.032542+0.001002j
[2025-07-30 15:29:59] [Iter 2656/4650] R4[405/2400], Temp: 0.9314, Energy: -85.038088+0.002283j
[2025-07-30 15:30:13] [Iter 2657/4650] R4[406/2400], Temp: 0.9310, Energy: -84.999553-0.000858j
[2025-07-30 15:30:28] [Iter 2658/4650] R4[407/2400], Temp: 0.9307, Energy: -84.998741-0.001583j
[2025-07-30 15:30:42] [Iter 2659/4650] R4[408/2400], Temp: 0.9304, Energy: -84.986013+0.000898j
[2025-07-30 15:30:57] [Iter 2660/4650] R4[409/2400], Temp: 0.9300, Energy: -84.950594-0.000602j
[2025-07-30 15:31:12] [Iter 2661/4650] R4[410/2400], Temp: 0.9297, Energy: -84.934918-0.004305j
[2025-07-30 15:31:27] [Iter 2662/4650] R4[411/2400], Temp: 0.9294, Energy: -84.998363-0.006018j
[2025-07-30 15:31:42] [Iter 2663/4650] R4[412/2400], Temp: 0.9290, Energy: -84.901306-0.000569j
[2025-07-30 15:31:57] [Iter 2664/4650] R4[413/2400], Temp: 0.9287, Energy: -84.967839+0.004453j
[2025-07-30 15:32:11] [Iter 2665/4650] R4[414/2400], Temp: 0.9284, Energy: -84.968471+0.001960j
[2025-07-30 15:32:26] [Iter 2666/4650] R4[415/2400], Temp: 0.9280, Energy: -84.993913+0.004159j
[2025-07-30 15:32:41] [Iter 2667/4650] R4[416/2400], Temp: 0.9277, Energy: -84.980460-0.001446j
[2025-07-30 15:32:56] [Iter 2668/4650] R4[417/2400], Temp: 0.9273, Energy: -84.995309+0.007801j
[2025-07-30 15:33:10] [Iter 2669/4650] R4[418/2400], Temp: 0.9270, Energy: -84.976288+0.006314j
[2025-07-30 15:33:25] [Iter 2670/4650] R4[419/2400], Temp: 0.9267, Energy: -84.987447+0.005104j
[2025-07-30 15:33:40] [Iter 2671/4650] R4[420/2400], Temp: 0.9263, Energy: -85.000017-0.001082j
[2025-07-30 15:33:54] [Iter 2672/4650] R4[421/2400], Temp: 0.9260, Energy: -84.988517-0.004346j
[2025-07-30 15:34:09] [Iter 2673/4650] R4[422/2400], Temp: 0.9256, Energy: -85.076998+0.006001j
[2025-07-30 15:34:24] [Iter 2674/4650] R4[423/2400], Temp: 0.9253, Energy: -85.029694+0.005989j
[2025-07-30 15:34:38] [Iter 2675/4650] R4[424/2400], Temp: 0.9249, Energy: -84.973425-0.003413j
[2025-07-30 15:34:53] [Iter 2676/4650] R4[425/2400], Temp: 0.9246, Energy: -84.990338-0.003408j
[2025-07-30 15:35:08] [Iter 2677/4650] R4[426/2400], Temp: 0.9243, Energy: -84.993377+0.008726j
[2025-07-30 15:35:22] [Iter 2678/4650] R4[427/2400], Temp: 0.9239, Energy: -84.978487-0.005516j
[2025-07-30 15:35:37] [Iter 2679/4650] R4[428/2400], Temp: 0.9236, Energy: -85.026666-0.003325j
[2025-07-30 15:35:51] [Iter 2680/4650] R4[429/2400], Temp: 0.9232, Energy: -85.034973+0.012318j
[2025-07-30 15:36:06] [Iter 2681/4650] R4[430/2400], Temp: 0.9229, Energy: -85.033996+0.004922j
[2025-07-30 15:36:21] [Iter 2682/4650] R4[431/2400], Temp: 0.9225, Energy: -85.010061+0.003744j
[2025-07-30 15:36:36] [Iter 2683/4650] R4[432/2400], Temp: 0.9222, Energy: -85.016373+0.003482j
[2025-07-30 15:36:51] [Iter 2684/4650] R4[433/2400], Temp: 0.9218, Energy: -85.005716+0.000711j
[2025-07-30 15:37:06] [Iter 2685/4650] R4[434/2400], Temp: 0.9215, Energy: -85.110511+0.001152j
[2025-07-30 15:37:21] [Iter 2686/4650] R4[435/2400], Temp: 0.9211, Energy: -85.060936+0.006237j
[2025-07-30 15:37:36] [Iter 2687/4650] R4[436/2400], Temp: 0.9208, Energy: -85.173868+0.003906j
[2025-07-30 15:37:51] [Iter 2688/4650] R4[437/2400], Temp: 0.9204, Energy: -85.137872+0.006369j
[2025-07-30 15:38:06] [Iter 2689/4650] R4[438/2400], Temp: 0.9200, Energy: -85.105227+0.005251j
[2025-07-30 15:38:20] [Iter 2690/4650] R4[439/2400], Temp: 0.9197, Energy: -85.037392-0.004453j
[2025-07-30 15:38:35] [Iter 2691/4650] R4[440/2400], Temp: 0.9193, Energy: -84.968869+0.017523j
[2025-07-30 15:38:50] [Iter 2692/4650] R4[441/2400], Temp: 0.9190, Energy: -85.014611+0.002759j
[2025-07-30 15:39:04] [Iter 2693/4650] R4[442/2400], Temp: 0.9186, Energy: -84.959753-0.000824j
[2025-07-30 15:39:19] [Iter 2694/4650] R4[443/2400], Temp: 0.9183, Energy: -84.947752-0.000452j
[2025-07-30 15:39:34] [Iter 2695/4650] R4[444/2400], Temp: 0.9179, Energy: -84.960979+0.001656j
[2025-07-30 15:39:49] [Iter 2696/4650] R4[445/2400], Temp: 0.9175, Energy: -85.013820+0.007749j
[2025-07-30 15:40:04] [Iter 2697/4650] R4[446/2400], Temp: 0.9172, Energy: -85.061386+0.001360j
[2025-07-30 15:40:19] [Iter 2698/4650] R4[447/2400], Temp: 0.9168, Energy: -85.002557-0.006902j
[2025-07-30 15:40:33] [Iter 2699/4650] R4[448/2400], Temp: 0.9165, Energy: -85.059810-0.005827j
[2025-07-30 15:40:48] [Iter 2700/4650] R4[449/2400], Temp: 0.9161, Energy: -85.021542+0.009743j
[2025-07-30 15:41:04] [Iter 2701/4650] R4[450/2400], Temp: 0.9157, Energy: -85.028010-0.005718j
[2025-07-30 15:41:19] [Iter 2702/4650] R4[451/2400], Temp: 0.9154, Energy: -85.031955-0.001483j
[2025-07-30 15:41:34] [Iter 2703/4650] R4[452/2400], Temp: 0.9150, Energy: -84.997853-0.000283j
[2025-07-30 15:41:49] [Iter 2704/4650] R4[453/2400], Temp: 0.9146, Energy: -84.985515+0.002412j
[2025-07-30 15:42:03] [Iter 2705/4650] R4[454/2400], Temp: 0.9143, Energy: -84.935922-0.003891j
[2025-07-30 15:42:18] [Iter 2706/4650] R4[455/2400], Temp: 0.9139, Energy: -84.921213-0.000006j
[2025-07-30 15:42:33] [Iter 2707/4650] R4[456/2400], Temp: 0.9135, Energy: -84.892666+0.000944j
[2025-07-30 15:42:48] [Iter 2708/4650] R4[457/2400], Temp: 0.9132, Energy: -84.991175-0.010225j
[2025-07-30 15:43:02] [Iter 2709/4650] R4[458/2400], Temp: 0.9128, Energy: -84.970752-0.017690j
[2025-07-30 15:43:17] [Iter 2710/4650] R4[459/2400], Temp: 0.9124, Energy: -84.941380-0.007093j
[2025-07-30 15:43:31] [Iter 2711/4650] R4[460/2400], Temp: 0.9121, Energy: -84.938422-0.009657j
[2025-07-30 15:43:46] [Iter 2712/4650] R4[461/2400], Temp: 0.9117, Energy: -84.934147-0.010976j
[2025-07-30 15:44:00] [Iter 2713/4650] R4[462/2400], Temp: 0.9113, Energy: -84.947055-0.006175j
[2025-07-30 15:44:15] [Iter 2714/4650] R4[463/2400], Temp: 0.9109, Energy: -84.937297-0.001512j
[2025-07-30 15:44:30] [Iter 2715/4650] R4[464/2400], Temp: 0.9106, Energy: -84.898313-0.002507j
[2025-07-30 15:44:45] [Iter 2716/4650] R4[465/2400], Temp: 0.9102, Energy: -84.968754-0.007553j
[2025-07-30 15:44:59] [Iter 2717/4650] R4[466/2400], Temp: 0.9098, Energy: -84.992216-0.008482j
[2025-07-30 15:45:14] [Iter 2718/4650] R4[467/2400], Temp: 0.9095, Energy: -84.958353-0.009527j
[2025-07-30 15:45:29] [Iter 2719/4650] R4[468/2400], Temp: 0.9091, Energy: -84.876178-0.004958j
[2025-07-30 15:45:44] [Iter 2720/4650] R4[469/2400], Temp: 0.9087, Energy: -84.870991+0.007881j
[2025-07-30 15:45:59] [Iter 2721/4650] R4[470/2400], Temp: 0.9083, Energy: -84.901697+0.001838j
[2025-07-30 15:46:13] [Iter 2722/4650] R4[471/2400], Temp: 0.9079, Energy: -84.913342-0.001628j
[2025-07-30 15:46:28] [Iter 2723/4650] R4[472/2400], Temp: 0.9076, Energy: -84.919869-0.005385j
[2025-07-30 15:46:43] [Iter 2724/4650] R4[473/2400], Temp: 0.9072, Energy: -84.988994-0.008735j
[2025-07-30 15:46:57] [Iter 2725/4650] R4[474/2400], Temp: 0.9068, Energy: -84.985297+0.000968j
[2025-07-30 15:47:12] [Iter 2726/4650] R4[475/2400], Temp: 0.9064, Energy: -84.957383-0.006628j
[2025-07-30 15:47:27] [Iter 2727/4650] R4[476/2400], Temp: 0.9060, Energy: -84.934808-0.007265j
[2025-07-30 15:47:42] [Iter 2728/4650] R4[477/2400], Temp: 0.9057, Energy: -84.965636-0.012196j
[2025-07-30 15:47:56] [Iter 2729/4650] R4[478/2400], Temp: 0.9053, Energy: -85.028514-0.002510j
[2025-07-30 15:48:11] [Iter 2730/4650] R4[479/2400], Temp: 0.9049, Energy: -85.065685+0.002523j
[2025-07-30 15:48:27] [Iter 2731/4650] R4[480/2400], Temp: 0.9045, Energy: -85.100756+0.004865j
[2025-07-30 15:48:41] [Iter 2732/4650] R4[481/2400], Temp: 0.9041, Energy: -85.028855+0.003690j
[2025-07-30 15:48:56] [Iter 2733/4650] R4[482/2400], Temp: 0.9037, Energy: -85.024470-0.003813j
[2025-07-30 15:49:10] [Iter 2734/4650] R4[483/2400], Temp: 0.9034, Energy: -85.052277+0.008032j
[2025-07-30 15:49:25] [Iter 2735/4650] R4[484/2400], Temp: 0.9030, Energy: -85.065324-0.003019j
[2025-07-30 15:49:39] [Iter 2736/4650] R4[485/2400], Temp: 0.9026, Energy: -85.036287-0.001212j
[2025-07-30 15:49:54] [Iter 2737/4650] R4[486/2400], Temp: 0.9022, Energy: -85.057346-0.000536j
[2025-07-30 15:50:09] [Iter 2738/4650] R4[487/2400], Temp: 0.9018, Energy: -85.047569+0.004152j
[2025-07-30 15:50:24] [Iter 2739/4650] R4[488/2400], Temp: 0.9014, Energy: -85.007013-0.011962j
[2025-07-30 15:50:39] [Iter 2740/4650] R4[489/2400], Temp: 0.9010, Energy: -84.983899-0.018577j
[2025-07-30 15:50:53] [Iter 2741/4650] R4[490/2400], Temp: 0.9006, Energy: -84.980390+0.002651j
[2025-07-30 15:51:08] [Iter 2742/4650] R4[491/2400], Temp: 0.9002, Energy: -84.944938+0.002762j
[2025-07-30 15:51:23] [Iter 2743/4650] R4[492/2400], Temp: 0.8998, Energy: -84.994268-0.005555j
[2025-07-30 15:51:38] [Iter 2744/4650] R4[493/2400], Temp: 0.8994, Energy: -84.965676-0.002997j
[2025-07-30 15:51:53] [Iter 2745/4650] R4[494/2400], Temp: 0.8991, Energy: -84.943113-0.001565j
[2025-07-30 15:52:08] [Iter 2746/4650] R4[495/2400], Temp: 0.8987, Energy: -84.939894-0.002717j
[2025-07-30 15:52:23] [Iter 2747/4650] R4[496/2400], Temp: 0.8983, Energy: -84.985345-0.002681j
[2025-07-30 15:52:38] [Iter 2748/4650] R4[497/2400], Temp: 0.8979, Energy: -84.913585+0.000442j
[2025-07-30 15:52:53] [Iter 2749/4650] R4[498/2400], Temp: 0.8975, Energy: -84.923999-0.015337j
[2025-07-30 15:53:08] [Iter 2750/4650] R4[499/2400], Temp: 0.8971, Energy: -84.923384-0.006656j
[2025-07-30 15:53:22] [Iter 2751/4650] R4[500/2400], Temp: 0.8967, Energy: -84.898817-0.002813j
[2025-07-30 15:53:37] [Iter 2752/4650] R4[501/2400], Temp: 0.8963, Energy: -84.986068-0.009723j
[2025-07-30 15:53:52] [Iter 2753/4650] R4[502/2400], Temp: 0.8959, Energy: -84.995117-0.010410j
[2025-07-30 15:54:07] [Iter 2754/4650] R4[503/2400], Temp: 0.8955, Energy: -84.989511-0.004543j
[2025-07-30 15:54:22] [Iter 2755/4650] R4[504/2400], Temp: 0.8951, Energy: -85.051290-0.005987j
[2025-07-30 15:54:36] [Iter 2756/4650] R4[505/2400], Temp: 0.8947, Energy: -85.039600-0.013430j
[2025-07-30 15:54:50] [Iter 2757/4650] R4[506/2400], Temp: 0.8943, Energy: -85.026592-0.012090j
[2025-07-30 15:55:05] [Iter 2758/4650] R4[507/2400], Temp: 0.8939, Energy: -84.921067-0.019580j
[2025-07-30 15:55:20] [Iter 2759/4650] R4[508/2400], Temp: 0.8935, Energy: -84.888854-0.007801j
[2025-07-30 15:55:34] [Iter 2760/4650] R4[509/2400], Temp: 0.8931, Energy: -84.936678+0.001915j
[2025-07-30 15:55:49] [Iter 2761/4650] R4[510/2400], Temp: 0.8927, Energy: -84.932359+0.001011j
[2025-07-30 15:56:04] [Iter 2762/4650] R4[511/2400], Temp: 0.8923, Energy: -84.921106-0.006180j
[2025-07-30 15:56:19] [Iter 2763/4650] R4[512/2400], Temp: 0.8918, Energy: -84.894587-0.004690j
[2025-07-30 15:56:34] [Iter 2764/4650] R4[513/2400], Temp: 0.8914, Energy: -84.922380-0.007102j
[2025-07-30 15:56:48] [Iter 2765/4650] R4[514/2400], Temp: 0.8910, Energy: -85.024424-0.015444j
[2025-07-30 15:57:03] [Iter 2766/4650] R4[515/2400], Temp: 0.8906, Energy: -85.001766-0.004193j
[2025-07-30 15:57:18] [Iter 2767/4650] R4[516/2400], Temp: 0.8902, Energy: -84.998241-0.002477j
[2025-07-30 15:57:33] [Iter 2768/4650] R4[517/2400], Temp: 0.8898, Energy: -85.007550-0.013709j
[2025-07-30 15:57:47] [Iter 2769/4650] R4[518/2400], Temp: 0.8894, Energy: -84.915710-0.009678j
[2025-07-30 15:58:02] [Iter 2770/4650] R4[519/2400], Temp: 0.8890, Energy: -85.079914-0.007219j
[2025-07-30 15:58:17] [Iter 2771/4650] R4[520/2400], Temp: 0.8886, Energy: -84.987155-0.016142j
[2025-07-30 15:58:32] [Iter 2772/4650] R4[521/2400], Temp: 0.8882, Energy: -85.034600+0.002463j
[2025-07-30 15:58:46] [Iter 2773/4650] R4[522/2400], Temp: 0.8877, Energy: -85.014474-0.003232j
[2025-07-30 15:59:01] [Iter 2774/4650] R4[523/2400], Temp: 0.8873, Energy: -85.035668-0.006741j
[2025-07-30 15:59:16] [Iter 2775/4650] R4[524/2400], Temp: 0.8869, Energy: -85.071454+0.003549j
[2025-07-30 15:59:31] [Iter 2776/4650] R4[525/2400], Temp: 0.8865, Energy: -85.040631-0.004865j
[2025-07-30 15:59:45] [Iter 2777/4650] R4[526/2400], Temp: 0.8861, Energy: -84.987304+0.002425j
[2025-07-30 16:00:00] [Iter 2778/4650] R4[527/2400], Temp: 0.8857, Energy: -84.926427-0.009298j
[2025-07-30 16:00:14] [Iter 2779/4650] R4[528/2400], Temp: 0.8853, Energy: -84.920589-0.001950j
[2025-07-30 16:00:29] [Iter 2780/4650] R4[529/2400], Temp: 0.8848, Energy: -84.902481-0.001405j
[2025-07-30 16:00:44] [Iter 2781/4650] R4[530/2400], Temp: 0.8844, Energy: -84.968736-0.009389j
[2025-07-30 16:00:58] [Iter 2782/4650] R4[531/2400], Temp: 0.8840, Energy: -84.973061-0.003222j
[2025-07-30 16:01:13] [Iter 2783/4650] R4[532/2400], Temp: 0.8836, Energy: -84.892657+0.000690j
[2025-07-30 16:01:27] [Iter 2784/4650] R4[533/2400], Temp: 0.8832, Energy: -84.838615-0.022195j
[2025-07-30 16:01:42] [Iter 2785/4650] R4[534/2400], Temp: 0.8827, Energy: -84.863104-0.002546j
[2025-07-30 16:01:57] [Iter 2786/4650] R4[535/2400], Temp: 0.8823, Energy: -84.852026+0.003008j
[2025-07-30 16:02:12] [Iter 2787/4650] R4[536/2400], Temp: 0.8819, Energy: -84.905749+0.004829j
[2025-07-30 16:02:26] [Iter 2788/4650] R4[537/2400], Temp: 0.8815, Energy: -84.953263+0.001760j
[2025-07-30 16:02:40] [Iter 2789/4650] R4[538/2400], Temp: 0.8811, Energy: -85.002081+0.004129j
[2025-07-30 16:02:55] [Iter 2790/4650] R4[539/2400], Temp: 0.8806, Energy: -85.016260-0.010798j
[2025-07-30 16:03:09] [Iter 2791/4650] R4[540/2400], Temp: 0.8802, Energy: -85.005793-0.006078j
[2025-07-30 16:03:24] [Iter 2792/4650] R4[541/2400], Temp: 0.8798, Energy: -85.026208+0.008134j
[2025-07-30 16:03:39] [Iter 2793/4650] R4[542/2400], Temp: 0.8794, Energy: -85.015294-0.002417j
[2025-07-30 16:03:53] [Iter 2794/4650] R4[543/2400], Temp: 0.8789, Energy: -85.008568-0.005227j
[2025-07-30 16:04:08] [Iter 2795/4650] R4[544/2400], Temp: 0.8785, Energy: -84.999829-0.004169j
[2025-07-30 16:04:23] [Iter 2796/4650] R4[545/2400], Temp: 0.8781, Energy: -84.999706+0.000280j
[2025-07-30 16:04:37] [Iter 2797/4650] R4[546/2400], Temp: 0.8776, Energy: -85.040947+0.011348j
[2025-07-30 16:04:51] [Iter 2798/4650] R4[547/2400], Temp: 0.8772, Energy: -85.086480+0.004836j
[2025-07-30 16:05:06] [Iter 2799/4650] R4[548/2400], Temp: 0.8768, Energy: -84.997664-0.004075j
[2025-07-30 16:05:21] [Iter 2800/4650] R4[549/2400], Temp: 0.8764, Energy: -85.061885-0.007683j
[2025-07-30 16:05:35] [Iter 2801/4650] R4[550/2400], Temp: 0.8759, Energy: -84.987946-0.009225j
[2025-07-30 16:05:50] [Iter 2802/4650] R4[551/2400], Temp: 0.8755, Energy: -85.008852-0.014105j
[2025-07-30 16:06:05] [Iter 2803/4650] R4[552/2400], Temp: 0.8751, Energy: -85.017645-0.006708j
[2025-07-30 16:06:19] [Iter 2804/4650] R4[553/2400], Temp: 0.8746, Energy: -85.029182-0.006075j
[2025-07-30 16:06:34] [Iter 2805/4650] R4[554/2400], Temp: 0.8742, Energy: -85.072662-0.006148j
[2025-07-30 16:06:49] [Iter 2806/4650] R4[555/2400], Temp: 0.8738, Energy: -85.052127+0.000553j
[2025-07-30 16:07:04] [Iter 2807/4650] R4[556/2400], Temp: 0.8733, Energy: -85.027449+0.001675j
[2025-07-30 16:07:18] [Iter 2808/4650] R4[557/2400], Temp: 0.8729, Energy: -85.045551-0.007816j
[2025-07-30 16:07:33] [Iter 2809/4650] R4[558/2400], Temp: 0.8724, Energy: -85.046591-0.000267j
[2025-07-30 16:07:48] [Iter 2810/4650] R4[559/2400], Temp: 0.8720, Energy: -85.047300+0.005744j
[2025-07-30 16:08:03] [Iter 2811/4650] R4[560/2400], Temp: 0.8716, Energy: -85.035360-0.001397j
[2025-07-30 16:08:17] [Iter 2812/4650] R4[561/2400], Temp: 0.8711, Energy: -85.005555+0.009133j
[2025-07-30 16:08:32] [Iter 2813/4650] R4[562/2400], Temp: 0.8707, Energy: -85.061449+0.007047j
[2025-07-30 16:08:46] [Iter 2814/4650] R4[563/2400], Temp: 0.8703, Energy: -85.020999+0.008538j
[2025-07-30 16:09:00] [Iter 2815/4650] R4[564/2400], Temp: 0.8698, Energy: -85.031468+0.002545j
[2025-07-30 16:09:16] [Iter 2816/4650] R4[565/2400], Temp: 0.8694, Energy: -85.038608+0.003320j
[2025-07-30 16:09:30] [Iter 2817/4650] R4[566/2400], Temp: 0.8689, Energy: -85.093290+0.007370j
[2025-07-30 16:09:45] [Iter 2818/4650] R4[567/2400], Temp: 0.8685, Energy: -85.131551+0.001317j
[2025-07-30 16:09:59] [Iter 2819/4650] R4[568/2400], Temp: 0.8680, Energy: -85.013514+0.005879j
[2025-07-30 16:10:14] [Iter 2820/4650] R4[569/2400], Temp: 0.8676, Energy: -85.015599+0.000788j
[2025-07-30 16:10:29] [Iter 2821/4650] R4[570/2400], Temp: 0.8672, Energy: -85.006948-0.002030j
[2025-07-30 16:10:43] [Iter 2822/4650] R4[571/2400], Temp: 0.8667, Energy: -85.020401+0.001949j
[2025-07-30 16:10:58] [Iter 2823/4650] R4[572/2400], Temp: 0.8663, Energy: -84.970590+0.003843j
[2025-07-30 16:11:12] [Iter 2824/4650] R4[573/2400], Temp: 0.8658, Energy: -85.022515-0.000808j
[2025-07-30 16:11:27] [Iter 2825/4650] R4[574/2400], Temp: 0.8654, Energy: -85.026580+0.010396j
[2025-07-30 16:11:41] [Iter 2826/4650] R4[575/2400], Temp: 0.8649, Energy: -85.040659+0.008179j
[2025-07-30 16:11:55] [Iter 2827/4650] R4[576/2400], Temp: 0.8645, Energy: -85.056172-0.002476j
[2025-07-30 16:12:10] [Iter 2828/4650] R4[577/2400], Temp: 0.8640, Energy: -85.011267-0.008947j
[2025-07-30 16:12:24] [Iter 2829/4650] R4[578/2400], Temp: 0.8636, Energy: -84.987661-0.004267j
[2025-07-30 16:12:39] [Iter 2830/4650] R4[579/2400], Temp: 0.8631, Energy: -84.973235+0.007039j
[2025-07-30 16:12:54] [Iter 2831/4650] R4[580/2400], Temp: 0.8627, Energy: -84.952022+0.000224j
[2025-07-30 16:13:09] [Iter 2832/4650] R4[581/2400], Temp: 0.8622, Energy: -84.964851+0.010612j
[2025-07-30 16:13:23] [Iter 2833/4650] R4[582/2400], Temp: 0.8618, Energy: -84.923795+0.006189j
[2025-07-30 16:13:38] [Iter 2834/4650] R4[583/2400], Temp: 0.8613, Energy: -84.981441+0.016483j
[2025-07-30 16:13:53] [Iter 2835/4650] R4[584/2400], Temp: 0.8609, Energy: -84.991447+0.005501j
[2025-07-30 16:14:08] [Iter 2836/4650] R4[585/2400], Temp: 0.8604, Energy: -85.000027+0.038655j
[2025-07-30 16:14:22] [Iter 2837/4650] R4[586/2400], Temp: 0.8600, Energy: -85.011243+0.011860j
[2025-07-30 16:14:37] [Iter 2838/4650] R4[587/2400], Temp: 0.8595, Energy: -85.041191+0.020653j
[2025-07-30 16:14:52] [Iter 2839/4650] R4[588/2400], Temp: 0.8591, Energy: -85.024485+0.010140j
[2025-07-30 16:15:06] [Iter 2840/4650] R4[589/2400], Temp: 0.8586, Energy: -85.056402+0.007182j
[2025-07-30 16:15:21] [Iter 2841/4650] R4[590/2400], Temp: 0.8582, Energy: -85.025939+0.006422j
[2025-07-30 16:15:35] [Iter 2842/4650] R4[591/2400], Temp: 0.8577, Energy: -85.047323-0.006998j
[2025-07-30 16:15:50] [Iter 2843/4650] R4[592/2400], Temp: 0.8572, Energy: -85.021209-0.006810j
[2025-07-30 16:16:05] [Iter 2844/4650] R4[593/2400], Temp: 0.8568, Energy: -84.953296-0.015901j
[2025-07-30 16:16:19] [Iter 2845/4650] R4[594/2400], Temp: 0.8563, Energy: -84.983179-0.009205j
[2025-07-30 16:16:33] [Iter 2846/4650] R4[595/2400], Temp: 0.8559, Energy: -84.978520-0.000731j
[2025-07-30 16:16:47] [Iter 2847/4650] R4[596/2400], Temp: 0.8554, Energy: -84.952796-0.005178j
[2025-07-30 16:17:02] [Iter 2848/4650] R4[597/2400], Temp: 0.8549, Energy: -84.955239+0.009161j
[2025-07-30 16:17:16] [Iter 2849/4650] R4[598/2400], Temp: 0.8545, Energy: -84.822318-0.033468j
[2025-07-30 16:17:31] [Iter 2850/4650] R4[599/2400], Temp: 0.8540, Energy: -84.889793-0.035468j
[2025-07-30 16:17:46] [Iter 2851/4650] R4[600/2400], Temp: 0.8536, Energy: -84.905844-0.029639j
[2025-07-30 16:18:01] [Iter 2852/4650] R4[601/2400], Temp: 0.8531, Energy: -85.043891+0.001582j
[2025-07-30 16:18:15] [Iter 2853/4650] R4[602/2400], Temp: 0.8526, Energy: -85.023149-0.007764j
[2025-07-30 16:18:30] [Iter 2854/4650] R4[603/2400], Temp: 0.8522, Energy: -84.989216-0.022770j
[2025-07-30 16:18:45] [Iter 2855/4650] R4[604/2400], Temp: 0.8517, Energy: -84.893102-0.019297j
[2025-07-30 16:19:00] [Iter 2856/4650] R4[605/2400], Temp: 0.8512, Energy: -84.937336-0.015911j
[2025-07-30 16:19:15] [Iter 2857/4650] R4[606/2400], Temp: 0.8508, Energy: -84.940515-0.006140j
[2025-07-30 16:19:29] [Iter 2858/4650] R4[607/2400], Temp: 0.8503, Energy: -85.000919+0.012002j
[2025-07-30 16:19:43] [Iter 2859/4650] R4[608/2400], Temp: 0.8498, Energy: -84.980131+0.005337j
[2025-07-30 16:19:58] [Iter 2860/4650] R4[609/2400], Temp: 0.8494, Energy: -85.036182+0.013116j
[2025-07-30 16:20:13] [Iter 2861/4650] R4[610/2400], Temp: 0.8489, Energy: -84.993843+0.008361j
[2025-07-30 16:20:28] [Iter 2862/4650] R4[611/2400], Temp: 0.8484, Energy: -84.988155+0.006187j
[2025-07-30 16:20:43] [Iter 2863/4650] R4[612/2400], Temp: 0.8480, Energy: -84.987543+0.002032j
[2025-07-30 16:20:57] [Iter 2864/4650] R4[613/2400], Temp: 0.8475, Energy: -84.991687+0.009293j
[2025-07-30 16:21:12] [Iter 2865/4650] R4[614/2400], Temp: 0.8470, Energy: -85.028088+0.003254j
[2025-07-30 16:21:27] [Iter 2866/4650] R4[615/2400], Temp: 0.8465, Energy: -84.951942+0.000263j
[2025-07-30 16:21:42] [Iter 2867/4650] R4[616/2400], Temp: 0.8461, Energy: -84.931526+0.010042j
[2025-07-30 16:21:57] [Iter 2868/4650] R4[617/2400], Temp: 0.8456, Energy: -84.957571+0.007554j
[2025-07-30 16:22:11] [Iter 2869/4650] R4[618/2400], Temp: 0.8451, Energy: -84.974922+0.011105j
[2025-07-30 16:22:25] [Iter 2870/4650] R4[619/2400], Temp: 0.8447, Energy: -84.919780+0.004951j
[2025-07-30 16:22:40] [Iter 2871/4650] R4[620/2400], Temp: 0.8442, Energy: -84.952317-0.002872j
[2025-07-30 16:22:55] [Iter 2872/4650] R4[621/2400], Temp: 0.8437, Energy: -84.890481+0.004134j
[2025-07-30 16:23:09] [Iter 2873/4650] R4[622/2400], Temp: 0.8432, Energy: -84.943438+0.006450j
[2025-07-30 16:23:24] [Iter 2874/4650] R4[623/2400], Temp: 0.8428, Energy: -85.056013-0.000155j
[2025-07-30 16:23:39] [Iter 2875/4650] R4[624/2400], Temp: 0.8423, Energy: -84.943346+0.000348j
[2025-07-30 16:23:53] [Iter 2876/4650] R4[625/2400], Temp: 0.8418, Energy: -84.994986-0.000147j
[2025-07-30 16:24:07] [Iter 2877/4650] R4[626/2400], Temp: 0.8413, Energy: -85.030802-0.001982j
[2025-07-30 16:24:22] [Iter 2878/4650] R4[627/2400], Temp: 0.8408, Energy: -85.037983+0.010469j
[2025-07-30 16:24:37] [Iter 2879/4650] R4[628/2400], Temp: 0.8404, Energy: -84.947530-0.005580j
[2025-07-30 16:24:51] [Iter 2880/4650] R4[629/2400], Temp: 0.8399, Energy: -85.080436+0.018706j
[2025-07-30 16:25:06] [Iter 2881/4650] R4[630/2400], Temp: 0.8394, Energy: -85.059522+0.009616j
[2025-07-30 16:25:21] [Iter 2882/4650] R4[631/2400], Temp: 0.8389, Energy: -85.008563+0.008030j
[2025-07-30 16:25:35] [Iter 2883/4650] R4[632/2400], Temp: 0.8384, Energy: -84.954876+0.028207j
[2025-07-30 16:25:50] [Iter 2884/4650] R4[633/2400], Temp: 0.8380, Energy: -84.973991-0.000633j
[2025-07-30 16:26:05] [Iter 2885/4650] R4[634/2400], Temp: 0.8375, Energy: -84.996558-0.000038j
[2025-07-30 16:26:19] [Iter 2886/4650] R4[635/2400], Temp: 0.8370, Energy: -85.029908-0.006236j
[2025-07-30 16:26:34] [Iter 2887/4650] R4[636/2400], Temp: 0.8365, Energy: -85.042671-0.004251j
[2025-07-30 16:26:48] [Iter 2888/4650] R4[637/2400], Temp: 0.8360, Energy: -85.049302-0.001711j
[2025-07-30 16:27:03] [Iter 2889/4650] R4[638/2400], Temp: 0.8355, Energy: -85.031470+0.002736j
[2025-07-30 16:27:18] [Iter 2890/4650] R4[639/2400], Temp: 0.8351, Energy: -84.989466-0.002369j
[2025-07-30 16:27:32] [Iter 2891/4650] R4[640/2400], Temp: 0.8346, Energy: -84.957287-0.007918j
[2025-07-30 16:27:47] [Iter 2892/4650] R4[641/2400], Temp: 0.8341, Energy: -85.012997+0.001816j
[2025-07-30 16:28:01] [Iter 2893/4650] R4[642/2400], Temp: 0.8336, Energy: -85.035271+0.005101j
[2025-07-30 16:28:15] [Iter 2894/4650] R4[643/2400], Temp: 0.8331, Energy: -85.084951+0.011827j
[2025-07-30 16:28:29] [Iter 2895/4650] R4[644/2400], Temp: 0.8326, Energy: -85.055363+0.003626j
[2025-07-30 16:28:44] [Iter 2896/4650] R4[645/2400], Temp: 0.8321, Energy: -84.975302-0.015815j
[2025-07-30 16:28:59] [Iter 2897/4650] R4[646/2400], Temp: 0.8316, Energy: -84.938365+0.004469j
[2025-07-30 16:29:14] [Iter 2898/4650] R4[647/2400], Temp: 0.8311, Energy: -85.038526+0.002104j
[2025-07-30 16:29:28] [Iter 2899/4650] R4[648/2400], Temp: 0.8307, Energy: -85.049480+0.002079j
[2025-07-30 16:29:43] [Iter 2900/4650] R4[649/2400], Temp: 0.8302, Energy: -85.002047+0.008853j
[2025-07-30 16:29:58] [Iter 2901/4650] R4[650/2400], Temp: 0.8297, Energy: -85.004222+0.003786j
[2025-07-30 16:30:12] [Iter 2902/4650] R4[651/2400], Temp: 0.8292, Energy: -84.954641+0.010820j
[2025-07-30 16:30:26] [Iter 2903/4650] R4[652/2400], Temp: 0.8287, Energy: -85.050458-0.002069j
[2025-07-30 16:30:41] [Iter 2904/4650] R4[653/2400], Temp: 0.8282, Energy: -85.045634+0.008328j
[2025-07-30 16:30:56] [Iter 2905/4650] R4[654/2400], Temp: 0.8277, Energy: -85.063053+0.001441j
[2025-07-30 16:31:11] [Iter 2906/4650] R4[655/2400], Temp: 0.8272, Energy: -85.068866+0.002359j
[2025-07-30 16:31:25] [Iter 2907/4650] R4[656/2400], Temp: 0.8267, Energy: -85.043078+0.003609j
[2025-07-30 16:31:40] [Iter 2908/4650] R4[657/2400], Temp: 0.8262, Energy: -85.136504+0.002150j
[2025-07-30 16:31:55] [Iter 2909/4650] R4[658/2400], Temp: 0.8257, Energy: -85.068190+0.004147j
[2025-07-30 16:32:09] [Iter 2910/4650] R4[659/2400], Temp: 0.8252, Energy: -85.061955-0.005308j
[2025-07-30 16:32:24] [Iter 2911/4650] R4[660/2400], Temp: 0.8247, Energy: -85.075174+0.003192j
[2025-07-30 16:32:38] [Iter 2912/4650] R4[661/2400], Temp: 0.8242, Energy: -85.014719-0.001362j
[2025-07-30 16:32:52] [Iter 2913/4650] R4[662/2400], Temp: 0.8237, Energy: -84.946577+0.003312j
[2025-07-30 16:33:07] [Iter 2914/4650] R4[663/2400], Temp: 0.8232, Energy: -84.975642+0.000655j
[2025-07-30 16:33:22] [Iter 2915/4650] R4[664/2400], Temp: 0.8227, Energy: -84.978389+0.000051j
[2025-07-30 16:33:37] [Iter 2916/4650] R4[665/2400], Temp: 0.8222, Energy: -85.015698+0.000932j
[2025-07-30 16:33:52] [Iter 2917/4650] R4[666/2400], Temp: 0.8217, Energy: -85.055983+0.001164j
[2025-07-30 16:34:07] [Iter 2918/4650] R4[667/2400], Temp: 0.8212, Energy: -85.041229+0.000501j
[2025-07-30 16:34:21] [Iter 2919/4650] R4[668/2400], Temp: 0.8207, Energy: -85.075174+0.004106j
[2025-07-30 16:34:36] [Iter 2920/4650] R4[669/2400], Temp: 0.8202, Energy: -85.040739-0.001765j
[2025-07-30 16:34:51] [Iter 2921/4650] R4[670/2400], Temp: 0.8197, Energy: -85.051535+0.014534j
[2025-07-30 16:35:05] [Iter 2922/4650] R4[671/2400], Temp: 0.8192, Energy: -85.020724+0.003617j
[2025-07-30 16:35:20] [Iter 2923/4650] R4[672/2400], Temp: 0.8187, Energy: -84.974685+0.007214j
[2025-07-30 16:35:35] [Iter 2924/4650] R4[673/2400], Temp: 0.8182, Energy: -85.043771+0.007926j
[2025-07-30 16:35:50] [Iter 2925/4650] R4[674/2400], Temp: 0.8177, Energy: -85.006955+0.000244j
[2025-07-30 16:36:04] [Iter 2926/4650] R4[675/2400], Temp: 0.8172, Energy: -85.051322-0.000722j
[2025-07-30 16:36:19] [Iter 2927/4650] R4[676/2400], Temp: 0.8167, Energy: -85.020978-0.000138j
[2025-07-30 16:36:34] [Iter 2928/4650] R4[677/2400], Temp: 0.8162, Energy: -85.055355-0.004639j
[2025-07-30 16:36:49] [Iter 2929/4650] R4[678/2400], Temp: 0.8157, Energy: -85.022616-0.007574j
[2025-07-30 16:37:04] [Iter 2930/4650] R4[679/2400], Temp: 0.8152, Energy: -84.990176-0.005456j
[2025-07-30 16:37:18] [Iter 2931/4650] R4[680/2400], Temp: 0.8147, Energy: -84.963590+0.007026j
[2025-07-30 16:37:33] [Iter 2932/4650] R4[681/2400], Temp: 0.8142, Energy: -84.968240-0.001119j
[2025-07-30 16:37:48] [Iter 2933/4650] R4[682/2400], Temp: 0.8136, Energy: -85.002480+0.001531j
[2025-07-30 16:38:03] [Iter 2934/4650] R4[683/2400], Temp: 0.8131, Energy: -84.983524+0.009966j
[2025-07-30 16:38:17] [Iter 2935/4650] R4[684/2400], Temp: 0.8126, Energy: -84.944066+0.005412j
[2025-07-30 16:38:32] [Iter 2936/4650] R4[685/2400], Temp: 0.8121, Energy: -84.939865-0.001223j
[2025-07-30 16:38:46] [Iter 2937/4650] R4[686/2400], Temp: 0.8116, Energy: -84.926855-0.002004j
[2025-07-30 16:39:01] [Iter 2938/4650] R4[687/2400], Temp: 0.8111, Energy: -84.990245-0.003893j
[2025-07-30 16:39:16] [Iter 2939/4650] R4[688/2400], Temp: 0.8106, Energy: -84.965329+0.000091j
[2025-07-30 16:39:31] [Iter 2940/4650] R4[689/2400], Temp: 0.8101, Energy: -84.988835+0.015205j
[2025-07-30 16:39:46] [Iter 2941/4650] R4[690/2400], Temp: 0.8095, Energy: -84.986610+0.001449j
[2025-07-30 16:40:01] [Iter 2942/4650] R4[691/2400], Temp: 0.8090, Energy: -84.934394+0.004413j
[2025-07-30 16:40:15] [Iter 2943/4650] R4[692/2400], Temp: 0.8085, Energy: -84.992362+0.001039j
[2025-07-30 16:40:30] [Iter 2944/4650] R4[693/2400], Temp: 0.8080, Energy: -85.004067-0.004384j
[2025-07-30 16:40:45] [Iter 2945/4650] R4[694/2400], Temp: 0.8075, Energy: -85.107070-0.002040j
[2025-07-30 16:41:00] [Iter 2946/4650] R4[695/2400], Temp: 0.8070, Energy: -85.108666+0.015307j
[2025-07-30 16:41:15] [Iter 2947/4650] R4[696/2400], Temp: 0.8065, Energy: -85.053258+0.006592j
[2025-07-30 16:41:29] [Iter 2948/4650] R4[697/2400], Temp: 0.8059, Energy: -85.092566+0.004624j
[2025-07-30 16:41:44] [Iter 2949/4650] R4[698/2400], Temp: 0.8054, Energy: -85.069820+0.000767j
[2025-07-30 16:41:59] [Iter 2950/4650] R4[699/2400], Temp: 0.8049, Energy: -85.102057+0.007654j
[2025-07-30 16:42:14] [Iter 2951/4650] R4[700/2400], Temp: 0.8044, Energy: -85.145557+0.001330j
[2025-07-30 16:42:28] [Iter 2952/4650] R4[701/2400], Temp: 0.8039, Energy: -85.101554-0.008964j
[2025-07-30 16:42:42] [Iter 2953/4650] R4[702/2400], Temp: 0.8033, Energy: -85.032702+0.004751j
[2025-07-30 16:42:57] [Iter 2954/4650] R4[703/2400], Temp: 0.8028, Energy: -85.054039+0.010106j
[2025-07-30 16:43:12] [Iter 2955/4650] R4[704/2400], Temp: 0.8023, Energy: -85.112415-0.001675j
[2025-07-30 16:43:27] [Iter 2956/4650] R4[705/2400], Temp: 0.8018, Energy: -85.181197-0.001301j
[2025-07-30 16:43:42] [Iter 2957/4650] R4[706/2400], Temp: 0.8013, Energy: -85.192604+0.001837j
[2025-07-30 16:43:57] [Iter 2958/4650] R4[707/2400], Temp: 0.8007, Energy: -85.122977-0.007181j
[2025-07-30 16:44:12] [Iter 2959/4650] R4[708/2400], Temp: 0.8002, Energy: -85.098115+0.000189j
[2025-07-30 16:44:26] [Iter 2960/4650] R4[709/2400], Temp: 0.7997, Energy: -85.016817+0.005815j
[2025-07-30 16:44:40] [Iter 2961/4650] R4[710/2400], Temp: 0.7992, Energy: -84.960711+0.003768j
[2025-07-30 16:44:55] [Iter 2962/4650] R4[711/2400], Temp: 0.7986, Energy: -85.074858+0.000550j
[2025-07-30 16:45:10] [Iter 2963/4650] R4[712/2400], Temp: 0.7981, Energy: -85.042136-0.000038j
[2025-07-30 16:45:25] [Iter 2964/4650] R4[713/2400], Temp: 0.7976, Energy: -85.062516-0.004050j
[2025-07-30 16:45:40] [Iter 2965/4650] R4[714/2400], Temp: 0.7971, Energy: -85.111868-0.001296j
[2025-07-30 16:45:55] [Iter 2966/4650] R4[715/2400], Temp: 0.7965, Energy: -85.104430+0.002394j
[2025-07-30 16:46:10] [Iter 2967/4650] R4[716/2400], Temp: 0.7960, Energy: -85.137973+0.005358j
[2025-07-30 16:46:25] [Iter 2968/4650] R4[717/2400], Temp: 0.7955, Energy: -85.065401+0.000149j
[2025-07-30 16:46:40] [Iter 2969/4650] R4[718/2400], Temp: 0.7950, Energy: -85.037783+0.002906j
[2025-07-30 16:46:55] [Iter 2970/4650] R4[719/2400], Temp: 0.7944, Energy: -85.006689-0.000998j
[2025-07-30 16:47:10] [Iter 2971/4650] R4[720/2400], Temp: 0.7939, Energy: -84.990729+0.002521j
[2025-07-30 16:47:24] [Iter 2972/4650] R4[721/2400], Temp: 0.7934, Energy: -84.983891-0.003236j
[2025-07-30 16:47:39] [Iter 2973/4650] R4[722/2400], Temp: 0.7928, Energy: -85.032553-0.006381j
[2025-07-30 16:47:54] [Iter 2974/4650] R4[723/2400], Temp: 0.7923, Energy: -85.001497+0.002251j
[2025-07-30 16:48:09] [Iter 2975/4650] R4[724/2400], Temp: 0.7918, Energy: -85.009201+0.006664j
[2025-07-30 16:48:23] [Iter 2976/4650] R4[725/2400], Temp: 0.7912, Energy: -84.982498+0.009671j
[2025-07-30 16:48:38] [Iter 2977/4650] R4[726/2400], Temp: 0.7907, Energy: -84.982661-0.003596j
[2025-07-30 16:48:53] [Iter 2978/4650] R4[727/2400], Temp: 0.7902, Energy: -84.994484+0.000868j
[2025-07-30 16:49:08] [Iter 2979/4650] R4[728/2400], Temp: 0.7896, Energy: -84.984646+0.010569j
[2025-07-30 16:49:22] [Iter 2980/4650] R4[729/2400], Temp: 0.7891, Energy: -84.996103+0.002263j
[2025-07-30 16:49:38] [Iter 2981/4650] R4[730/2400], Temp: 0.7886, Energy: -84.978377+0.001998j
[2025-07-30 16:49:53] [Iter 2982/4650] R4[731/2400], Temp: 0.7880, Energy: -84.945107-0.007232j
[2025-07-30 16:50:08] [Iter 2983/4650] R4[732/2400], Temp: 0.7875, Energy: -85.002522+0.000158j
[2025-07-30 16:50:22] [Iter 2984/4650] R4[733/2400], Temp: 0.7870, Energy: -85.013768+0.012130j
[2025-07-30 16:50:36] [Iter 2985/4650] R4[734/2400], Temp: 0.7864, Energy: -85.048006+0.002402j
[2025-07-30 16:50:51] [Iter 2986/4650] R4[735/2400], Temp: 0.7859, Energy: -84.968388-0.006098j
[2025-07-30 16:51:05] [Iter 2987/4650] R4[736/2400], Temp: 0.7854, Energy: -84.969114-0.004693j
[2025-07-30 16:51:20] [Iter 2988/4650] R4[737/2400], Temp: 0.7848, Energy: -85.034161+0.025719j
[2025-07-30 16:51:35] [Iter 2989/4650] R4[738/2400], Temp: 0.7843, Energy: -85.132597+0.010775j
[2025-07-30 16:51:50] [Iter 2990/4650] R4[739/2400], Temp: 0.7837, Energy: -85.065466+0.000673j
[2025-07-30 16:52:05] [Iter 2991/4650] R4[740/2400], Temp: 0.7832, Energy: -85.055439+0.003952j
[2025-07-30 16:52:19] [Iter 2992/4650] R4[741/2400], Temp: 0.7827, Energy: -85.030847+0.001460j
[2025-07-30 16:52:34] [Iter 2993/4650] R4[742/2400], Temp: 0.7821, Energy: -85.095578+0.004362j
[2025-07-30 16:52:49] [Iter 2994/4650] R4[743/2400], Temp: 0.7816, Energy: -85.073169+0.002137j
[2025-07-30 16:53:04] [Iter 2995/4650] R4[744/2400], Temp: 0.7810, Energy: -85.060941+0.000044j
[2025-07-30 16:53:19] [Iter 2996/4650] R4[745/2400], Temp: 0.7805, Energy: -85.037183-0.003641j
[2025-07-30 16:53:34] [Iter 2997/4650] R4[746/2400], Temp: 0.7800, Energy: -85.097083-0.000697j
[2025-07-30 16:53:48] [Iter 2998/4650] R4[747/2400], Temp: 0.7794, Energy: -85.065802-0.008181j
[2025-07-30 16:54:03] [Iter 2999/4650] R4[748/2400], Temp: 0.7789, Energy: -85.023288+0.011501j
[2025-07-30 16:54:18] [Iter 3000/4650] R4[749/2400], Temp: 0.7783, Energy: -84.974987+0.000008j
[2025-07-30 16:54:18] ✓ Checkpoint saved: checkpoint_iter_003000.pkl
[2025-07-30 16:54:32] [Iter 3001/4650] R4[750/2400], Temp: 0.7778, Energy: -84.961933+0.004013j
[2025-07-30 16:54:47] [Iter 3002/4650] R4[751/2400], Temp: 0.7772, Energy: -84.970354+0.000792j
[2025-07-30 16:55:02] [Iter 3003/4650] R4[752/2400], Temp: 0.7767, Energy: -84.972578-0.001152j
[2025-07-30 16:55:17] [Iter 3004/4650] R4[753/2400], Temp: 0.7762, Energy: -85.009837+0.002146j
[2025-07-30 16:55:32] [Iter 3005/4650] R4[754/2400], Temp: 0.7756, Energy: -84.985251+0.000270j
[2025-07-30 16:55:46] [Iter 3006/4650] R4[755/2400], Temp: 0.7751, Energy: -85.059668-0.004404j
[2025-07-30 16:56:01] [Iter 3007/4650] R4[756/2400], Temp: 0.7745, Energy: -85.069254-0.000905j
[2025-07-30 16:56:16] [Iter 3008/4650] R4[757/2400], Temp: 0.7740, Energy: -85.062422+0.002973j
[2025-07-30 16:56:31] [Iter 3009/4650] R4[758/2400], Temp: 0.7734, Energy: -85.011321-0.000618j
[2025-07-30 16:56:46] [Iter 3010/4650] R4[759/2400], Temp: 0.7729, Energy: -85.071579+0.001466j
[2025-07-30 16:57:00] [Iter 3011/4650] R4[760/2400], Temp: 0.7723, Energy: -85.077886-0.007405j
[2025-07-30 16:57:15] [Iter 3012/4650] R4[761/2400], Temp: 0.7718, Energy: -85.055468+0.003355j
[2025-07-30 16:57:30] [Iter 3013/4650] R4[762/2400], Temp: 0.7712, Energy: -85.044677+0.001973j
[2025-07-30 16:57:45] [Iter 3014/4650] R4[763/2400], Temp: 0.7707, Energy: -85.097413-0.003668j
[2025-07-30 16:58:00] [Iter 3015/4650] R4[764/2400], Temp: 0.7701, Energy: -85.114994-0.006164j
[2025-07-30 16:58:15] [Iter 3016/4650] R4[765/2400], Temp: 0.7696, Energy: -85.117933+0.005769j
[2025-07-30 16:58:29] [Iter 3017/4650] R4[766/2400], Temp: 0.7690, Energy: -85.133713-0.005359j
[2025-07-30 16:58:44] [Iter 3018/4650] R4[767/2400], Temp: 0.7685, Energy: -85.160450-0.003352j
[2025-07-30 16:58:59] [Iter 3019/4650] R4[768/2400], Temp: 0.7679, Energy: -85.272314-0.003642j
[2025-07-30 16:59:13] [Iter 3020/4650] R4[769/2400], Temp: 0.7674, Energy: -85.164255+0.004144j
[2025-07-30 16:59:28] [Iter 3021/4650] R4[770/2400], Temp: 0.7668, Energy: -85.160733-0.003611j
[2025-07-30 16:59:43] [Iter 3022/4650] R4[771/2400], Temp: 0.7663, Energy: -85.213078+0.004411j
[2025-07-30 16:59:58] [Iter 3023/4650] R4[772/2400], Temp: 0.7657, Energy: -85.160963-0.005431j
[2025-07-30 17:00:13] [Iter 3024/4650] R4[773/2400], Temp: 0.7651, Energy: -85.103045-0.005261j
[2025-07-30 17:00:28] [Iter 3025/4650] R4[774/2400], Temp: 0.7646, Energy: -85.131521+0.006248j
[2025-07-30 17:00:42] [Iter 3026/4650] R4[775/2400], Temp: 0.7640, Energy: -85.119372-0.001350j
[2025-07-30 17:00:57] [Iter 3027/4650] R4[776/2400], Temp: 0.7635, Energy: -85.020851-0.008648j
[2025-07-30 17:01:12] [Iter 3028/4650] R4[777/2400], Temp: 0.7629, Energy: -85.102064-0.006185j
[2025-07-30 17:01:27] [Iter 3029/4650] R4[778/2400], Temp: 0.7624, Energy: -85.109378+0.003419j
[2025-07-30 17:01:41] [Iter 3030/4650] R4[779/2400], Temp: 0.7618, Energy: -85.109375+0.005227j
[2025-07-30 17:01:56] [Iter 3031/4650] R4[780/2400], Temp: 0.7612, Energy: -85.091374-0.003931j
[2025-07-30 17:02:11] [Iter 3032/4650] R4[781/2400], Temp: 0.7607, Energy: -85.118067-0.001742j
[2025-07-30 17:02:26] [Iter 3033/4650] R4[782/2400], Temp: 0.7601, Energy: -85.138897-0.002033j
[2025-07-30 17:02:40] [Iter 3034/4650] R4[783/2400], Temp: 0.7596, Energy: -85.037586+0.004679j
[2025-07-30 17:02:55] [Iter 3035/4650] R4[784/2400], Temp: 0.7590, Energy: -85.045748+0.005426j
[2025-07-30 17:03:10] [Iter 3036/4650] R4[785/2400], Temp: 0.7585, Energy: -85.060591-0.014139j
[2025-07-30 17:03:24] [Iter 3037/4650] R4[786/2400], Temp: 0.7579, Energy: -84.991493+0.002593j
[2025-07-30 17:03:39] [Iter 3038/4650] R4[787/2400], Temp: 0.7573, Energy: -84.997205-0.002282j
[2025-07-30 17:03:54] [Iter 3039/4650] R4[788/2400], Temp: 0.7568, Energy: -84.999788-0.000621j
[2025-07-30 17:04:09] [Iter 3040/4650] R4[789/2400], Temp: 0.7562, Energy: -85.034608-0.001180j
[2025-07-30 17:04:24] [Iter 3041/4650] R4[790/2400], Temp: 0.7556, Energy: -84.992709+0.003273j
[2025-07-30 17:04:38] [Iter 3042/4650] R4[791/2400], Temp: 0.7551, Energy: -85.024167+0.004826j
[2025-07-30 17:04:53] [Iter 3043/4650] R4[792/2400], Temp: 0.7545, Energy: -85.073656-0.002222j
[2025-07-30 17:05:08] [Iter 3044/4650] R4[793/2400], Temp: 0.7540, Energy: -85.105214+0.007079j
[2025-07-30 17:05:23] [Iter 3045/4650] R4[794/2400], Temp: 0.7534, Energy: -85.088929-0.002563j
[2025-07-30 17:05:38] [Iter 3046/4650] R4[795/2400], Temp: 0.7528, Energy: -85.113039+0.011987j
[2025-07-30 17:05:52] [Iter 3047/4650] R4[796/2400], Temp: 0.7523, Energy: -85.004374+0.002491j
[2025-07-30 17:06:07] [Iter 3048/4650] R4[797/2400], Temp: 0.7517, Energy: -85.024071+0.009785j
[2025-07-30 17:06:22] [Iter 3049/4650] R4[798/2400], Temp: 0.7511, Energy: -85.032932-0.003399j
[2025-07-30 17:06:37] [Iter 3050/4650] R4[799/2400], Temp: 0.7506, Energy: -85.003788+0.008800j
[2025-07-30 17:06:52] [Iter 3051/4650] R4[800/2400], Temp: 0.7500, Energy: -85.036609-0.005695j
[2025-07-30 17:07:07] [Iter 3052/4650] R4[801/2400], Temp: 0.7494, Energy: -84.993267-0.009596j
[2025-07-30 17:07:21] [Iter 3053/4650] R4[802/2400], Temp: 0.7489, Energy: -85.033468-0.011192j
[2025-07-30 17:07:36] [Iter 3054/4650] R4[803/2400], Temp: 0.7483, Energy: -85.028963-0.007116j
[2025-07-30 17:07:51] [Iter 3055/4650] R4[804/2400], Temp: 0.7477, Energy: -85.013489-0.012435j
[2025-07-30 17:08:05] [Iter 3056/4650] R4[805/2400], Temp: 0.7472, Energy: -85.025488-0.016149j
[2025-07-30 17:08:20] [Iter 3057/4650] R4[806/2400], Temp: 0.7466, Energy: -85.030659+0.002576j
[2025-07-30 17:08:35] [Iter 3058/4650] R4[807/2400], Temp: 0.7460, Energy: -84.986858+0.005715j
[2025-07-30 17:08:49] [Iter 3059/4650] R4[808/2400], Temp: 0.7455, Energy: -84.986649-0.010222j
[2025-07-30 17:09:04] [Iter 3060/4650] R4[809/2400], Temp: 0.7449, Energy: -84.948484-0.011944j
[2025-07-30 17:09:18] [Iter 3061/4650] R4[810/2400], Temp: 0.7443, Energy: -84.908689-0.000055j
[2025-07-30 17:09:33] [Iter 3062/4650] R4[811/2400], Temp: 0.7437, Energy: -84.988746+0.003245j
[2025-07-30 17:09:48] [Iter 3063/4650] R4[812/2400], Temp: 0.7432, Energy: -85.033350+0.013908j
[2025-07-30 17:10:03] [Iter 3064/4650] R4[813/2400], Temp: 0.7426, Energy: -85.010190+0.000809j
[2025-07-30 17:10:18] [Iter 3065/4650] R4[814/2400], Temp: 0.7420, Energy: -85.053694+0.002857j
[2025-07-30 17:10:33] [Iter 3066/4650] R4[815/2400], Temp: 0.7415, Energy: -85.031236+0.000705j
[2025-07-30 17:10:47] [Iter 3067/4650] R4[816/2400], Temp: 0.7409, Energy: -85.053151+0.000686j
[2025-07-30 17:11:02] [Iter 3068/4650] R4[817/2400], Temp: 0.7403, Energy: -85.013385+0.003139j
[2025-07-30 17:11:16] [Iter 3069/4650] R4[818/2400], Temp: 0.7397, Energy: -85.020185-0.000417j
[2025-07-30 17:11:31] [Iter 3070/4650] R4[819/2400], Temp: 0.7392, Energy: -85.010643+0.008085j
[2025-07-30 17:11:46] [Iter 3071/4650] R4[820/2400], Temp: 0.7386, Energy: -85.104668-0.004340j
[2025-07-30 17:12:00] [Iter 3072/4650] R4[821/2400], Temp: 0.7380, Energy: -85.067780+0.001433j
[2025-07-30 17:12:15] [Iter 3073/4650] R4[822/2400], Temp: 0.7374, Energy: -85.033006+0.002720j
[2025-07-30 17:12:29] [Iter 3074/4650] R4[823/2400], Temp: 0.7369, Energy: -85.073512+0.002343j
[2025-07-30 17:12:44] [Iter 3075/4650] R4[824/2400], Temp: 0.7363, Energy: -84.949222-0.000980j
[2025-07-30 17:12:59] [Iter 3076/4650] R4[825/2400], Temp: 0.7357, Energy: -85.006915+0.003859j
[2025-07-30 17:13:14] [Iter 3077/4650] R4[826/2400], Temp: 0.7351, Energy: -85.048492-0.001318j
[2025-07-30 17:13:29] [Iter 3078/4650] R4[827/2400], Temp: 0.7345, Energy: -85.033818-0.006658j
[2025-07-30 17:13:43] [Iter 3079/4650] R4[828/2400], Temp: 0.7340, Energy: -85.003310-0.004042j
[2025-07-30 17:13:58] [Iter 3080/4650] R4[829/2400], Temp: 0.7334, Energy: -84.995159+0.004788j
[2025-07-30 17:14:13] [Iter 3081/4650] R4[830/2400], Temp: 0.7328, Energy: -84.985773-0.008599j
[2025-07-30 17:14:27] [Iter 3082/4650] R4[831/2400], Temp: 0.7322, Energy: -84.997982-0.000268j
[2025-07-30 17:14:42] [Iter 3083/4650] R4[832/2400], Temp: 0.7316, Energy: -84.939895+0.005207j
[2025-07-30 17:14:56] [Iter 3084/4650] R4[833/2400], Temp: 0.7311, Energy: -84.941431+0.009151j
[2025-07-30 17:15:11] [Iter 3085/4650] R4[834/2400], Temp: 0.7305, Energy: -84.998481+0.000430j
[2025-07-30 17:15:25] [Iter 3086/4650] R4[835/2400], Temp: 0.7299, Energy: -85.012657+0.005234j
[2025-07-30 17:15:40] [Iter 3087/4650] R4[836/2400], Temp: 0.7293, Energy: -85.062742-0.001465j
[2025-07-30 17:15:55] [Iter 3088/4650] R4[837/2400], Temp: 0.7287, Energy: -85.034067+0.000858j
[2025-07-30 17:16:10] [Iter 3089/4650] R4[838/2400], Temp: 0.7282, Energy: -85.101325-0.002055j
[2025-07-30 17:16:25] [Iter 3090/4650] R4[839/2400], Temp: 0.7276, Energy: -85.077830+0.003582j
[2025-07-30 17:16:40] [Iter 3091/4650] R4[840/2400], Temp: 0.7270, Energy: -85.095669+0.001715j
[2025-07-30 17:16:55] [Iter 3092/4650] R4[841/2400], Temp: 0.7264, Energy: -85.073613+0.008306j
[2025-07-30 17:17:10] [Iter 3093/4650] R4[842/2400], Temp: 0.7258, Energy: -85.094953-0.005587j
[2025-07-30 17:17:25] [Iter 3094/4650] R4[843/2400], Temp: 0.7252, Energy: -85.113843+0.007638j
[2025-07-30 17:17:39] [Iter 3095/4650] R4[844/2400], Temp: 0.7247, Energy: -85.084277-0.004666j
[2025-07-30 17:17:54] [Iter 3096/4650] R4[845/2400], Temp: 0.7241, Energy: -85.065631+0.004745j
[2025-07-30 17:18:09] [Iter 3097/4650] R4[846/2400], Temp: 0.7235, Energy: -84.994263-0.000161j
[2025-07-30 17:18:23] [Iter 3098/4650] R4[847/2400], Temp: 0.7229, Energy: -85.029890+0.003305j
[2025-07-30 17:18:38] [Iter 3099/4650] R4[848/2400], Temp: 0.7223, Energy: -85.017495+0.000114j
[2025-07-30 17:18:53] [Iter 3100/4650] R4[849/2400], Temp: 0.7217, Energy: -85.059247+0.006770j
[2025-07-30 17:19:08] [Iter 3101/4650] R4[850/2400], Temp: 0.7211, Energy: -85.070288-0.006975j
[2025-07-30 17:19:23] [Iter 3102/4650] R4[851/2400], Temp: 0.7206, Energy: -85.091839+0.002071j
[2025-07-30 17:19:38] [Iter 3103/4650] R4[852/2400], Temp: 0.7200, Energy: -85.083007+0.009779j
[2025-07-30 17:19:52] [Iter 3104/4650] R4[853/2400], Temp: 0.7194, Energy: -85.085661-0.000008j
[2025-07-30 17:20:07] [Iter 3105/4650] R4[854/2400], Temp: 0.7188, Energy: -85.037909+0.009272j
[2025-07-30 17:20:22] [Iter 3106/4650] R4[855/2400], Temp: 0.7182, Energy: -85.052111+0.002530j
[2025-07-30 17:20:37] [Iter 3107/4650] R4[856/2400], Temp: 0.7176, Energy: -85.061011+0.002849j
[2025-07-30 17:20:51] [Iter 3108/4650] R4[857/2400], Temp: 0.7170, Energy: -85.034313+0.000211j
[2025-07-30 17:21:05] [Iter 3109/4650] R4[858/2400], Temp: 0.7164, Energy: -85.109247+0.015535j
[2025-07-30 17:21:20] [Iter 3110/4650] R4[859/2400], Temp: 0.7158, Energy: -85.098679-0.002772j
[2025-07-30 17:21:35] [Iter 3111/4650] R4[860/2400], Temp: 0.7153, Energy: -85.038046-0.002374j
[2025-07-30 17:21:50] [Iter 3112/4650] R4[861/2400], Temp: 0.7147, Energy: -85.009191-0.006906j
[2025-07-30 17:22:04] [Iter 3113/4650] R4[862/2400], Temp: 0.7141, Energy: -85.044926-0.003023j
[2025-07-30 17:22:19] [Iter 3114/4650] R4[863/2400], Temp: 0.7135, Energy: -85.054201+0.001101j
[2025-07-30 17:22:34] [Iter 3115/4650] R4[864/2400], Temp: 0.7129, Energy: -85.072541-0.000706j
[2025-07-30 17:22:49] [Iter 3116/4650] R4[865/2400], Temp: 0.7123, Energy: -85.046844-0.006253j
[2025-07-30 17:23:04] [Iter 3117/4650] R4[866/2400], Temp: 0.7117, Energy: -85.039948-0.003202j
[2025-07-30 17:23:19] [Iter 3118/4650] R4[867/2400], Temp: 0.7111, Energy: -85.022288+0.002954j
[2025-07-30 17:23:34] [Iter 3119/4650] R4[868/2400], Temp: 0.7105, Energy: -84.982040-0.006864j
[2025-07-30 17:23:48] [Iter 3120/4650] R4[869/2400], Temp: 0.7099, Energy: -85.005182+0.009584j
[2025-07-30 17:24:03] [Iter 3121/4650] R4[870/2400], Temp: 0.7093, Energy: -85.061393-0.007875j
[2025-07-30 17:24:18] [Iter 3122/4650] R4[871/2400], Temp: 0.7087, Energy: -85.122663+0.002980j
[2025-07-30 17:24:33] [Iter 3123/4650] R4[872/2400], Temp: 0.7081, Energy: -85.016955-0.003648j
[2025-07-30 17:24:47] [Iter 3124/4650] R4[873/2400], Temp: 0.7075, Energy: -84.996576-0.005493j
[2025-07-30 17:25:02] [Iter 3125/4650] R4[874/2400], Temp: 0.7069, Energy: -84.995541-0.001506j
[2025-07-30 17:25:17] [Iter 3126/4650] R4[875/2400], Temp: 0.7064, Energy: -84.982009-0.005539j
[2025-07-30 17:25:31] [Iter 3127/4650] R4[876/2400], Temp: 0.7058, Energy: -84.975638-0.001340j
[2025-07-30 17:25:46] [Iter 3128/4650] R4[877/2400], Temp: 0.7052, Energy: -84.989414+0.000437j
[2025-07-30 17:26:00] [Iter 3129/4650] R4[878/2400], Temp: 0.7046, Energy: -85.011402-0.011397j
[2025-07-30 17:26:15] [Iter 3130/4650] R4[879/2400], Temp: 0.7040, Energy: -85.005978+0.000792j
[2025-07-30 17:26:30] [Iter 3131/4650] R4[880/2400], Temp: 0.7034, Energy: -85.044393-0.004932j
[2025-07-30 17:26:45] [Iter 3132/4650] R4[881/2400], Temp: 0.7028, Energy: -85.018948+0.004785j
[2025-07-30 17:27:00] [Iter 3133/4650] R4[882/2400], Temp: 0.7022, Energy: -84.971027-0.001115j
[2025-07-30 17:27:15] [Iter 3134/4650] R4[883/2400], Temp: 0.7016, Energy: -84.955696-0.000732j
[2025-07-30 17:27:30] [Iter 3135/4650] R4[884/2400], Temp: 0.7010, Energy: -84.968840-0.000047j
[2025-07-30 17:27:45] [Iter 3136/4650] R4[885/2400], Temp: 0.7004, Energy: -85.009333-0.002490j
[2025-07-30 17:28:00] [Iter 3137/4650] R4[886/2400], Temp: 0.6998, Energy: -85.041135+0.016994j
[2025-07-30 17:28:15] [Iter 3138/4650] R4[887/2400], Temp: 0.6992, Energy: -85.077481+0.007915j
[2025-07-30 17:28:30] [Iter 3139/4650] R4[888/2400], Temp: 0.6986, Energy: -85.031200+0.008692j
[2025-07-30 17:28:45] [Iter 3140/4650] R4[889/2400], Temp: 0.6980, Energy: -85.081498+0.000319j
[2025-07-30 17:29:00] [Iter 3141/4650] R4[890/2400], Temp: 0.6974, Energy: -85.046272-0.001433j
[2025-07-30 17:29:14] [Iter 3142/4650] R4[891/2400], Temp: 0.6968, Energy: -85.034462+0.004993j
[2025-07-30 17:29:29] [Iter 3143/4650] R4[892/2400], Temp: 0.6962, Energy: -85.107402+0.000851j
[2025-07-30 17:29:44] [Iter 3144/4650] R4[893/2400], Temp: 0.6956, Energy: -85.036062+0.007372j
[2025-07-30 17:29:58] [Iter 3145/4650] R4[894/2400], Temp: 0.6950, Energy: -85.012229+0.001994j
[2025-07-30 17:30:13] [Iter 3146/4650] R4[895/2400], Temp: 0.6944, Energy: -85.040903+0.006443j
[2025-07-30 17:30:28] [Iter 3147/4650] R4[896/2400], Temp: 0.6938, Energy: -85.023685+0.000683j
[2025-07-30 17:30:42] [Iter 3148/4650] R4[897/2400], Temp: 0.6932, Energy: -84.973309+0.001161j
[2025-07-30 17:30:57] [Iter 3149/4650] R4[898/2400], Temp: 0.6926, Energy: -85.006455-0.003807j
[2025-07-30 17:31:11] [Iter 3150/4650] R4[899/2400], Temp: 0.6919, Energy: -85.020968+0.005605j
[2025-07-30 17:31:25] [Iter 3151/4650] R4[900/2400], Temp: 0.6913, Energy: -85.051953+0.000676j
[2025-07-30 17:31:40] [Iter 3152/4650] R4[901/2400], Temp: 0.6907, Energy: -84.976796-0.006594j
[2025-07-30 17:31:55] [Iter 3153/4650] R4[902/2400], Temp: 0.6901, Energy: -85.016559-0.006096j
[2025-07-30 17:32:10] [Iter 3154/4650] R4[903/2400], Temp: 0.6895, Energy: -84.982394-0.008954j
[2025-07-30 17:32:24] [Iter 3155/4650] R4[904/2400], Temp: 0.6889, Energy: -85.006980-0.000250j
[2025-07-30 17:32:39] [Iter 3156/4650] R4[905/2400], Temp: 0.6883, Energy: -85.026329-0.004256j
[2025-07-30 17:32:54] [Iter 3157/4650] R4[906/2400], Temp: 0.6877, Energy: -85.067737-0.001718j
[2025-07-30 17:33:09] [Iter 3158/4650] R4[907/2400], Temp: 0.6871, Energy: -85.035157+0.000123j
[2025-07-30 17:33:23] [Iter 3159/4650] R4[908/2400], Temp: 0.6865, Energy: -85.100586+0.001344j
[2025-07-30 17:33:38] [Iter 3160/4650] R4[909/2400], Temp: 0.6859, Energy: -85.103653+0.001170j
[2025-07-30 17:33:53] [Iter 3161/4650] R4[910/2400], Temp: 0.6853, Energy: -85.087873+0.008550j
[2025-07-30 17:34:07] [Iter 3162/4650] R4[911/2400], Temp: 0.6847, Energy: -85.092848-0.000634j
[2025-07-30 17:34:22] [Iter 3163/4650] R4[912/2400], Temp: 0.6841, Energy: -85.126953+0.002500j
[2025-07-30 17:34:37] [Iter 3164/4650] R4[913/2400], Temp: 0.6835, Energy: -85.083214+0.002862j
[2025-07-30 17:34:52] [Iter 3165/4650] R4[914/2400], Temp: 0.6828, Energy: -85.102732+0.002691j
[2025-07-30 17:35:07] [Iter 3166/4650] R4[915/2400], Temp: 0.6822, Energy: -85.056252-0.004555j
[2025-07-30 17:35:21] [Iter 3167/4650] R4[916/2400], Temp: 0.6816, Energy: -85.103614+0.001327j
[2025-07-30 17:35:35] [Iter 3168/4650] R4[917/2400], Temp: 0.6810, Energy: -85.173857-0.002389j
[2025-07-30 17:35:50] [Iter 3169/4650] R4[918/2400], Temp: 0.6804, Energy: -85.166712-0.001640j
[2025-07-30 17:36:04] [Iter 3170/4650] R4[919/2400], Temp: 0.6798, Energy: -85.137528-0.003268j
[2025-07-30 17:36:19] [Iter 3171/4650] R4[920/2400], Temp: 0.6792, Energy: -85.158603+0.010235j
[2025-07-30 17:36:33] [Iter 3172/4650] R4[921/2400], Temp: 0.6786, Energy: -85.126485+0.000143j
[2025-07-30 17:36:48] [Iter 3173/4650] R4[922/2400], Temp: 0.6780, Energy: -85.125955+0.007548j
[2025-07-30 17:37:03] [Iter 3174/4650] R4[923/2400], Temp: 0.6773, Energy: -85.132595-0.005064j
[2025-07-30 17:37:17] [Iter 3175/4650] R4[924/2400], Temp: 0.6767, Energy: -85.051952+0.002235j
[2025-07-30 17:37:32] [Iter 3176/4650] R4[925/2400], Temp: 0.6761, Energy: -85.102148+0.005794j
[2025-07-30 17:37:46] [Iter 3177/4650] R4[926/2400], Temp: 0.6755, Energy: -85.049689+0.000818j
[2025-07-30 17:38:01] [Iter 3178/4650] R4[927/2400], Temp: 0.6749, Energy: -85.174727-0.005018j
[2025-07-30 17:38:16] [Iter 3179/4650] R4[928/2400], Temp: 0.6743, Energy: -85.100042-0.000145j
[2025-07-30 17:38:31] [Iter 3180/4650] R4[929/2400], Temp: 0.6737, Energy: -85.123126-0.004456j
[2025-07-30 17:38:46] [Iter 3181/4650] R4[930/2400], Temp: 0.6731, Energy: -84.988318-0.005921j
[2025-07-30 17:39:00] [Iter 3182/4650] R4[931/2400], Temp: 0.6724, Energy: -85.110626-0.007299j
[2025-07-30 17:39:15] [Iter 3183/4650] R4[932/2400], Temp: 0.6718, Energy: -85.116710-0.007753j
[2025-07-30 17:39:30] [Iter 3184/4650] R4[933/2400], Temp: 0.6712, Energy: -85.055224-0.007543j
[2025-07-30 17:39:44] [Iter 3185/4650] R4[934/2400], Temp: 0.6706, Energy: -85.041542-0.008101j
[2025-07-30 17:39:59] [Iter 3186/4650] R4[935/2400], Temp: 0.6700, Energy: -85.108053-0.011202j
[2025-07-30 17:40:14] [Iter 3187/4650] R4[936/2400], Temp: 0.6694, Energy: -85.016842-0.012119j
[2025-07-30 17:40:28] [Iter 3188/4650] R4[937/2400], Temp: 0.6688, Energy: -85.039575-0.016499j
[2025-07-30 17:40:43] [Iter 3189/4650] R4[938/2400], Temp: 0.6681, Energy: -85.084724-0.012583j
[2025-07-30 17:40:58] [Iter 3190/4650] R4[939/2400], Temp: 0.6675, Energy: -85.004742-0.007060j
[2025-07-30 17:41:13] [Iter 3191/4650] R4[940/2400], Temp: 0.6669, Energy: -84.984101-0.009199j
[2025-07-30 17:41:28] [Iter 3192/4650] R4[941/2400], Temp: 0.6663, Energy: -85.019651-0.008540j
[2025-07-30 17:41:43] [Iter 3193/4650] R4[942/2400], Temp: 0.6657, Energy: -84.946689-0.007985j
[2025-07-30 17:41:57] [Iter 3194/4650] R4[943/2400], Temp: 0.6651, Energy: -85.038335-0.007139j
[2025-07-30 17:42:12] [Iter 3195/4650] R4[944/2400], Temp: 0.6644, Energy: -84.998442+0.001348j
[2025-07-30 17:42:27] [Iter 3196/4650] R4[945/2400], Temp: 0.6638, Energy: -84.978338+0.003380j
[2025-07-30 17:42:42] [Iter 3197/4650] R4[946/2400], Temp: 0.6632, Energy: -85.065767-0.010304j
[2025-07-30 17:42:56] [Iter 3198/4650] R4[947/2400], Temp: 0.6626, Energy: -84.979691-0.009195j
[2025-07-30 17:43:11] [Iter 3199/4650] R4[948/2400], Temp: 0.6620, Energy: -84.892585-0.009163j
[2025-07-30 17:43:25] [Iter 3200/4650] R4[949/2400], Temp: 0.6613, Energy: -84.996613-0.007247j
[2025-07-30 17:43:40] [Iter 3201/4650] R4[950/2400], Temp: 0.6607, Energy: -85.024311-0.006748j
[2025-07-30 17:43:55] [Iter 3202/4650] R4[951/2400], Temp: 0.6601, Energy: -85.012294-0.004062j
[2025-07-30 17:44:10] [Iter 3203/4650] R4[952/2400], Temp: 0.6595, Energy: -84.992692+0.003910j
[2025-07-30 17:44:25] [Iter 3204/4650] R4[953/2400], Temp: 0.6589, Energy: -84.991110+0.000829j
[2025-07-30 17:44:39] [Iter 3205/4650] R4[954/2400], Temp: 0.6582, Energy: -85.008455+0.010439j
[2025-07-30 17:44:54] [Iter 3206/4650] R4[955/2400], Temp: 0.6576, Energy: -84.975562-0.001329j
[2025-07-30 17:45:08] [Iter 3207/4650] R4[956/2400], Temp: 0.6570, Energy: -85.001276-0.000102j
[2025-07-30 17:45:23] [Iter 3208/4650] R4[957/2400], Temp: 0.6564, Energy: -85.011840+0.003249j
[2025-07-30 17:45:38] [Iter 3209/4650] R4[958/2400], Temp: 0.6558, Energy: -84.943216-0.000397j
[2025-07-30 17:45:53] [Iter 3210/4650] R4[959/2400], Temp: 0.6551, Energy: -84.991404-0.010129j
[2025-07-30 17:46:08] [Iter 3211/4650] R4[960/2400], Temp: 0.6545, Energy: -84.992467-0.012049j
[2025-07-30 17:46:22] [Iter 3212/4650] R4[961/2400], Temp: 0.6539, Energy: -84.942274+0.026105j
[2025-07-30 17:46:37] [Iter 3213/4650] R4[962/2400], Temp: 0.6533, Energy: -84.953975-0.011379j
[2025-07-30 17:46:51] [Iter 3214/4650] R4[963/2400], Temp: 0.6526, Energy: -84.962744-0.006600j
[2025-07-30 17:47:06] [Iter 3215/4650] R4[964/2400], Temp: 0.6520, Energy: -85.042115-0.001794j
[2025-07-30 17:47:20] [Iter 3216/4650] R4[965/2400], Temp: 0.6514, Energy: -85.014187-0.004452j
[2025-07-30 17:47:35] [Iter 3217/4650] R4[966/2400], Temp: 0.6508, Energy: -84.977633+0.003965j
[2025-07-30 17:47:50] [Iter 3218/4650] R4[967/2400], Temp: 0.6501, Energy: -84.970898+0.005675j
[2025-07-30 17:48:05] [Iter 3219/4650] R4[968/2400], Temp: 0.6495, Energy: -85.055516-0.004514j
[2025-07-30 17:48:19] [Iter 3220/4650] R4[969/2400], Temp: 0.6489, Energy: -84.941726+0.005310j
[2025-07-30 17:48:34] [Iter 3221/4650] R4[970/2400], Temp: 0.6483, Energy: -84.905787-0.003685j
[2025-07-30 17:48:49] [Iter 3222/4650] R4[971/2400], Temp: 0.6476, Energy: -84.882836-0.003106j
[2025-07-30 17:49:04] [Iter 3223/4650] R4[972/2400], Temp: 0.6470, Energy: -84.980765-0.001335j
[2025-07-30 17:49:18] [Iter 3224/4650] R4[973/2400], Temp: 0.6464, Energy: -85.058178-0.002679j
[2025-07-30 17:49:33] [Iter 3225/4650] R4[974/2400], Temp: 0.6458, Energy: -85.035773+0.002641j
[2025-07-30 17:49:48] [Iter 3226/4650] R4[975/2400], Temp: 0.6451, Energy: -85.070525+0.007920j
[2025-07-30 17:50:02] [Iter 3227/4650] R4[976/2400], Temp: 0.6445, Energy: -85.054437-0.001963j
[2025-07-30 17:50:17] [Iter 3228/4650] R4[977/2400], Temp: 0.6439, Energy: -85.019519+0.002504j
[2025-07-30 17:50:31] [Iter 3229/4650] R4[978/2400], Temp: 0.6433, Energy: -85.083117+0.005236j
[2025-07-30 17:50:46] [Iter 3230/4650] R4[979/2400], Temp: 0.6426, Energy: -85.021710+0.002444j
[2025-07-30 17:51:01] [Iter 3231/4650] R4[980/2400], Temp: 0.6420, Energy: -85.056654+0.001241j
[2025-07-30 17:51:15] [Iter 3232/4650] R4[981/2400], Temp: 0.6414, Energy: -85.092273+0.005534j
[2025-07-30 17:51:30] [Iter 3233/4650] R4[982/2400], Temp: 0.6408, Energy: -84.982233+0.003598j
[2025-07-30 17:51:45] [Iter 3234/4650] R4[983/2400], Temp: 0.6401, Energy: -84.996650+0.004606j
[2025-07-30 17:51:59] [Iter 3235/4650] R4[984/2400], Temp: 0.6395, Energy: -85.022921-0.000647j
[2025-07-30 17:52:14] [Iter 3236/4650] R4[985/2400], Temp: 0.6389, Energy: -85.010389-0.002164j
[2025-07-30 17:52:29] [Iter 3237/4650] R4[986/2400], Temp: 0.6382, Energy: -85.006385-0.002948j
[2025-07-30 17:52:44] [Iter 3238/4650] R4[987/2400], Temp: 0.6376, Energy: -84.942049+0.001083j
[2025-07-30 17:52:59] [Iter 3239/4650] R4[988/2400], Temp: 0.6370, Energy: -84.932664+0.000990j
[2025-07-30 17:53:13] [Iter 3240/4650] R4[989/2400], Temp: 0.6364, Energy: -84.871385+0.001048j
[2025-07-30 17:53:28] [Iter 3241/4650] R4[990/2400], Temp: 0.6357, Energy: -84.904138+0.004095j
[2025-07-30 17:53:42] [Iter 3242/4650] R4[991/2400], Temp: 0.6351, Energy: -84.921054-0.004219j
[2025-07-30 17:53:57] [Iter 3243/4650] R4[992/2400], Temp: 0.6345, Energy: -84.960875-0.004824j
[2025-07-30 17:54:12] [Iter 3244/4650] R4[993/2400], Temp: 0.6338, Energy: -84.951803+0.014722j
[2025-07-30 17:54:26] [Iter 3245/4650] R4[994/2400], Temp: 0.6332, Energy: -85.013349+0.006344j
[2025-07-30 17:54:41] [Iter 3246/4650] R4[995/2400], Temp: 0.6326, Energy: -84.975686-0.006702j
[2025-07-30 17:54:56] [Iter 3247/4650] R4[996/2400], Temp: 0.6319, Energy: -85.023167-0.004440j
[2025-07-30 17:55:11] [Iter 3248/4650] R4[997/2400], Temp: 0.6313, Energy: -85.002944-0.018247j
[2025-07-30 17:55:26] [Iter 3249/4650] R4[998/2400], Temp: 0.6307, Energy: -85.020069+0.003771j
[2025-07-30 17:55:40] [Iter 3250/4650] R4[999/2400], Temp: 0.6300, Energy: -84.996248+0.004709j
[2025-07-30 17:55:54] [Iter 3251/4650] R4[1000/2400], Temp: 0.6294, Energy: -85.012808+0.000111j
[2025-07-30 17:56:09] [Iter 3252/4650] R4[1001/2400], Temp: 0.6288, Energy: -85.007748-0.001693j
[2025-07-30 17:56:23] [Iter 3253/4650] R4[1002/2400], Temp: 0.6281, Energy: -85.059817-0.001515j
[2025-07-30 17:56:38] [Iter 3254/4650] R4[1003/2400], Temp: 0.6275, Energy: -85.078601+0.006534j
[2025-07-30 17:56:53] [Iter 3255/4650] R4[1004/2400], Temp: 0.6269, Energy: -85.095187+0.014568j
[2025-07-30 17:57:08] [Iter 3256/4650] R4[1005/2400], Temp: 0.6262, Energy: -85.077015+0.007645j
[2025-07-30 17:57:23] [Iter 3257/4650] R4[1006/2400], Temp: 0.6256, Energy: -85.043643+0.000583j
[2025-07-30 17:57:38] [Iter 3258/4650] R4[1007/2400], Temp: 0.6250, Energy: -85.018398+0.002939j
[2025-07-30 17:57:52] [Iter 3259/4650] R4[1008/2400], Temp: 0.6243, Energy: -85.026381+0.001170j
[2025-07-30 17:58:07] [Iter 3260/4650] R4[1009/2400], Temp: 0.6237, Energy: -85.058579+0.002402j
[2025-07-30 17:58:22] [Iter 3261/4650] R4[1010/2400], Temp: 0.6231, Energy: -85.027104+0.003735j
[2025-07-30 17:58:36] [Iter 3262/4650] R4[1011/2400], Temp: 0.6224, Energy: -84.961259-0.002561j
[2025-07-30 17:58:51] [Iter 3263/4650] R4[1012/2400], Temp: 0.6218, Energy: -85.049113+0.000846j
[2025-07-30 17:59:05] [Iter 3264/4650] R4[1013/2400], Temp: 0.6212, Energy: -85.043369+0.007442j
[2025-07-30 17:59:19] [Iter 3265/4650] R4[1014/2400], Temp: 0.6205, Energy: -85.011164+0.005262j
[2025-07-30 17:59:35] [Iter 3266/4650] R4[1015/2400], Temp: 0.6199, Energy: -85.045761+0.007307j
[2025-07-30 17:59:49] [Iter 3267/4650] R4[1016/2400], Temp: 0.6193, Energy: -84.985515+0.002322j
[2025-07-30 18:00:04] [Iter 3268/4650] R4[1017/2400], Temp: 0.6186, Energy: -84.983792+0.000848j
[2025-07-30 18:00:18] [Iter 3269/4650] R4[1018/2400], Temp: 0.6180, Energy: -84.975290-0.001461j
[2025-07-30 18:00:33] [Iter 3270/4650] R4[1019/2400], Temp: 0.6174, Energy: -84.960550-0.004615j
[2025-07-30 18:00:48] [Iter 3271/4650] R4[1020/2400], Temp: 0.6167, Energy: -84.905106+0.000132j
[2025-07-30 18:01:02] [Iter 3272/4650] R4[1021/2400], Temp: 0.6161, Energy: -84.863179+0.001534j
[2025-07-30 18:01:17] [Iter 3273/4650] R4[1022/2400], Temp: 0.6154, Energy: -84.903566-0.002129j
[2025-07-30 18:01:32] [Iter 3274/4650] R4[1023/2400], Temp: 0.6148, Energy: -84.930692-0.008740j
[2025-07-30 18:01:46] [Iter 3275/4650] R4[1024/2400], Temp: 0.6142, Energy: -84.897537-0.000927j
[2025-07-30 18:02:01] [Iter 3276/4650] R4[1025/2400], Temp: 0.6135, Energy: -84.996279-0.000456j
[2025-07-30 18:02:16] [Iter 3277/4650] R4[1026/2400], Temp: 0.6129, Energy: -85.052627+0.003020j
[2025-07-30 18:02:31] [Iter 3278/4650] R4[1027/2400], Temp: 0.6123, Energy: -85.016906+0.002989j
[2025-07-30 18:02:46] [Iter 3279/4650] R4[1028/2400], Temp: 0.6116, Energy: -85.049504-0.006505j
[2025-07-30 18:03:01] [Iter 3280/4650] R4[1029/2400], Temp: 0.6110, Energy: -85.031567+0.004436j
[2025-07-30 18:03:15] [Iter 3281/4650] R4[1030/2400], Temp: 0.6103, Energy: -85.039030+0.000875j
[2025-07-30 18:03:29] [Iter 3282/4650] R4[1031/2400], Temp: 0.6097, Energy: -85.107035+0.006748j
[2025-07-30 18:03:44] [Iter 3283/4650] R4[1032/2400], Temp: 0.6091, Energy: -85.068914+0.003316j
[2025-07-30 18:03:59] [Iter 3284/4650] R4[1033/2400], Temp: 0.6084, Energy: -85.047031+0.001987j
[2025-07-30 18:04:14] [Iter 3285/4650] R4[1034/2400], Temp: 0.6078, Energy: -85.083363+0.005598j
[2025-07-30 18:04:29] [Iter 3286/4650] R4[1035/2400], Temp: 0.6072, Energy: -85.019851+0.006024j
[2025-07-30 18:04:44] [Iter 3287/4650] R4[1036/2400], Temp: 0.6065, Energy: -85.131410+0.009402j
[2025-07-30 18:04:58] [Iter 3288/4650] R4[1037/2400], Temp: 0.6059, Energy: -85.139615-0.001841j
[2025-07-30 18:05:13] [Iter 3289/4650] R4[1038/2400], Temp: 0.6052, Energy: -85.054542-0.002938j
[2025-07-30 18:05:28] [Iter 3290/4650] R4[1039/2400], Temp: 0.6046, Energy: -85.105244+0.000824j
[2025-07-30 18:05:42] [Iter 3291/4650] R4[1040/2400], Temp: 0.6040, Energy: -85.073822+0.002489j
[2025-07-30 18:05:57] [Iter 3292/4650] R4[1041/2400], Temp: 0.6033, Energy: -85.001020-0.000681j
[2025-07-30 18:06:12] [Iter 3293/4650] R4[1042/2400], Temp: 0.6027, Energy: -85.039127+0.009436j
[2025-07-30 18:06:26] [Iter 3294/4650] R4[1043/2400], Temp: 0.6020, Energy: -85.040204+0.000312j
[2025-07-30 18:06:41] [Iter 3295/4650] R4[1044/2400], Temp: 0.6014, Energy: -85.060138+0.000486j
[2025-07-30 18:06:55] [Iter 3296/4650] R4[1045/2400], Temp: 0.6008, Energy: -85.052469-0.003634j
[2025-07-30 18:07:10] [Iter 3297/4650] R4[1046/2400], Temp: 0.6001, Energy: -85.008021-0.007766j
[2025-07-30 18:07:25] [Iter 3298/4650] R4[1047/2400], Temp: 0.5995, Energy: -85.023201-0.001022j
[2025-07-30 18:07:40] [Iter 3299/4650] R4[1048/2400], Temp: 0.5988, Energy: -85.007085+0.001519j
[2025-07-30 18:07:55] [Iter 3300/4650] R4[1049/2400], Temp: 0.5982, Energy: -85.074096-0.015413j
[2025-07-30 18:08:10] [Iter 3301/4650] R4[1050/2400], Temp: 0.5975, Energy: -85.080707+0.003657j
[2025-07-30 18:08:24] [Iter 3302/4650] R4[1051/2400], Temp: 0.5969, Energy: -85.135562-0.009953j
[2025-07-30 18:08:38] [Iter 3303/4650] R4[1052/2400], Temp: 0.5963, Energy: -85.013899-0.004425j
[2025-07-30 18:08:53] [Iter 3304/4650] R4[1053/2400], Temp: 0.5956, Energy: -85.036229-0.003598j
[2025-07-30 18:09:08] [Iter 3305/4650] R4[1054/2400], Temp: 0.5950, Energy: -85.026802-0.006224j
[2025-07-30 18:09:23] [Iter 3306/4650] R4[1055/2400], Temp: 0.5943, Energy: -84.991881+0.003767j
[2025-07-30 18:09:37] [Iter 3307/4650] R4[1056/2400], Temp: 0.5937, Energy: -85.028956+0.002544j
[2025-07-30 18:09:52] [Iter 3308/4650] R4[1057/2400], Temp: 0.5930, Energy: -85.009402-0.005237j
[2025-07-30 18:10:07] [Iter 3309/4650] R4[1058/2400], Temp: 0.5924, Energy: -85.081444-0.000368j
[2025-07-30 18:10:21] [Iter 3310/4650] R4[1059/2400], Temp: 0.5918, Energy: -85.081891-0.002928j
[2025-07-30 18:10:36] [Iter 3311/4650] R4[1060/2400], Temp: 0.5911, Energy: -85.072722-0.006039j
[2025-07-30 18:10:51] [Iter 3312/4650] R4[1061/2400], Temp: 0.5905, Energy: -85.075658-0.009710j
[2025-07-30 18:11:05] [Iter 3313/4650] R4[1062/2400], Temp: 0.5898, Energy: -85.026228+0.000028j
[2025-07-30 18:11:20] [Iter 3314/4650] R4[1063/2400], Temp: 0.5892, Energy: -84.952476+0.003592j
[2025-07-30 18:11:35] [Iter 3315/4650] R4[1064/2400], Temp: 0.5885, Energy: -84.969306-0.000451j
[2025-07-30 18:11:50] [Iter 3316/4650] R4[1065/2400], Temp: 0.5879, Energy: -84.928184-0.012908j
[2025-07-30 18:12:04] [Iter 3317/4650] R4[1066/2400], Temp: 0.5873, Energy: -84.967257+0.004000j
[2025-07-30 18:12:19] [Iter 3318/4650] R4[1067/2400], Temp: 0.5866, Energy: -84.961481-0.000756j
[2025-07-30 18:12:34] [Iter 3319/4650] R4[1068/2400], Temp: 0.5860, Energy: -85.008747-0.000580j
[2025-07-30 18:12:49] [Iter 3320/4650] R4[1069/2400], Temp: 0.5853, Energy: -84.981148+0.000797j
[2025-07-30 18:13:04] [Iter 3321/4650] R4[1070/2400], Temp: 0.5847, Energy: -85.041134-0.011142j
[2025-07-30 18:13:19] [Iter 3322/4650] R4[1071/2400], Temp: 0.5840, Energy: -85.047978-0.005897j
[2025-07-30 18:13:33] [Iter 3323/4650] R4[1072/2400], Temp: 0.5834, Energy: -85.049682-0.005203j
[2025-07-30 18:13:48] [Iter 3324/4650] R4[1073/2400], Temp: 0.5827, Energy: -85.029613+0.006485j
[2025-07-30 18:14:03] [Iter 3325/4650] R4[1074/2400], Temp: 0.5821, Energy: -85.086209+0.000219j
[2025-07-30 18:14:18] [Iter 3326/4650] R4[1075/2400], Temp: 0.5814, Energy: -85.113426+0.006701j
[2025-07-30 18:14:32] [Iter 3327/4650] R4[1076/2400], Temp: 0.5808, Energy: -85.005717-0.010668j
[2025-07-30 18:14:46] [Iter 3328/4650] R4[1077/2400], Temp: 0.5802, Energy: -85.010595+0.004438j
[2025-07-30 18:15:01] [Iter 3329/4650] R4[1078/2400], Temp: 0.5795, Energy: -84.998803-0.004616j
[2025-07-30 18:15:16] [Iter 3330/4650] R4[1079/2400], Temp: 0.5789, Energy: -85.086824-0.005979j
[2025-07-30 18:15:30] [Iter 3331/4650] R4[1080/2400], Temp: 0.5782, Energy: -85.048428-0.003612j
[2025-07-30 18:15:45] [Iter 3332/4650] R4[1081/2400], Temp: 0.5776, Energy: -84.990175+0.000271j
[2025-07-30 18:15:59] [Iter 3333/4650] R4[1082/2400], Temp: 0.5769, Energy: -84.990389-0.006858j
[2025-07-30 18:16:14] [Iter 3334/4650] R4[1083/2400], Temp: 0.5763, Energy: -84.954560+0.005848j
[2025-07-30 18:16:28] [Iter 3335/4650] R4[1084/2400], Temp: 0.5756, Energy: -84.977805+0.011192j
[2025-07-30 18:16:43] [Iter 3336/4650] R4[1085/2400], Temp: 0.5750, Energy: -84.987127+0.007207j
[2025-07-30 18:16:58] [Iter 3337/4650] R4[1086/2400], Temp: 0.5743, Energy: -85.012912+0.005966j
[2025-07-30 18:17:12] [Iter 3338/4650] R4[1087/2400], Temp: 0.5737, Energy: -84.920557+0.001701j
[2025-07-30 18:17:27] [Iter 3339/4650] R4[1088/2400], Temp: 0.5730, Energy: -84.970224+0.002522j
[2025-07-30 18:17:42] [Iter 3340/4650] R4[1089/2400], Temp: 0.5724, Energy: -84.999046+0.003305j
[2025-07-30 18:17:57] [Iter 3341/4650] R4[1090/2400], Temp: 0.5717, Energy: -84.955776-0.001514j
[2025-07-30 18:18:12] [Iter 3342/4650] R4[1091/2400], Temp: 0.5711, Energy: -84.910717+0.007858j
[2025-07-30 18:18:27] [Iter 3343/4650] R4[1092/2400], Temp: 0.5705, Energy: -84.951143+0.006634j
[2025-07-30 18:18:42] [Iter 3344/4650] R4[1093/2400], Temp: 0.5698, Energy: -84.918548+0.009227j
[2025-07-30 18:18:56] [Iter 3345/4650] R4[1094/2400], Temp: 0.5692, Energy: -84.952818+0.003988j
[2025-07-30 18:19:11] [Iter 3346/4650] R4[1095/2400], Temp: 0.5685, Energy: -84.881140+0.008498j
[2025-07-30 18:19:26] [Iter 3347/4650] R4[1096/2400], Temp: 0.5679, Energy: -84.880296+0.006139j
[2025-07-30 18:19:41] [Iter 3348/4650] R4[1097/2400], Temp: 0.5672, Energy: -84.850235+0.002730j
[2025-07-30 18:19:55] [Iter 3349/4650] R4[1098/2400], Temp: 0.5666, Energy: -85.010931+0.005392j
[2025-07-30 18:20:10] [Iter 3350/4650] R4[1099/2400], Temp: 0.5659, Energy: -85.012412-0.004970j
[2025-07-30 18:20:24] [Iter 3351/4650] R4[1100/2400], Temp: 0.5653, Energy: -85.013337+0.003510j
[2025-07-30 18:20:39] [Iter 3352/4650] R4[1101/2400], Temp: 0.5646, Energy: -85.084121-0.002445j
[2025-07-30 18:20:53] [Iter 3353/4650] R4[1102/2400], Temp: 0.5640, Energy: -84.984006+0.006529j
[2025-07-30 18:21:08] [Iter 3354/4650] R4[1103/2400], Temp: 0.5633, Energy: -85.017959-0.007915j
[2025-07-30 18:21:23] [Iter 3355/4650] R4[1104/2400], Temp: 0.5627, Energy: -85.010895-0.008842j
[2025-07-30 18:21:37] [Iter 3356/4650] R4[1105/2400], Temp: 0.5620, Energy: -85.014963+0.003861j
[2025-07-30 18:21:52] [Iter 3357/4650] R4[1106/2400], Temp: 0.5614, Energy: -85.022658-0.002613j
[2025-07-30 18:22:07] [Iter 3358/4650] R4[1107/2400], Temp: 0.5607, Energy: -85.033163+0.007192j
[2025-07-30 18:22:21] [Iter 3359/4650] R4[1108/2400], Temp: 0.5601, Energy: -85.029570-0.004618j
[2025-07-30 18:22:36] [Iter 3360/4650] R4[1109/2400], Temp: 0.5594, Energy: -84.936664+0.005943j
[2025-07-30 18:22:51] [Iter 3361/4650] R4[1110/2400], Temp: 0.5588, Energy: -84.906993+0.015333j
[2025-07-30 18:23:05] [Iter 3362/4650] R4[1111/2400], Temp: 0.5581, Energy: -84.957585+0.005722j
[2025-07-30 18:23:20] [Iter 3363/4650] R4[1112/2400], Temp: 0.5575, Energy: -85.040468-0.003006j
[2025-07-30 18:23:35] [Iter 3364/4650] R4[1113/2400], Temp: 0.5568, Energy: -85.049038+0.002665j
[2025-07-30 18:23:50] [Iter 3365/4650] R4[1114/2400], Temp: 0.5562, Energy: -85.024037-0.000773j
[2025-07-30 18:24:04] [Iter 3366/4650] R4[1115/2400], Temp: 0.5555, Energy: -85.049119-0.001127j
[2025-07-30 18:24:19] [Iter 3367/4650] R4[1116/2400], Temp: 0.5549, Energy: -85.020371+0.004806j
[2025-07-30 18:24:34] [Iter 3368/4650] R4[1117/2400], Temp: 0.5542, Energy: -85.056187+0.000578j
[2025-07-30 18:24:48] [Iter 3369/4650] R4[1118/2400], Temp: 0.5536, Energy: -85.070670-0.003662j
[2025-07-30 18:25:03] [Iter 3370/4650] R4[1119/2400], Temp: 0.5529, Energy: -85.069529+0.002088j
[2025-07-30 18:25:18] [Iter 3371/4650] R4[1120/2400], Temp: 0.5523, Energy: -85.027914-0.004146j
[2025-07-30 18:25:33] [Iter 3372/4650] R4[1121/2400], Temp: 0.5516, Energy: -85.004648+0.003708j
[2025-07-30 18:25:48] [Iter 3373/4650] R4[1122/2400], Temp: 0.5510, Energy: -85.053465+0.003867j
[2025-07-30 18:26:03] [Iter 3374/4650] R4[1123/2400], Temp: 0.5503, Energy: -85.095804+0.001673j
[2025-07-30 18:26:17] [Iter 3375/4650] R4[1124/2400], Temp: 0.5497, Energy: -85.018379-0.005482j
[2025-07-30 18:26:32] [Iter 3376/4650] R4[1125/2400], Temp: 0.5490, Energy: -85.022489+0.001231j
[2025-07-30 18:26:47] [Iter 3377/4650] R4[1126/2400], Temp: 0.5484, Energy: -84.999270-0.001349j
[2025-07-30 18:27:01] [Iter 3378/4650] R4[1127/2400], Temp: 0.5477, Energy: -85.015705+0.007018j
[2025-07-30 18:27:16] [Iter 3379/4650] R4[1128/2400], Temp: 0.5471, Energy: -84.954238+0.002585j
[2025-07-30 18:27:31] [Iter 3380/4650] R4[1129/2400], Temp: 0.5464, Energy: -85.005454-0.004543j
[2025-07-30 18:27:45] [Iter 3381/4650] R4[1130/2400], Temp: 0.5458, Energy: -84.989202-0.002149j
[2025-07-30 18:28:00] [Iter 3382/4650] R4[1131/2400], Temp: 0.5451, Energy: -85.030835-0.002899j
[2025-07-30 18:28:15] [Iter 3383/4650] R4[1132/2400], Temp: 0.5444, Energy: -85.041966-0.000620j
[2025-07-30 18:28:30] [Iter 3384/4650] R4[1133/2400], Temp: 0.5438, Energy: -85.018171-0.002889j
[2025-07-30 18:28:45] [Iter 3385/4650] R4[1134/2400], Temp: 0.5431, Energy: -85.039042+0.001073j
[2025-07-30 18:28:59] [Iter 3386/4650] R4[1135/2400], Temp: 0.5425, Energy: -85.131178+0.003262j
[2025-07-30 18:29:13] [Iter 3387/4650] R4[1136/2400], Temp: 0.5418, Energy: -85.153278+0.000893j
[2025-07-30 18:29:28] [Iter 3388/4650] R4[1137/2400], Temp: 0.5412, Energy: -85.107512-0.000197j
[2025-07-30 18:29:42] [Iter 3389/4650] R4[1138/2400], Temp: 0.5405, Energy: -85.014877+0.006344j
[2025-07-30 18:29:57] [Iter 3390/4650] R4[1139/2400], Temp: 0.5399, Energy: -85.065435+0.001315j
[2025-07-30 18:30:12] [Iter 3391/4650] R4[1140/2400], Temp: 0.5392, Energy: -84.987611+0.001257j
[2025-07-30 18:30:27] [Iter 3392/4650] R4[1141/2400], Temp: 0.5386, Energy: -85.025144-0.009187j
[2025-07-30 18:30:42] [Iter 3393/4650] R4[1142/2400], Temp: 0.5379, Energy: -84.945283-0.000717j
[2025-07-30 18:30:57] [Iter 3394/4650] R4[1143/2400], Temp: 0.5373, Energy: -85.036358-0.005704j
[2025-07-30 18:31:12] [Iter 3395/4650] R4[1144/2400], Temp: 0.5366, Energy: -84.980099-0.012294j
[2025-07-30 18:31:27] [Iter 3396/4650] R4[1145/2400], Temp: 0.5360, Energy: -84.943090-0.013231j
[2025-07-30 18:31:41] [Iter 3397/4650] R4[1146/2400], Temp: 0.5353, Energy: -85.014511-0.008012j
[2025-07-30 18:31:56] [Iter 3398/4650] R4[1147/2400], Temp: 0.5347, Energy: -85.028286-0.004727j
[2025-07-30 18:32:11] [Iter 3399/4650] R4[1148/2400], Temp: 0.5340, Energy: -85.075130+0.000250j
[2025-07-30 18:32:25] [Iter 3400/4650] R4[1149/2400], Temp: 0.5334, Energy: -85.110088+0.000266j
[2025-07-30 18:32:40] [Iter 3401/4650] R4[1150/2400], Temp: 0.5327, Energy: -85.010708-0.007209j
[2025-07-30 18:32:55] [Iter 3402/4650] R4[1151/2400], Temp: 0.5320, Energy: -85.057557-0.000262j
[2025-07-30 18:33:10] [Iter 3403/4650] R4[1152/2400], Temp: 0.5314, Energy: -85.044506+0.000990j
[2025-07-30 18:33:25] [Iter 3404/4650] R4[1153/2400], Temp: 0.5307, Energy: -85.136437-0.003200j
[2025-07-30 18:33:40] [Iter 3405/4650] R4[1154/2400], Temp: 0.5301, Energy: -85.038620+0.016371j
[2025-07-30 18:33:55] [Iter 3406/4650] R4[1155/2400], Temp: 0.5294, Energy: -84.999526-0.003911j
[2025-07-30 18:34:09] [Iter 3407/4650] R4[1156/2400], Temp: 0.5288, Energy: -85.069967+0.011411j
[2025-07-30 18:34:24] [Iter 3408/4650] R4[1157/2400], Temp: 0.5281, Energy: -85.036187+0.013505j
[2025-07-30 18:34:38] [Iter 3409/4650] R4[1158/2400], Temp: 0.5275, Energy: -85.000334-0.005468j
[2025-07-30 18:34:53] [Iter 3410/4650] R4[1159/2400], Temp: 0.5268, Energy: -85.012924+0.002665j
[2025-07-30 18:35:08] [Iter 3411/4650] R4[1160/2400], Temp: 0.5262, Energy: -84.976089+0.005708j
[2025-07-30 18:35:23] [Iter 3412/4650] R4[1161/2400], Temp: 0.5255, Energy: -84.988135+0.001149j
[2025-07-30 18:35:37] [Iter 3413/4650] R4[1162/2400], Temp: 0.5249, Energy: -85.054390-0.008449j
[2025-07-30 18:35:52] [Iter 3414/4650] R4[1163/2400], Temp: 0.5242, Energy: -85.129946+0.003288j
[2025-07-30 18:36:06] [Iter 3415/4650] R4[1164/2400], Temp: 0.5236, Energy: -85.068385+0.004330j
[2025-07-30 18:36:21] [Iter 3416/4650] R4[1165/2400], Temp: 0.5229, Energy: -85.051627+0.010481j
[2025-07-30 18:36:35] [Iter 3417/4650] R4[1166/2400], Temp: 0.5222, Energy: -85.114246+0.005542j
[2025-07-30 18:36:50] [Iter 3418/4650] R4[1167/2400], Temp: 0.5216, Energy: -85.094472-0.001388j
[2025-07-30 18:37:05] [Iter 3419/4650] R4[1168/2400], Temp: 0.5209, Energy: -85.071586+0.005986j
[2025-07-30 18:37:20] [Iter 3420/4650] R4[1169/2400], Temp: 0.5203, Energy: -85.068802+0.008498j
[2025-07-30 18:37:34] [Iter 3421/4650] R4[1170/2400], Temp: 0.5196, Energy: -85.076209+0.025228j
[2025-07-30 18:37:48] [Iter 3422/4650] R4[1171/2400], Temp: 0.5190, Energy: -85.115351+0.004594j
[2025-07-30 18:38:03] [Iter 3423/4650] R4[1172/2400], Temp: 0.5183, Energy: -85.062991+0.000774j
[2025-07-30 18:38:18] [Iter 3424/4650] R4[1173/2400], Temp: 0.5177, Energy: -85.093540+0.016290j
[2025-07-30 18:38:32] [Iter 3425/4650] R4[1174/2400], Temp: 0.5170, Energy: -85.089250+0.023319j
[2025-07-30 18:38:47] [Iter 3426/4650] R4[1175/2400], Temp: 0.5164, Energy: -85.062350+0.018261j
[2025-07-30 18:39:02] [Iter 3427/4650] R4[1176/2400], Temp: 0.5157, Energy: -85.099309-0.000620j
[2025-07-30 18:39:16] [Iter 3428/4650] R4[1177/2400], Temp: 0.5151, Energy: -85.116947+0.007011j
[2025-07-30 18:39:31] [Iter 3429/4650] R4[1178/2400], Temp: 0.5144, Energy: -85.126212-0.009789j
[2025-07-30 18:39:46] [Iter 3430/4650] R4[1179/2400], Temp: 0.5137, Energy: -85.126278+0.011682j
[2025-07-30 18:40:01] [Iter 3431/4650] R4[1180/2400], Temp: 0.5131, Energy: -85.169029-0.001720j
[2025-07-30 18:40:16] [Iter 3432/4650] R4[1181/2400], Temp: 0.5124, Energy: -85.139837-0.002171j
[2025-07-30 18:40:31] [Iter 3433/4650] R4[1182/2400], Temp: 0.5118, Energy: -85.063606+0.005232j
[2025-07-30 18:40:45] [Iter 3434/4650] R4[1183/2400], Temp: 0.5111, Energy: -85.014208+0.002141j
[2025-07-30 18:41:00] [Iter 3435/4650] R4[1184/2400], Temp: 0.5105, Energy: -85.020062+0.001003j
[2025-07-30 18:41:15] [Iter 3436/4650] R4[1185/2400], Temp: 0.5098, Energy: -85.148729-0.002700j
[2025-07-30 18:41:29] [Iter 3437/4650] R4[1186/2400], Temp: 0.5092, Energy: -85.162282-0.001370j
[2025-07-30 18:41:44] [Iter 3438/4650] R4[1187/2400], Temp: 0.5085, Energy: -85.126935-0.004495j
[2025-07-30 18:41:59] [Iter 3439/4650] R4[1188/2400], Temp: 0.5079, Energy: -85.083347+0.001284j
[2025-07-30 18:42:14] [Iter 3440/4650] R4[1189/2400], Temp: 0.5072, Energy: -85.088331-0.000879j
[2025-07-30 18:42:28] [Iter 3441/4650] R4[1190/2400], Temp: 0.5065, Energy: -85.069090+0.004204j
[2025-07-30 18:42:43] [Iter 3442/4650] R4[1191/2400], Temp: 0.5059, Energy: -85.046339-0.006837j
[2025-07-30 18:42:58] [Iter 3443/4650] R4[1192/2400], Temp: 0.5052, Energy: -85.052843+0.000899j
[2025-07-30 18:43:13] [Iter 3444/4650] R4[1193/2400], Temp: 0.5046, Energy: -84.978632-0.008347j
[2025-07-30 18:43:28] [Iter 3445/4650] R4[1194/2400], Temp: 0.5039, Energy: -85.032495-0.002112j
[2025-07-30 18:43:43] [Iter 3446/4650] R4[1195/2400], Temp: 0.5033, Energy: -85.077222-0.004914j
[2025-07-30 18:43:57] [Iter 3447/4650] R4[1196/2400], Temp: 0.5026, Energy: -85.091406-0.004312j
[2025-07-30 18:44:12] [Iter 3448/4650] R4[1197/2400], Temp: 0.5020, Energy: -85.087166-0.007892j
[2025-07-30 18:44:27] [Iter 3449/4650] R4[1198/2400], Temp: 0.5013, Energy: -85.022658-0.004002j
[2025-07-30 18:44:42] [Iter 3450/4650] R4[1199/2400], Temp: 0.5007, Energy: -85.111695-0.000393j
[2025-07-30 18:44:57] [Iter 3451/4650] R4[1200/2400], Temp: 0.5000, Energy: -85.042701-0.003218j
[2025-07-30 18:45:11] [Iter 3452/4650] R4[1201/2400], Temp: 0.4993, Energy: -85.022997-0.004867j
[2025-07-30 18:45:26] [Iter 3453/4650] R4[1202/2400], Temp: 0.4987, Energy: -85.031338+0.001448j
[2025-07-30 18:45:41] [Iter 3454/4650] R4[1203/2400], Temp: 0.4980, Energy: -85.018082+0.000084j
[2025-07-30 18:45:55] [Iter 3455/4650] R4[1204/2400], Temp: 0.4974, Energy: -85.011192+0.001496j
[2025-07-30 18:46:10] [Iter 3456/4650] R4[1205/2400], Temp: 0.4967, Energy: -85.033345+0.002608j
[2025-07-30 18:46:25] [Iter 3457/4650] R4[1206/2400], Temp: 0.4961, Energy: -84.930140-0.001043j
[2025-07-30 18:46:40] [Iter 3458/4650] R4[1207/2400], Temp: 0.4954, Energy: -85.015892-0.001511j
[2025-07-30 18:46:55] [Iter 3459/4650] R4[1208/2400], Temp: 0.4948, Energy: -85.040751+0.001681j
[2025-07-30 18:47:09] [Iter 3460/4650] R4[1209/2400], Temp: 0.4941, Energy: -84.968704-0.001461j
[2025-07-30 18:47:24] [Iter 3461/4650] R4[1210/2400], Temp: 0.4935, Energy: -84.936386-0.005736j
[2025-07-30 18:47:39] [Iter 3462/4650] R4[1211/2400], Temp: 0.4928, Energy: -85.011478-0.002655j
[2025-07-30 18:47:53] [Iter 3463/4650] R4[1212/2400], Temp: 0.4921, Energy: -85.039862-0.003410j
[2025-07-30 18:48:08] [Iter 3464/4650] R4[1213/2400], Temp: 0.4915, Energy: -85.048373+0.000039j
[2025-07-30 18:48:23] [Iter 3465/4650] R4[1214/2400], Temp: 0.4908, Energy: -85.007157+0.001927j
[2025-07-30 18:48:37] [Iter 3466/4650] R4[1215/2400], Temp: 0.4902, Energy: -85.031366-0.003346j
[2025-07-30 18:48:52] [Iter 3467/4650] R4[1216/2400], Temp: 0.4895, Energy: -84.964258+0.002682j
[2025-07-30 18:49:07] [Iter 3468/4650] R4[1217/2400], Temp: 0.4889, Energy: -85.043861+0.005163j
[2025-07-30 18:49:21] [Iter 3469/4650] R4[1218/2400], Temp: 0.4882, Energy: -85.065129-0.001995j
[2025-07-30 18:49:36] [Iter 3470/4650] R4[1219/2400], Temp: 0.4876, Energy: -85.075794+0.000048j
[2025-07-30 18:49:51] [Iter 3471/4650] R4[1220/2400], Temp: 0.4869, Energy: -85.023533+0.003472j
[2025-07-30 18:50:06] [Iter 3472/4650] R4[1221/2400], Temp: 0.4863, Energy: -85.097080+0.001592j
[2025-07-30 18:50:21] [Iter 3473/4650] R4[1222/2400], Temp: 0.4856, Energy: -85.038735+0.009557j
[2025-07-30 18:50:36] [Iter 3474/4650] R4[1223/2400], Temp: 0.4849, Energy: -85.050490+0.001900j
[2025-07-30 18:50:50] [Iter 3475/4650] R4[1224/2400], Temp: 0.4843, Energy: -85.096045-0.008204j
[2025-07-30 18:51:05] [Iter 3476/4650] R4[1225/2400], Temp: 0.4836, Energy: -85.107156-0.000616j
[2025-07-30 18:51:20] [Iter 3477/4650] R4[1226/2400], Temp: 0.4830, Energy: -85.036452+0.002260j
[2025-07-30 18:51:35] [Iter 3478/4650] R4[1227/2400], Temp: 0.4823, Energy: -85.102943+0.007858j
[2025-07-30 18:51:49] [Iter 3479/4650] R4[1228/2400], Temp: 0.4817, Energy: -85.013182+0.003178j
[2025-07-30 18:52:04] [Iter 3480/4650] R4[1229/2400], Temp: 0.4810, Energy: -85.076430-0.000139j
[2025-07-30 18:52:19] [Iter 3481/4650] R4[1230/2400], Temp: 0.4804, Energy: -85.067368-0.000163j
[2025-07-30 18:52:33] [Iter 3482/4650] R4[1231/2400], Temp: 0.4797, Energy: -85.077905+0.003918j
[2025-07-30 18:52:48] [Iter 3483/4650] R4[1232/2400], Temp: 0.4791, Energy: -85.081039-0.000667j
[2025-07-30 18:53:03] [Iter 3484/4650] R4[1233/2400], Temp: 0.4784, Energy: -85.075098-0.008596j
[2025-07-30 18:53:18] [Iter 3485/4650] R4[1234/2400], Temp: 0.4778, Energy: -85.036867-0.000060j
[2025-07-30 18:53:32] [Iter 3486/4650] R4[1235/2400], Temp: 0.4771, Energy: -85.011480-0.004221j
[2025-07-30 18:53:47] [Iter 3487/4650] R4[1236/2400], Temp: 0.4764, Energy: -84.989824+0.004970j
[2025-07-30 18:54:02] [Iter 3488/4650] R4[1237/2400], Temp: 0.4758, Energy: -85.003958-0.007982j
[2025-07-30 18:54:17] [Iter 3489/4650] R4[1238/2400], Temp: 0.4751, Energy: -85.039237+0.002666j
[2025-07-30 18:54:32] [Iter 3490/4650] R4[1239/2400], Temp: 0.4745, Energy: -85.016174-0.015880j
[2025-07-30 18:54:46] [Iter 3491/4650] R4[1240/2400], Temp: 0.4738, Energy: -85.004314-0.010092j
[2025-07-30 18:55:01] [Iter 3492/4650] R4[1241/2400], Temp: 0.4732, Energy: -84.994802-0.007060j
[2025-07-30 18:55:15] [Iter 3493/4650] R4[1242/2400], Temp: 0.4725, Energy: -84.976390+0.002144j
[2025-07-30 18:55:30] [Iter 3494/4650] R4[1243/2400], Temp: 0.4719, Energy: -85.018036+0.006127j
[2025-07-30 18:55:45] [Iter 3495/4650] R4[1244/2400], Temp: 0.4712, Energy: -84.950097-0.008701j
[2025-07-30 18:55:59] [Iter 3496/4650] R4[1245/2400], Temp: 0.4706, Energy: -84.971219+0.001853j
[2025-07-30 18:56:14] [Iter 3497/4650] R4[1246/2400], Temp: 0.4699, Energy: -84.881190-0.012390j
[2025-07-30 18:56:28] [Iter 3498/4650] R4[1247/2400], Temp: 0.4693, Energy: -84.934975-0.005414j
[2025-07-30 18:56:43] [Iter 3499/4650] R4[1248/2400], Temp: 0.4686, Energy: -84.964081-0.010334j
[2025-07-30 18:56:58] [Iter 3500/4650] R4[1249/2400], Temp: 0.4680, Energy: -85.027783-0.005128j
[2025-07-30 18:56:58] ✓ Checkpoint saved: checkpoint_iter_003500.pkl
[2025-07-30 18:57:12] [Iter 3501/4650] R4[1250/2400], Temp: 0.4673, Energy: -85.010667-0.002158j
[2025-07-30 18:57:27] [Iter 3502/4650] R4[1251/2400], Temp: 0.4666, Energy: -85.019808-0.000409j
[2025-07-30 18:57:42] [Iter 3503/4650] R4[1252/2400], Temp: 0.4660, Energy: -85.034167+0.010287j
[2025-07-30 18:57:57] [Iter 3504/4650] R4[1253/2400], Temp: 0.4653, Energy: -85.042201-0.006109j
[2025-07-30 18:58:11] [Iter 3505/4650] R4[1254/2400], Temp: 0.4647, Energy: -85.101632+0.007217j
[2025-07-30 18:58:26] [Iter 3506/4650] R4[1255/2400], Temp: 0.4640, Energy: -85.059660-0.007042j
[2025-07-30 18:58:42] [Iter 3507/4650] R4[1256/2400], Temp: 0.4634, Energy: -85.106508-0.006053j
[2025-07-30 18:58:56] [Iter 3508/4650] R4[1257/2400], Temp: 0.4627, Energy: -85.069076+0.002868j
[2025-07-30 18:59:11] [Iter 3509/4650] R4[1258/2400], Temp: 0.4621, Energy: -85.089998-0.008684j
[2025-07-30 18:59:26] [Iter 3510/4650] R4[1259/2400], Temp: 0.4614, Energy: -84.998154-0.011592j
[2025-07-30 18:59:41] [Iter 3511/4650] R4[1260/2400], Temp: 0.4608, Energy: -85.043500+0.008871j
[2025-07-30 18:59:55] [Iter 3512/4650] R4[1261/2400], Temp: 0.4601, Energy: -85.065905+0.006042j
[2025-07-30 19:00:10] [Iter 3513/4650] R4[1262/2400], Temp: 0.4595, Energy: -85.111879+0.000499j
[2025-07-30 19:00:25] [Iter 3514/4650] R4[1263/2400], Temp: 0.4588, Energy: -85.077455-0.003033j
[2025-07-30 19:00:40] [Iter 3515/4650] R4[1264/2400], Temp: 0.4582, Energy: -85.044803-0.000907j
[2025-07-30 19:00:55] [Iter 3516/4650] R4[1265/2400], Temp: 0.4575, Energy: -85.050978+0.007122j
[2025-07-30 19:01:10] [Iter 3517/4650] R4[1266/2400], Temp: 0.4569, Energy: -85.047870+0.003569j
[2025-07-30 19:01:25] [Iter 3518/4650] R4[1267/2400], Temp: 0.4562, Energy: -85.031474+0.003293j
[2025-07-30 19:01:39] [Iter 3519/4650] R4[1268/2400], Temp: 0.4556, Energy: -85.012909+0.004273j
[2025-07-30 19:01:54] [Iter 3520/4650] R4[1269/2400], Temp: 0.4549, Energy: -85.077131-0.001180j
[2025-07-30 19:02:09] [Iter 3521/4650] R4[1270/2400], Temp: 0.4542, Energy: -85.061752+0.005279j
[2025-07-30 19:02:24] [Iter 3522/4650] R4[1271/2400], Temp: 0.4536, Energy: -84.956606+0.003084j
[2025-07-30 19:02:38] [Iter 3523/4650] R4[1272/2400], Temp: 0.4529, Energy: -84.950261+0.000716j
[2025-07-30 19:02:53] [Iter 3524/4650] R4[1273/2400], Temp: 0.4523, Energy: -85.010035+0.002999j
[2025-07-30 19:03:08] [Iter 3525/4650] R4[1274/2400], Temp: 0.4516, Energy: -84.933550+0.002253j
[2025-07-30 19:03:23] [Iter 3526/4650] R4[1275/2400], Temp: 0.4510, Energy: -84.947622-0.002086j
[2025-07-30 19:03:38] [Iter 3527/4650] R4[1276/2400], Temp: 0.4503, Energy: -84.941945-0.001824j
[2025-07-30 19:03:52] [Iter 3528/4650] R4[1277/2400], Temp: 0.4497, Energy: -84.946666+0.004274j
[2025-07-30 19:04:07] [Iter 3529/4650] R4[1278/2400], Temp: 0.4490, Energy: -84.988426+0.002257j
[2025-07-30 19:04:22] [Iter 3530/4650] R4[1279/2400], Temp: 0.4484, Energy: -85.014979-0.000786j
[2025-07-30 19:04:37] [Iter 3531/4650] R4[1280/2400], Temp: 0.4477, Energy: -85.028212-0.003466j
[2025-07-30 19:04:51] [Iter 3532/4650] R4[1281/2400], Temp: 0.4471, Energy: -84.982872-0.001090j
[2025-07-30 19:05:06] [Iter 3533/4650] R4[1282/2400], Temp: 0.4464, Energy: -84.967097+0.000740j
[2025-07-30 19:05:20] [Iter 3534/4650] R4[1283/2400], Temp: 0.4458, Energy: -85.003416+0.001724j
[2025-07-30 19:05:35] [Iter 3535/4650] R4[1284/2400], Temp: 0.4451, Energy: -85.020790-0.001956j
[2025-07-30 19:05:49] [Iter 3536/4650] R4[1285/2400], Temp: 0.4445, Energy: -84.955595-0.001193j
[2025-07-30 19:06:04] [Iter 3537/4650] R4[1286/2400], Temp: 0.4438, Energy: -85.008236-0.007994j
[2025-07-30 19:06:18] [Iter 3538/4650] R4[1287/2400], Temp: 0.4432, Energy: -85.014348+0.002900j
[2025-07-30 19:06:33] [Iter 3539/4650] R4[1288/2400], Temp: 0.4425, Energy: -84.978647-0.001362j
[2025-07-30 19:06:48] [Iter 3540/4650] R4[1289/2400], Temp: 0.4419, Energy: -85.007326+0.003053j
[2025-07-30 19:07:03] [Iter 3541/4650] R4[1290/2400], Temp: 0.4412, Energy: -84.969794-0.002414j
[2025-07-30 19:07:18] [Iter 3542/4650] R4[1291/2400], Temp: 0.4406, Energy: -85.006889+0.000763j
[2025-07-30 19:07:32] [Iter 3543/4650] R4[1292/2400], Temp: 0.4399, Energy: -85.001167+0.001712j
[2025-07-30 19:07:47] [Iter 3544/4650] R4[1293/2400], Temp: 0.4393, Energy: -85.019507-0.007597j
[2025-07-30 19:08:01] [Iter 3545/4650] R4[1294/2400], Temp: 0.4386, Energy: -85.003788-0.004079j
[2025-07-30 19:08:16] [Iter 3546/4650] R4[1295/2400], Temp: 0.4380, Energy: -85.059272+0.009306j
[2025-07-30 19:08:31] [Iter 3547/4650] R4[1296/2400], Temp: 0.4373, Energy: -85.011446+0.004922j
[2025-07-30 19:08:46] [Iter 3548/4650] R4[1297/2400], Temp: 0.4367, Energy: -85.010881+0.008659j
[2025-07-30 19:09:01] [Iter 3549/4650] R4[1298/2400], Temp: 0.4360, Energy: -84.903394+0.005523j
[2025-07-30 19:09:16] [Iter 3550/4650] R4[1299/2400], Temp: 0.4354, Energy: -84.930763+0.007531j
[2025-07-30 19:09:31] [Iter 3551/4650] R4[1300/2400], Temp: 0.4347, Energy: -84.983282+0.004711j
[2025-07-30 19:09:46] [Iter 3552/4650] R4[1301/2400], Temp: 0.4341, Energy: -84.992262+0.005001j
[2025-07-30 19:10:00] [Iter 3553/4650] R4[1302/2400], Temp: 0.4334, Energy: -85.002694+0.003694j
[2025-07-30 19:10:14] [Iter 3554/4650] R4[1303/2400], Temp: 0.4328, Energy: -85.032359+0.008031j
[2025-07-30 19:10:29] [Iter 3555/4650] R4[1304/2400], Temp: 0.4321, Energy: -85.033716+0.003281j
[2025-07-30 19:10:44] [Iter 3556/4650] R4[1305/2400], Temp: 0.4315, Energy: -85.062207-0.004225j
[2025-07-30 19:10:59] [Iter 3557/4650] R4[1306/2400], Temp: 0.4308, Energy: -85.089396+0.008051j
[2025-07-30 19:11:14] [Iter 3558/4650] R4[1307/2400], Temp: 0.4302, Energy: -85.091112-0.003668j
[2025-07-30 19:11:29] [Iter 3559/4650] R4[1308/2400], Temp: 0.4295, Energy: -85.102763+0.001734j
[2025-07-30 19:11:44] [Iter 3560/4650] R4[1309/2400], Temp: 0.4289, Energy: -85.039030+0.002516j
[2025-07-30 19:11:59] [Iter 3561/4650] R4[1310/2400], Temp: 0.4283, Energy: -85.019236+0.012931j
[2025-07-30 19:12:13] [Iter 3562/4650] R4[1311/2400], Temp: 0.4276, Energy: -85.034332-0.003833j
[2025-07-30 19:12:28] [Iter 3563/4650] R4[1312/2400], Temp: 0.4270, Energy: -85.011777-0.005400j
[2025-07-30 19:12:43] [Iter 3564/4650] R4[1313/2400], Temp: 0.4263, Energy: -85.070025-0.003397j
[2025-07-30 19:12:57] [Iter 3565/4650] R4[1314/2400], Temp: 0.4257, Energy: -85.087100-0.009337j
[2025-07-30 19:13:12] [Iter 3566/4650] R4[1315/2400], Temp: 0.4250, Energy: -85.091704-0.010794j
[2025-07-30 19:13:27] [Iter 3567/4650] R4[1316/2400], Temp: 0.4244, Energy: -85.090737-0.008537j
[2025-07-30 19:13:41] [Iter 3568/4650] R4[1317/2400], Temp: 0.4237, Energy: -85.073963-0.007877j
[2025-07-30 19:13:56] [Iter 3569/4650] R4[1318/2400], Temp: 0.4231, Energy: -85.124621-0.000238j
[2025-07-30 19:14:11] [Iter 3570/4650] R4[1319/2400], Temp: 0.4224, Energy: -85.174535-0.001459j
[2025-07-30 19:14:26] [Iter 3571/4650] R4[1320/2400], Temp: 0.4218, Energy: -85.064752-0.006129j
[2025-07-30 19:14:40] [Iter 3572/4650] R4[1321/2400], Temp: 0.4211, Energy: -85.057384+0.007141j
[2025-07-30 19:14:55] [Iter 3573/4650] R4[1322/2400], Temp: 0.4205, Energy: -85.083597+0.002132j
[2025-07-30 19:15:10] [Iter 3574/4650] R4[1323/2400], Temp: 0.4198, Energy: -85.109860+0.003700j
[2025-07-30 19:15:25] [Iter 3575/4650] R4[1324/2400], Temp: 0.4192, Energy: -85.103083+0.010050j
[2025-07-30 19:15:39] [Iter 3576/4650] R4[1325/2400], Temp: 0.4186, Energy: -85.145841+0.003711j
[2025-07-30 19:15:54] [Iter 3577/4650] R4[1326/2400], Temp: 0.4179, Energy: -85.111264+0.010134j
[2025-07-30 19:16:09] [Iter 3578/4650] R4[1327/2400], Temp: 0.4173, Energy: -85.168405-0.004471j
[2025-07-30 19:16:24] [Iter 3579/4650] R4[1328/2400], Temp: 0.4166, Energy: -85.171355-0.001415j
[2025-07-30 19:16:39] [Iter 3580/4650] R4[1329/2400], Temp: 0.4160, Energy: -85.147316+0.005253j
[2025-07-30 19:16:53] [Iter 3581/4650] R4[1330/2400], Temp: 0.4153, Energy: -85.161630+0.004849j
[2025-07-30 19:17:08] [Iter 3582/4650] R4[1331/2400], Temp: 0.4147, Energy: -85.137659+0.005715j
[2025-07-30 19:17:23] [Iter 3583/4650] R4[1332/2400], Temp: 0.4140, Energy: -85.090759+0.009764j
[2025-07-30 19:17:38] [Iter 3584/4650] R4[1333/2400], Temp: 0.4134, Energy: -85.002296+0.006227j
[2025-07-30 19:17:52] [Iter 3585/4650] R4[1334/2400], Temp: 0.4127, Energy: -85.044417-0.005431j
[2025-07-30 19:18:06] [Iter 3586/4650] R4[1335/2400], Temp: 0.4121, Energy: -85.072833-0.003219j
[2025-07-30 19:18:21] [Iter 3587/4650] R4[1336/2400], Temp: 0.4115, Energy: -85.098453+0.011249j
[2025-07-30 19:18:36] [Iter 3588/4650] R4[1337/2400], Temp: 0.4108, Energy: -85.103404-0.004470j
[2025-07-30 19:18:51] [Iter 3589/4650] R4[1338/2400], Temp: 0.4102, Energy: -85.007208-0.003977j
[2025-07-30 19:19:06] [Iter 3590/4650] R4[1339/2400], Temp: 0.4095, Energy: -85.058176+0.000455j
[2025-07-30 19:19:21] [Iter 3591/4650] R4[1340/2400], Temp: 0.4089, Energy: -85.046244+0.003212j
[2025-07-30 19:19:36] [Iter 3592/4650] R4[1341/2400], Temp: 0.4082, Energy: -84.978304-0.001392j
[2025-07-30 19:19:51] [Iter 3593/4650] R4[1342/2400], Temp: 0.4076, Energy: -84.980591+0.018946j
[2025-07-30 19:20:05] [Iter 3594/4650] R4[1343/2400], Temp: 0.4070, Energy: -84.988029+0.014911j
[2025-07-30 19:20:19] [Iter 3595/4650] R4[1344/2400], Temp: 0.4063, Energy: -85.007398+0.010355j
[2025-07-30 19:20:33] [Iter 3596/4650] R4[1345/2400], Temp: 0.4057, Energy: -84.989476+0.003251j
[2025-07-30 19:20:48] [Iter 3597/4650] R4[1346/2400], Temp: 0.4050, Energy: -85.073672-0.001849j
[2025-07-30 19:21:03] [Iter 3598/4650] R4[1347/2400], Temp: 0.4044, Energy: -85.045974-0.001709j
[2025-07-30 19:21:18] [Iter 3599/4650] R4[1348/2400], Temp: 0.4037, Energy: -85.073305+0.003711j
[2025-07-30 19:21:32] [Iter 3600/4650] R4[1349/2400], Temp: 0.4031, Energy: -85.096321+0.003440j
[2025-07-30 19:21:47] [Iter 3601/4650] R4[1350/2400], Temp: 0.4025, Energy: -85.024930+0.004199j
[2025-07-30 19:22:02] [Iter 3602/4650] R4[1351/2400], Temp: 0.4018, Energy: -85.033924+0.002789j
[2025-07-30 19:22:16] [Iter 3603/4650] R4[1352/2400], Temp: 0.4012, Energy: -85.131447+0.004000j
[2025-07-30 19:22:31] [Iter 3604/4650] R4[1353/2400], Temp: 0.4005, Energy: -85.023598-0.003856j
[2025-07-30 19:22:46] [Iter 3605/4650] R4[1354/2400], Temp: 0.3999, Energy: -84.985234+0.012342j
[2025-07-30 19:23:01] [Iter 3606/4650] R4[1355/2400], Temp: 0.3992, Energy: -84.936607-0.000097j
[2025-07-30 19:23:16] [Iter 3607/4650] R4[1356/2400], Temp: 0.3986, Energy: -84.968003-0.003637j
[2025-07-30 19:23:30] [Iter 3608/4650] R4[1357/2400], Temp: 0.3980, Energy: -85.035581+0.005503j
[2025-07-30 19:23:45] [Iter 3609/4650] R4[1358/2400], Temp: 0.3973, Energy: -84.976960+0.010265j
[2025-07-30 19:24:00] [Iter 3610/4650] R4[1359/2400], Temp: 0.3967, Energy: -84.945929-0.009756j
[2025-07-30 19:24:14] [Iter 3611/4650] R4[1360/2400], Temp: 0.3960, Energy: -84.970835-0.001788j
[2025-07-30 19:24:29] [Iter 3612/4650] R4[1361/2400], Temp: 0.3954, Energy: -84.991935-0.001340j
[2025-07-30 19:24:43] [Iter 3613/4650] R4[1362/2400], Temp: 0.3948, Energy: -85.015280-0.006361j
[2025-07-30 19:24:58] [Iter 3614/4650] R4[1363/2400], Temp: 0.3941, Energy: -85.045329+0.004626j
[2025-07-30 19:25:13] [Iter 3615/4650] R4[1364/2400], Temp: 0.3935, Energy: -85.045422-0.006398j
[2025-07-30 19:25:27] [Iter 3616/4650] R4[1365/2400], Temp: 0.3928, Energy: -84.995528-0.004164j
[2025-07-30 19:25:42] [Iter 3617/4650] R4[1366/2400], Temp: 0.3922, Energy: -85.015082+0.005884j
[2025-07-30 19:25:57] [Iter 3618/4650] R4[1367/2400], Temp: 0.3916, Energy: -85.077799+0.001692j
[2025-07-30 19:26:12] [Iter 3619/4650] R4[1368/2400], Temp: 0.3909, Energy: -85.129217+0.001534j
[2025-07-30 19:26:26] [Iter 3620/4650] R4[1369/2400], Temp: 0.3903, Energy: -85.100088+0.004899j
[2025-07-30 19:26:41] [Iter 3621/4650] R4[1370/2400], Temp: 0.3897, Energy: -85.154343+0.000692j
[2025-07-30 19:26:56] [Iter 3622/4650] R4[1371/2400], Temp: 0.3890, Energy: -85.143100+0.005538j
[2025-07-30 19:27:10] [Iter 3623/4650] R4[1372/2400], Temp: 0.3884, Energy: -85.057957-0.004930j
[2025-07-30 19:27:24] [Iter 3624/4650] R4[1373/2400], Temp: 0.3877, Energy: -85.083057+0.000887j
[2025-07-30 19:27:38] [Iter 3625/4650] R4[1374/2400], Temp: 0.3871, Energy: -85.113913-0.000314j
[2025-07-30 19:27:53] [Iter 3626/4650] R4[1375/2400], Temp: 0.3865, Energy: -85.119830-0.003320j
[2025-07-30 19:28:08] [Iter 3627/4650] R4[1376/2400], Temp: 0.3858, Energy: -85.106223-0.005816j
[2025-07-30 19:28:23] [Iter 3628/4650] R4[1377/2400], Temp: 0.3852, Energy: -85.061734-0.002825j
[2025-07-30 19:28:37] [Iter 3629/4650] R4[1378/2400], Temp: 0.3846, Energy: -85.061397+0.000911j
[2025-07-30 19:28:52] [Iter 3630/4650] R4[1379/2400], Temp: 0.3839, Energy: -85.028893+0.000964j
[2025-07-30 19:29:07] [Iter 3631/4650] R4[1380/2400], Temp: 0.3833, Energy: -84.994363+0.006048j
[2025-07-30 19:29:22] [Iter 3632/4650] R4[1381/2400], Temp: 0.3826, Energy: -84.953406-0.005033j
[2025-07-30 19:29:37] [Iter 3633/4650] R4[1382/2400], Temp: 0.3820, Energy: -84.989872-0.005603j
[2025-07-30 19:29:52] [Iter 3634/4650] R4[1383/2400], Temp: 0.3814, Energy: -84.983983-0.006465j
[2025-07-30 19:30:07] [Iter 3635/4650] R4[1384/2400], Temp: 0.3807, Energy: -85.014055-0.004455j
[2025-07-30 19:30:21] [Iter 3636/4650] R4[1385/2400], Temp: 0.3801, Energy: -84.953756-0.002839j
[2025-07-30 19:30:36] [Iter 3637/4650] R4[1386/2400], Temp: 0.3795, Energy: -84.968028-0.004468j
[2025-07-30 19:30:51] [Iter 3638/4650] R4[1387/2400], Temp: 0.3788, Energy: -84.972653+0.015346j
[2025-07-30 19:31:06] [Iter 3639/4650] R4[1388/2400], Temp: 0.3782, Energy: -85.030271+0.001709j
[2025-07-30 19:31:21] [Iter 3640/4650] R4[1389/2400], Temp: 0.3776, Energy: -85.012553+0.003070j
[2025-07-30 19:31:36] [Iter 3641/4650] R4[1390/2400], Temp: 0.3769, Energy: -85.040435-0.002718j
[2025-07-30 19:31:50] [Iter 3642/4650] R4[1391/2400], Temp: 0.3763, Energy: -85.014574-0.003069j
[2025-07-30 19:32:05] [Iter 3643/4650] R4[1392/2400], Temp: 0.3757, Energy: -85.062467+0.002107j
[2025-07-30 19:32:20] [Iter 3644/4650] R4[1393/2400], Temp: 0.3750, Energy: -85.067132+0.017765j
[2025-07-30 19:32:35] [Iter 3645/4650] R4[1394/2400], Temp: 0.3744, Energy: -85.096653-0.013796j
[2025-07-30 19:32:49] [Iter 3646/4650] R4[1395/2400], Temp: 0.3738, Energy: -85.046022+0.000167j
[2025-07-30 19:33:05] [Iter 3647/4650] R4[1396/2400], Temp: 0.3731, Energy: -85.122624+0.013289j
[2025-07-30 19:33:19] [Iter 3648/4650] R4[1397/2400], Temp: 0.3725, Energy: -85.065767-0.002772j
[2025-07-30 19:33:34] [Iter 3649/4650] R4[1398/2400], Temp: 0.3719, Energy: -85.071743+0.003034j
[2025-07-30 19:33:48] [Iter 3650/4650] R4[1399/2400], Temp: 0.3712, Energy: -85.136363+0.012640j
[2025-07-30 19:34:03] [Iter 3651/4650] R4[1400/2400], Temp: 0.3706, Energy: -85.079017+0.000249j
[2025-07-30 19:34:17] [Iter 3652/4650] R4[1401/2400], Temp: 0.3700, Energy: -85.059986+0.001775j
[2025-07-30 19:34:32] [Iter 3653/4650] R4[1402/2400], Temp: 0.3693, Energy: -85.041066-0.000177j
[2025-07-30 19:34:46] [Iter 3654/4650] R4[1403/2400], Temp: 0.3687, Energy: -85.051486+0.009963j
[2025-07-30 19:35:02] [Iter 3655/4650] R4[1404/2400], Temp: 0.3681, Energy: -85.049926-0.000759j
[2025-07-30 19:35:16] [Iter 3656/4650] R4[1405/2400], Temp: 0.3674, Energy: -85.058638-0.007786j
[2025-07-30 19:35:31] [Iter 3657/4650] R4[1406/2400], Temp: 0.3668, Energy: -85.106624-0.001219j
[2025-07-30 19:35:46] [Iter 3658/4650] R4[1407/2400], Temp: 0.3662, Energy: -85.043130-0.001033j
[2025-07-30 19:36:01] [Iter 3659/4650] R4[1408/2400], Temp: 0.3655, Energy: -85.046890+0.000095j
[2025-07-30 19:36:16] [Iter 3660/4650] R4[1409/2400], Temp: 0.3649, Energy: -85.037005+0.008890j
[2025-07-30 19:36:31] [Iter 3661/4650] R4[1410/2400], Temp: 0.3643, Energy: -85.003266+0.014081j
[2025-07-30 19:36:45] [Iter 3662/4650] R4[1411/2400], Temp: 0.3636, Energy: -85.012907+0.005620j
[2025-07-30 19:36:59] [Iter 3663/4650] R4[1412/2400], Temp: 0.3630, Energy: -84.973537+0.002420j
[2025-07-30 19:37:14] [Iter 3664/4650] R4[1413/2400], Temp: 0.3624, Energy: -84.937543+0.008965j
[2025-07-30 19:37:28] [Iter 3665/4650] R4[1414/2400], Temp: 0.3618, Energy: -84.975902-0.000598j
[2025-07-30 19:37:43] [Iter 3666/4650] R4[1415/2400], Temp: 0.3611, Energy: -84.997886-0.002217j
[2025-07-30 19:37:58] [Iter 3667/4650] R4[1416/2400], Temp: 0.3605, Energy: -85.026450+0.000555j
[2025-07-30 19:38:13] [Iter 3668/4650] R4[1417/2400], Temp: 0.3599, Energy: -85.013839+0.000952j
[2025-07-30 19:38:28] [Iter 3669/4650] R4[1418/2400], Temp: 0.3592, Energy: -85.006440+0.003592j
[2025-07-30 19:38:42] [Iter 3670/4650] R4[1419/2400], Temp: 0.3586, Energy: -85.097421+0.003716j
[2025-07-30 19:38:57] [Iter 3671/4650] R4[1420/2400], Temp: 0.3580, Energy: -85.081699-0.001436j
[2025-07-30 19:39:12] [Iter 3672/4650] R4[1421/2400], Temp: 0.3574, Energy: -84.998828+0.004540j
[2025-07-30 19:39:26] [Iter 3673/4650] R4[1422/2400], Temp: 0.3567, Energy: -85.043370+0.003462j
[2025-07-30 19:39:40] [Iter 3674/4650] R4[1423/2400], Temp: 0.3561, Energy: -84.994099-0.003426j
[2025-07-30 19:39:55] [Iter 3675/4650] R4[1424/2400], Temp: 0.3555, Energy: -85.011785+0.002729j
[2025-07-30 19:40:10] [Iter 3676/4650] R4[1425/2400], Temp: 0.3549, Energy: -85.010884+0.012291j
[2025-07-30 19:40:24] [Iter 3677/4650] R4[1426/2400], Temp: 0.3542, Energy: -85.037967+0.001777j
[2025-07-30 19:40:39] [Iter 3678/4650] R4[1427/2400], Temp: 0.3536, Energy: -84.963951+0.001887j
[2025-07-30 19:40:53] [Iter 3679/4650] R4[1428/2400], Temp: 0.3530, Energy: -84.990706+0.000777j
[2025-07-30 19:41:08] [Iter 3680/4650] R4[1429/2400], Temp: 0.3524, Energy: -84.939304+0.002681j
[2025-07-30 19:41:22] [Iter 3681/4650] R4[1430/2400], Temp: 0.3517, Energy: -84.961980+0.004707j
[2025-07-30 19:41:37] [Iter 3682/4650] R4[1431/2400], Temp: 0.3511, Energy: -84.949354+0.000040j
[2025-07-30 19:41:52] [Iter 3683/4650] R4[1432/2400], Temp: 0.3505, Energy: -84.966188-0.004470j
[2025-07-30 19:42:06] [Iter 3684/4650] R4[1433/2400], Temp: 0.3499, Energy: -85.012614-0.009097j
[2025-07-30 19:42:21] [Iter 3685/4650] R4[1434/2400], Temp: 0.3492, Energy: -84.977639+0.001520j
[2025-07-30 19:42:35] [Iter 3686/4650] R4[1435/2400], Temp: 0.3486, Energy: -84.928423-0.020776j
[2025-07-30 19:42:50] [Iter 3687/4650] R4[1436/2400], Temp: 0.3480, Energy: -84.961619-0.001513j
[2025-07-30 19:43:04] [Iter 3688/4650] R4[1437/2400], Temp: 0.3474, Energy: -84.944162-0.012317j
[2025-07-30 19:43:19] [Iter 3689/4650] R4[1438/2400], Temp: 0.3467, Energy: -84.938528-0.006964j
[2025-07-30 19:43:33] [Iter 3690/4650] R4[1439/2400], Temp: 0.3461, Energy: -84.997134+0.001915j
[2025-07-30 19:43:48] [Iter 3691/4650] R4[1440/2400], Temp: 0.3455, Energy: -84.990477-0.001068j
[2025-07-30 19:44:03] [Iter 3692/4650] R4[1441/2400], Temp: 0.3449, Energy: -84.976981+0.002546j
[2025-07-30 19:44:18] [Iter 3693/4650] R4[1442/2400], Temp: 0.3442, Energy: -84.990106+0.004520j
[2025-07-30 19:44:32] [Iter 3694/4650] R4[1443/2400], Temp: 0.3436, Energy: -84.999036+0.002182j
[2025-07-30 19:44:47] [Iter 3695/4650] R4[1444/2400], Temp: 0.3430, Energy: -84.987945+0.006689j
[2025-07-30 19:45:02] [Iter 3696/4650] R4[1445/2400], Temp: 0.3424, Energy: -84.956980-0.003878j
[2025-07-30 19:45:17] [Iter 3697/4650] R4[1446/2400], Temp: 0.3418, Energy: -85.076331+0.005572j
[2025-07-30 19:45:31] [Iter 3698/4650] R4[1447/2400], Temp: 0.3411, Energy: -85.053224-0.000271j
[2025-07-30 19:45:46] [Iter 3699/4650] R4[1448/2400], Temp: 0.3405, Energy: -85.028067-0.000725j
[2025-07-30 19:46:01] [Iter 3700/4650] R4[1449/2400], Temp: 0.3399, Energy: -85.017903-0.001141j
[2025-07-30 19:46:16] [Iter 3701/4650] R4[1450/2400], Temp: 0.3393, Energy: -85.039418+0.004551j
[2025-07-30 19:46:30] [Iter 3702/4650] R4[1451/2400], Temp: 0.3387, Energy: -85.029694-0.000569j
[2025-07-30 19:46:45] [Iter 3703/4650] R4[1452/2400], Temp: 0.3380, Energy: -85.021706-0.005077j
[2025-07-30 19:47:01] [Iter 3704/4650] R4[1453/2400], Temp: 0.3374, Energy: -85.004483-0.001261j
[2025-07-30 19:47:15] [Iter 3705/4650] R4[1454/2400], Temp: 0.3368, Energy: -85.027908+0.004477j
[2025-07-30 19:47:30] [Iter 3706/4650] R4[1455/2400], Temp: 0.3362, Energy: -84.992588-0.000824j
[2025-07-30 19:47:45] [Iter 3707/4650] R4[1456/2400], Temp: 0.3356, Energy: -84.991760-0.006144j
[2025-07-30 19:48:00] [Iter 3708/4650] R4[1457/2400], Temp: 0.3349, Energy: -85.032450+0.004325j
[2025-07-30 19:48:15] [Iter 3709/4650] R4[1458/2400], Temp: 0.3343, Energy: -85.028388-0.007475j
[2025-07-30 19:48:29] [Iter 3710/4650] R4[1459/2400], Temp: 0.3337, Energy: -84.968910+0.002088j
[2025-07-30 19:48:44] [Iter 3711/4650] R4[1460/2400], Temp: 0.3331, Energy: -85.093038-0.001583j
[2025-07-30 19:48:59] [Iter 3712/4650] R4[1461/2400], Temp: 0.3325, Energy: -85.043854-0.003981j
[2025-07-30 19:49:14] [Iter 3713/4650] R4[1462/2400], Temp: 0.3319, Energy: -85.028652-0.003244j
[2025-07-30 19:49:29] [Iter 3714/4650] R4[1463/2400], Temp: 0.3312, Energy: -84.998518-0.003332j
[2025-07-30 19:49:44] [Iter 3715/4650] R4[1464/2400], Temp: 0.3306, Energy: -85.015965+0.000817j
[2025-07-30 19:49:58] [Iter 3716/4650] R4[1465/2400], Temp: 0.3300, Energy: -85.009620-0.002482j
[2025-07-30 19:50:13] [Iter 3717/4650] R4[1466/2400], Temp: 0.3294, Energy: -85.102081-0.004907j
[2025-07-30 19:50:28] [Iter 3718/4650] R4[1467/2400], Temp: 0.3288, Energy: -85.055254-0.004534j
[2025-07-30 19:50:43] [Iter 3719/4650] R4[1468/2400], Temp: 0.3282, Energy: -85.069596+0.008324j
[2025-07-30 19:50:58] [Iter 3720/4650] R4[1469/2400], Temp: 0.3276, Energy: -85.067932+0.006071j
[2025-07-30 19:51:13] [Iter 3721/4650] R4[1470/2400], Temp: 0.3269, Energy: -85.090399-0.009869j
[2025-07-30 19:51:27] [Iter 3722/4650] R4[1471/2400], Temp: 0.3263, Energy: -85.050430-0.010642j
[2025-07-30 19:51:42] [Iter 3723/4650] R4[1472/2400], Temp: 0.3257, Energy: -85.023294-0.002456j
[2025-07-30 19:51:57] [Iter 3724/4650] R4[1473/2400], Temp: 0.3251, Energy: -85.076216-0.007048j
[2025-07-30 19:52:12] [Iter 3725/4650] R4[1474/2400], Temp: 0.3245, Energy: -85.081627-0.002298j
[2025-07-30 19:52:26] [Iter 3726/4650] R4[1475/2400], Temp: 0.3239, Energy: -85.023053-0.000354j
[2025-07-30 19:52:41] [Iter 3727/4650] R4[1476/2400], Temp: 0.3233, Energy: -85.028748-0.004795j
[2025-07-30 19:52:55] [Iter 3728/4650] R4[1477/2400], Temp: 0.3227, Energy: -85.055058+0.002104j
[2025-07-30 19:53:10] [Iter 3729/4650] R4[1478/2400], Temp: 0.3220, Energy: -85.007033+0.001669j
[2025-07-30 19:53:24] [Iter 3730/4650] R4[1479/2400], Temp: 0.3214, Energy: -85.024864-0.000751j
[2025-07-30 19:53:39] [Iter 3731/4650] R4[1480/2400], Temp: 0.3208, Energy: -84.992998-0.007751j
[2025-07-30 19:53:54] [Iter 3732/4650] R4[1481/2400], Temp: 0.3202, Energy: -84.984541-0.002462j
[2025-07-30 19:54:09] [Iter 3733/4650] R4[1482/2400], Temp: 0.3196, Energy: -84.997276-0.004930j
[2025-07-30 19:54:24] [Iter 3734/4650] R4[1483/2400], Temp: 0.3190, Energy: -84.991798+0.004166j
[2025-07-30 19:54:39] [Iter 3735/4650] R4[1484/2400], Temp: 0.3184, Energy: -85.022748+0.013945j
[2025-07-30 19:54:54] [Iter 3736/4650] R4[1485/2400], Temp: 0.3178, Energy: -85.017991+0.000253j
[2025-07-30 19:55:09] [Iter 3737/4650] R4[1486/2400], Temp: 0.3172, Energy: -84.902552-0.004038j
[2025-07-30 19:55:23] [Iter 3738/4650] R4[1487/2400], Temp: 0.3165, Energy: -84.942751+0.005706j
[2025-07-30 19:55:38] [Iter 3739/4650] R4[1488/2400], Temp: 0.3159, Energy: -84.968969-0.000040j
[2025-07-30 19:55:53] [Iter 3740/4650] R4[1489/2400], Temp: 0.3153, Energy: -85.004837+0.005969j
[2025-07-30 19:56:08] [Iter 3741/4650] R4[1490/2400], Temp: 0.3147, Energy: -84.988979-0.007820j
[2025-07-30 19:56:23] [Iter 3742/4650] R4[1491/2400], Temp: 0.3141, Energy: -85.010619-0.006337j
[2025-07-30 19:56:38] [Iter 3743/4650] R4[1492/2400], Temp: 0.3135, Energy: -84.960017+0.005512j
[2025-07-30 19:56:53] [Iter 3744/4650] R4[1493/2400], Temp: 0.3129, Energy: -85.002304+0.010102j
[2025-07-30 19:57:07] [Iter 3745/4650] R4[1494/2400], Temp: 0.3123, Energy: -84.961109-0.006190j
[2025-07-30 19:57:22] [Iter 3746/4650] R4[1495/2400], Temp: 0.3117, Energy: -84.920602+0.006110j
[2025-07-30 19:57:37] [Iter 3747/4650] R4[1496/2400], Temp: 0.3111, Energy: -84.993981+0.010870j
[2025-07-30 19:57:52] [Iter 3748/4650] R4[1497/2400], Temp: 0.3105, Energy: -85.002303+0.006085j
[2025-07-30 19:58:07] [Iter 3749/4650] R4[1498/2400], Temp: 0.3099, Energy: -85.026165-0.002627j
[2025-07-30 19:58:22] [Iter 3750/4650] R4[1499/2400], Temp: 0.3093, Energy: -85.058525+0.003843j
[2025-07-30 19:58:37] [Iter 3751/4650] R4[1500/2400], Temp: 0.3087, Energy: -85.085950+0.002944j
[2025-07-30 19:58:52] [Iter 3752/4650] R4[1501/2400], Temp: 0.3081, Energy: -85.054388+0.003017j
[2025-07-30 19:59:06] [Iter 3753/4650] R4[1502/2400], Temp: 0.3074, Energy: -85.062425-0.004844j
[2025-07-30 19:59:21] [Iter 3754/4650] R4[1503/2400], Temp: 0.3068, Energy: -85.069570+0.003882j
[2025-07-30 19:59:36] [Iter 3755/4650] R4[1504/2400], Temp: 0.3062, Energy: -85.015943+0.000698j
[2025-07-30 19:59:51] [Iter 3756/4650] R4[1505/2400], Temp: 0.3056, Energy: -85.012108+0.000988j
[2025-07-30 20:00:05] [Iter 3757/4650] R4[1506/2400], Temp: 0.3050, Energy: -84.964339+0.003174j
[2025-07-30 20:00:20] [Iter 3758/4650] R4[1507/2400], Temp: 0.3044, Energy: -84.991393+0.002175j
[2025-07-30 20:00:35] [Iter 3759/4650] R4[1508/2400], Temp: 0.3038, Energy: -85.001745+0.009329j
[2025-07-30 20:00:50] [Iter 3760/4650] R4[1509/2400], Temp: 0.3032, Energy: -84.909506-0.003729j
[2025-07-30 20:01:04] [Iter 3761/4650] R4[1510/2400], Temp: 0.3026, Energy: -84.966009+0.003452j
[2025-07-30 20:01:19] [Iter 3762/4650] R4[1511/2400], Temp: 0.3020, Energy: -85.001180-0.007742j
[2025-07-30 20:01:34] [Iter 3763/4650] R4[1512/2400], Temp: 0.3014, Energy: -84.996641-0.000404j
[2025-07-30 20:01:49] [Iter 3764/4650] R4[1513/2400], Temp: 0.3008, Energy: -84.968940-0.000511j
[2025-07-30 20:02:04] [Iter 3765/4650] R4[1514/2400], Temp: 0.3002, Energy: -84.971097-0.002645j
[2025-07-30 20:02:18] [Iter 3766/4650] R4[1515/2400], Temp: 0.2996, Energy: -84.979155+0.001857j
[2025-07-30 20:02:32] [Iter 3767/4650] R4[1516/2400], Temp: 0.2990, Energy: -85.020623+0.000855j
[2025-07-30 20:02:47] [Iter 3768/4650] R4[1517/2400], Temp: 0.2984, Energy: -85.056115-0.001378j
[2025-07-30 20:03:02] [Iter 3769/4650] R4[1518/2400], Temp: 0.2978, Energy: -85.075731+0.000336j
[2025-07-30 20:03:17] [Iter 3770/4650] R4[1519/2400], Temp: 0.2972, Energy: -85.093327-0.008144j
[2025-07-30 20:03:32] [Iter 3771/4650] R4[1520/2400], Temp: 0.2966, Energy: -85.001937-0.008165j
[2025-07-30 20:03:46] [Iter 3772/4650] R4[1521/2400], Temp: 0.2960, Energy: -84.958174+0.007158j
[2025-07-30 20:04:01] [Iter 3773/4650] R4[1522/2400], Temp: 0.2954, Energy: -84.922483+0.002690j
[2025-07-30 20:04:16] [Iter 3774/4650] R4[1523/2400], Temp: 0.2948, Energy: -84.899693-0.000837j
[2025-07-30 20:04:30] [Iter 3775/4650] R4[1524/2400], Temp: 0.2942, Energy: -84.910847-0.006045j
[2025-07-30 20:04:45] [Iter 3776/4650] R4[1525/2400], Temp: 0.2936, Energy: -84.871916-0.004305j
[2025-07-30 20:05:00] [Iter 3777/4650] R4[1526/2400], Temp: 0.2931, Energy: -84.868303+0.000762j
[2025-07-30 20:05:14] [Iter 3778/4650] R4[1527/2400], Temp: 0.2925, Energy: -84.945876-0.011101j
[2025-07-30 20:05:29] [Iter 3779/4650] R4[1528/2400], Temp: 0.2919, Energy: -85.003720-0.017096j
[2025-07-30 20:05:44] [Iter 3780/4650] R4[1529/2400], Temp: 0.2913, Energy: -84.963434-0.002013j
[2025-07-30 20:05:59] [Iter 3781/4650] R4[1530/2400], Temp: 0.2907, Energy: -85.008981-0.006461j
[2025-07-30 20:06:13] [Iter 3782/4650] R4[1531/2400], Temp: 0.2901, Energy: -85.107690-0.000759j
[2025-07-30 20:06:28] [Iter 3783/4650] R4[1532/2400], Temp: 0.2895, Energy: -85.048400+0.000870j
[2025-07-30 20:06:42] [Iter 3784/4650] R4[1533/2400], Temp: 0.2889, Energy: -85.059195-0.006691j
[2025-07-30 20:06:57] [Iter 3785/4650] R4[1534/2400], Temp: 0.2883, Energy: -85.013335+0.001760j
[2025-07-30 20:07:12] [Iter 3786/4650] R4[1535/2400], Temp: 0.2877, Energy: -84.968286-0.004512j
[2025-07-30 20:07:27] [Iter 3787/4650] R4[1536/2400], Temp: 0.2871, Energy: -84.985374+0.005036j
[2025-07-30 20:07:42] [Iter 3788/4650] R4[1537/2400], Temp: 0.2865, Energy: -85.030549-0.006530j
[2025-07-30 20:07:56] [Iter 3789/4650] R4[1538/2400], Temp: 0.2859, Energy: -84.968203-0.000997j
[2025-07-30 20:08:10] [Iter 3790/4650] R4[1539/2400], Temp: 0.2853, Energy: -85.071434+0.004937j
[2025-07-30 20:08:25] [Iter 3791/4650] R4[1540/2400], Temp: 0.2847, Energy: -85.055212-0.001430j
[2025-07-30 20:08:40] [Iter 3792/4650] R4[1541/2400], Temp: 0.2842, Energy: -85.021182+0.010376j
[2025-07-30 20:08:55] [Iter 3793/4650] R4[1542/2400], Temp: 0.2836, Energy: -85.058770+0.003924j
[2025-07-30 20:09:09] [Iter 3794/4650] R4[1543/2400], Temp: 0.2830, Energy: -85.035026-0.000525j
[2025-07-30 20:09:23] [Iter 3795/4650] R4[1544/2400], Temp: 0.2824, Energy: -85.061037+0.005705j
[2025-07-30 20:09:38] [Iter 3796/4650] R4[1545/2400], Temp: 0.2818, Energy: -85.024553+0.003912j
[2025-07-30 20:09:53] [Iter 3797/4650] R4[1546/2400], Temp: 0.2812, Energy: -85.003386+0.002017j
[2025-07-30 20:10:08] [Iter 3798/4650] R4[1547/2400], Temp: 0.2806, Energy: -85.043462+0.000824j
[2025-07-30 20:10:22] [Iter 3799/4650] R4[1548/2400], Temp: 0.2800, Energy: -85.095325+0.007023j
[2025-07-30 20:10:37] [Iter 3800/4650] R4[1549/2400], Temp: 0.2794, Energy: -85.109700+0.006186j
[2025-07-30 20:10:52] [Iter 3801/4650] R4[1550/2400], Temp: 0.2789, Energy: -85.088910+0.005376j
[2025-07-30 20:11:06] [Iter 3802/4650] R4[1551/2400], Temp: 0.2783, Energy: -85.022704+0.003018j
[2025-07-30 20:11:22] [Iter 3803/4650] R4[1552/2400], Temp: 0.2777, Energy: -85.046629+0.008890j
[2025-07-30 20:11:36] [Iter 3804/4650] R4[1553/2400], Temp: 0.2771, Energy: -85.058789+0.001520j
[2025-07-30 20:11:51] [Iter 3805/4650] R4[1554/2400], Temp: 0.2765, Energy: -84.977698+0.007420j
[2025-07-30 20:12:06] [Iter 3806/4650] R4[1555/2400], Temp: 0.2759, Energy: -85.031779+0.003705j
[2025-07-30 20:12:21] [Iter 3807/4650] R4[1556/2400], Temp: 0.2753, Energy: -85.034123+0.008112j
[2025-07-30 20:12:35] [Iter 3808/4650] R4[1557/2400], Temp: 0.2748, Energy: -84.995138-0.001002j
[2025-07-30 20:12:50] [Iter 3809/4650] R4[1558/2400], Temp: 0.2742, Energy: -85.016574+0.005395j
[2025-07-30 20:13:05] [Iter 3810/4650] R4[1559/2400], Temp: 0.2736, Energy: -85.063791+0.003399j
[2025-07-30 20:13:20] [Iter 3811/4650] R4[1560/2400], Temp: 0.2730, Energy: -85.000399-0.001822j
[2025-07-30 20:13:35] [Iter 3812/4650] R4[1561/2400], Temp: 0.2724, Energy: -84.972505+0.002326j
[2025-07-30 20:13:49] [Iter 3813/4650] R4[1562/2400], Temp: 0.2718, Energy: -84.934672-0.005735j
[2025-07-30 20:14:04] [Iter 3814/4650] R4[1563/2400], Temp: 0.2713, Energy: -84.939427+0.001601j
[2025-07-30 20:14:18] [Iter 3815/4650] R4[1564/2400], Temp: 0.2707, Energy: -84.910631-0.013241j
[2025-07-30 20:14:33] [Iter 3816/4650] R4[1565/2400], Temp: 0.2701, Energy: -84.907899-0.003992j
[2025-07-30 20:14:48] [Iter 3817/4650] R4[1566/2400], Temp: 0.2695, Energy: -84.897788-0.003013j
[2025-07-30 20:15:02] [Iter 3818/4650] R4[1567/2400], Temp: 0.2689, Energy: -84.890242-0.014815j
[2025-07-30 20:15:17] [Iter 3819/4650] R4[1568/2400], Temp: 0.2684, Energy: -84.959610+0.010004j
[2025-07-30 20:15:32] [Iter 3820/4650] R4[1569/2400], Temp: 0.2678, Energy: -84.936269+0.001262j
[2025-07-30 20:15:46] [Iter 3821/4650] R4[1570/2400], Temp: 0.2672, Energy: -84.893596-0.019918j
[2025-07-30 20:16:01] [Iter 3822/4650] R4[1571/2400], Temp: 0.2666, Energy: -84.903607-0.001430j
[2025-07-30 20:16:16] [Iter 3823/4650] R4[1572/2400], Temp: 0.2660, Energy: -84.920042+0.002805j
[2025-07-30 20:16:30] [Iter 3824/4650] R4[1573/2400], Temp: 0.2655, Energy: -84.940511-0.000098j
[2025-07-30 20:16:45] [Iter 3825/4650] R4[1574/2400], Temp: 0.2649, Energy: -84.907692+0.009289j
[2025-07-30 20:16:59] [Iter 3826/4650] R4[1575/2400], Temp: 0.2643, Energy: -84.956532+0.005792j
[2025-07-30 20:17:14] [Iter 3827/4650] R4[1576/2400], Temp: 0.2637, Energy: -84.946570+0.012244j
[2025-07-30 20:17:29] [Iter 3828/4650] R4[1577/2400], Temp: 0.2631, Energy: -84.944358+0.002534j
[2025-07-30 20:17:44] [Iter 3829/4650] R4[1578/2400], Temp: 0.2626, Energy: -84.900720+0.007387j
[2025-07-30 20:17:58] [Iter 3830/4650] R4[1579/2400], Temp: 0.2620, Energy: -85.013490+0.005179j
[2025-07-30 20:18:13] [Iter 3831/4650] R4[1580/2400], Temp: 0.2614, Energy: -84.988482+0.000899j
[2025-07-30 20:18:28] [Iter 3832/4650] R4[1581/2400], Temp: 0.2608, Energy: -85.073229-0.006109j
[2025-07-30 20:18:43] [Iter 3833/4650] R4[1582/2400], Temp: 0.2603, Energy: -85.043553-0.011703j
[2025-07-30 20:18:58] [Iter 3834/4650] R4[1583/2400], Temp: 0.2597, Energy: -85.086985+0.012466j
[2025-07-30 20:19:12] [Iter 3835/4650] R4[1584/2400], Temp: 0.2591, Energy: -85.091111-0.004619j
[2025-07-30 20:19:27] [Iter 3836/4650] R4[1585/2400], Temp: 0.2585, Energy: -85.083857-0.004417j
[2025-07-30 20:19:42] [Iter 3837/4650] R4[1586/2400], Temp: 0.2580, Energy: -85.097712-0.020844j
[2025-07-30 20:19:56] [Iter 3838/4650] R4[1587/2400], Temp: 0.2574, Energy: -85.037254+0.000620j
[2025-07-30 20:20:11] [Iter 3839/4650] R4[1588/2400], Temp: 0.2568, Energy: -85.086870+0.000285j
[2025-07-30 20:20:26] [Iter 3840/4650] R4[1589/2400], Temp: 0.2563, Energy: -85.085170+0.015522j
[2025-07-30 20:20:41] [Iter 3841/4650] R4[1590/2400], Temp: 0.2557, Energy: -85.101991-0.001837j
[2025-07-30 20:20:55] [Iter 3842/4650] R4[1591/2400], Temp: 0.2551, Energy: -85.084569+0.002931j
[2025-07-30 20:21:10] [Iter 3843/4650] R4[1592/2400], Temp: 0.2545, Energy: -85.139812+0.001866j
[2025-07-30 20:21:25] [Iter 3844/4650] R4[1593/2400], Temp: 0.2540, Energy: -85.127391-0.002549j
[2025-07-30 20:21:39] [Iter 3845/4650] R4[1594/2400], Temp: 0.2534, Energy: -85.128376+0.002140j
[2025-07-30 20:21:54] [Iter 3846/4650] R4[1595/2400], Temp: 0.2528, Energy: -85.073175-0.002688j
[2025-07-30 20:22:09] [Iter 3847/4650] R4[1596/2400], Temp: 0.2523, Energy: -85.090317+0.005283j
[2025-07-30 20:22:23] [Iter 3848/4650] R4[1597/2400], Temp: 0.2517, Energy: -85.105589-0.006710j
[2025-07-30 20:22:37] [Iter 3849/4650] R4[1598/2400], Temp: 0.2511, Energy: -85.062118+0.010925j
[2025-07-30 20:22:52] [Iter 3850/4650] R4[1599/2400], Temp: 0.2506, Energy: -85.048922-0.002396j
[2025-07-30 20:23:06] [Iter 3851/4650] R4[1600/2400], Temp: 0.2500, Energy: -85.052630-0.004620j
[2025-07-30 20:23:21] [Iter 3852/4650] R4[1601/2400], Temp: 0.2494, Energy: -85.117180+0.011994j
[2025-07-30 20:23:36] [Iter 3853/4650] R4[1602/2400], Temp: 0.2489, Energy: -85.098671+0.010669j
[2025-07-30 20:23:51] [Iter 3854/4650] R4[1603/2400], Temp: 0.2483, Energy: -85.100597-0.009936j
[2025-07-30 20:24:05] [Iter 3855/4650] R4[1604/2400], Temp: 0.2477, Energy: -85.032130+0.008340j
[2025-07-30 20:24:20] [Iter 3856/4650] R4[1605/2400], Temp: 0.2472, Energy: -85.054289-0.004226j
[2025-07-30 20:24:35] [Iter 3857/4650] R4[1606/2400], Temp: 0.2466, Energy: -85.007020+0.014472j
[2025-07-30 20:24:50] [Iter 3858/4650] R4[1607/2400], Temp: 0.2460, Energy: -85.032781-0.008713j
[2025-07-30 20:25:04] [Iter 3859/4650] R4[1608/2400], Temp: 0.2455, Energy: -85.003248-0.009754j
[2025-07-30 20:25:19] [Iter 3860/4650] R4[1609/2400], Temp: 0.2449, Energy: -84.967848-0.000713j
[2025-07-30 20:25:34] [Iter 3861/4650] R4[1610/2400], Temp: 0.2444, Energy: -84.963742+0.000093j
[2025-07-30 20:25:49] [Iter 3862/4650] R4[1611/2400], Temp: 0.2438, Energy: -84.908239-0.007562j
[2025-07-30 20:26:04] [Iter 3863/4650] R4[1612/2400], Temp: 0.2432, Energy: -84.938684+0.000685j
[2025-07-30 20:26:18] [Iter 3864/4650] R4[1613/2400], Temp: 0.2427, Energy: -84.953555-0.005810j
[2025-07-30 20:26:33] [Iter 3865/4650] R4[1614/2400], Temp: 0.2421, Energy: -84.915188+0.004139j
[2025-07-30 20:26:48] [Iter 3866/4650] R4[1615/2400], Temp: 0.2415, Energy: -84.936787-0.003828j
[2025-07-30 20:27:03] [Iter 3867/4650] R4[1616/2400], Temp: 0.2410, Energy: -85.036510-0.004074j
[2025-07-30 20:27:17] [Iter 3868/4650] R4[1617/2400], Temp: 0.2404, Energy: -84.988356-0.002863j
[2025-07-30 20:27:32] [Iter 3869/4650] R4[1618/2400], Temp: 0.2399, Energy: -84.965338-0.002749j
[2025-07-30 20:27:47] [Iter 3870/4650] R4[1619/2400], Temp: 0.2393, Energy: -84.960648-0.003225j
[2025-07-30 20:28:01] [Iter 3871/4650] R4[1620/2400], Temp: 0.2388, Energy: -84.993006+0.001657j
[2025-07-30 20:28:16] [Iter 3872/4650] R4[1621/2400], Temp: 0.2382, Energy: -84.982038+0.000920j
[2025-07-30 20:28:31] [Iter 3873/4650] R4[1622/2400], Temp: 0.2376, Energy: -85.033263-0.000819j
[2025-07-30 20:28:46] [Iter 3874/4650] R4[1623/2400], Temp: 0.2371, Energy: -84.982134-0.007518j
[2025-07-30 20:29:01] [Iter 3875/4650] R4[1624/2400], Temp: 0.2365, Energy: -84.948052-0.003351j
[2025-07-30 20:29:15] [Iter 3876/4650] R4[1625/2400], Temp: 0.2360, Energy: -84.930919+0.012366j
[2025-07-30 20:29:30] [Iter 3877/4650] R4[1626/2400], Temp: 0.2354, Energy: -84.991099-0.002796j
[2025-07-30 20:29:44] [Iter 3878/4650] R4[1627/2400], Temp: 0.2349, Energy: -85.009575+0.001459j
[2025-07-30 20:29:59] [Iter 3879/4650] R4[1628/2400], Temp: 0.2343, Energy: -84.969990-0.000106j
[2025-07-30 20:30:14] [Iter 3880/4650] R4[1629/2400], Temp: 0.2337, Energy: -85.011571+0.000093j
[2025-07-30 20:30:29] [Iter 3881/4650] R4[1630/2400], Temp: 0.2332, Energy: -85.040987+0.007863j
[2025-07-30 20:30:44] [Iter 3882/4650] R4[1631/2400], Temp: 0.2326, Energy: -85.081644-0.000070j
[2025-07-30 20:30:58] [Iter 3883/4650] R4[1632/2400], Temp: 0.2321, Energy: -85.045162+0.004460j
[2025-07-30 20:31:13] [Iter 3884/4650] R4[1633/2400], Temp: 0.2315, Energy: -85.053476-0.008089j
[2025-07-30 20:31:27] [Iter 3885/4650] R4[1634/2400], Temp: 0.2310, Energy: -85.029012-0.007872j
[2025-07-30 20:31:42] [Iter 3886/4650] R4[1635/2400], Temp: 0.2304, Energy: -85.027119-0.001622j
[2025-07-30 20:31:57] [Iter 3887/4650] R4[1636/2400], Temp: 0.2299, Energy: -85.054978-0.004499j
[2025-07-30 20:32:12] [Iter 3888/4650] R4[1637/2400], Temp: 0.2293, Energy: -85.098069-0.004600j
[2025-07-30 20:32:26] [Iter 3889/4650] R4[1638/2400], Temp: 0.2288, Energy: -85.059767-0.001767j
[2025-07-30 20:32:41] [Iter 3890/4650] R4[1639/2400], Temp: 0.2282, Energy: -85.007518-0.002783j
[2025-07-30 20:32:55] [Iter 3891/4650] R4[1640/2400], Temp: 0.2277, Energy: -84.974873-0.004160j
[2025-07-30 20:33:09] [Iter 3892/4650] R4[1641/2400], Temp: 0.2271, Energy: -84.960298+0.009409j
[2025-07-30 20:33:24] [Iter 3893/4650] R4[1642/2400], Temp: 0.2266, Energy: -85.006082+0.009513j
[2025-07-30 20:33:39] [Iter 3894/4650] R4[1643/2400], Temp: 0.2260, Energy: -84.983271-0.000086j
[2025-07-30 20:33:54] [Iter 3895/4650] R4[1644/2400], Temp: 0.2255, Energy: -85.057617-0.003094j
[2025-07-30 20:34:09] [Iter 3896/4650] R4[1645/2400], Temp: 0.2249, Energy: -85.033022-0.008683j
[2025-07-30 20:34:24] [Iter 3897/4650] R4[1646/2400], Temp: 0.2244, Energy: -84.974323+0.002244j
[2025-07-30 20:34:39] [Iter 3898/4650] R4[1647/2400], Temp: 0.2238, Energy: -85.058564+0.012548j
[2025-07-30 20:34:53] [Iter 3899/4650] R4[1648/2400], Temp: 0.2233, Energy: -85.053078-0.005894j
[2025-07-30 20:35:08] [Iter 3900/4650] R4[1649/2400], Temp: 0.2228, Energy: -85.071783+0.001006j
[2025-07-30 20:35:23] [Iter 3901/4650] R4[1650/2400], Temp: 0.2222, Energy: -85.028215-0.005968j
[2025-07-30 20:35:38] [Iter 3902/4650] R4[1651/2400], Temp: 0.2217, Energy: -85.071041+0.000471j
[2025-07-30 20:35:53] [Iter 3903/4650] R4[1652/2400], Temp: 0.2211, Energy: -85.011205+0.002146j
[2025-07-30 20:36:07] [Iter 3904/4650] R4[1653/2400], Temp: 0.2206, Energy: -85.005412+0.000848j
[2025-07-30 20:36:22] [Iter 3905/4650] R4[1654/2400], Temp: 0.2200, Energy: -85.040960+0.002842j
[2025-07-30 20:36:37] [Iter 3906/4650] R4[1655/2400], Temp: 0.2195, Energy: -85.050380-0.002019j
[2025-07-30 20:36:52] [Iter 3907/4650] R4[1656/2400], Temp: 0.2190, Energy: -85.010124+0.011916j
[2025-07-30 20:37:06] [Iter 3908/4650] R4[1657/2400], Temp: 0.2184, Energy: -85.016020-0.006968j
[2025-07-30 20:37:21] [Iter 3909/4650] R4[1658/2400], Temp: 0.2179, Energy: -85.000194+0.009212j
[2025-07-30 20:37:35] [Iter 3910/4650] R4[1659/2400], Temp: 0.2173, Energy: -85.028816+0.004965j
[2025-07-30 20:37:50] [Iter 3911/4650] R4[1660/2400], Temp: 0.2168, Energy: -85.054384+0.008032j
[2025-07-30 20:38:05] [Iter 3912/4650] R4[1661/2400], Temp: 0.2163, Energy: -85.051625+0.000888j
[2025-07-30 20:38:20] [Iter 3913/4650] R4[1662/2400], Temp: 0.2157, Energy: -85.082873-0.000825j
[2025-07-30 20:38:34] [Iter 3914/4650] R4[1663/2400], Temp: 0.2152, Energy: -85.099913-0.000617j
[2025-07-30 20:38:49] [Iter 3915/4650] R4[1664/2400], Temp: 0.2146, Energy: -85.088906-0.008659j
[2025-07-30 20:39:03] [Iter 3916/4650] R4[1665/2400], Temp: 0.2141, Energy: -85.061093-0.003898j
[2025-07-30 20:39:17] [Iter 3917/4650] R4[1666/2400], Temp: 0.2136, Energy: -85.038534-0.001573j
[2025-07-30 20:39:32] [Iter 3918/4650] R4[1667/2400], Temp: 0.2130, Energy: -85.042997-0.000217j
[2025-07-30 20:39:47] [Iter 3919/4650] R4[1668/2400], Temp: 0.2125, Energy: -85.056785+0.006743j
[2025-07-30 20:40:01] [Iter 3920/4650] R4[1669/2400], Temp: 0.2120, Energy: -85.051812+0.005598j
[2025-07-30 20:40:16] [Iter 3921/4650] R4[1670/2400], Temp: 0.2114, Energy: -84.967095-0.001812j
[2025-07-30 20:40:31] [Iter 3922/4650] R4[1671/2400], Temp: 0.2109, Energy: -85.044610+0.007829j
[2025-07-30 20:40:46] [Iter 3923/4650] R4[1672/2400], Temp: 0.2104, Energy: -85.031502-0.007517j
[2025-07-30 20:41:00] [Iter 3924/4650] R4[1673/2400], Temp: 0.2098, Energy: -84.983520+0.008363j
[2025-07-30 20:41:15] [Iter 3925/4650] R4[1674/2400], Temp: 0.2093, Energy: -85.045166-0.002605j
[2025-07-30 20:41:29] [Iter 3926/4650] R4[1675/2400], Temp: 0.2088, Energy: -84.989227+0.007749j
[2025-07-30 20:41:44] [Iter 3927/4650] R4[1676/2400], Temp: 0.2082, Energy: -84.988962-0.003625j
[2025-07-30 20:41:59] [Iter 3928/4650] R4[1677/2400], Temp: 0.2077, Energy: -85.035455-0.001608j
[2025-07-30 20:42:14] [Iter 3929/4650] R4[1678/2400], Temp: 0.2072, Energy: -85.051019+0.001634j
[2025-07-30 20:42:28] [Iter 3930/4650] R4[1679/2400], Temp: 0.2066, Energy: -85.005932+0.002230j
[2025-07-30 20:42:43] [Iter 3931/4650] R4[1680/2400], Temp: 0.2061, Energy: -85.013332-0.005691j
[2025-07-30 20:42:58] [Iter 3932/4650] R4[1681/2400], Temp: 0.2056, Energy: -84.958875+0.006674j
[2025-07-30 20:43:12] [Iter 3933/4650] R4[1682/2400], Temp: 0.2050, Energy: -85.006783+0.013227j
[2025-07-30 20:43:27] [Iter 3934/4650] R4[1683/2400], Temp: 0.2045, Energy: -85.053160+0.003407j
[2025-07-30 20:43:42] [Iter 3935/4650] R4[1684/2400], Temp: 0.2040, Energy: -85.055304-0.004434j
[2025-07-30 20:43:56] [Iter 3936/4650] R4[1685/2400], Temp: 0.2035, Energy: -85.089458+0.003225j
[2025-07-30 20:44:11] [Iter 3937/4650] R4[1686/2400], Temp: 0.2029, Energy: -85.044799+0.008757j
[2025-07-30 20:44:26] [Iter 3938/4650] R4[1687/2400], Temp: 0.2024, Energy: -85.034081+0.006048j
[2025-07-30 20:44:41] [Iter 3939/4650] R4[1688/2400], Temp: 0.2019, Energy: -84.962555-0.007117j
[2025-07-30 20:44:55] [Iter 3940/4650] R4[1689/2400], Temp: 0.2014, Energy: -84.940585+0.020388j
[2025-07-30 20:45:10] [Iter 3941/4650] R4[1690/2400], Temp: 0.2008, Energy: -84.936785-0.002012j
[2025-07-30 20:45:25] [Iter 3942/4650] R4[1691/2400], Temp: 0.2003, Energy: -84.986156+0.008678j
[2025-07-30 20:45:40] [Iter 3943/4650] R4[1692/2400], Temp: 0.1998, Energy: -84.904226+0.016311j
[2025-07-30 20:45:55] [Iter 3944/4650] R4[1693/2400], Temp: 0.1993, Energy: -84.983203+0.006711j
[2025-07-30 20:46:09] [Iter 3945/4650] R4[1694/2400], Temp: 0.1987, Energy: -85.046644-0.001380j
[2025-07-30 20:46:24] [Iter 3946/4650] R4[1695/2400], Temp: 0.1982, Energy: -85.072752+0.000240j
[2025-07-30 20:46:38] [Iter 3947/4650] R4[1696/2400], Temp: 0.1977, Energy: -84.988389+0.010410j
[2025-07-30 20:46:52] [Iter 3948/4650] R4[1697/2400], Temp: 0.1972, Energy: -84.959354-0.001211j
[2025-07-30 20:47:07] [Iter 3949/4650] R4[1698/2400], Temp: 0.1967, Energy: -84.993107+0.005817j
[2025-07-30 20:47:22] [Iter 3950/4650] R4[1699/2400], Temp: 0.1961, Energy: -84.975048+0.002188j
[2025-07-30 20:47:37] [Iter 3951/4650] R4[1700/2400], Temp: 0.1956, Energy: -84.953699-0.013007j
[2025-07-30 20:47:51] [Iter 3952/4650] R4[1701/2400], Temp: 0.1951, Energy: -84.893666-0.004378j
[2025-07-30 20:48:06] [Iter 3953/4650] R4[1702/2400], Temp: 0.1946, Energy: -84.981019-0.004539j
[2025-07-30 20:48:20] [Iter 3954/4650] R4[1703/2400], Temp: 0.1941, Energy: -84.943892-0.005364j
[2025-07-30 20:48:35] [Iter 3955/4650] R4[1704/2400], Temp: 0.1935, Energy: -84.911056-0.000036j
[2025-07-30 20:48:50] [Iter 3956/4650] R4[1705/2400], Temp: 0.1930, Energy: -84.923348+0.008511j
[2025-07-30 20:49:05] [Iter 3957/4650] R4[1706/2400], Temp: 0.1925, Energy: -84.947915-0.020768j
[2025-07-30 20:49:20] [Iter 3958/4650] R4[1707/2400], Temp: 0.1920, Energy: -84.997290-0.000790j
[2025-07-30 20:49:34] [Iter 3959/4650] R4[1708/2400], Temp: 0.1915, Energy: -84.980952+0.002526j
[2025-07-30 20:49:49] [Iter 3960/4650] R4[1709/2400], Temp: 0.1910, Energy: -85.014480-0.006650j
[2025-07-30 20:50:03] [Iter 3961/4650] R4[1710/2400], Temp: 0.1905, Energy: -84.987703+0.008331j
[2025-07-30 20:50:18] [Iter 3962/4650] R4[1711/2400], Temp: 0.1899, Energy: -84.910965-0.007009j
[2025-07-30 20:50:33] [Iter 3963/4650] R4[1712/2400], Temp: 0.1894, Energy: -84.962927+0.000228j
[2025-07-30 20:50:48] [Iter 3964/4650] R4[1713/2400], Temp: 0.1889, Energy: -85.000460-0.003067j
[2025-07-30 20:51:03] [Iter 3965/4650] R4[1714/2400], Temp: 0.1884, Energy: -84.987022-0.000561j
[2025-07-30 20:51:18] [Iter 3966/4650] R4[1715/2400], Temp: 0.1879, Energy: -84.940484+0.001220j
[2025-07-30 20:51:33] [Iter 3967/4650] R4[1716/2400], Temp: 0.1874, Energy: -85.037069+0.000106j
[2025-07-30 20:51:47] [Iter 3968/4650] R4[1717/2400], Temp: 0.1869, Energy: -84.975321+0.004225j
[2025-07-30 20:52:02] [Iter 3969/4650] R4[1718/2400], Temp: 0.1864, Energy: -85.027962-0.008195j
[2025-07-30 20:52:16] [Iter 3970/4650] R4[1719/2400], Temp: 0.1858, Energy: -85.013831-0.011265j
[2025-07-30 20:52:31] [Iter 3971/4650] R4[1720/2400], Temp: 0.1853, Energy: -84.932229+0.003852j
[2025-07-30 20:52:46] [Iter 3972/4650] R4[1721/2400], Temp: 0.1848, Energy: -84.911120-0.001059j
[2025-07-30 20:53:01] [Iter 3973/4650] R4[1722/2400], Temp: 0.1843, Energy: -85.010765+0.007067j
[2025-07-30 20:53:16] [Iter 3974/4650] R4[1723/2400], Temp: 0.1838, Energy: -84.988210-0.003264j
[2025-07-30 20:53:30] [Iter 3975/4650] R4[1724/2400], Temp: 0.1833, Energy: -84.914649-0.000055j
[2025-07-30 20:53:45] [Iter 3976/4650] R4[1725/2400], Temp: 0.1828, Energy: -84.982229+0.000901j
[2025-07-30 20:54:01] [Iter 3977/4650] R4[1726/2400], Temp: 0.1823, Energy: -84.998614-0.003277j
[2025-07-30 20:54:15] [Iter 3978/4650] R4[1727/2400], Temp: 0.1818, Energy: -84.997326+0.001351j
[2025-07-30 20:54:30] [Iter 3979/4650] R4[1728/2400], Temp: 0.1813, Energy: -84.951821-0.004665j
[2025-07-30 20:54:45] [Iter 3980/4650] R4[1729/2400], Temp: 0.1808, Energy: -84.976607+0.005688j
[2025-07-30 20:54:59] [Iter 3981/4650] R4[1730/2400], Temp: 0.1803, Energy: -85.052658+0.000086j
[2025-07-30 20:55:14] [Iter 3982/4650] R4[1731/2400], Temp: 0.1798, Energy: -85.035989+0.001313j
[2025-07-30 20:55:29] [Iter 3983/4650] R4[1732/2400], Temp: 0.1793, Energy: -84.964076-0.001508j
[2025-07-30 20:55:44] [Iter 3984/4650] R4[1733/2400], Temp: 0.1788, Energy: -84.995981-0.000354j
[2025-07-30 20:55:59] [Iter 3985/4650] R4[1734/2400], Temp: 0.1783, Energy: -84.940809+0.006068j
[2025-07-30 20:56:13] [Iter 3986/4650] R4[1735/2400], Temp: 0.1778, Energy: -84.997418-0.003193j
[2025-07-30 20:56:28] [Iter 3987/4650] R4[1736/2400], Temp: 0.1773, Energy: -84.973898-0.005127j
[2025-07-30 20:56:43] [Iter 3988/4650] R4[1737/2400], Temp: 0.1768, Energy: -84.976632+0.011707j
[2025-07-30 20:56:57] [Iter 3989/4650] R4[1738/2400], Temp: 0.1763, Energy: -84.936619-0.010309j
[2025-07-30 20:57:12] [Iter 3990/4650] R4[1739/2400], Temp: 0.1758, Energy: -84.943352+0.007682j
[2025-07-30 20:57:26] [Iter 3991/4650] R4[1740/2400], Temp: 0.1753, Energy: -84.911891-0.018978j
[2025-07-30 20:57:41] [Iter 3992/4650] R4[1741/2400], Temp: 0.1748, Energy: -84.937358-0.003692j
[2025-07-30 20:57:56] [Iter 3993/4650] R4[1742/2400], Temp: 0.1743, Energy: -84.962374+0.011864j
[2025-07-30 20:58:11] [Iter 3994/4650] R4[1743/2400], Temp: 0.1738, Energy: -85.095460-0.000757j
[2025-07-30 20:58:26] [Iter 3995/4650] R4[1744/2400], Temp: 0.1733, Energy: -85.077789+0.006956j
[2025-07-30 20:58:41] [Iter 3996/4650] R4[1745/2400], Temp: 0.1728, Energy: -85.000718-0.016706j
[2025-07-30 20:58:55] [Iter 3997/4650] R4[1746/2400], Temp: 0.1723, Energy: -85.043696+0.001323j
[2025-07-30 20:59:10] [Iter 3998/4650] R4[1747/2400], Temp: 0.1718, Energy: -85.042149-0.000273j
[2025-07-30 20:59:24] [Iter 3999/4650] R4[1748/2400], Temp: 0.1713, Energy: -84.983296-0.007571j
[2025-07-30 20:59:39] [Iter 4000/4650] R4[1749/2400], Temp: 0.1708, Energy: -85.084315+0.004900j
[2025-07-30 20:59:39] ✓ Checkpoint saved: checkpoint_iter_004000.pkl
[2025-07-30 20:59:54] [Iter 4001/4650] R4[1750/2400], Temp: 0.1703, Energy: -85.049911-0.009669j
[2025-07-30 21:00:08] [Iter 4002/4650] R4[1751/2400], Temp: 0.1698, Energy: -85.018657+0.002976j
[2025-07-30 21:00:23] [Iter 4003/4650] R4[1752/2400], Temp: 0.1693, Energy: -85.002930+0.004652j
[2025-07-30 21:00:38] [Iter 4004/4650] R4[1753/2400], Temp: 0.1689, Energy: -85.037927-0.007771j
[2025-07-30 21:00:52] [Iter 4005/4650] R4[1754/2400], Temp: 0.1684, Energy: -85.029590-0.006750j
[2025-07-30 21:01:07] [Iter 4006/4650] R4[1755/2400], Temp: 0.1679, Energy: -84.996970+0.004775j
[2025-07-30 21:01:21] [Iter 4007/4650] R4[1756/2400], Temp: 0.1674, Energy: -84.975569-0.003391j
[2025-07-30 21:01:36] [Iter 4008/4650] R4[1757/2400], Temp: 0.1669, Energy: -84.985390-0.001350j
[2025-07-30 21:01:50] [Iter 4009/4650] R4[1758/2400], Temp: 0.1664, Energy: -84.942676-0.004206j
[2025-07-30 21:02:05] [Iter 4010/4650] R4[1759/2400], Temp: 0.1659, Energy: -84.966525-0.004119j
[2025-07-30 21:02:20] [Iter 4011/4650] R4[1760/2400], Temp: 0.1654, Energy: -84.940278-0.004669j
[2025-07-30 21:02:35] [Iter 4012/4650] R4[1761/2400], Temp: 0.1649, Energy: -84.946948-0.012552j
[2025-07-30 21:02:49] [Iter 4013/4650] R4[1762/2400], Temp: 0.1645, Energy: -84.970918+0.001123j
[2025-07-30 21:03:04] [Iter 4014/4650] R4[1763/2400], Temp: 0.1640, Energy: -84.956042-0.001667j
[2025-07-30 21:03:19] [Iter 4015/4650] R4[1764/2400], Temp: 0.1635, Energy: -84.871109-0.001848j
[2025-07-30 21:03:34] [Iter 4016/4650] R4[1765/2400], Temp: 0.1630, Energy: -84.932484+0.003683j
[2025-07-30 21:03:49] [Iter 4017/4650] R4[1766/2400], Temp: 0.1625, Energy: -84.896065+0.000344j
[2025-07-30 21:04:03] [Iter 4018/4650] R4[1767/2400], Temp: 0.1620, Energy: -84.950887+0.002599j
[2025-07-30 21:04:18] [Iter 4019/4650] R4[1768/2400], Temp: 0.1616, Energy: -84.961268+0.001408j
[2025-07-30 21:04:32] [Iter 4020/4650] R4[1769/2400], Temp: 0.1611, Energy: -84.998467+0.004189j
[2025-07-30 21:04:47] [Iter 4021/4650] R4[1770/2400], Temp: 0.1606, Energy: -84.930002-0.007817j
[2025-07-30 21:05:02] [Iter 4022/4650] R4[1771/2400], Temp: 0.1601, Energy: -84.947059+0.007054j
[2025-07-30 21:05:17] [Iter 4023/4650] R4[1772/2400], Temp: 0.1596, Energy: -85.043296-0.007277j
[2025-07-30 21:05:32] [Iter 4024/4650] R4[1773/2400], Temp: 0.1592, Energy: -85.038795+0.007859j
[2025-07-30 21:05:46] [Iter 4025/4650] R4[1774/2400], Temp: 0.1587, Energy: -85.047947-0.006130j
[2025-07-30 21:06:01] [Iter 4026/4650] R4[1775/2400], Temp: 0.1582, Energy: -85.060103+0.003594j
[2025-07-30 21:06:15] [Iter 4027/4650] R4[1776/2400], Temp: 0.1577, Energy: -85.124293-0.004314j
[2025-07-30 21:06:30] [Iter 4028/4650] R4[1777/2400], Temp: 0.1572, Energy: -85.111260+0.000606j
[2025-07-30 21:06:44] [Iter 4029/4650] R4[1778/2400], Temp: 0.1568, Energy: -85.108284+0.014560j
[2025-07-30 21:06:59] [Iter 4030/4650] R4[1779/2400], Temp: 0.1563, Energy: -85.143649+0.001404j
[2025-07-30 21:07:13] [Iter 4031/4650] R4[1780/2400], Temp: 0.1558, Energy: -85.120366+0.000089j
[2025-07-30 21:07:28] [Iter 4032/4650] R4[1781/2400], Temp: 0.1553, Energy: -85.046513-0.005706j
[2025-07-30 21:07:43] [Iter 4033/4650] R4[1782/2400], Temp: 0.1549, Energy: -85.052568+0.005448j
[2025-07-30 21:07:58] [Iter 4034/4650] R4[1783/2400], Temp: 0.1544, Energy: -85.029365-0.003193j
[2025-07-30 21:08:12] [Iter 4035/4650] R4[1784/2400], Temp: 0.1539, Energy: -85.080767-0.003800j
[2025-07-30 21:08:27] [Iter 4036/4650] R4[1785/2400], Temp: 0.1535, Energy: -85.042229-0.008884j
[2025-07-30 21:08:42] [Iter 4037/4650] R4[1786/2400], Temp: 0.1530, Energy: -84.999574-0.007914j
[2025-07-30 21:08:56] [Iter 4038/4650] R4[1787/2400], Temp: 0.1525, Energy: -84.994800+0.003207j
[2025-07-30 21:09:11] [Iter 4039/4650] R4[1788/2400], Temp: 0.1520, Energy: -85.091073+0.009219j
[2025-07-30 21:09:26] [Iter 4040/4650] R4[1789/2400], Temp: 0.1516, Energy: -85.109850-0.002599j
[2025-07-30 21:09:41] [Iter 4041/4650] R4[1790/2400], Temp: 0.1511, Energy: -85.059311-0.003125j
[2025-07-30 21:09:56] [Iter 4042/4650] R4[1791/2400], Temp: 0.1506, Energy: -85.055998+0.000986j
[2025-07-30 21:10:11] [Iter 4043/4650] R4[1792/2400], Temp: 0.1502, Energy: -85.063752-0.003187j
[2025-07-30 21:10:25] [Iter 4044/4650] R4[1793/2400], Temp: 0.1497, Energy: -85.123891-0.000716j
[2025-07-30 21:10:40] [Iter 4045/4650] R4[1794/2400], Temp: 0.1492, Energy: -85.072819+0.013201j
[2025-07-30 21:10:54] [Iter 4046/4650] R4[1795/2400], Temp: 0.1488, Energy: -85.042472+0.011877j
[2025-07-30 21:11:09] [Iter 4047/4650] R4[1796/2400], Temp: 0.1483, Energy: -85.077063-0.004666j
[2025-07-30 21:11:24] [Iter 4048/4650] R4[1797/2400], Temp: 0.1478, Energy: -85.087302+0.002747j
[2025-07-30 21:11:39] [Iter 4049/4650] R4[1798/2400], Temp: 0.1474, Energy: -85.093346+0.004738j
[2025-07-30 21:11:54] [Iter 4050/4650] R4[1799/2400], Temp: 0.1469, Energy: -85.098318+0.005201j
[2025-07-30 21:12:08] [Iter 4051/4650] R4[1800/2400], Temp: 0.1464, Energy: -85.066236+0.000402j
[2025-07-30 21:12:23] [Iter 4052/4650] R4[1801/2400], Temp: 0.1460, Energy: -85.049546-0.003119j
[2025-07-30 21:12:38] [Iter 4053/4650] R4[1802/2400], Temp: 0.1455, Energy: -85.066590-0.004719j
[2025-07-30 21:12:52] [Iter 4054/4650] R4[1803/2400], Temp: 0.1451, Energy: -84.990670+0.000331j
[2025-07-30 21:13:07] [Iter 4055/4650] R4[1804/2400], Temp: 0.1446, Energy: -85.037649+0.005003j
[2025-07-30 21:13:22] [Iter 4056/4650] R4[1805/2400], Temp: 0.1441, Energy: -85.100011-0.002182j
[2025-07-30 21:13:36] [Iter 4057/4650] R4[1806/2400], Temp: 0.1437, Energy: -85.078627-0.010415j
[2025-07-30 21:13:51] [Iter 4058/4650] R4[1807/2400], Temp: 0.1432, Energy: -85.126858+0.000933j
[2025-07-30 21:14:06] [Iter 4059/4650] R4[1808/2400], Temp: 0.1428, Energy: -85.106928+0.010901j
[2025-07-30 21:14:20] [Iter 4060/4650] R4[1809/2400], Temp: 0.1423, Energy: -85.045603+0.006146j
[2025-07-30 21:14:35] [Iter 4061/4650] R4[1810/2400], Temp: 0.1418, Energy: -84.995740+0.006076j
[2025-07-30 21:14:49] [Iter 4062/4650] R4[1811/2400], Temp: 0.1414, Energy: -85.053421-0.002591j
[2025-07-30 21:15:04] [Iter 4063/4650] R4[1812/2400], Temp: 0.1409, Energy: -85.001536-0.000972j
[2025-07-30 21:15:19] [Iter 4064/4650] R4[1813/2400], Temp: 0.1405, Energy: -84.999681+0.006814j
[2025-07-30 21:15:34] [Iter 4065/4650] R4[1814/2400], Temp: 0.1400, Energy: -85.084503-0.005273j
[2025-07-30 21:15:48] [Iter 4066/4650] R4[1815/2400], Temp: 0.1396, Energy: -85.058161-0.001630j
[2025-07-30 21:16:03] [Iter 4067/4650] R4[1816/2400], Temp: 0.1391, Energy: -85.047172+0.007225j
[2025-07-30 21:16:18] [Iter 4068/4650] R4[1817/2400], Temp: 0.1387, Energy: -85.032847+0.002415j
[2025-07-30 21:16:32] [Iter 4069/4650] R4[1818/2400], Temp: 0.1382, Energy: -84.966321+0.001703j
[2025-07-30 21:16:47] [Iter 4070/4650] R4[1819/2400], Temp: 0.1378, Energy: -84.995744+0.001059j
[2025-07-30 21:17:02] [Iter 4071/4650] R4[1820/2400], Temp: 0.1373, Energy: -84.976174-0.001397j
[2025-07-30 21:17:17] [Iter 4072/4650] R4[1821/2400], Temp: 0.1369, Energy: -84.943332-0.002619j
[2025-07-30 21:17:32] [Iter 4073/4650] R4[1822/2400], Temp: 0.1364, Energy: -85.022225-0.006579j
[2025-07-30 21:17:46] [Iter 4074/4650] R4[1823/2400], Temp: 0.1360, Energy: -84.987768+0.003470j
[2025-07-30 21:18:00] [Iter 4075/4650] R4[1824/2400], Temp: 0.1355, Energy: -84.932590+0.013515j
[2025-07-30 21:18:15] [Iter 4076/4650] R4[1825/2400], Temp: 0.1351, Energy: -84.983942+0.004538j
[2025-07-30 21:18:29] [Iter 4077/4650] R4[1826/2400], Temp: 0.1346, Energy: -85.067308+0.002100j
[2025-07-30 21:18:44] [Iter 4078/4650] R4[1827/2400], Temp: 0.1342, Energy: -85.061621+0.002165j
[2025-07-30 21:18:58] [Iter 4079/4650] R4[1828/2400], Temp: 0.1337, Energy: -85.015400-0.002220j
[2025-07-30 21:19:12] [Iter 4080/4650] R4[1829/2400], Temp: 0.1333, Energy: -84.973213-0.001834j
[2025-07-30 21:19:27] [Iter 4081/4650] R4[1830/2400], Temp: 0.1328, Energy: -85.026271+0.000371j
[2025-07-30 21:19:42] [Iter 4082/4650] R4[1831/2400], Temp: 0.1324, Energy: -85.054202+0.001912j
[2025-07-30 21:19:57] [Iter 4083/4650] R4[1832/2400], Temp: 0.1320, Energy: -85.022971+0.005710j
[2025-07-30 21:20:12] [Iter 4084/4650] R4[1833/2400], Temp: 0.1315, Energy: -85.013362-0.002576j
[2025-07-30 21:20:27] [Iter 4085/4650] R4[1834/2400], Temp: 0.1311, Energy: -85.022573+0.014144j
[2025-07-30 21:20:41] [Iter 4086/4650] R4[1835/2400], Temp: 0.1306, Energy: -85.031932-0.007024j
[2025-07-30 21:20:56] [Iter 4087/4650] R4[1836/2400], Temp: 0.1302, Energy: -85.037631-0.002166j
[2025-07-30 21:21:11] [Iter 4088/4650] R4[1837/2400], Temp: 0.1297, Energy: -85.067454-0.005484j
[2025-07-30 21:21:25] [Iter 4089/4650] R4[1838/2400], Temp: 0.1293, Energy: -85.069218-0.006887j
[2025-07-30 21:21:39] [Iter 4090/4650] R4[1839/2400], Temp: 0.1289, Energy: -85.014877-0.006676j
[2025-07-30 21:21:54] [Iter 4091/4650] R4[1840/2400], Temp: 0.1284, Energy: -85.046202-0.001989j
[2025-07-30 21:22:09] [Iter 4092/4650] R4[1841/2400], Temp: 0.1280, Energy: -85.073151-0.012129j
[2025-07-30 21:22:24] [Iter 4093/4650] R4[1842/2400], Temp: 0.1276, Energy: -84.987237-0.012036j
[2025-07-30 21:22:38] [Iter 4094/4650] R4[1843/2400], Temp: 0.1271, Energy: -84.923437-0.000712j
[2025-07-30 21:22:53] [Iter 4095/4650] R4[1844/2400], Temp: 0.1267, Energy: -84.989759-0.019660j
[2025-07-30 21:23:07] [Iter 4096/4650] R4[1845/2400], Temp: 0.1262, Energy: -84.953425-0.008497j
[2025-07-30 21:23:22] [Iter 4097/4650] R4[1846/2400], Temp: 0.1258, Energy: -84.958307+0.000515j
[2025-07-30 21:23:37] [Iter 4098/4650] R4[1847/2400], Temp: 0.1254, Energy: -84.988970+0.005335j
[2025-07-30 21:23:52] [Iter 4099/4650] R4[1848/2400], Temp: 0.1249, Energy: -85.022923-0.012883j
[2025-07-30 21:24:07] [Iter 4100/4650] R4[1849/2400], Temp: 0.1245, Energy: -85.015798+0.005614j
[2025-07-30 21:24:22] [Iter 4101/4650] R4[1850/2400], Temp: 0.1241, Energy: -84.989722-0.011511j
[2025-07-30 21:24:37] [Iter 4102/4650] R4[1851/2400], Temp: 0.1236, Energy: -84.967365-0.011259j
[2025-07-30 21:24:52] [Iter 4103/4650] R4[1852/2400], Temp: 0.1232, Energy: -84.972833-0.007775j
[2025-07-30 21:25:06] [Iter 4104/4650] R4[1853/2400], Temp: 0.1228, Energy: -85.050364-0.004837j
[2025-07-30 21:25:21] [Iter 4105/4650] R4[1854/2400], Temp: 0.1224, Energy: -85.078908-0.004319j
[2025-07-30 21:25:36] [Iter 4106/4650] R4[1855/2400], Temp: 0.1219, Energy: -85.087837-0.004407j
[2025-07-30 21:25:51] [Iter 4107/4650] R4[1856/2400], Temp: 0.1215, Energy: -85.080941-0.009700j
[2025-07-30 21:26:05] [Iter 4108/4650] R4[1857/2400], Temp: 0.1211, Energy: -85.116230-0.003277j
[2025-07-30 21:26:19] [Iter 4109/4650] R4[1858/2400], Temp: 0.1206, Energy: -85.102426-0.001852j
[2025-07-30 21:26:34] [Iter 4110/4650] R4[1859/2400], Temp: 0.1202, Energy: -85.108566+0.006197j
[2025-07-30 21:26:49] [Iter 4111/4650] R4[1860/2400], Temp: 0.1198, Energy: -85.107881-0.010124j
[2025-07-30 21:27:04] [Iter 4112/4650] R4[1861/2400], Temp: 0.1194, Energy: -85.094368-0.013651j
[2025-07-30 21:27:18] [Iter 4113/4650] R4[1862/2400], Temp: 0.1189, Energy: -85.030963-0.008848j
[2025-07-30 21:27:32] [Iter 4114/4650] R4[1863/2400], Temp: 0.1185, Energy: -85.110086-0.004214j
[2025-07-30 21:27:47] [Iter 4115/4650] R4[1864/2400], Temp: 0.1181, Energy: -85.063167-0.005855j
[2025-07-30 21:28:02] [Iter 4116/4650] R4[1865/2400], Temp: 0.1177, Energy: -85.010052-0.008963j
[2025-07-30 21:28:17] [Iter 4117/4650] R4[1866/2400], Temp: 0.1173, Energy: -85.093863-0.002562j
[2025-07-30 21:28:31] [Iter 4118/4650] R4[1867/2400], Temp: 0.1168, Energy: -85.038231-0.001268j
[2025-07-30 21:28:46] [Iter 4119/4650] R4[1868/2400], Temp: 0.1164, Energy: -85.044702-0.003531j
[2025-07-30 21:29:01] [Iter 4120/4650] R4[1869/2400], Temp: 0.1160, Energy: -85.150132+0.000538j
[2025-07-30 21:29:15] [Iter 4121/4650] R4[1870/2400], Temp: 0.1156, Energy: -85.144460-0.002278j
[2025-07-30 21:29:30] [Iter 4122/4650] R4[1871/2400], Temp: 0.1152, Energy: -85.118333+0.002419j
[2025-07-30 21:29:45] [Iter 4123/4650] R4[1872/2400], Temp: 0.1147, Energy: -85.154970-0.000656j
[2025-07-30 21:29:59] [Iter 4124/4650] R4[1873/2400], Temp: 0.1143, Energy: -85.077110+0.005274j
[2025-07-30 21:30:14] [Iter 4125/4650] R4[1874/2400], Temp: 0.1139, Energy: -85.099156+0.003300j
[2025-07-30 21:30:29] [Iter 4126/4650] R4[1875/2400], Temp: 0.1135, Energy: -85.052863+0.001693j
[2025-07-30 21:30:44] [Iter 4127/4650] R4[1876/2400], Temp: 0.1131, Energy: -85.029045-0.003858j
[2025-07-30 21:30:59] [Iter 4128/4650] R4[1877/2400], Temp: 0.1127, Energy: -84.975867-0.002403j
[2025-07-30 21:31:13] [Iter 4129/4650] R4[1878/2400], Temp: 0.1123, Energy: -85.025744-0.003339j
[2025-07-30 21:31:28] [Iter 4130/4650] R4[1879/2400], Temp: 0.1118, Energy: -85.006742-0.003378j
[2025-07-30 21:31:42] [Iter 4131/4650] R4[1880/2400], Temp: 0.1114, Energy: -85.008029+0.000214j
[2025-07-30 21:31:57] [Iter 4132/4650] R4[1881/2400], Temp: 0.1110, Energy: -85.092172-0.002013j
[2025-07-30 21:32:11] [Iter 4133/4650] R4[1882/2400], Temp: 0.1106, Energy: -85.095763+0.006119j
[2025-07-30 21:32:26] [Iter 4134/4650] R4[1883/2400], Temp: 0.1102, Energy: -85.064223-0.009203j
[2025-07-30 21:32:40] [Iter 4135/4650] R4[1884/2400], Temp: 0.1098, Energy: -85.047633-0.002230j
[2025-07-30 21:32:55] [Iter 4136/4650] R4[1885/2400], Temp: 0.1094, Energy: -85.060773+0.006721j
[2025-07-30 21:33:10] [Iter 4137/4650] R4[1886/2400], Temp: 0.1090, Energy: -85.011594-0.001311j
[2025-07-30 21:33:25] [Iter 4138/4650] R4[1887/2400], Temp: 0.1086, Energy: -85.050777-0.002174j
[2025-07-30 21:33:40] [Iter 4139/4650] R4[1888/2400], Temp: 0.1082, Energy: -85.107656-0.006109j
[2025-07-30 21:33:55] [Iter 4140/4650] R4[1889/2400], Temp: 0.1077, Energy: -85.107882-0.014977j
[2025-07-30 21:34:09] [Iter 4141/4650] R4[1890/2400], Temp: 0.1073, Energy: -85.079465+0.000291j
[2025-07-30 21:34:24] [Iter 4142/4650] R4[1891/2400], Temp: 0.1069, Energy: -85.044868-0.003199j
[2025-07-30 21:34:38] [Iter 4143/4650] R4[1892/2400], Temp: 0.1065, Energy: -85.042139-0.002045j
[2025-07-30 21:34:53] [Iter 4144/4650] R4[1893/2400], Temp: 0.1061, Energy: -85.017871+0.013491j
[2025-07-30 21:35:08] [Iter 4145/4650] R4[1894/2400], Temp: 0.1057, Energy: -85.048712-0.005459j
[2025-07-30 21:35:23] [Iter 4146/4650] R4[1895/2400], Temp: 0.1053, Energy: -85.035263-0.004205j
[2025-07-30 21:35:38] [Iter 4147/4650] R4[1896/2400], Temp: 0.1049, Energy: -85.005076+0.003931j
[2025-07-30 21:35:52] [Iter 4148/4650] R4[1897/2400], Temp: 0.1045, Energy: -85.017964-0.007699j
[2025-07-30 21:36:07] [Iter 4149/4650] R4[1898/2400], Temp: 0.1041, Energy: -84.995867+0.002497j
[2025-07-30 21:36:21] [Iter 4150/4650] R4[1899/2400], Temp: 0.1037, Energy: -85.053618-0.001650j
[2025-07-30 21:36:36] [Iter 4151/4650] R4[1900/2400], Temp: 0.1033, Energy: -85.016016+0.004230j
[2025-07-30 21:36:51] [Iter 4152/4650] R4[1901/2400], Temp: 0.1029, Energy: -84.999890+0.005857j
[2025-07-30 21:37:06] [Iter 4153/4650] R4[1902/2400], Temp: 0.1025, Energy: -85.090927-0.004925j
[2025-07-30 21:37:21] [Iter 4154/4650] R4[1903/2400], Temp: 0.1021, Energy: -85.063253+0.000702j
[2025-07-30 21:37:36] [Iter 4155/4650] R4[1904/2400], Temp: 0.1017, Energy: -85.083825+0.004352j
[2025-07-30 21:37:50] [Iter 4156/4650] R4[1905/2400], Temp: 0.1013, Energy: -85.048643-0.002463j
[2025-07-30 21:38:05] [Iter 4157/4650] R4[1906/2400], Temp: 0.1009, Energy: -85.110099-0.006303j
[2025-07-30 21:38:20] [Iter 4158/4650] R4[1907/2400], Temp: 0.1006, Energy: -85.086492-0.003459j
[2025-07-30 21:38:35] [Iter 4159/4650] R4[1908/2400], Temp: 0.1002, Energy: -85.107125+0.003089j
[2025-07-30 21:38:49] [Iter 4160/4650] R4[1909/2400], Temp: 0.0998, Energy: -85.086367+0.003166j
[2025-07-30 21:39:04] [Iter 4161/4650] R4[1910/2400], Temp: 0.0994, Energy: -85.095836-0.002677j
[2025-07-30 21:39:19] [Iter 4162/4650] R4[1911/2400], Temp: 0.0990, Energy: -84.990684+0.000821j
[2025-07-30 21:39:33] [Iter 4163/4650] R4[1912/2400], Temp: 0.0986, Energy: -85.056771-0.001723j
[2025-07-30 21:39:48] [Iter 4164/4650] R4[1913/2400], Temp: 0.0982, Energy: -85.094426-0.005971j
[2025-07-30 21:40:02] [Iter 4165/4650] R4[1914/2400], Temp: 0.0978, Energy: -85.166572-0.008291j
[2025-07-30 21:40:16] [Iter 4166/4650] R4[1915/2400], Temp: 0.0974, Energy: -85.151376-0.001459j
[2025-07-30 21:40:31] [Iter 4167/4650] R4[1916/2400], Temp: 0.0970, Energy: -85.128875+0.003955j
[2025-07-30 21:40:45] [Iter 4168/4650] R4[1917/2400], Temp: 0.0966, Energy: -85.023235-0.000684j
[2025-07-30 21:41:00] [Iter 4169/4650] R4[1918/2400], Temp: 0.0963, Energy: -85.045178+0.001687j
[2025-07-30 21:41:15] [Iter 4170/4650] R4[1919/2400], Temp: 0.0959, Energy: -85.095048+0.011473j
[2025-07-30 21:41:29] [Iter 4171/4650] R4[1920/2400], Temp: 0.0955, Energy: -85.090021-0.001144j
[2025-07-30 21:41:44] [Iter 4172/4650] R4[1921/2400], Temp: 0.0951, Energy: -85.119870+0.001914j
[2025-07-30 21:41:59] [Iter 4173/4650] R4[1922/2400], Temp: 0.0947, Energy: -85.074634-0.001603j
[2025-07-30 21:42:13] [Iter 4174/4650] R4[1923/2400], Temp: 0.0943, Energy: -85.115830-0.006183j
[2025-07-30 21:42:28] [Iter 4175/4650] R4[1924/2400], Temp: 0.0940, Energy: -85.135027-0.002119j
[2025-07-30 21:42:42] [Iter 4176/4650] R4[1925/2400], Temp: 0.0936, Energy: -85.044847-0.005475j
[2025-07-30 21:42:56] [Iter 4177/4650] R4[1926/2400], Temp: 0.0932, Energy: -85.050820+0.007659j
[2025-07-30 21:43:11] [Iter 4178/4650] R4[1927/2400], Temp: 0.0928, Energy: -85.049645+0.010207j
[2025-07-30 21:43:25] [Iter 4179/4650] R4[1928/2400], Temp: 0.0924, Energy: -85.097856-0.001833j
[2025-07-30 21:43:40] [Iter 4180/4650] R4[1929/2400], Temp: 0.0921, Energy: -85.066138-0.000067j
[2025-07-30 21:43:55] [Iter 4181/4650] R4[1930/2400], Temp: 0.0917, Energy: -85.081475-0.003990j
[2025-07-30 21:44:09] [Iter 4182/4650] R4[1931/2400], Temp: 0.0913, Energy: -85.109860-0.001379j
[2025-07-30 21:44:24] [Iter 4183/4650] R4[1932/2400], Temp: 0.0909, Energy: -85.088758+0.002552j
[2025-07-30 21:44:39] [Iter 4184/4650] R4[1933/2400], Temp: 0.0905, Energy: -85.155630+0.005616j
[2025-07-30 21:44:53] [Iter 4185/4650] R4[1934/2400], Temp: 0.0902, Energy: -85.145426-0.001658j
[2025-07-30 21:45:08] [Iter 4186/4650] R4[1935/2400], Temp: 0.0898, Energy: -85.093938-0.004684j
[2025-07-30 21:45:22] [Iter 4187/4650] R4[1936/2400], Temp: 0.0894, Energy: -85.129404+0.000040j
[2025-07-30 21:45:37] [Iter 4188/4650] R4[1937/2400], Temp: 0.0891, Energy: -85.113985+0.001016j
[2025-07-30 21:45:52] [Iter 4189/4650] R4[1938/2400], Temp: 0.0887, Energy: -85.144257-0.004342j
[2025-07-30 21:46:06] [Iter 4190/4650] R4[1939/2400], Temp: 0.0883, Energy: -85.111179-0.011787j
[2025-07-30 21:46:21] [Iter 4191/4650] R4[1940/2400], Temp: 0.0879, Energy: -85.140471+0.003807j
[2025-07-30 21:46:35] [Iter 4192/4650] R4[1941/2400], Temp: 0.0876, Energy: -85.129880+0.006970j
[2025-07-30 21:46:50] [Iter 4193/4650] R4[1942/2400], Temp: 0.0872, Energy: -85.166431+0.007232j
[2025-07-30 21:47:05] [Iter 4194/4650] R4[1943/2400], Temp: 0.0868, Energy: -85.128717+0.007125j
[2025-07-30 21:47:20] [Iter 4195/4650] R4[1944/2400], Temp: 0.0865, Energy: -85.141615-0.001625j
[2025-07-30 21:47:34] [Iter 4196/4650] R4[1945/2400], Temp: 0.0861, Energy: -85.106694+0.008378j
[2025-07-30 21:47:49] [Iter 4197/4650] R4[1946/2400], Temp: 0.0857, Energy: -85.171412+0.001349j
[2025-07-30 21:48:03] [Iter 4198/4650] R4[1947/2400], Temp: 0.0854, Energy: -85.140224+0.004231j
[2025-07-30 21:48:18] [Iter 4199/4650] R4[1948/2400], Temp: 0.0850, Energy: -85.083646-0.000593j
[2025-07-30 21:48:32] [Iter 4200/4650] R4[1949/2400], Temp: 0.0846, Energy: -85.116251+0.006993j
[2025-07-30 21:48:47] [Iter 4201/4650] R4[1950/2400], Temp: 0.0843, Energy: -85.119969+0.003115j
[2025-07-30 21:49:01] [Iter 4202/4650] R4[1951/2400], Temp: 0.0839, Energy: -85.066280+0.004492j
[2025-07-30 21:49:16] [Iter 4203/4650] R4[1952/2400], Temp: 0.0835, Energy: -85.044213+0.003987j
[2025-07-30 21:49:31] [Iter 4204/4650] R4[1953/2400], Temp: 0.0832, Energy: -85.049977+0.006545j
[2025-07-30 21:49:45] [Iter 4205/4650] R4[1954/2400], Temp: 0.0828, Energy: -85.077777+0.010537j
[2025-07-30 21:50:01] [Iter 4206/4650] R4[1955/2400], Temp: 0.0825, Energy: -85.089783+0.009750j
[2025-07-30 21:50:15] [Iter 4207/4650] R4[1956/2400], Temp: 0.0821, Energy: -85.056912+0.008301j
[2025-07-30 21:50:30] [Iter 4208/4650] R4[1957/2400], Temp: 0.0817, Energy: -85.000778+0.006666j
[2025-07-30 21:50:45] [Iter 4209/4650] R4[1958/2400], Temp: 0.0814, Energy: -84.984462+0.001693j
[2025-07-30 21:51:00] [Iter 4210/4650] R4[1959/2400], Temp: 0.0810, Energy: -85.069812+0.003074j
[2025-07-30 21:51:14] [Iter 4211/4650] R4[1960/2400], Temp: 0.0807, Energy: -85.108747+0.003851j
[2025-07-30 21:51:29] [Iter 4212/4650] R4[1961/2400], Temp: 0.0803, Energy: -85.154323+0.005287j
[2025-07-30 21:51:44] [Iter 4213/4650] R4[1962/2400], Temp: 0.0800, Energy: -85.111906+0.005691j
[2025-07-30 21:51:58] [Iter 4214/4650] R4[1963/2400], Temp: 0.0796, Energy: -85.161221+0.005524j
[2025-07-30 21:52:13] [Iter 4215/4650] R4[1964/2400], Temp: 0.0792, Energy: -85.188971-0.002733j
[2025-07-30 21:52:28] [Iter 4216/4650] R4[1965/2400], Temp: 0.0789, Energy: -85.070611+0.004918j
[2025-07-30 21:52:43] [Iter 4217/4650] R4[1966/2400], Temp: 0.0785, Energy: -85.110277+0.002466j
[2025-07-30 21:52:57] [Iter 4218/4650] R4[1967/2400], Temp: 0.0782, Energy: -85.103922+0.003949j
[2025-07-30 21:53:12] [Iter 4219/4650] R4[1968/2400], Temp: 0.0778, Energy: -85.052649+0.001992j
[2025-07-30 21:53:28] [Iter 4220/4650] R4[1969/2400], Temp: 0.0775, Energy: -85.049975+0.006907j
[2025-07-30 21:53:42] [Iter 4221/4650] R4[1970/2400], Temp: 0.0771, Energy: -85.065813+0.001340j
[2025-07-30 21:53:57] [Iter 4222/4650] R4[1971/2400], Temp: 0.0768, Energy: -85.086360+0.004059j
[2025-07-30 21:54:12] [Iter 4223/4650] R4[1972/2400], Temp: 0.0764, Energy: -85.026715-0.002772j
[2025-07-30 21:54:27] [Iter 4224/4650] R4[1973/2400], Temp: 0.0761, Energy: -84.984085-0.005336j
[2025-07-30 21:54:42] [Iter 4225/4650] R4[1974/2400], Temp: 0.0757, Energy: -85.025058-0.002299j
[2025-07-30 21:54:57] [Iter 4226/4650] R4[1975/2400], Temp: 0.0754, Energy: -84.935597-0.000356j
[2025-07-30 21:55:12] [Iter 4227/4650] R4[1976/2400], Temp: 0.0751, Energy: -85.016026-0.007060j
[2025-07-30 21:55:27] [Iter 4228/4650] R4[1977/2400], Temp: 0.0747, Energy: -84.976589+0.000080j
[2025-07-30 21:55:41] [Iter 4229/4650] R4[1978/2400], Temp: 0.0744, Energy: -84.970623-0.001740j
[2025-07-30 21:55:56] [Iter 4230/4650] R4[1979/2400], Temp: 0.0740, Energy: -84.968883-0.003914j
[2025-07-30 21:56:10] [Iter 4231/4650] R4[1980/2400], Temp: 0.0737, Energy: -84.966387+0.001068j
[2025-07-30 21:56:25] [Iter 4232/4650] R4[1981/2400], Temp: 0.0733, Energy: -84.981550-0.000038j
[2025-07-30 21:56:40] [Iter 4233/4650] R4[1982/2400], Temp: 0.0730, Energy: -85.005873-0.002355j
[2025-07-30 21:56:55] [Iter 4234/4650] R4[1983/2400], Temp: 0.0727, Energy: -84.992018+0.006079j
[2025-07-30 21:57:10] [Iter 4235/4650] R4[1984/2400], Temp: 0.0723, Energy: -84.941970-0.008290j
[2025-07-30 21:57:25] [Iter 4236/4650] R4[1985/2400], Temp: 0.0720, Energy: -84.913468+0.000445j
[2025-07-30 21:57:39] [Iter 4237/4650] R4[1986/2400], Temp: 0.0716, Energy: -84.911912-0.001941j
[2025-07-30 21:57:54] [Iter 4238/4650] R4[1987/2400], Temp: 0.0713, Energy: -85.039209-0.000712j
[2025-07-30 21:58:08] [Iter 4239/4650] R4[1988/2400], Temp: 0.0710, Energy: -85.003147-0.001903j
[2025-07-30 21:58:23] [Iter 4240/4650] R4[1989/2400], Temp: 0.0706, Energy: -85.002093-0.004720j
[2025-07-30 21:58:38] [Iter 4241/4650] R4[1990/2400], Temp: 0.0703, Energy: -84.975199+0.003493j
[2025-07-30 21:58:52] [Iter 4242/4650] R4[1991/2400], Temp: 0.0700, Energy: -85.005427-0.001579j
[2025-07-30 21:59:07] [Iter 4243/4650] R4[1992/2400], Temp: 0.0696, Energy: -84.989148-0.003084j
[2025-07-30 21:59:21] [Iter 4244/4650] R4[1993/2400], Temp: 0.0693, Energy: -84.993333-0.003562j
[2025-07-30 21:59:36] [Iter 4245/4650] R4[1994/2400], Temp: 0.0690, Energy: -85.020485+0.011007j
[2025-07-30 21:59:51] [Iter 4246/4650] R4[1995/2400], Temp: 0.0686, Energy: -84.979789+0.001697j
[2025-07-30 22:00:06] [Iter 4247/4650] R4[1996/2400], Temp: 0.0683, Energy: -84.991731+0.003932j
[2025-07-30 22:00:21] [Iter 4248/4650] R4[1997/2400], Temp: 0.0680, Energy: -84.957323-0.007851j
[2025-07-30 22:00:35] [Iter 4249/4650] R4[1998/2400], Temp: 0.0676, Energy: -85.048901+0.006413j
[2025-07-30 22:00:50] [Iter 4250/4650] R4[1999/2400], Temp: 0.0673, Energy: -85.023613-0.002804j
[2025-07-30 22:01:04] [Iter 4251/4650] R4[2000/2400], Temp: 0.0670, Energy: -85.007031-0.002203j
[2025-07-30 22:01:19] [Iter 4252/4650] R4[2001/2400], Temp: 0.0667, Energy: -84.984746-0.001314j
[2025-07-30 22:01:34] [Iter 4253/4650] R4[2002/2400], Temp: 0.0663, Energy: -84.994081+0.004738j
[2025-07-30 22:01:49] [Iter 4254/4650] R4[2003/2400], Temp: 0.0660, Energy: -84.967979-0.002213j
[2025-07-30 22:02:03] [Iter 4255/4650] R4[2004/2400], Temp: 0.0657, Energy: -85.030299-0.003458j
[2025-07-30 22:02:18] [Iter 4256/4650] R4[2005/2400], Temp: 0.0654, Energy: -85.077732+0.001998j
[2025-07-30 22:02:32] [Iter 4257/4650] R4[2006/2400], Temp: 0.0650, Energy: -85.015682+0.010701j
[2025-07-30 22:02:47] [Iter 4258/4650] R4[2007/2400], Temp: 0.0647, Energy: -85.026305+0.010580j
[2025-07-30 22:03:01] [Iter 4259/4650] R4[2008/2400], Temp: 0.0644, Energy: -84.981002-0.003587j
[2025-07-30 22:03:16] [Iter 4260/4650] R4[2009/2400], Temp: 0.0641, Energy: -84.957857+0.004370j
[2025-07-30 22:03:31] [Iter 4261/4650] R4[2010/2400], Temp: 0.0638, Energy: -84.950435+0.003012j
[2025-07-30 22:03:45] [Iter 4262/4650] R4[2011/2400], Temp: 0.0634, Energy: -84.956244-0.001573j
[2025-07-30 22:04:00] [Iter 4263/4650] R4[2012/2400], Temp: 0.0631, Energy: -85.011533+0.008212j
[2025-07-30 22:04:15] [Iter 4264/4650] R4[2013/2400], Temp: 0.0628, Energy: -84.930727-0.001254j
[2025-07-30 22:04:30] [Iter 4265/4650] R4[2014/2400], Temp: 0.0625, Energy: -84.934801+0.013225j
[2025-07-30 22:04:44] [Iter 4266/4650] R4[2015/2400], Temp: 0.0622, Energy: -84.918096+0.008968j
[2025-07-30 22:04:59] [Iter 4267/4650] R4[2016/2400], Temp: 0.0618, Energy: -84.990459-0.007003j
[2025-07-30 22:05:13] [Iter 4268/4650] R4[2017/2400], Temp: 0.0615, Energy: -85.054071-0.001934j
[2025-07-30 22:05:28] [Iter 4269/4650] R4[2018/2400], Temp: 0.0612, Energy: -85.057843-0.010081j
[2025-07-30 22:05:42] [Iter 4270/4650] R4[2019/2400], Temp: 0.0609, Energy: -85.015496-0.005260j
[2025-07-30 22:05:56] [Iter 4271/4650] R4[2020/2400], Temp: 0.0606, Energy: -85.050115-0.005986j
[2025-07-30 22:06:11] [Iter 4272/4650] R4[2021/2400], Temp: 0.0603, Energy: -84.989285+0.002748j
[2025-07-30 22:06:26] [Iter 4273/4650] R4[2022/2400], Temp: 0.0600, Energy: -84.944751+0.011780j
[2025-07-30 22:06:40] [Iter 4274/4650] R4[2023/2400], Temp: 0.0597, Energy: -84.987255-0.004239j
[2025-07-30 22:06:55] [Iter 4275/4650] R4[2024/2400], Temp: 0.0593, Energy: -84.991754+0.000350j
[2025-07-30 22:07:10] [Iter 4276/4650] R4[2025/2400], Temp: 0.0590, Energy: -85.097105+0.001662j
[2025-07-30 22:07:25] [Iter 4277/4650] R4[2026/2400], Temp: 0.0587, Energy: -85.048844+0.000112j
[2025-07-30 22:07:40] [Iter 4278/4650] R4[2027/2400], Temp: 0.0584, Energy: -85.064988+0.012559j
[2025-07-30 22:07:54] [Iter 4279/4650] R4[2028/2400], Temp: 0.0581, Energy: -85.002837+0.007240j
[2025-07-30 22:08:09] [Iter 4280/4650] R4[2029/2400], Temp: 0.0578, Energy: -85.028935-0.012747j
[2025-07-30 22:08:24] [Iter 4281/4650] R4[2030/2400], Temp: 0.0575, Energy: -85.043795+0.010257j
[2025-07-30 22:08:38] [Iter 4282/4650] R4[2031/2400], Temp: 0.0572, Energy: -85.039466+0.010008j
[2025-07-30 22:08:53] [Iter 4283/4650] R4[2032/2400], Temp: 0.0569, Energy: -84.996749+0.000583j
[2025-07-30 22:09:08] [Iter 4284/4650] R4[2033/2400], Temp: 0.0566, Energy: -85.031472+0.009903j
[2025-07-30 22:09:22] [Iter 4285/4650] R4[2034/2400], Temp: 0.0563, Energy: -85.025867-0.013756j
[2025-07-30 22:09:37] [Iter 4286/4650] R4[2035/2400], Temp: 0.0560, Energy: -85.038310-0.009127j
[2025-07-30 22:09:51] [Iter 4287/4650] R4[2036/2400], Temp: 0.0557, Energy: -85.022190+0.001502j
[2025-07-30 22:10:06] [Iter 4288/4650] R4[2037/2400], Temp: 0.0554, Energy: -85.107614+0.000309j
[2025-07-30 22:10:20] [Iter 4289/4650] R4[2038/2400], Temp: 0.0551, Energy: -85.099963+0.005166j
[2025-07-30 22:10:35] [Iter 4290/4650] R4[2039/2400], Temp: 0.0548, Energy: -85.165269+0.000923j
[2025-07-30 22:10:49] [Iter 4291/4650] R4[2040/2400], Temp: 0.0545, Energy: -85.221360-0.000094j
[2025-07-30 22:11:04] [Iter 4292/4650] R4[2041/2400], Temp: 0.0542, Energy: -85.180370-0.000987j
[2025-07-30 22:11:19] [Iter 4293/4650] R4[2042/2400], Temp: 0.0539, Energy: -85.031251-0.003555j
[2025-07-30 22:11:34] [Iter 4294/4650] R4[2043/2400], Temp: 0.0536, Energy: -85.178313+0.001040j
[2025-07-30 22:11:49] [Iter 4295/4650] R4[2044/2400], Temp: 0.0533, Energy: -85.128350+0.006097j
[2025-07-30 22:12:03] [Iter 4296/4650] R4[2045/2400], Temp: 0.0530, Energy: -85.075214-0.006241j
[2025-07-30 22:12:18] [Iter 4297/4650] R4[2046/2400], Temp: 0.0527, Energy: -84.991422+0.008367j
[2025-07-30 22:12:33] [Iter 4298/4650] R4[2047/2400], Temp: 0.0524, Energy: -84.982796+0.002641j
[2025-07-30 22:12:48] [Iter 4299/4650] R4[2048/2400], Temp: 0.0521, Energy: -85.027600+0.002334j
[2025-07-30 22:13:04] [Iter 4300/4650] R4[2049/2400], Temp: 0.0519, Energy: -84.999528+0.005970j
[2025-07-30 22:13:18] [Iter 4301/4650] R4[2050/2400], Temp: 0.0516, Energy: -85.008269-0.013852j
[2025-07-30 22:13:33] [Iter 4302/4650] R4[2051/2400], Temp: 0.0513, Energy: -85.023189-0.002576j
[2025-07-30 22:13:48] [Iter 4303/4650] R4[2052/2400], Temp: 0.0510, Energy: -85.046668+0.004685j
[2025-07-30 22:14:03] [Iter 4304/4650] R4[2053/2400], Temp: 0.0507, Energy: -84.972588+0.006237j
[2025-07-30 22:14:18] [Iter 4305/4650] R4[2054/2400], Temp: 0.0504, Energy: -84.975605+0.008681j
[2025-07-30 22:14:33] [Iter 4306/4650] R4[2055/2400], Temp: 0.0501, Energy: -85.006939+0.005964j
[2025-07-30 22:14:47] [Iter 4307/4650] R4[2056/2400], Temp: 0.0498, Energy: -85.014430-0.005188j
[2025-07-30 22:15:02] [Iter 4308/4650] R4[2057/2400], Temp: 0.0496, Energy: -85.012882-0.005968j
[2025-07-30 22:15:16] [Iter 4309/4650] R4[2058/2400], Temp: 0.0493, Energy: -84.972454+0.007932j
[2025-07-30 22:15:31] [Iter 4310/4650] R4[2059/2400], Temp: 0.0490, Energy: -84.908053-0.000410j
[2025-07-30 22:15:45] [Iter 4311/4650] R4[2060/2400], Temp: 0.0487, Energy: -84.917885+0.007935j
[2025-07-30 22:16:00] [Iter 4312/4650] R4[2061/2400], Temp: 0.0484, Energy: -85.010099-0.000556j
[2025-07-30 22:16:15] [Iter 4313/4650] R4[2062/2400], Temp: 0.0481, Energy: -85.009805+0.005914j
[2025-07-30 22:16:29] [Iter 4314/4650] R4[2063/2400], Temp: 0.0479, Energy: -85.072305-0.002345j
[2025-07-30 22:16:44] [Iter 4315/4650] R4[2064/2400], Temp: 0.0476, Energy: -85.126332+0.002547j
[2025-07-30 22:16:59] [Iter 4316/4650] R4[2065/2400], Temp: 0.0473, Energy: -85.059834-0.001836j
[2025-07-30 22:17:13] [Iter 4317/4650] R4[2066/2400], Temp: 0.0470, Energy: -84.986086-0.003063j
[2025-07-30 22:17:28] [Iter 4318/4650] R4[2067/2400], Temp: 0.0468, Energy: -85.031086+0.001676j
[2025-07-30 22:17:42] [Iter 4319/4650] R4[2068/2400], Temp: 0.0465, Energy: -84.954654-0.005081j
[2025-07-30 22:17:57] [Iter 4320/4650] R4[2069/2400], Temp: 0.0462, Energy: -85.017572+0.002414j
[2025-07-30 22:18:11] [Iter 4321/4650] R4[2070/2400], Temp: 0.0459, Energy: -84.986388-0.003293j
[2025-07-30 22:18:26] [Iter 4322/4650] R4[2071/2400], Temp: 0.0457, Energy: -85.018618+0.000365j
[2025-07-30 22:18:40] [Iter 4323/4650] R4[2072/2400], Temp: 0.0454, Energy: -84.962273+0.002679j
[2025-07-30 22:18:55] [Iter 4324/4650] R4[2073/2400], Temp: 0.0451, Energy: -85.083601-0.001764j
[2025-07-30 22:19:10] [Iter 4325/4650] R4[2074/2400], Temp: 0.0448, Energy: -85.067294+0.003895j
[2025-07-30 22:19:24] [Iter 4326/4650] R4[2075/2400], Temp: 0.0446, Energy: -85.084175+0.002773j
[2025-07-30 22:19:39] [Iter 4327/4650] R4[2076/2400], Temp: 0.0443, Energy: -85.086845+0.007354j
[2025-07-30 22:19:54] [Iter 4328/4650] R4[2077/2400], Temp: 0.0440, Energy: -85.031982+0.005556j
[2025-07-30 22:20:08] [Iter 4329/4650] R4[2078/2400], Temp: 0.0438, Energy: -85.057548-0.003289j
[2025-07-30 22:20:23] [Iter 4330/4650] R4[2079/2400], Temp: 0.0435, Energy: -85.111792+0.001731j
[2025-07-30 22:20:38] [Iter 4331/4650] R4[2080/2400], Temp: 0.0432, Energy: -85.181543-0.007744j
[2025-07-30 22:20:53] [Iter 4332/4650] R4[2081/2400], Temp: 0.0430, Energy: -85.103894-0.003996j
[2025-07-30 22:21:08] [Iter 4333/4650] R4[2082/2400], Temp: 0.0427, Energy: -85.142165+0.012941j
[2025-07-30 22:21:22] [Iter 4334/4650] R4[2083/2400], Temp: 0.0424, Energy: -85.124373-0.009342j
[2025-07-30 22:21:37] [Iter 4335/4650] R4[2084/2400], Temp: 0.0422, Energy: -85.172199-0.005665j
[2025-07-30 22:21:51] [Iter 4336/4650] R4[2085/2400], Temp: 0.0419, Energy: -85.119777+0.000710j
[2025-07-30 22:22:06] [Iter 4337/4650] R4[2086/2400], Temp: 0.0416, Energy: -85.099676-0.014292j
[2025-07-30 22:22:20] [Iter 4338/4650] R4[2087/2400], Temp: 0.0414, Energy: -85.079813-0.003912j
[2025-07-30 22:22:35] [Iter 4339/4650] R4[2088/2400], Temp: 0.0411, Energy: -85.136037+0.002495j
[2025-07-30 22:22:50] [Iter 4340/4650] R4[2089/2400], Temp: 0.0409, Energy: -85.048317-0.007169j
[2025-07-30 22:23:04] [Iter 4341/4650] R4[2090/2400], Temp: 0.0406, Energy: -85.028020-0.007647j
[2025-07-30 22:23:19] [Iter 4342/4650] R4[2091/2400], Temp: 0.0403, Energy: -84.987795-0.004750j
[2025-07-30 22:23:34] [Iter 4343/4650] R4[2092/2400], Temp: 0.0401, Energy: -85.048069-0.001527j
[2025-07-30 22:23:49] [Iter 4344/4650] R4[2093/2400], Temp: 0.0398, Energy: -85.047887-0.002510j
[2025-07-30 22:24:04] [Iter 4345/4650] R4[2094/2400], Temp: 0.0396, Energy: -85.063493+0.008537j
[2025-07-30 22:24:18] [Iter 4346/4650] R4[2095/2400], Temp: 0.0393, Energy: -85.021943-0.004576j
[2025-07-30 22:24:34] [Iter 4347/4650] R4[2096/2400], Temp: 0.0391, Energy: -85.096845-0.011682j
[2025-07-30 22:24:49] [Iter 4348/4650] R4[2097/2400], Temp: 0.0388, Energy: -85.117198-0.009831j
[2025-07-30 22:25:03] [Iter 4349/4650] R4[2098/2400], Temp: 0.0386, Energy: -85.108699+0.016472j
[2025-07-30 22:25:17] [Iter 4350/4650] R4[2099/2400], Temp: 0.0383, Energy: -85.151044-0.019957j
[2025-07-30 22:25:32] [Iter 4351/4650] R4[2100/2400], Temp: 0.0381, Energy: -85.086164-0.000784j
[2025-07-30 22:25:47] [Iter 4352/4650] R4[2101/2400], Temp: 0.0378, Energy: -85.080100-0.002015j
[2025-07-30 22:26:02] [Iter 4353/4650] R4[2102/2400], Temp: 0.0376, Energy: -85.046340+0.000825j
[2025-07-30 22:26:17] [Iter 4354/4650] R4[2103/2400], Temp: 0.0373, Energy: -85.055484-0.003948j
[2025-07-30 22:26:32] [Iter 4355/4650] R4[2104/2400], Temp: 0.0371, Energy: -85.039252+0.005101j
[2025-07-30 22:26:47] [Iter 4356/4650] R4[2105/2400], Temp: 0.0368, Energy: -85.028471-0.001170j
[2025-07-30 22:27:02] [Iter 4357/4650] R4[2106/2400], Temp: 0.0366, Energy: -85.015833+0.011379j
[2025-07-30 22:27:17] [Iter 4358/4650] R4[2107/2400], Temp: 0.0363, Energy: -85.063189-0.003574j
[2025-07-30 22:27:31] [Iter 4359/4650] R4[2108/2400], Temp: 0.0361, Energy: -85.108802+0.004035j
[2025-07-30 22:27:46] [Iter 4360/4650] R4[2109/2400], Temp: 0.0358, Energy: -85.048372-0.005054j
[2025-07-30 22:28:01] [Iter 4361/4650] R4[2110/2400], Temp: 0.0356, Energy: -85.028101+0.011707j
[2025-07-30 22:28:16] [Iter 4362/4650] R4[2111/2400], Temp: 0.0354, Energy: -85.037426+0.002606j
[2025-07-30 22:28:30] [Iter 4363/4650] R4[2112/2400], Temp: 0.0351, Energy: -85.048648-0.007062j
[2025-07-30 22:28:45] [Iter 4364/4650] R4[2113/2400], Temp: 0.0349, Energy: -85.003716+0.005270j
[2025-07-30 22:28:59] [Iter 4365/4650] R4[2114/2400], Temp: 0.0346, Energy: -85.032496+0.005616j
[2025-07-30 22:29:14] [Iter 4366/4650] R4[2115/2400], Temp: 0.0344, Energy: -85.025918-0.003048j
[2025-07-30 22:29:28] [Iter 4367/4650] R4[2116/2400], Temp: 0.0342, Energy: -85.104095+0.008454j
[2025-07-30 22:29:43] [Iter 4368/4650] R4[2117/2400], Temp: 0.0339, Energy: -85.071451-0.002257j
[2025-07-30 22:29:58] [Iter 4369/4650] R4[2118/2400], Temp: 0.0337, Energy: -85.041935+0.005410j
[2025-07-30 22:30:13] [Iter 4370/4650] R4[2119/2400], Temp: 0.0334, Energy: -85.038448+0.003563j
[2025-07-30 22:30:28] [Iter 4371/4650] R4[2120/2400], Temp: 0.0332, Energy: -85.097603+0.010896j
[2025-07-30 22:30:43] [Iter 4372/4650] R4[2121/2400], Temp: 0.0330, Energy: -85.059320+0.009708j
[2025-07-30 22:30:58] [Iter 4373/4650] R4[2122/2400], Temp: 0.0327, Energy: -85.048309-0.000508j
[2025-07-30 22:31:13] [Iter 4374/4650] R4[2123/2400], Temp: 0.0325, Energy: -85.073573-0.000088j
[2025-07-30 22:31:27] [Iter 4375/4650] R4[2124/2400], Temp: 0.0323, Energy: -85.069642-0.000419j
[2025-07-30 22:31:42] [Iter 4376/4650] R4[2125/2400], Temp: 0.0320, Energy: -85.085312+0.001658j
[2025-07-30 22:31:56] [Iter 4377/4650] R4[2126/2400], Temp: 0.0318, Energy: -85.062164+0.010768j
[2025-07-30 22:32:11] [Iter 4378/4650] R4[2127/2400], Temp: 0.0316, Energy: -85.122739-0.000768j
[2025-07-30 22:32:25] [Iter 4379/4650] R4[2128/2400], Temp: 0.0314, Energy: -85.092281+0.000489j
[2025-07-30 22:32:40] [Iter 4380/4650] R4[2129/2400], Temp: 0.0311, Energy: -85.122251+0.002301j
[2025-07-30 22:32:55] [Iter 4381/4650] R4[2130/2400], Temp: 0.0309, Energy: -85.097563-0.000703j
[2025-07-30 22:33:10] [Iter 4382/4650] R4[2131/2400], Temp: 0.0307, Energy: -85.101521-0.003171j
[2025-07-30 22:33:25] [Iter 4383/4650] R4[2132/2400], Temp: 0.0305, Energy: -85.120054+0.002701j
[2025-07-30 22:33:39] [Iter 4384/4650] R4[2133/2400], Temp: 0.0302, Energy: -85.120216+0.000489j
[2025-07-30 22:33:54] [Iter 4385/4650] R4[2134/2400], Temp: 0.0300, Energy: -85.099218+0.006305j
[2025-07-30 22:34:09] [Iter 4386/4650] R4[2135/2400], Temp: 0.0298, Energy: -85.107374+0.003356j
[2025-07-30 22:34:23] [Iter 4387/4650] R4[2136/2400], Temp: 0.0296, Energy: -85.058495-0.003228j
[2025-07-30 22:34:38] [Iter 4388/4650] R4[2137/2400], Temp: 0.0293, Energy: -85.000279+0.000813j
[2025-07-30 22:34:53] [Iter 4389/4650] R4[2138/2400], Temp: 0.0291, Energy: -84.997981-0.001721j
[2025-07-30 22:35:08] [Iter 4390/4650] R4[2139/2400], Temp: 0.0289, Energy: -85.104205+0.003142j
[2025-07-30 22:35:22] [Iter 4391/4650] R4[2140/2400], Temp: 0.0287, Energy: -85.057172-0.000936j
[2025-07-30 22:35:37] [Iter 4392/4650] R4[2141/2400], Temp: 0.0285, Energy: -85.049647-0.004391j
[2025-07-30 22:35:51] [Iter 4393/4650] R4[2142/2400], Temp: 0.0282, Energy: -85.044797-0.002846j
[2025-07-30 22:36:06] [Iter 4394/4650] R4[2143/2400], Temp: 0.0280, Energy: -85.042128-0.003193j
[2025-07-30 22:36:21] [Iter 4395/4650] R4[2144/2400], Temp: 0.0278, Energy: -85.075756+0.007090j
[2025-07-30 22:36:36] [Iter 4396/4650] R4[2145/2400], Temp: 0.0276, Energy: -85.023713-0.003486j
[2025-07-30 22:36:50] [Iter 4397/4650] R4[2146/2400], Temp: 0.0274, Energy: -85.045520+0.005834j
[2025-07-30 22:37:05] [Iter 4398/4650] R4[2147/2400], Temp: 0.0272, Energy: -85.049869+0.000856j
[2025-07-30 22:37:19] [Iter 4399/4650] R4[2148/2400], Temp: 0.0270, Energy: -85.027727-0.001000j
[2025-07-30 22:37:34] [Iter 4400/4650] R4[2149/2400], Temp: 0.0267, Energy: -85.046830-0.009063j
[2025-07-30 22:37:48] [Iter 4401/4650] R4[2150/2400], Temp: 0.0265, Energy: -85.009792-0.008499j
[2025-07-30 22:38:03] [Iter 4402/4650] R4[2151/2400], Temp: 0.0263, Energy: -85.071299+0.003333j
[2025-07-30 22:38:18] [Iter 4403/4650] R4[2152/2400], Temp: 0.0261, Energy: -85.173648+0.001015j
[2025-07-30 22:38:33] [Iter 4404/4650] R4[2153/2400], Temp: 0.0259, Energy: -85.115252-0.000719j
[2025-07-30 22:38:47] [Iter 4405/4650] R4[2154/2400], Temp: 0.0257, Energy: -85.095530+0.001043j
[2025-07-30 22:39:02] [Iter 4406/4650] R4[2155/2400], Temp: 0.0255, Energy: -85.044380-0.002404j
[2025-07-30 22:39:17] [Iter 4407/4650] R4[2156/2400], Temp: 0.0253, Energy: -85.117182-0.004106j
[2025-07-30 22:39:32] [Iter 4408/4650] R4[2157/2400], Temp: 0.0251, Energy: -85.105981-0.000569j
[2025-07-30 22:39:47] [Iter 4409/4650] R4[2158/2400], Temp: 0.0249, Energy: -85.077253-0.001747j
[2025-07-30 22:40:01] [Iter 4410/4650] R4[2159/2400], Temp: 0.0247, Energy: -85.058301+0.002825j
[2025-07-30 22:40:16] [Iter 4411/4650] R4[2160/2400], Temp: 0.0245, Energy: -85.100708-0.013756j
[2025-07-30 22:40:31] [Iter 4412/4650] R4[2161/2400], Temp: 0.0243, Energy: -85.048114+0.001235j
[2025-07-30 22:40:46] [Iter 4413/4650] R4[2162/2400], Temp: 0.0241, Energy: -85.123235-0.002547j
[2025-07-30 22:41:01] [Iter 4414/4650] R4[2163/2400], Temp: 0.0239, Energy: -85.114301+0.004242j
[2025-07-30 22:41:15] [Iter 4415/4650] R4[2164/2400], Temp: 0.0237, Energy: -85.053076-0.000136j
[2025-07-30 22:41:30] [Iter 4416/4650] R4[2165/2400], Temp: 0.0235, Energy: -85.052306+0.000032j
[2025-07-30 22:41:44] [Iter 4417/4650] R4[2166/2400], Temp: 0.0233, Energy: -85.044608-0.007663j
[2025-07-30 22:41:59] [Iter 4418/4650] R4[2167/2400], Temp: 0.0231, Energy: -85.014075+0.004998j
[2025-07-30 22:42:14] [Iter 4419/4650] R4[2168/2400], Temp: 0.0229, Energy: -84.983582-0.001893j
[2025-07-30 22:42:28] [Iter 4420/4650] R4[2169/2400], Temp: 0.0227, Energy: -85.007789+0.008411j
[2025-07-30 22:42:43] [Iter 4421/4650] R4[2170/2400], Temp: 0.0225, Energy: -84.935357-0.003048j
[2025-07-30 22:42:58] [Iter 4422/4650] R4[2171/2400], Temp: 0.0223, Energy: -84.940965+0.004594j
[2025-07-30 22:43:13] [Iter 4423/4650] R4[2172/2400], Temp: 0.0221, Energy: -84.941575+0.001193j
[2025-07-30 22:43:27] [Iter 4424/4650] R4[2173/2400], Temp: 0.0219, Energy: -85.004053+0.007570j
[2025-07-30 22:43:42] [Iter 4425/4650] R4[2174/2400], Temp: 0.0217, Energy: -85.067686+0.015121j
[2025-07-30 22:43:57] [Iter 4426/4650] R4[2175/2400], Temp: 0.0215, Energy: -85.007683+0.003877j
[2025-07-30 22:44:11] [Iter 4427/4650] R4[2176/2400], Temp: 0.0213, Energy: -85.020203-0.002259j
[2025-07-30 22:44:26] [Iter 4428/4650] R4[2177/2400], Temp: 0.0212, Energy: -85.053470-0.000277j
[2025-07-30 22:44:41] [Iter 4429/4650] R4[2178/2400], Temp: 0.0210, Energy: -85.030091-0.008352j
[2025-07-30 22:44:55] [Iter 4430/4650] R4[2179/2400], Temp: 0.0208, Energy: -85.058837-0.001424j
[2025-07-30 22:45:09] [Iter 4431/4650] R4[2180/2400], Temp: 0.0206, Energy: -85.066454+0.013086j
[2025-07-30 22:45:25] [Iter 4432/4650] R4[2181/2400], Temp: 0.0204, Energy: -85.007738+0.000842j
[2025-07-30 22:45:39] [Iter 4433/4650] R4[2182/2400], Temp: 0.0202, Energy: -84.993108-0.000741j
[2025-07-30 22:45:54] [Iter 4434/4650] R4[2183/2400], Temp: 0.0200, Energy: -84.960860-0.001447j
[2025-07-30 22:46:09] [Iter 4435/4650] R4[2184/2400], Temp: 0.0199, Energy: -84.987354+0.001357j
[2025-07-30 22:46:23] [Iter 4436/4650] R4[2185/2400], Temp: 0.0197, Energy: -84.928221-0.002529j
[2025-07-30 22:46:38] [Iter 4437/4650] R4[2186/2400], Temp: 0.0195, Energy: -84.985272+0.000006j
[2025-07-30 22:46:52] [Iter 4438/4650] R4[2187/2400], Temp: 0.0193, Energy: -84.936674+0.000723j
[2025-07-30 22:47:07] [Iter 4439/4650] R4[2188/2400], Temp: 0.0191, Energy: -84.944217+0.001994j
[2025-07-30 22:47:22] [Iter 4440/4650] R4[2189/2400], Temp: 0.0190, Energy: -85.046415-0.011118j
[2025-07-30 22:47:37] [Iter 4441/4650] R4[2190/2400], Temp: 0.0188, Energy: -85.040318-0.004729j
[2025-07-30 22:47:52] [Iter 4442/4650] R4[2191/2400], Temp: 0.0186, Energy: -85.042361-0.000640j
[2025-07-30 22:48:06] [Iter 4443/4650] R4[2192/2400], Temp: 0.0184, Energy: -85.030639+0.000462j
[2025-07-30 22:48:21] [Iter 4444/4650] R4[2193/2400], Temp: 0.0182, Energy: -84.971494-0.002070j
[2025-07-30 22:48:36] [Iter 4445/4650] R4[2194/2400], Temp: 0.0181, Energy: -84.988043+0.002409j
[2025-07-30 22:48:51] [Iter 4446/4650] R4[2195/2400], Temp: 0.0179, Energy: -85.011521+0.000130j
[2025-07-30 22:49:06] [Iter 4447/4650] R4[2196/2400], Temp: 0.0177, Energy: -85.037786+0.004018j
[2025-07-30 22:49:20] [Iter 4448/4650] R4[2197/2400], Temp: 0.0175, Energy: -85.067947+0.002126j
[2025-07-30 22:49:35] [Iter 4449/4650] R4[2198/2400], Temp: 0.0174, Energy: -85.077306+0.006339j
[2025-07-30 22:49:50] [Iter 4450/4650] R4[2199/2400], Temp: 0.0172, Energy: -85.101283+0.005364j
[2025-07-30 22:50:04] [Iter 4451/4650] R4[2200/2400], Temp: 0.0170, Energy: -85.058480-0.001859j
[2025-07-30 22:50:18] [Iter 4452/4650] R4[2201/2400], Temp: 0.0169, Energy: -85.082742-0.002398j
[2025-07-30 22:50:33] [Iter 4453/4650] R4[2202/2400], Temp: 0.0167, Energy: -85.094513-0.000743j
[2025-07-30 22:50:48] [Iter 4454/4650] R4[2203/2400], Temp: 0.0165, Energy: -85.101415+0.008746j
[2025-07-30 22:51:03] [Iter 4455/4650] R4[2204/2400], Temp: 0.0164, Energy: -85.141103-0.007520j
[2025-07-30 22:51:18] [Iter 4456/4650] R4[2205/2400], Temp: 0.0162, Energy: -85.058514+0.004789j
[2025-07-30 22:51:32] [Iter 4457/4650] R4[2206/2400], Temp: 0.0160, Energy: -85.083775-0.005364j
[2025-07-30 22:51:47] [Iter 4458/4650] R4[2207/2400], Temp: 0.0159, Energy: -85.104811-0.000340j
[2025-07-30 22:52:02] [Iter 4459/4650] R4[2208/2400], Temp: 0.0157, Energy: -85.126849+0.005760j
[2025-07-30 22:52:17] [Iter 4460/4650] R4[2209/2400], Temp: 0.0155, Energy: -85.055949+0.001542j
[2025-07-30 22:52:32] [Iter 4461/4650] R4[2210/2400], Temp: 0.0154, Energy: -85.094101-0.011020j
[2025-07-30 22:52:46] [Iter 4462/4650] R4[2211/2400], Temp: 0.0152, Energy: -85.138555+0.011841j
[2025-07-30 22:53:01] [Iter 4463/4650] R4[2212/2400], Temp: 0.0151, Energy: -85.079918+0.002614j
[2025-07-30 22:53:15] [Iter 4464/4650] R4[2213/2400], Temp: 0.0149, Energy: -85.095000-0.005527j
[2025-07-30 22:53:30] [Iter 4465/4650] R4[2214/2400], Temp: 0.0147, Energy: -85.005209-0.000123j
[2025-07-30 22:53:45] [Iter 4466/4650] R4[2215/2400], Temp: 0.0146, Energy: -85.154712+0.001477j
[2025-07-30 22:53:59] [Iter 4467/4650] R4[2216/2400], Temp: 0.0144, Energy: -85.147587+0.002674j
[2025-07-30 22:54:13] [Iter 4468/4650] R4[2217/2400], Temp: 0.0143, Energy: -85.134171-0.004434j
[2025-07-30 22:54:28] [Iter 4469/4650] R4[2218/2400], Temp: 0.0141, Energy: -85.113708+0.001306j
[2025-07-30 22:54:43] [Iter 4470/4650] R4[2219/2400], Temp: 0.0140, Energy: -85.067232+0.000037j
[2025-07-30 22:54:58] [Iter 4471/4650] R4[2220/2400], Temp: 0.0138, Energy: -85.074198+0.002450j
[2025-07-30 22:55:13] [Iter 4472/4650] R4[2221/2400], Temp: 0.0137, Energy: -85.084537+0.000429j
[2025-07-30 22:55:27] [Iter 4473/4650] R4[2222/2400], Temp: 0.0135, Energy: -85.036962+0.002353j
[2025-07-30 22:55:42] [Iter 4474/4650] R4[2223/2400], Temp: 0.0134, Energy: -85.021893+0.003816j
[2025-07-30 22:55:57] [Iter 4475/4650] R4[2224/2400], Temp: 0.0132, Energy: -85.017514+0.002042j
[2025-07-30 22:56:12] [Iter 4476/4650] R4[2225/2400], Temp: 0.0131, Energy: -85.021442-0.006928j
[2025-07-30 22:56:27] [Iter 4477/4650] R4[2226/2400], Temp: 0.0129, Energy: -84.999791-0.007476j
[2025-07-30 22:56:42] [Iter 4478/4650] R4[2227/2400], Temp: 0.0128, Energy: -85.042142-0.003800j
[2025-07-30 22:56:56] [Iter 4479/4650] R4[2228/2400], Temp: 0.0126, Energy: -85.040205-0.009801j
[2025-07-30 22:57:11] [Iter 4480/4650] R4[2229/2400], Temp: 0.0125, Energy: -85.104295+0.001275j
[2025-07-30 22:57:25] [Iter 4481/4650] R4[2230/2400], Temp: 0.0123, Energy: -85.092971-0.000011j
[2025-07-30 22:57:40] [Iter 4482/4650] R4[2231/2400], Temp: 0.0122, Energy: -85.098926+0.004060j
[2025-07-30 22:57:55] [Iter 4483/4650] R4[2232/2400], Temp: 0.0120, Energy: -85.043089-0.000425j
[2025-07-30 22:58:09] [Iter 4484/4650] R4[2233/2400], Temp: 0.0119, Energy: -85.093420+0.008911j
[2025-07-30 22:58:23] [Iter 4485/4650] R4[2234/2400], Temp: 0.0118, Energy: -85.075634-0.000940j
[2025-07-30 22:58:37] [Iter 4486/4650] R4[2235/2400], Temp: 0.0116, Energy: -85.060017-0.010880j
[2025-07-30 22:58:52] [Iter 4487/4650] R4[2236/2400], Temp: 0.0115, Energy: -85.049268-0.013492j
[2025-07-30 22:59:07] [Iter 4488/4650] R4[2237/2400], Temp: 0.0113, Energy: -85.080419+0.000978j
[2025-07-30 22:59:22] [Iter 4489/4650] R4[2238/2400], Temp: 0.0112, Energy: -85.037935+0.010174j
[2025-07-30 22:59:37] [Iter 4490/4650] R4[2239/2400], Temp: 0.0111, Energy: -85.084854-0.001968j
[2025-07-30 22:59:52] [Iter 4491/4650] R4[2240/2400], Temp: 0.0109, Energy: -85.039737-0.001922j
[2025-07-30 23:00:06] [Iter 4492/4650] R4[2241/2400], Temp: 0.0108, Energy: -85.069301-0.002887j
[2025-07-30 23:00:21] [Iter 4493/4650] R4[2242/2400], Temp: 0.0107, Energy: -85.026369-0.001715j
[2025-07-30 23:00:36] [Iter 4494/4650] R4[2243/2400], Temp: 0.0105, Energy: -84.991441+0.000631j
[2025-07-30 23:00:50] [Iter 4495/4650] R4[2244/2400], Temp: 0.0104, Energy: -85.035979-0.005769j
[2025-07-30 23:01:05] [Iter 4496/4650] R4[2245/2400], Temp: 0.0103, Energy: -84.997811-0.011417j
[2025-07-30 23:01:19] [Iter 4497/4650] R4[2246/2400], Temp: 0.0101, Energy: -84.993278-0.015471j
[2025-07-30 23:01:34] [Iter 4498/4650] R4[2247/2400], Temp: 0.0100, Energy: -85.035236+0.007134j
[2025-07-30 23:01:49] [Iter 4499/4650] R4[2248/2400], Temp: 0.0099, Energy: -85.008811+0.003786j
[2025-07-30 23:02:03] [Iter 4500/4650] R4[2249/2400], Temp: 0.0097, Energy: -85.075465-0.005583j
[2025-07-30 23:02:03] ✓ Checkpoint saved: checkpoint_iter_004500.pkl
[2025-07-30 23:02:18] [Iter 4501/4650] R4[2250/2400], Temp: 0.0096, Energy: -85.010766+0.004622j
[2025-07-30 23:02:33] [Iter 4502/4650] R4[2251/2400], Temp: 0.0095, Energy: -85.020833+0.000720j
[2025-07-30 23:02:48] [Iter 4503/4650] R4[2252/2400], Temp: 0.0094, Energy: -84.990817-0.005069j
[2025-07-30 23:03:02] [Iter 4504/4650] R4[2253/2400], Temp: 0.0092, Energy: -85.027741+0.000177j
[2025-07-30 23:03:16] [Iter 4505/4650] R4[2254/2400], Temp: 0.0091, Energy: -85.048831+0.013466j
[2025-07-30 23:03:31] [Iter 4506/4650] R4[2255/2400], Temp: 0.0090, Energy: -85.068477-0.010214j
[2025-07-30 23:03:46] [Iter 4507/4650] R4[2256/2400], Temp: 0.0089, Energy: -85.103569+0.003761j
[2025-07-30 23:04:00] [Iter 4508/4650] R4[2257/2400], Temp: 0.0087, Energy: -85.180765+0.005398j
[2025-07-30 23:04:15] [Iter 4509/4650] R4[2258/2400], Temp: 0.0086, Energy: -85.144934+0.007322j
[2025-07-30 23:04:29] [Iter 4510/4650] R4[2259/2400], Temp: 0.0085, Energy: -85.083092-0.008827j
[2025-07-30 23:04:43] [Iter 4511/4650] R4[2260/2400], Temp: 0.0084, Energy: -85.029210-0.003676j
[2025-07-30 23:04:58] [Iter 4512/4650] R4[2261/2400], Temp: 0.0083, Energy: -85.088787+0.002522j
[2025-07-30 23:05:12] [Iter 4513/4650] R4[2262/2400], Temp: 0.0081, Energy: -85.046522+0.012048j
[2025-07-30 23:05:27] [Iter 4514/4650] R4[2263/2400], Temp: 0.0080, Energy: -85.040483+0.002923j
[2025-07-30 23:05:42] [Iter 4515/4650] R4[2264/2400], Temp: 0.0079, Energy: -85.029417-0.004785j
[2025-07-30 23:05:56] [Iter 4516/4650] R4[2265/2400], Temp: 0.0078, Energy: -85.016378-0.009566j
[2025-07-30 23:06:11] [Iter 4517/4650] R4[2266/2400], Temp: 0.0077, Energy: -85.036827-0.004065j
[2025-07-30 23:06:26] [Iter 4518/4650] R4[2267/2400], Temp: 0.0076, Energy: -85.052548+0.001504j
[2025-07-30 23:06:41] [Iter 4519/4650] R4[2268/2400], Temp: 0.0074, Energy: -85.001068-0.008967j
[2025-07-30 23:06:55] [Iter 4520/4650] R4[2269/2400], Temp: 0.0073, Energy: -85.078937-0.000106j
[2025-07-30 23:07:09] [Iter 4521/4650] R4[2270/2400], Temp: 0.0072, Energy: -85.062186+0.001107j
[2025-07-30 23:07:24] [Iter 4522/4650] R4[2271/2400], Temp: 0.0071, Energy: -85.066006-0.000812j
[2025-07-30 23:07:38] [Iter 4523/4650] R4[2272/2400], Temp: 0.0070, Energy: -85.127196+0.000505j
[2025-07-30 23:07:53] [Iter 4524/4650] R4[2273/2400], Temp: 0.0069, Energy: -85.065875+0.005923j
[2025-07-30 23:08:07] [Iter 4525/4650] R4[2274/2400], Temp: 0.0068, Energy: -85.092219+0.001146j
[2025-07-30 23:08:22] [Iter 4526/4650] R4[2275/2400], Temp: 0.0067, Energy: -85.071447+0.005322j
[2025-07-30 23:08:37] [Iter 4527/4650] R4[2276/2400], Temp: 0.0066, Energy: -85.118102+0.011291j
[2025-07-30 23:08:52] [Iter 4528/4650] R4[2277/2400], Temp: 0.0065, Energy: -85.067651-0.002572j
[2025-07-30 23:09:06] [Iter 4529/4650] R4[2278/2400], Temp: 0.0064, Energy: -85.025703+0.002326j
[2025-07-30 23:09:21] [Iter 4530/4650] R4[2279/2400], Temp: 0.0063, Energy: -85.052007-0.009012j
[2025-07-30 23:09:36] [Iter 4531/4650] R4[2280/2400], Temp: 0.0062, Energy: -84.964756-0.002968j
[2025-07-30 23:09:51] [Iter 4532/4650] R4[2281/2400], Temp: 0.0061, Energy: -84.986748+0.006399j
[2025-07-30 23:10:06] [Iter 4533/4650] R4[2282/2400], Temp: 0.0060, Energy: -84.939081-0.005067j
[2025-07-30 23:10:21] [Iter 4534/4650] R4[2283/2400], Temp: 0.0059, Energy: -84.985007+0.001830j
[2025-07-30 23:10:35] [Iter 4535/4650] R4[2284/2400], Temp: 0.0058, Energy: -84.903788-0.004620j
[2025-07-30 23:10:50] [Iter 4536/4650] R4[2285/2400], Temp: 0.0057, Energy: -84.941612-0.005634j
[2025-07-30 23:11:04] [Iter 4537/4650] R4[2286/2400], Temp: 0.0056, Energy: -84.996878+0.002030j
[2025-07-30 23:11:19] [Iter 4538/4650] R4[2287/2400], Temp: 0.0055, Energy: -84.999549+0.005695j
[2025-07-30 23:11:33] [Iter 4539/4650] R4[2288/2400], Temp: 0.0054, Energy: -85.021912+0.004331j
[2025-07-30 23:11:47] [Iter 4540/4650] R4[2289/2400], Temp: 0.0053, Energy: -85.050307-0.004626j
[2025-07-30 23:12:02] [Iter 4541/4650] R4[2290/2400], Temp: 0.0052, Energy: -85.079617+0.003252j
[2025-07-30 23:12:17] [Iter 4542/4650] R4[2291/2400], Temp: 0.0051, Energy: -84.976852+0.003452j
[2025-07-30 23:12:31] [Iter 4543/4650] R4[2292/2400], Temp: 0.0050, Energy: -85.044196+0.001719j
[2025-07-30 23:12:45] [Iter 4544/4650] R4[2293/2400], Temp: 0.0049, Energy: -85.008216+0.007135j
[2025-07-30 23:13:00] [Iter 4545/4650] R4[2294/2400], Temp: 0.0048, Energy: -84.929134-0.002885j
[2025-07-30 23:13:15] [Iter 4546/4650] R4[2295/2400], Temp: 0.0047, Energy: -84.991736-0.005349j
[2025-07-30 23:13:30] [Iter 4547/4650] R4[2296/2400], Temp: 0.0046, Energy: -85.018137+0.010506j
[2025-07-30 23:13:45] [Iter 4548/4650] R4[2297/2400], Temp: 0.0045, Energy: -85.002168+0.005451j
[2025-07-30 23:13:59] [Iter 4549/4650] R4[2298/2400], Temp: 0.0045, Energy: -85.005055-0.006802j
[2025-07-30 23:14:14] [Iter 4550/4650] R4[2299/2400], Temp: 0.0044, Energy: -84.993971-0.002884j
[2025-07-30 23:14:29] [Iter 4551/4650] R4[2300/2400], Temp: 0.0043, Energy: -84.973171-0.008751j
[2025-07-30 23:14:43] [Iter 4552/4650] R4[2301/2400], Temp: 0.0042, Energy: -84.988245+0.007031j
[2025-07-30 23:14:58] [Iter 4553/4650] R4[2302/2400], Temp: 0.0041, Energy: -85.020419-0.004999j
[2025-07-30 23:15:13] [Iter 4554/4650] R4[2303/2400], Temp: 0.0040, Energy: -85.085774-0.001505j
[2025-07-30 23:15:28] [Iter 4555/4650] R4[2304/2400], Temp: 0.0039, Energy: -85.128741-0.005796j
[2025-07-30 23:15:43] [Iter 4556/4650] R4[2305/2400], Temp: 0.0039, Energy: -85.115742-0.003625j
[2025-07-30 23:15:57] [Iter 4557/4650] R4[2306/2400], Temp: 0.0038, Energy: -85.179606+0.000452j
[2025-07-30 23:16:12] [Iter 4558/4650] R4[2307/2400], Temp: 0.0037, Energy: -85.164541-0.007250j
[2025-07-30 23:16:26] [Iter 4559/4650] R4[2308/2400], Temp: 0.0036, Energy: -85.124785-0.008785j
[2025-07-30 23:16:41] [Iter 4560/4650] R4[2309/2400], Temp: 0.0035, Energy: -85.140396-0.004797j
[2025-07-30 23:16:56] [Iter 4561/4650] R4[2310/2400], Temp: 0.0035, Energy: -85.119347-0.011758j
[2025-07-30 23:17:10] [Iter 4562/4650] R4[2311/2400], Temp: 0.0034, Energy: -85.160672-0.000856j
[2025-07-30 23:17:25] [Iter 4563/4650] R4[2312/2400], Temp: 0.0033, Energy: -85.165874+0.000410j
[2025-07-30 23:17:40] [Iter 4564/4650] R4[2313/2400], Temp: 0.0032, Energy: -85.197636-0.005843j
[2025-07-30 23:17:54] [Iter 4565/4650] R4[2314/2400], Temp: 0.0032, Energy: -85.171508-0.008726j
[2025-07-30 23:18:09] [Iter 4566/4650] R4[2315/2400], Temp: 0.0031, Energy: -85.134562-0.012315j
[2025-07-30 23:18:23] [Iter 4567/4650] R4[2316/2400], Temp: 0.0030, Energy: -85.099245+0.006031j
[2025-07-30 23:18:38] [Iter 4568/4650] R4[2317/2400], Temp: 0.0029, Energy: -85.101729-0.013759j
[2025-07-30 23:18:53] [Iter 4569/4650] R4[2318/2400], Temp: 0.0029, Energy: -85.028958+0.006685j
[2025-07-30 23:19:08] [Iter 4570/4650] R4[2319/2400], Temp: 0.0028, Energy: -85.003852-0.010952j
[2025-07-30 23:19:22] [Iter 4571/4650] R4[2320/2400], Temp: 0.0027, Energy: -85.073520+0.000841j
[2025-07-30 23:19:37] [Iter 4572/4650] R4[2321/2400], Temp: 0.0027, Energy: -85.033324+0.003970j
[2025-07-30 23:19:51] [Iter 4573/4650] R4[2322/2400], Temp: 0.0026, Energy: -85.016917+0.004023j
[2025-07-30 23:20:06] [Iter 4574/4650] R4[2323/2400], Temp: 0.0025, Energy: -84.977213-0.007219j
[2025-07-30 23:20:21] [Iter 4575/4650] R4[2324/2400], Temp: 0.0025, Energy: -85.025243-0.020764j
[2025-07-30 23:20:36] [Iter 4576/4650] R4[2325/2400], Temp: 0.0024, Energy: -85.015548+0.003732j
[2025-07-30 23:20:50] [Iter 4577/4650] R4[2326/2400], Temp: 0.0023, Energy: -85.067140-0.010467j
[2025-07-30 23:21:05] [Iter 4578/4650] R4[2327/2400], Temp: 0.0023, Energy: -85.084638-0.008204j
[2025-07-30 23:21:20] [Iter 4579/4650] R4[2328/2400], Temp: 0.0022, Energy: -85.052750+0.005595j
[2025-07-30 23:21:35] [Iter 4580/4650] R4[2329/2400], Temp: 0.0022, Energy: -85.058811+0.005076j
[2025-07-30 23:21:49] [Iter 4581/4650] R4[2330/2400], Temp: 0.0021, Energy: -85.062821-0.022036j
[2025-07-30 23:22:04] [Iter 4582/4650] R4[2331/2400], Temp: 0.0020, Energy: -85.101280+0.011618j
[2025-07-30 23:22:18] [Iter 4583/4650] R4[2332/2400], Temp: 0.0020, Energy: -85.060651+0.002469j
[2025-07-30 23:22:33] [Iter 4584/4650] R4[2333/2400], Temp: 0.0019, Energy: -85.112418+0.004224j
[2025-07-30 23:22:47] [Iter 4585/4650] R4[2334/2400], Temp: 0.0019, Energy: -85.057344+0.007048j
[2025-07-30 23:23:02] [Iter 4586/4650] R4[2335/2400], Temp: 0.0018, Energy: -85.061658-0.000256j
[2025-07-30 23:23:17] [Iter 4587/4650] R4[2336/2400], Temp: 0.0018, Energy: -84.979817+0.012380j
[2025-07-30 23:23:32] [Iter 4588/4650] R4[2337/2400], Temp: 0.0017, Energy: -85.168279-0.002739j
[2025-07-30 23:23:46] [Iter 4589/4650] R4[2338/2400], Temp: 0.0016, Energy: -85.121561-0.002040j
[2025-07-30 23:24:00] [Iter 4590/4650] R4[2339/2400], Temp: 0.0016, Energy: -85.088690-0.003437j
[2025-07-30 23:24:15] [Iter 4591/4650] R4[2340/2400], Temp: 0.0015, Energy: -85.058240-0.007044j
[2025-07-30 23:24:29] [Iter 4592/4650] R4[2341/2400], Temp: 0.0015, Energy: -85.063538-0.006330j
[2025-07-30 23:24:44] [Iter 4593/4650] R4[2342/2400], Temp: 0.0014, Energy: -85.096971-0.001234j
[2025-07-30 23:24:59] [Iter 4594/4650] R4[2343/2400], Temp: 0.0014, Energy: -85.092721+0.000352j
[2025-07-30 23:25:14] [Iter 4595/4650] R4[2344/2400], Temp: 0.0013, Energy: -85.116773+0.001017j
[2025-07-30 23:25:29] [Iter 4596/4650] R4[2345/2400], Temp: 0.0013, Energy: -85.117770+0.007232j
[2025-07-30 23:25:44] [Iter 4597/4650] R4[2346/2400], Temp: 0.0012, Energy: -84.987911-0.002036j
[2025-07-30 23:25:58] [Iter 4598/4650] R4[2347/2400], Temp: 0.0012, Energy: -85.012869-0.003393j
[2025-07-30 23:26:13] [Iter 4599/4650] R4[2348/2400], Temp: 0.0012, Energy: -85.108031-0.012882j
[2025-07-30 23:26:27] [Iter 4600/4650] R4[2349/2400], Temp: 0.0011, Energy: -85.106863-0.011337j
[2025-07-30 23:26:41] [Iter 4601/4650] R4[2350/2400], Temp: 0.0011, Energy: -85.054328-0.009169j
[2025-07-30 23:26:56] [Iter 4602/4650] R4[2351/2400], Temp: 0.0010, Energy: -85.100659+0.002401j
[2025-07-30 23:27:10] [Iter 4603/4650] R4[2352/2400], Temp: 0.0010, Energy: -85.062228+0.003799j
[2025-07-30 23:27:25] [Iter 4604/4650] R4[2353/2400], Temp: 0.0009, Energy: -84.992401+0.000272j
[2025-07-30 23:27:40] [Iter 4605/4650] R4[2354/2400], Temp: 0.0009, Energy: -84.970233-0.002226j
[2025-07-30 23:27:55] [Iter 4606/4650] R4[2355/2400], Temp: 0.0009, Energy: -84.981701-0.005382j
[2025-07-30 23:28:10] [Iter 4607/4650] R4[2356/2400], Temp: 0.0008, Energy: -85.042883+0.003500j
[2025-07-30 23:28:25] [Iter 4608/4650] R4[2357/2400], Temp: 0.0008, Energy: -85.064687-0.007992j
[2025-07-30 23:28:40] [Iter 4609/4650] R4[2358/2400], Temp: 0.0008, Energy: -85.033832+0.004330j
[2025-07-30 23:28:54] [Iter 4610/4650] R4[2359/2400], Temp: 0.0007, Energy: -85.063369-0.001935j
[2025-07-30 23:29:09] [Iter 4611/4650] R4[2360/2400], Temp: 0.0007, Energy: -85.059163-0.007771j
[2025-07-30 23:29:24] [Iter 4612/4650] R4[2361/2400], Temp: 0.0007, Energy: -85.075641+0.004314j
[2025-07-30 23:29:39] [Iter 4613/4650] R4[2362/2400], Temp: 0.0006, Energy: -85.059662-0.002199j
[2025-07-30 23:29:54] [Iter 4614/4650] R4[2363/2400], Temp: 0.0006, Energy: -85.039450-0.006215j
[2025-07-30 23:30:08] [Iter 4615/4650] R4[2364/2400], Temp: 0.0006, Energy: -85.088948-0.001228j
[2025-07-30 23:30:22] [Iter 4616/4650] R4[2365/2400], Temp: 0.0005, Energy: -85.076192+0.000200j
[2025-07-30 23:30:37] [Iter 4617/4650] R4[2366/2400], Temp: 0.0005, Energy: -85.075706-0.007201j
[2025-07-30 23:30:51] [Iter 4618/4650] R4[2367/2400], Temp: 0.0005, Energy: -85.120835-0.007387j
[2025-07-30 23:31:06] [Iter 4619/4650] R4[2368/2400], Temp: 0.0004, Energy: -85.154236+0.004021j
[2025-07-30 23:31:21] [Iter 4620/4650] R4[2369/2400], Temp: 0.0004, Energy: -85.146064+0.005153j
[2025-07-30 23:31:35] [Iter 4621/4650] R4[2370/2400], Temp: 0.0004, Energy: -85.082573+0.011006j
[2025-07-30 23:31:50] [Iter 4622/4650] R4[2371/2400], Temp: 0.0004, Energy: -85.049180-0.006888j
[2025-07-30 23:32:04] [Iter 4623/4650] R4[2372/2400], Temp: 0.0003, Energy: -85.030036-0.000621j
[2025-07-30 23:32:19] [Iter 4624/4650] R4[2373/2400], Temp: 0.0003, Energy: -84.936906+0.004716j
[2025-07-30 23:32:33] [Iter 4625/4650] R4[2374/2400], Temp: 0.0003, Energy: -84.971423+0.008558j
[2025-07-30 23:32:47] [Iter 4626/4650] R4[2375/2400], Temp: 0.0003, Energy: -85.058838+0.001723j
[2025-07-30 23:33:02] [Iter 4627/4650] R4[2376/2400], Temp: 0.0002, Energy: -85.057118+0.006932j
[2025-07-30 23:33:17] [Iter 4628/4650] R4[2377/2400], Temp: 0.0002, Energy: -85.028309+0.008898j
[2025-07-30 23:33:32] [Iter 4629/4650] R4[2378/2400], Temp: 0.0002, Energy: -85.039831-0.000101j
[2025-07-30 23:33:47] [Iter 4630/4650] R4[2379/2400], Temp: 0.0002, Energy: -85.028199+0.002860j
[2025-07-30 23:34:01] [Iter 4631/4650] R4[2380/2400], Temp: 0.0002, Energy: -85.027727+0.009765j
[2025-07-30 23:34:16] [Iter 4632/4650] R4[2381/2400], Temp: 0.0002, Energy: -84.982647-0.001827j
[2025-07-30 23:34:30] [Iter 4633/4650] R4[2382/2400], Temp: 0.0001, Energy: -84.986907+0.013016j
[2025-07-30 23:34:44] [Iter 4634/4650] R4[2383/2400], Temp: 0.0001, Energy: -84.965449-0.002192j
[2025-07-30 23:34:59] [Iter 4635/4650] R4[2384/2400], Temp: 0.0001, Energy: -84.977463+0.002516j
[2025-07-30 23:35:14] [Iter 4636/4650] R4[2385/2400], Temp: 0.0001, Energy: -84.962076-0.003858j
[2025-07-30 23:35:28] [Iter 4637/4650] R4[2386/2400], Temp: 0.0001, Energy: -84.939949-0.004006j
[2025-07-30 23:35:43] [Iter 4638/4650] R4[2387/2400], Temp: 0.0001, Energy: -84.981896+0.002061j
[2025-07-30 23:35:58] [Iter 4639/4650] R4[2388/2400], Temp: 0.0001, Energy: -84.961665+0.005752j
[2025-07-30 23:36:12] [Iter 4640/4650] R4[2389/2400], Temp: 0.0001, Energy: -85.022888+0.000065j
[2025-07-30 23:36:27] [Iter 4641/4650] R4[2390/2400], Temp: 0.0000, Energy: -85.062815+0.006130j
[2025-07-30 23:36:42] [Iter 4642/4650] R4[2391/2400], Temp: 0.0000, Energy: -84.965367+0.005910j
[2025-07-30 23:36:57] [Iter 4643/4650] R4[2392/2400], Temp: 0.0000, Energy: -84.967700+0.003344j
[2025-07-30 23:37:11] [Iter 4644/4650] R4[2393/2400], Temp: 0.0000, Energy: -85.044995-0.005590j
[2025-07-30 23:37:26] [Iter 4645/4650] R4[2394/2400], Temp: 0.0000, Energy: -84.985204+0.002546j
[2025-07-30 23:37:41] [Iter 4646/4650] R4[2395/2400], Temp: 0.0000, Energy: -84.976783+0.001510j
[2025-07-30 23:37:56] [Iter 4647/4650] R4[2396/2400], Temp: 0.0000, Energy: -84.983529+0.004317j
[2025-07-30 23:38:10] [Iter 4648/4650] R4[2397/2400], Temp: 0.0000, Energy: -85.035329+0.003552j
[2025-07-30 23:38:24] [Iter 4649/4650] R4[2398/2400], Temp: 0.0000, Energy: -84.939579+0.000422j
[2025-07-30 23:38:39] [Iter 4650/4650] R4[2399/2400], Temp: 0.0000, Energy: -84.992214-0.004034j
[2025-07-30 23:38:39] ✅ Training completed | Restarts: 4
[2025-07-30 23:38:39] ============================================================
[2025-07-30 23:38:39] Training completed | Runtime: 68481.8s
[2025-07-30 23:38:53] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-07-30 23:38:53] ============================================================
