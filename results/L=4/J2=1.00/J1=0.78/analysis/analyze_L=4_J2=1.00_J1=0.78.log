[2025-07-25 01:27:06] ================================================================================
[2025-07-25 01:27:06] 加载量子态: L=4, J2=1.00, J1=0.78
[2025-07-25 01:27:06] 设置样本数为: 1048576
[2025-07-25 01:27:06] 开始生成共享样本集...
[2025-07-25 01:28:15] 样本生成完成,耗时: 69.382 秒
[2025-07-25 01:28:15] ================================================================================
[2025-07-25 01:28:15] 开始计算自旋结构因子...
[2025-07-25 01:28:15] 初始化操作符缓存...
[2025-07-25 01:28:15] 预构建所有自旋相关操作符...
[2025-07-25 01:28:16] 开始计算自旋相关函数...
[2025-07-25 01:28:23] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 7.889s
[2025-07-25 01:28:32] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 8.425s
[2025-07-25 01:28:35] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 3.561s
[2025-07-25 01:28:39] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 3.569s
[2025-07-25 01:28:43] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 3.569s
[2025-07-25 01:28:46] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 3.560s
[2025-07-25 01:28:50] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 3.568s
[2025-07-25 01:28:53] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 3.569s
[2025-07-25 01:28:57] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 3.548s
[2025-07-25 01:29:00] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 3.568s
[2025-07-25 01:29:04] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 3.561s
[2025-07-25 01:29:08] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 3.560s
[2025-07-25 01:29:11] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 3.568s
[2025-07-25 01:29:15] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 3.550s
[2025-07-25 01:29:18] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 3.568s
[2025-07-25 01:29:22] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 3.569s
[2025-07-25 01:29:25] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 3.566s
[2025-07-25 01:29:29] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 3.550s
[2025-07-25 01:29:32] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 3.567s
[2025-07-25 01:29:36] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 3.568s
[2025-07-25 01:29:40] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 3.562s
[2025-07-25 01:29:43] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 3.563s
[2025-07-25 01:29:47] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 3.568s
[2025-07-25 01:29:50] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 3.550s
[2025-07-25 01:29:54] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 3.569s
[2025-07-25 01:29:57] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 3.568s
[2025-07-25 01:30:01] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 3.564s
[2025-07-25 01:30:05] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 3.568s
[2025-07-25 01:30:08] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 3.549s
[2025-07-25 01:30:12] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 3.550s
[2025-07-25 01:30:15] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 3.568s
[2025-07-25 01:30:19] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 3.563s
[2025-07-25 01:30:22] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 3.567s
[2025-07-25 01:30:26] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 3.567s
[2025-07-25 01:30:29] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 3.550s
[2025-07-25 01:30:33] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 3.567s
[2025-07-25 01:30:37] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 3.576s
[2025-07-25 01:30:40] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 3.562s
[2025-07-25 01:30:44] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 3.569s
[2025-07-25 01:30:47] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 3.551s
[2025-07-25 01:30:51] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 3.570s
[2025-07-25 01:30:54] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 3.568s
[2025-07-25 01:30:58] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 3.567s
[2025-07-25 01:31:02] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 3.561s
[2025-07-25 01:31:05] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 3.569s
[2025-07-25 01:31:09] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 3.569s
[2025-07-25 01:31:12] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 3.550s
[2025-07-25 01:31:16] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 3.568s
[2025-07-25 01:31:19] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 3.551s
[2025-07-25 01:31:23] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 3.562s
[2025-07-25 01:31:27] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 3.568s
[2025-07-25 01:31:30] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 3.565s
[2025-07-25 01:31:34] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 3.568s
[2025-07-25 01:31:37] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 3.569s
[2025-07-25 01:31:41] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 3.549s
[2025-07-25 01:31:44] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 3.568s
[2025-07-25 01:31:48] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 3.561s
[2025-07-25 01:31:51] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 3.551s
[2025-07-25 01:31:55] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 3.568s
[2025-07-25 01:31:59] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 3.550s
[2025-07-25 01:32:02] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 3.568s
[2025-07-25 01:32:06] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 3.568s
[2025-07-25 01:32:09] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 3.566s
[2025-07-25 01:32:13] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 3.550s
[2025-07-25 01:32:13] 自旋相关函数计算完成,总耗时 237.29 秒
[2025-07-25 01:32:13] 计算傅里叶变换...
[2025-07-25 01:32:14] 自旋结构因子计算完成
[2025-07-25 01:32:14] 自旋相关函数平均误差: 0.000539
[2025-07-25 01:32:14] ================================================================================
[2025-07-25 01:32:14] 开始计算对角二聚体结构因子...
[2025-07-25 01:32:14] 识别所有对角二聚体...
[2025-07-25 01:32:14] 总共找到 64 个西北-东南方向对角二聚体和 64 个西南-东北方向对角二聚体
[2025-07-25 01:32:14] 预计算对角二聚体操作符...
[2025-07-25 01:32:17] 开始计算西北-东南方向对角二聚体相关函数 (64 个算符)...
[2025-07-25 01:32:20] NW-SE对角算符进度: 1/64, 当前算符: D_(0, 14) * D_(0, 14), 耗时: 3.608s
[2025-07-25 01:32:31] NW-SE对角算符进度: 2/64, 当前算符: D_(0, 14) * D_(1, 31), 耗时: 10.989s
[2025-07-25 01:32:37] NW-SE对角算符进度: 3/64, 当前算符: D_(0, 14) * D_(2, 16), 耗时: 5.964s
[2025-07-25 01:32:43] NW-SE对角算符进度: 4/64, 当前算符: D_(0, 14) * D_(3, 1), 耗时: 5.965s
[2025-07-25 01:32:49] NW-SE对角算符进度: 5/64, 当前算符: D_(0, 14) * D_(4, 2), 耗时: 5.934s
[2025-07-25 01:32:55] NW-SE对角算符进度: 6/64, 当前算符: D_(0, 14) * D_(5, 19), 耗时: 5.964s
[2025-07-25 01:33:01] NW-SE对角算符进度: 7/64, 当前算符: D_(0, 14) * D_(6, 20), 耗时: 5.932s
[2025-07-25 01:33:07] NW-SE对角算符进度: 8/64, 当前算符: D_(0, 14) * D_(7, 5), 耗时: 5.965s
[2025-07-25 01:33:13] NW-SE对角算符进度: 9/64, 当前算符: D_(0, 14) * D_(8, 6), 耗时: 5.944s
[2025-07-25 01:33:19] NW-SE对角算符进度: 10/64, 当前算符: D_(0, 14) * D_(9, 23), 耗时: 5.963s
[2025-07-25 01:33:25] NW-SE对角算符进度: 11/64, 当前算符: D_(0, 14) * D_(10, 24), 耗时: 5.935s
[2025-07-25 01:33:31] NW-SE对角算符进度: 12/64, 当前算符: D_(0, 14) * D_(11, 9), 耗时: 5.939s
[2025-07-25 01:33:37] NW-SE对角算符进度: 13/64, 当前算符: D_(0, 14) * D_(12, 10), 耗时: 5.963s
[2025-07-25 01:33:43] NW-SE对角算符进度: 14/64, 当前算符: D_(0, 14) * D_(13, 27), 耗时: 5.931s
[2025-07-25 01:33:56] NW-SE对角算符进度: 15/64, 当前算符: D_(0, 14) * D_(14, 28), 耗时: 13.269s
[2025-07-25 01:34:02] NW-SE对角算符进度: 16/64, 当前算符: D_(0, 14) * D_(15, 13), 耗时: 5.970s
[2025-07-25 01:34:08] NW-SE对角算符进度: 17/64, 当前算符: D_(0, 14) * D_(16, 30), 耗时: 5.949s
[2025-07-25 01:34:14] NW-SE对角算符进度: 18/64, 当前算符: D_(0, 14) * D_(17, 47), 耗时: 5.931s
[2025-07-25 01:34:20] NW-SE对角算符进度: 19/64, 当前算符: D_(0, 14) * D_(18, 32), 耗时: 5.969s
[2025-07-25 01:34:26] NW-SE对角算符进度: 20/64, 当前算符: D_(0, 14) * D_(19, 17), 耗时: 5.928s
[2025-07-25 01:34:32] NW-SE对角算符进度: 21/64, 当前算符: D_(0, 14) * D_(20, 18), 耗时: 5.969s
[2025-07-25 01:34:38] NW-SE对角算符进度: 22/64, 当前算符: D_(0, 14) * D_(21, 35), 耗时: 5.954s
[2025-07-25 01:34:43] NW-SE对角算符进度: 23/64, 当前算符: D_(0, 14) * D_(22, 36), 耗时: 5.971s
[2025-07-25 01:34:49] NW-SE对角算符进度: 24/64, 当前算符: D_(0, 14) * D_(23, 21), 耗时: 5.934s
[2025-07-25 01:34:55] NW-SE对角算符进度: 25/64, 当前算符: D_(0, 14) * D_(24, 22), 耗时: 5.969s
[2025-07-25 01:35:01] NW-SE对角算符进度: 26/64, 当前算符: D_(0, 14) * D_(25, 39), 耗时: 5.950s
[2025-07-25 01:35:07] NW-SE对角算符进度: 27/64, 当前算符: D_(0, 14) * D_(26, 40), 耗时: 5.970s
[2025-07-25 01:35:13] NW-SE对角算符进度: 28/64, 当前算符: D_(0, 14) * D_(27, 25), 耗时: 5.950s
[2025-07-25 01:35:19] NW-SE对角算符进度: 29/64, 当前算符: D_(0, 14) * D_(28, 26), 耗时: 5.969s
[2025-07-25 01:35:25] NW-SE对角算符进度: 30/64, 当前算符: D_(0, 14) * D_(29, 43), 耗时: 5.933s
[2025-07-25 01:35:31] NW-SE对角算符进度: 31/64, 当前算符: D_(0, 14) * D_(30, 44), 耗时: 5.970s
[2025-07-25 01:35:37] NW-SE对角算符进度: 32/64, 当前算符: D_(0, 14) * D_(31, 29), 耗时: 5.956s
[2025-07-25 01:35:43] NW-SE对角算符进度: 33/64, 当前算符: D_(0, 14) * D_(32, 46), 耗时: 5.969s
[2025-07-25 01:35:49] NW-SE对角算符进度: 34/64, 当前算符: D_(0, 14) * D_(33, 63), 耗时: 5.968s
[2025-07-25 01:35:55] NW-SE对角算符进度: 35/64, 当前算符: D_(0, 14) * D_(34, 48), 耗时: 5.968s
[2025-07-25 01:36:01] NW-SE对角算符进度: 36/64, 当前算符: D_(0, 14) * D_(35, 33), 耗时: 5.934s
[2025-07-25 01:36:07] NW-SE对角算符进度: 37/64, 当前算符: D_(0, 14) * D_(36, 34), 耗时: 5.968s
[2025-07-25 01:36:13] NW-SE对角算符进度: 38/64, 当前算符: D_(0, 14) * D_(37, 51), 耗时: 5.955s
[2025-07-25 01:36:19] NW-SE对角算符进度: 39/64, 当前算符: D_(0, 14) * D_(38, 52), 耗时: 5.968s
[2025-07-25 01:36:25] NW-SE对角算符进度: 40/64, 当前算符: D_(0, 14) * D_(39, 37), 耗时: 5.930s
[2025-07-25 01:36:31] NW-SE对角算符进度: 41/64, 当前算符: D_(0, 14) * D_(40, 38), 耗时: 5.969s
[2025-07-25 01:36:37] NW-SE对角算符进度: 42/64, 当前算符: D_(0, 14) * D_(41, 55), 耗时: 5.956s
[2025-07-25 01:36:43] NW-SE对角算符进度: 43/64, 当前算符: D_(0, 14) * D_(42, 56), 耗时: 5.968s
[2025-07-25 01:36:49] NW-SE对角算符进度: 44/64, 当前算符: D_(0, 14) * D_(43, 41), 耗时: 5.955s
[2025-07-25 01:36:55] NW-SE对角算符进度: 45/64, 当前算符: D_(0, 14) * D_(44, 42), 耗时: 5.969s
[2025-07-25 01:37:01] NW-SE对角算符进度: 46/64, 当前算符: D_(0, 14) * D_(45, 59), 耗时: 5.931s
[2025-07-25 01:37:06] NW-SE对角算符进度: 47/64, 当前算符: D_(0, 14) * D_(46, 60), 耗时: 5.968s
[2025-07-25 01:37:12] NW-SE对角算符进度: 48/64, 当前算符: D_(0, 14) * D_(47, 45), 耗时: 5.933s
[2025-07-25 01:37:18] NW-SE对角算符进度: 49/64, 当前算符: D_(0, 14) * D_(48, 62), 耗时: 5.969s
[2025-07-25 01:37:24] NW-SE对角算符进度: 50/64, 当前算符: D_(0, 14) * D_(49, 15), 耗时: 5.969s
[2025-07-25 01:37:29] NW-SE对角算符进度: 51/64, 当前算符: D_(0, 14) * D_(50, 0), 耗时: 4.663s
[2025-07-25 01:37:35] NW-SE对角算符进度: 52/64, 当前算符: D_(0, 14) * D_(51, 49), 耗时: 5.954s
[2025-07-25 01:37:41] NW-SE对角算符进度: 53/64, 当前算符: D_(0, 14) * D_(52, 50), 耗时: 5.968s
[2025-07-25 01:37:47] NW-SE对角算符进度: 54/64, 当前算符: D_(0, 14) * D_(53, 3), 耗时: 5.933s
[2025-07-25 01:37:53] NW-SE对角算符进度: 55/64, 当前算符: D_(0, 14) * D_(54, 4), 耗时: 5.968s
[2025-07-25 01:37:59] NW-SE对角算符进度: 56/64, 当前算符: D_(0, 14) * D_(55, 53), 耗时: 5.957s
[2025-07-25 01:38:05] NW-SE对角算符进度: 57/64, 当前算符: D_(0, 14) * D_(56, 54), 耗时: 5.968s
[2025-07-25 01:38:11] NW-SE对角算符进度: 58/64, 当前算符: D_(0, 14) * D_(57, 7), 耗时: 5.931s
[2025-07-25 01:38:17] NW-SE对角算符进度: 59/64, 当前算符: D_(0, 14) * D_(58, 8), 耗时: 5.968s
[2025-07-25 01:38:23] NW-SE对角算符进度: 60/64, 当前算符: D_(0, 14) * D_(59, 57), 耗时: 6.024s
[2025-07-25 01:38:29] NW-SE对角算符进度: 61/64, 当前算符: D_(0, 14) * D_(60, 58), 耗时: 5.969s
[2025-07-25 01:38:35] NW-SE对角算符进度: 62/64, 当前算符: D_(0, 14) * D_(61, 11), 耗时: 5.956s
[2025-07-25 01:38:41] NW-SE对角算符进度: 63/64, 当前算符: D_(0, 14) * D_(62, 12), 耗时: 5.927s
[2025-07-25 01:38:47] NW-SE对角算符进度: 64/64, 当前算符: D_(0, 14) * D_(63, 61), 耗时: 5.970s
[2025-07-25 01:38:47] 西北-东南方向对角二聚体相关函数计算完成,耗时: 389.98 秒
[2025-07-25 01:38:47] ================================================================================
[2025-07-25 01:38:47] 开始计算西南-东北方向对角二聚体相关函数 (64 个算符)...
[2025-07-25 01:38:50] SW-NE对角算符进度: 1/64, 当前算符: D_(0, 2) * D_(0, 2), 耗时: 3.564s
[2025-07-25 01:38:56] SW-NE对角算符进度: 2/64, 当前算符: D_(0, 2) * D_(1, 19), 耗时: 5.968s
[2025-07-25 01:39:01] SW-NE对角算符进度: 3/64, 当前算符: D_(0, 2) * D_(2, 20), 耗时: 4.658s
[2025-07-25 01:39:07] SW-NE对角算符进度: 4/64, 当前算符: D_(0, 2) * D_(3, 5), 耗时: 5.968s
[2025-07-25 01:39:13] SW-NE对角算符进度: 5/64, 当前算符: D_(0, 2) * D_(4, 6), 耗时: 5.967s
[2025-07-25 01:39:19] SW-NE对角算符进度: 6/64, 当前算符: D_(0, 2) * D_(5, 23), 耗时: 5.927s
[2025-07-25 01:39:25] SW-NE对角算符进度: 7/64, 当前算符: D_(0, 2) * D_(6, 24), 耗时: 5.967s
[2025-07-25 01:39:31] SW-NE对角算符进度: 8/64, 当前算符: D_(0, 2) * D_(7, 9), 耗时: 5.955s
[2025-07-25 01:39:36] SW-NE对角算符进度: 9/64, 当前算符: D_(0, 2) * D_(8, 10), 耗时: 5.957s
[2025-07-25 01:39:42] SW-NE对角算符进度: 10/64, 当前算符: D_(0, 2) * D_(9, 27), 耗时: 5.969s
[2025-07-25 01:39:48] SW-NE对角算符进度: 11/64, 当前算符: D_(0, 2) * D_(10, 28), 耗时: 5.931s
[2025-07-25 01:39:54] SW-NE对角算符进度: 12/64, 当前算符: D_(0, 2) * D_(11, 13), 耗时: 5.968s
[2025-07-25 01:40:00] SW-NE对角算符进度: 13/64, 当前算符: D_(0, 2) * D_(12, 14), 耗时: 5.957s
[2025-07-25 01:40:06] SW-NE对角算符进度: 14/64, 当前算符: D_(0, 2) * D_(13, 31), 耗时: 5.968s
[2025-07-25 01:40:12] SW-NE对角算符进度: 15/64, 当前算符: D_(0, 2) * D_(14, 16), 耗时: 5.927s
[2025-07-25 01:40:18] SW-NE对角算符进度: 16/64, 当前算符: D_(0, 2) * D_(15, 1), 耗时: 5.967s
[2025-07-25 01:40:24] SW-NE对角算符进度: 17/64, 当前算符: D_(0, 2) * D_(16, 18), 耗时: 6.023s
[2025-07-25 01:40:30] SW-NE对角算符进度: 18/64, 当前算符: D_(0, 2) * D_(17, 35), 耗时: 5.967s
[2025-07-25 01:40:36] SW-NE对角算符进度: 19/64, 当前算符: D_(0, 2) * D_(18, 36), 耗时: 5.955s
[2025-07-25 01:40:42] SW-NE对角算符进度: 20/64, 当前算符: D_(0, 2) * D_(19, 21), 耗时: 5.969s
[2025-07-25 01:40:48] SW-NE对角算符进度: 21/64, 当前算符: D_(0, 2) * D_(20, 22), 耗时: 5.927s
[2025-07-25 01:40:54] SW-NE对角算符进度: 22/64, 当前算符: D_(0, 2) * D_(21, 39), 耗时: 5.933s
[2025-07-25 01:41:00] SW-NE对角算符进度: 23/64, 当前算符: D_(0, 2) * D_(22, 40), 耗时: 5.969s
[2025-07-25 01:41:06] SW-NE对角算符进度: 24/64, 当前算符: D_(0, 2) * D_(23, 25), 耗时: 5.954s
[2025-07-25 01:41:12] SW-NE对角算符进度: 25/64, 当前算符: D_(0, 2) * D_(24, 26), 耗时: 5.952s
[2025-07-25 01:41:18] SW-NE对角算符进度: 26/64, 当前算符: D_(0, 2) * D_(25, 43), 耗时: 5.967s
[2025-07-25 01:41:24] SW-NE对角算符进度: 27/64, 当前算符: D_(0, 2) * D_(26, 44), 耗时: 5.950s
[2025-07-25 01:41:30] SW-NE对角算符进度: 28/64, 当前算符: D_(0, 2) * D_(27, 29), 耗时: 5.968s
[2025-07-25 01:41:36] SW-NE对角算符进度: 29/64, 当前算符: D_(0, 2) * D_(28, 30), 耗时: 5.932s
[2025-07-25 01:41:42] SW-NE对角算符进度: 30/64, 当前算符: D_(0, 2) * D_(29, 47), 耗时: 5.968s
[2025-07-25 01:41:48] SW-NE对角算符进度: 31/64, 当前算符: D_(0, 2) * D_(30, 32), 耗时: 5.955s
[2025-07-25 01:41:54] SW-NE对角算符进度: 32/64, 当前算符: D_(0, 2) * D_(31, 17), 耗时: 5.969s
[2025-07-25 01:41:59] SW-NE对角算符进度: 33/64, 当前算符: D_(0, 2) * D_(32, 34), 耗时: 5.954s
[2025-07-25 01:42:05] SW-NE对角算符进度: 34/64, 当前算符: D_(0, 2) * D_(33, 51), 耗时: 5.969s
[2025-07-25 01:42:11] SW-NE对角算符进度: 35/64, 当前算符: D_(0, 2) * D_(34, 52), 耗时: 5.932s
[2025-07-25 01:42:17] SW-NE对角算符进度: 36/64, 当前算符: D_(0, 2) * D_(35, 37), 耗时: 5.968s
[2025-07-25 01:42:23] SW-NE对角算符进度: 37/64, 当前算符: D_(0, 2) * D_(36, 38), 耗时: 5.953s
[2025-07-25 01:42:29] SW-NE对角算符进度: 38/64, 当前算符: D_(0, 2) * D_(37, 55), 耗时: 5.968s
[2025-07-25 01:42:35] SW-NE对角算符进度: 39/64, 当前算符: D_(0, 2) * D_(38, 56), 耗时: 5.968s
[2025-07-25 01:42:41] SW-NE对角算符进度: 40/64, 当前算符: D_(0, 2) * D_(39, 41), 耗时: 5.932s
[2025-07-25 01:42:47] SW-NE对角算符进度: 41/64, 当前算符: D_(0, 2) * D_(40, 42), 耗时: 5.930s
[2025-07-25 01:42:53] SW-NE对角算符进度: 42/64, 当前算符: D_(0, 2) * D_(41, 59), 耗时: 5.968s
[2025-07-25 01:42:59] SW-NE对角算符进度: 43/64, 当前算符: D_(0, 2) * D_(42, 60), 耗时: 5.949s
[2025-07-25 01:43:05] SW-NE对角算符进度: 44/64, 当前算符: D_(0, 2) * D_(43, 45), 耗时: 5.968s
[2025-07-25 01:43:11] SW-NE对角算符进度: 45/64, 当前算符: D_(0, 2) * D_(44, 46), 耗时: 5.934s
[2025-07-25 01:43:17] SW-NE对角算符进度: 46/64, 当前算符: D_(0, 2) * D_(45, 63), 耗时: 5.968s
[2025-07-25 01:43:23] SW-NE对角算符进度: 47/64, 当前算符: D_(0, 2) * D_(46, 48), 耗时: 5.954s
[2025-07-25 01:43:29] SW-NE对角算符进度: 48/64, 当前算符: D_(0, 2) * D_(47, 33), 耗时: 5.969s
[2025-07-25 01:43:35] SW-NE对角算符进度: 49/64, 当前算符: D_(0, 2) * D_(48, 50), 耗时: 5.954s
[2025-07-25 01:43:41] SW-NE对角算符进度: 50/64, 当前算符: D_(0, 2) * D_(49, 3), 耗时: 5.968s
[2025-07-25 01:43:47] SW-NE对角算符进度: 51/64, 当前算符: D_(0, 2) * D_(50, 4), 耗时: 5.931s
[2025-07-25 01:43:53] SW-NE对角算符进度: 52/64, 当前算符: D_(0, 2) * D_(51, 53), 耗时: 6.004s
[2025-07-25 01:43:59] SW-NE对角算符进度: 53/64, 当前算符: D_(0, 2) * D_(52, 54), 耗时: 5.956s
[2025-07-25 01:44:05] SW-NE对角算符进度: 54/64, 当前算符: D_(0, 2) * D_(53, 7), 耗时: 5.968s
[2025-07-25 01:44:11] SW-NE对角算符进度: 55/64, 当前算符: D_(0, 2) * D_(54, 8), 耗时: 5.930s
[2025-07-25 01:44:16] SW-NE对角算符进度: 56/64, 当前算符: D_(0, 2) * D_(55, 57), 耗时: 5.934s
[2025-07-25 01:44:22] SW-NE对角算符进度: 57/64, 当前算符: D_(0, 2) * D_(56, 58), 耗时: 5.932s
[2025-07-25 01:44:28] SW-NE对角算符进度: 58/64, 当前算符: D_(0, 2) * D_(57, 11), 耗时: 5.967s
[2025-07-25 01:44:34] SW-NE对角算符进度: 59/64, 当前算符: D_(0, 2) * D_(58, 12), 耗时: 5.953s
[2025-07-25 01:44:40] SW-NE对角算符进度: 60/64, 当前算符: D_(0, 2) * D_(59, 61), 耗时: 5.968s
[2025-07-25 01:44:46] SW-NE对角算符进度: 61/64, 当前算符: D_(0, 2) * D_(60, 62), 耗时: 5.931s
[2025-07-25 01:44:52] SW-NE对角算符进度: 62/64, 当前算符: D_(0, 2) * D_(61, 15), 耗时: 5.968s
[2025-07-25 01:44:57] SW-NE对角算符进度: 63/64, 当前算符: D_(0, 2) * D_(62, 0), 耗时: 4.659s
[2025-07-25 01:45:03] SW-NE对角算符进度: 64/64, 当前算符: D_(0, 2) * D_(63, 49), 耗时: 5.969s
[2025-07-25 01:45:03] 西南-东北方向对角二聚体相关函数计算完成,耗时: 376.32 秒
[2025-07-25 01:45:03] 计算傅里叶变换...
[2025-07-25 01:45:03] 对角二聚体结构因子计算完成
[2025-07-25 01:45:04] 对角二聚体相关函数平均误差: 0.000122
