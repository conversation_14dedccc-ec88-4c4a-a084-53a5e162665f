[2025-07-25 02:21:43] ================================================================================
[2025-07-25 02:21:43] 加载量子态: L=4, J2=1.00, J1=0.81
[2025-07-25 02:21:43] 设置样本数为: 1048576
[2025-07-25 02:21:43] 开始生成共享样本集...
[2025-07-25 02:22:53] 样本生成完成,耗时: 69.781 秒
[2025-07-25 02:22:53] ================================================================================
[2025-07-25 02:22:53] 开始计算自旋结构因子...
[2025-07-25 02:22:53] 初始化操作符缓存...
[2025-07-25 02:22:53] 预构建所有自旋相关操作符...
[2025-07-25 02:22:53] 开始计算自旋相关函数...
[2025-07-25 02:23:01] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 7.823s
[2025-07-25 02:23:10] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 8.501s
[2025-07-25 02:23:13] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 3.587s
[2025-07-25 02:23:17] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 3.587s
[2025-07-25 02:23:20] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 3.586s
[2025-07-25 02:23:24] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 3.586s
[2025-07-25 02:23:28] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 3.588s
[2025-07-25 02:23:31] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 3.585s
[2025-07-25 02:23:35] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 3.578s
[2025-07-25 02:23:38] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 3.587s
[2025-07-25 02:23:42] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 3.586s
[2025-07-25 02:23:46] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 3.586s
[2025-07-25 02:23:49] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 3.590s
[2025-07-25 02:23:53] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 3.567s
[2025-07-25 02:23:56] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 3.588s
[2025-07-25 02:24:00] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 3.586s
[2025-07-25 02:24:03] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 3.587s
[2025-07-25 02:24:07] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 3.574s
[2025-07-25 02:24:11] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 3.589s
[2025-07-25 02:24:14] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 3.586s
[2025-07-25 02:24:18] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 3.587s
[2025-07-25 02:24:21] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 3.586s
[2025-07-25 02:24:25] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 3.588s
[2025-07-25 02:24:29] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 3.574s
[2025-07-25 02:24:32] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 3.587s
[2025-07-25 02:24:36] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 3.588s
[2025-07-25 02:24:39] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 3.586s
[2025-07-25 02:24:43] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 3.587s
[2025-07-25 02:24:47] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 3.575s
[2025-07-25 02:24:50] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 3.575s
[2025-07-25 02:24:54] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 3.586s
[2025-07-25 02:24:57] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 3.586s
[2025-07-25 02:25:01] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 3.586s
[2025-07-25 02:25:04] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 3.588s
[2025-07-25 02:25:08] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 3.574s
[2025-07-25 02:25:12] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 3.589s
[2025-07-25 02:25:15] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 3.587s
[2025-07-25 02:25:19] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 3.586s
[2025-07-25 02:25:22] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 3.587s
[2025-07-25 02:25:26] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 3.572s
[2025-07-25 02:25:30] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 3.590s
[2025-07-25 02:25:33] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 3.588s
[2025-07-25 02:25:37] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 3.587s
[2025-07-25 02:25:40] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 3.586s
[2025-07-25 02:25:44] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 3.586s
[2025-07-25 02:25:47] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 3.585s
[2025-07-25 02:25:51] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 3.575s
[2025-07-25 02:25:55] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 3.587s
[2025-07-25 02:25:58] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 3.577s
[2025-07-25 02:26:02] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 3.587s
[2025-07-25 02:26:05] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 3.589s
[2025-07-25 02:26:09] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 3.587s
[2025-07-25 02:26:13] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 3.587s
[2025-07-25 02:26:16] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 3.585s
[2025-07-25 02:26:20] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 3.571s
[2025-07-25 02:26:23] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 3.587s
[2025-07-25 02:26:27] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 3.586s
[2025-07-25 02:26:30] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 3.579s
[2025-07-25 02:26:34] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 3.586s
[2025-07-25 02:26:38] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 3.576s
[2025-07-25 02:26:41] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 3.587s
[2025-07-25 02:26:45] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 3.587s
[2025-07-25 02:26:48] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 3.587s
[2025-07-25 02:26:52] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 3.566s
[2025-07-25 02:26:52] 自旋相关函数计算完成,总耗时 238.61 秒
[2025-07-25 02:26:52] 计算傅里叶变换...
[2025-07-25 02:26:53] 自旋结构因子计算完成
[2025-07-25 02:26:53] 自旋相关函数平均误差: 0.000526
[2025-07-25 02:26:53] ================================================================================
[2025-07-25 02:26:53] 开始计算对角二聚体结构因子...
[2025-07-25 02:26:53] 识别所有对角二聚体...
[2025-07-25 02:26:53] 总共找到 64 个西北-东南方向对角二聚体和 64 个西南-东北方向对角二聚体
[2025-07-25 02:26:53] 预计算对角二聚体操作符...
[2025-07-25 02:26:55] 开始计算西北-东南方向对角二聚体相关函数 (64 个算符)...
[2025-07-25 02:26:59] NW-SE对角算符进度: 1/64, 当前算符: D_(0, 14) * D_(0, 14), 耗时: 3.618s
[2025-07-25 02:27:10] NW-SE对角算符进度: 2/64, 当前算符: D_(0, 14) * D_(1, 31), 耗时: 10.969s
[2025-07-25 02:27:16] NW-SE对角算符进度: 3/64, 当前算符: D_(0, 14) * D_(2, 16), 耗时: 5.997s
[2025-07-25 02:27:22] NW-SE对角算符进度: 4/64, 当前算符: D_(0, 14) * D_(3, 1), 耗时: 5.997s
[2025-07-25 02:27:28] NW-SE对角算符进度: 5/64, 当前算符: D_(0, 14) * D_(4, 2), 耗时: 5.979s
[2025-07-25 02:27:34] NW-SE对角算符进度: 6/64, 当前算符: D_(0, 14) * D_(5, 19), 耗时: 5.996s
[2025-07-25 02:27:40] NW-SE对角算符进度: 7/64, 当前算符: D_(0, 14) * D_(6, 20), 耗时: 5.955s
[2025-07-25 02:27:46] NW-SE对角算符进度: 8/64, 当前算符: D_(0, 14) * D_(7, 5), 耗时: 5.998s
[2025-07-25 02:27:52] NW-SE对角算符进度: 9/64, 当前算符: D_(0, 14) * D_(8, 6), 耗时: 5.980s
[2025-07-25 02:27:58] NW-SE对角算符进度: 10/64, 当前算符: D_(0, 14) * D_(9, 23), 耗时: 5.997s
[2025-07-25 02:28:04] NW-SE对角算符进度: 11/64, 当前算符: D_(0, 14) * D_(10, 24), 耗时: 5.980s
[2025-07-25 02:28:10] NW-SE对角算符进度: 12/64, 当前算符: D_(0, 14) * D_(11, 9), 耗时: 5.980s
[2025-07-25 02:28:16] NW-SE对角算符进度: 13/64, 当前算符: D_(0, 14) * D_(12, 10), 耗时: 5.998s
[2025-07-25 02:28:22] NW-SE对角算符进度: 14/64, 当前算符: D_(0, 14) * D_(13, 27), 耗时: 5.954s
[2025-07-25 02:28:35] NW-SE对角算符进度: 15/64, 当前算符: D_(0, 14) * D_(14, 28), 耗时: 13.195s
[2025-07-25 02:28:41] NW-SE对角算符进度: 16/64, 当前算符: D_(0, 14) * D_(15, 13), 耗时: 6.002s
[2025-07-25 02:28:47] NW-SE对角算符进度: 17/64, 当前算符: D_(0, 14) * D_(16, 30), 耗时: 5.998s
[2025-07-25 02:28:53] NW-SE对角算符进度: 18/64, 当前算符: D_(0, 14) * D_(17, 47), 耗时: 5.975s
[2025-07-25 02:28:59] NW-SE对角算符进度: 19/64, 当前算符: D_(0, 14) * D_(18, 32), 耗时: 6.003s
[2025-07-25 02:29:05] NW-SE对角算符进度: 20/64, 当前算符: D_(0, 14) * D_(19, 17), 耗时: 5.977s
[2025-07-25 02:29:11] NW-SE对角算符进度: 21/64, 当前算符: D_(0, 14) * D_(20, 18), 耗时: 6.002s
[2025-07-25 02:29:17] NW-SE对角算符进度: 22/64, 当前算符: D_(0, 14) * D_(21, 35), 耗时: 5.997s
[2025-07-25 02:29:23] NW-SE对角算符进度: 23/64, 当前算符: D_(0, 14) * D_(22, 36), 耗时: 5.998s
[2025-07-25 02:29:29] NW-SE对角算符进度: 24/64, 当前算符: D_(0, 14) * D_(23, 21), 耗时: 5.980s
[2025-07-25 02:29:35] NW-SE对角算符进度: 25/64, 当前算符: D_(0, 14) * D_(24, 22), 耗时: 5.999s
[2025-07-25 02:29:41] NW-SE对角算符进度: 26/64, 当前算符: D_(0, 14) * D_(25, 39), 耗时: 5.999s
[2025-07-25 02:29:47] NW-SE对角算符进度: 27/64, 当前算符: D_(0, 14) * D_(26, 40), 耗时: 6.000s
[2025-07-25 02:29:53] NW-SE对角算符进度: 28/64, 当前算符: D_(0, 14) * D_(27, 25), 耗时: 5.998s
[2025-07-25 02:29:59] NW-SE对角算符进度: 29/64, 当前算符: D_(0, 14) * D_(28, 26), 耗时: 6.003s
[2025-07-25 02:30:05] NW-SE对角算符进度: 30/64, 当前算符: D_(0, 14) * D_(29, 43), 耗时: 5.960s
[2025-07-25 02:30:11] NW-SE对角算符进度: 31/64, 当前算符: D_(0, 14) * D_(30, 44), 耗时: 5.998s
[2025-07-25 02:30:17] NW-SE对角算符进度: 32/64, 当前算符: D_(0, 14) * D_(31, 29), 耗时: 5.997s
[2025-07-25 02:30:23] NW-SE对角算符进度: 33/64, 当前算符: D_(0, 14) * D_(32, 46), 耗时: 6.002s
[2025-07-25 02:30:29] NW-SE对角算符进度: 34/64, 当前算符: D_(0, 14) * D_(33, 63), 耗时: 6.002s
[2025-07-25 02:30:35] NW-SE对角算符进度: 35/64, 当前算符: D_(0, 14) * D_(34, 48), 耗时: 5.999s
[2025-07-25 02:30:41] NW-SE对角算符进度: 36/64, 当前算符: D_(0, 14) * D_(35, 33), 耗时: 5.974s
[2025-07-25 02:30:47] NW-SE对角算符进度: 37/64, 当前算符: D_(0, 14) * D_(36, 34), 耗时: 5.998s
[2025-07-25 02:30:53] NW-SE对角算符进度: 38/64, 当前算符: D_(0, 14) * D_(37, 51), 耗时: 5.997s
[2025-07-25 02:30:59] NW-SE对角算符进度: 39/64, 当前算符: D_(0, 14) * D_(38, 52), 耗时: 6.003s
[2025-07-25 02:31:05] NW-SE对角算符进度: 40/64, 当前算符: D_(0, 14) * D_(39, 37), 耗时: 5.981s
[2025-07-25 02:31:11] NW-SE对角算符进度: 41/64, 当前算符: D_(0, 14) * D_(40, 38), 耗时: 5.997s
[2025-07-25 02:31:17] NW-SE对角算符进度: 42/64, 当前算符: D_(0, 14) * D_(41, 55), 耗时: 5.997s
[2025-07-25 02:31:23] NW-SE对角算符进度: 43/64, 当前算符: D_(0, 14) * D_(42, 56), 耗时: 6.003s
[2025-07-25 02:31:29] NW-SE对角算符进度: 44/64, 当前算符: D_(0, 14) * D_(43, 41), 耗时: 5.999s
[2025-07-25 02:31:35] NW-SE对角算符进度: 45/64, 当前算符: D_(0, 14) * D_(44, 42), 耗时: 5.997s
[2025-07-25 02:31:41] NW-SE对角算符进度: 46/64, 当前算符: D_(0, 14) * D_(45, 59), 耗时: 5.965s
[2025-07-25 02:31:47] NW-SE对角算符进度: 47/64, 当前算符: D_(0, 14) * D_(46, 60), 耗时: 5.999s
[2025-07-25 02:31:53] NW-SE对角算符进度: 48/64, 当前算符: D_(0, 14) * D_(47, 45), 耗时: 5.964s
[2025-07-25 02:31:59] NW-SE对角算符进度: 49/64, 当前算符: D_(0, 14) * D_(48, 62), 耗时: 5.996s
[2025-07-25 02:32:05] NW-SE对角算符进度: 50/64, 当前算符: D_(0, 14) * D_(49, 15), 耗时: 5.999s
[2025-07-25 02:32:09] NW-SE对角算符进度: 51/64, 当前算符: D_(0, 14) * D_(50, 0), 耗时: 4.694s
[2025-07-25 02:32:15] NW-SE对角算符进度: 52/64, 当前算符: D_(0, 14) * D_(51, 49), 耗时: 5.998s
[2025-07-25 02:32:21] NW-SE对角算符进度: 53/64, 当前算符: D_(0, 14) * D_(52, 50), 耗时: 6.000s
[2025-07-25 02:32:27] NW-SE对角算符进度: 54/64, 当前算符: D_(0, 14) * D_(53, 3), 耗时: 5.977s
[2025-07-25 02:32:33] NW-SE对角算符进度: 55/64, 当前算符: D_(0, 14) * D_(54, 4), 耗时: 6.003s
[2025-07-25 02:32:39] NW-SE对角算符进度: 56/64, 当前算符: D_(0, 14) * D_(55, 53), 耗时: 5.997s
[2025-07-25 02:32:45] NW-SE对角算符进度: 57/64, 当前算符: D_(0, 14) * D_(56, 54), 耗时: 6.001s
[2025-07-25 02:32:51] NW-SE对角算符进度: 58/64, 当前算符: D_(0, 14) * D_(57, 7), 耗时: 5.977s
[2025-07-25 02:32:57] NW-SE对角算符进度: 59/64, 当前算符: D_(0, 14) * D_(58, 8), 耗时: 6.003s
[2025-07-25 02:33:03] NW-SE对角算符进度: 60/64, 当前算符: D_(0, 14) * D_(59, 57), 耗时: 5.959s
[2025-07-25 02:33:09] NW-SE对角算符进度: 61/64, 当前算符: D_(0, 14) * D_(60, 58), 耗时: 5.999s
[2025-07-25 02:33:15] NW-SE对角算符进度: 62/64, 当前算符: D_(0, 14) * D_(61, 11), 耗时: 5.999s
[2025-07-25 02:33:21] NW-SE对角算符进度: 63/64, 当前算符: D_(0, 14) * D_(62, 12), 耗时: 5.977s
[2025-07-25 02:33:27] NW-SE对角算符进度: 64/64, 当前算符: D_(0, 14) * D_(63, 61), 耗时: 5.999s
[2025-07-25 02:33:27] 西北-东南方向对角二聚体相关函数计算完成,耗时: 392.02 秒
[2025-07-25 02:33:27] ================================================================================
[2025-07-25 02:33:27] 开始计算西南-东北方向对角二聚体相关函数 (64 个算符)...
[2025-07-25 02:33:31] SW-NE对角算符进度: 1/64, 当前算符: D_(0, 2) * D_(0, 2), 耗时: 3.579s
[2025-07-25 02:33:37] SW-NE对角算符进度: 2/64, 当前算符: D_(0, 2) * D_(1, 19), 耗时: 5.997s
[2025-07-25 02:33:42] SW-NE对角算符进度: 3/64, 当前算符: D_(0, 2) * D_(2, 20), 耗时: 4.696s
[2025-07-25 02:33:48] SW-NE对角算符进度: 4/64, 当前算符: D_(0, 2) * D_(3, 5), 耗时: 6.001s
[2025-07-25 02:33:54] SW-NE对角算符进度: 5/64, 当前算符: D_(0, 2) * D_(4, 6), 耗时: 6.001s
[2025-07-25 02:34:00] SW-NE对角算符进度: 6/64, 当前算符: D_(0, 2) * D_(5, 23), 耗时: 5.973s
[2025-07-25 02:34:06] SW-NE对角算符进度: 7/64, 当前算符: D_(0, 2) * D_(6, 24), 耗时: 5.998s
[2025-07-25 02:34:12] SW-NE对角算符进度: 8/64, 当前算符: D_(0, 2) * D_(7, 9), 耗时: 5.999s
[2025-07-25 02:34:18] SW-NE对角算符进度: 9/64, 当前算符: D_(0, 2) * D_(8, 10), 耗时: 5.997s
[2025-07-25 02:34:24] SW-NE对角算符进度: 10/64, 当前算符: D_(0, 2) * D_(9, 27), 耗时: 5.997s
[2025-07-25 02:34:30] SW-NE对角算符进度: 11/64, 当前算符: D_(0, 2) * D_(10, 28), 耗时: 5.975s
[2025-07-25 02:34:36] SW-NE对角算符进度: 12/64, 当前算符: D_(0, 2) * D_(11, 13), 耗时: 5.997s
[2025-07-25 02:34:42] SW-NE对角算符进度: 13/64, 当前算符: D_(0, 2) * D_(12, 14), 耗时: 5.998s
[2025-07-25 02:34:48] SW-NE对角算符进度: 14/64, 当前算符: D_(0, 2) * D_(13, 31), 耗时: 6.002s
[2025-07-25 02:34:54] SW-NE对角算符进度: 15/64, 当前算符: D_(0, 2) * D_(14, 16), 耗时: 5.976s
[2025-07-25 02:35:00] SW-NE对角算符进度: 16/64, 当前算符: D_(0, 2) * D_(15, 1), 耗时: 6.001s
[2025-07-25 02:35:06] SW-NE对角算符进度: 17/64, 当前算符: D_(0, 2) * D_(16, 18), 耗时: 5.974s
[2025-07-25 02:35:12] SW-NE对角算符进度: 18/64, 当前算符: D_(0, 2) * D_(17, 35), 耗时: 5.999s
[2025-07-25 02:35:18] SW-NE对角算符进度: 19/64, 当前算符: D_(0, 2) * D_(18, 36), 耗时: 5.998s
[2025-07-25 02:35:24] SW-NE对角算符进度: 20/64, 当前算符: D_(0, 2) * D_(19, 21), 耗时: 5.997s
[2025-07-25 02:35:30] SW-NE对角算符进度: 21/64, 当前算符: D_(0, 2) * D_(20, 22), 耗时: 5.957s
[2025-07-25 02:35:35] SW-NE对角算符进度: 22/64, 当前算符: D_(0, 2) * D_(21, 39), 耗时: 5.976s
[2025-07-25 02:35:41] SW-NE对角算符进度: 23/64, 当前算符: D_(0, 2) * D_(22, 40), 耗时: 5.998s
[2025-07-25 02:35:47] SW-NE对角算符进度: 24/64, 当前算符: D_(0, 2) * D_(23, 25), 耗时: 5.997s
[2025-07-25 02:35:53] SW-NE对角算符进度: 25/64, 当前算符: D_(0, 2) * D_(24, 26), 耗时: 5.997s
[2025-07-25 02:35:59] SW-NE对角算符进度: 26/64, 当前算符: D_(0, 2) * D_(25, 43), 耗时: 6.000s
[2025-07-25 02:36:05] SW-NE对角算符进度: 27/64, 当前算符: D_(0, 2) * D_(26, 44), 耗时: 6.002s
[2025-07-25 02:36:11] SW-NE对角算符进度: 28/64, 当前算符: D_(0, 2) * D_(27, 29), 耗时: 6.002s
[2025-07-25 02:36:17] SW-NE对角算符进度: 29/64, 当前算符: D_(0, 2) * D_(28, 30), 耗时: 5.982s
[2025-07-25 02:36:23] SW-NE对角算符进度: 30/64, 当前算符: D_(0, 2) * D_(29, 47), 耗时: 5.999s
[2025-07-25 02:36:29] SW-NE对角算符进度: 31/64, 当前算符: D_(0, 2) * D_(30, 32), 耗时: 5.999s
[2025-07-25 02:36:35] SW-NE对角算符进度: 32/64, 当前算符: D_(0, 2) * D_(31, 17), 耗时: 5.997s
[2025-07-25 02:36:41] SW-NE对角算符进度: 33/64, 当前算符: D_(0, 2) * D_(32, 34), 耗时: 5.999s
[2025-07-25 02:36:47] SW-NE对角算符进度: 34/64, 当前算符: D_(0, 2) * D_(33, 51), 耗时: 6.003s
[2025-07-25 02:36:53] SW-NE对角算符进度: 35/64, 当前算符: D_(0, 2) * D_(34, 52), 耗时: 5.976s
[2025-07-25 02:36:59] SW-NE对角算符进度: 36/64, 当前算符: D_(0, 2) * D_(35, 37), 耗时: 5.999s
[2025-07-25 02:37:05] SW-NE对角算符进度: 37/64, 当前算符: D_(0, 2) * D_(36, 38), 耗时: 5.999s
[2025-07-25 02:37:11] SW-NE对角算符进度: 38/64, 当前算符: D_(0, 2) * D_(37, 55), 耗时: 6.000s
[2025-07-25 02:37:17] SW-NE对角算符进度: 39/64, 当前算符: D_(0, 2) * D_(38, 56), 耗时: 5.997s
[2025-07-25 02:37:23] SW-NE对角算符进度: 40/64, 当前算符: D_(0, 2) * D_(39, 41), 耗时: 5.977s
[2025-07-25 02:37:29] SW-NE对角算符进度: 41/64, 当前算符: D_(0, 2) * D_(40, 42), 耗时: 5.958s
[2025-07-25 02:37:35] SW-NE对角算符进度: 42/64, 当前算符: D_(0, 2) * D_(41, 59), 耗时: 6.000s
[2025-07-25 02:37:41] SW-NE对角算符进度: 43/64, 当前算符: D_(0, 2) * D_(42, 60), 耗时: 6.001s
[2025-07-25 02:37:47] SW-NE对角算符进度: 44/64, 当前算符: D_(0, 2) * D_(43, 45), 耗时: 6.000s
[2025-07-25 02:37:53] SW-NE对角算符进度: 45/64, 当前算符: D_(0, 2) * D_(44, 46), 耗时: 5.975s
[2025-07-25 02:37:59] SW-NE对角算符进度: 46/64, 当前算符: D_(0, 2) * D_(45, 63), 耗时: 5.996s
[2025-07-25 02:38:05] SW-NE对角算符进度: 47/64, 当前算符: D_(0, 2) * D_(46, 48), 耗时: 6.000s
[2025-07-25 02:38:11] SW-NE对角算符进度: 48/64, 当前算符: D_(0, 2) * D_(47, 33), 耗时: 5.997s
[2025-07-25 02:38:17] SW-NE对角算符进度: 49/64, 当前算符: D_(0, 2) * D_(48, 50), 耗时: 5.996s
[2025-07-25 02:38:23] SW-NE对角算符进度: 50/64, 当前算符: D_(0, 2) * D_(49, 3), 耗时: 5.999s
[2025-07-25 02:38:29] SW-NE对角算符进度: 51/64, 当前算符: D_(0, 2) * D_(50, 4), 耗时: 5.972s
[2025-07-25 02:38:35] SW-NE对角算符进度: 52/64, 当前算符: D_(0, 2) * D_(51, 53), 耗时: 5.998s
[2025-07-25 02:38:41] SW-NE对角算符进度: 53/64, 当前算符: D_(0, 2) * D_(52, 54), 耗时: 5.997s
[2025-07-25 02:38:47] SW-NE对角算符进度: 54/64, 当前算符: D_(0, 2) * D_(53, 7), 耗时: 5.999s
[2025-07-25 02:38:53] SW-NE对角算符进度: 55/64, 当前算符: D_(0, 2) * D_(54, 8), 耗时: 5.977s
[2025-07-25 02:38:59] SW-NE对角算符进度: 56/64, 当前算符: D_(0, 2) * D_(55, 57), 耗时: 5.967s
[2025-07-25 02:39:05] SW-NE对角算符进度: 57/64, 当前算符: D_(0, 2) * D_(56, 58), 耗时: 5.967s
[2025-07-25 02:39:11] SW-NE对角算符进度: 58/64, 当前算符: D_(0, 2) * D_(57, 11), 耗时: 5.999s
[2025-07-25 02:39:17] SW-NE对角算符进度: 59/64, 当前算符: D_(0, 2) * D_(58, 12), 耗时: 5.998s
[2025-07-25 02:39:23] SW-NE对角算符进度: 60/64, 当前算符: D_(0, 2) * D_(59, 61), 耗时: 5.999s
[2025-07-25 02:39:29] SW-NE对角算符进度: 61/64, 当前算符: D_(0, 2) * D_(60, 62), 耗时: 5.970s
[2025-07-25 02:39:35] SW-NE对角算符进度: 62/64, 当前算符: D_(0, 2) * D_(61, 15), 耗时: 5.999s
[2025-07-25 02:39:40] SW-NE对角算符进度: 63/64, 当前算符: D_(0, 2) * D_(62, 0), 耗时: 4.694s
[2025-07-25 02:39:46] SW-NE对角算符进度: 64/64, 当前算符: D_(0, 2) * D_(63, 49), 耗时: 5.998s
[2025-07-25 02:39:46] 西南-东北方向对角二聚体相关函数计算完成,耗时: 378.54 秒
[2025-07-25 02:39:46] 计算傅里叶变换...
[2025-07-25 02:39:47] 对角二聚体结构因子计算完成
[2025-07-25 02:39:47] 对角二聚体相关函数平均误差: 0.000116
