[2025-07-25 02:40:01] ================================================================================
[2025-07-25 02:40:01] 加载量子态: L=4, J2=1.00, J1=0.82
[2025-07-25 02:40:01] 设置样本数为: 1048576
[2025-07-25 02:40:01] 开始生成共享样本集...
[2025-07-25 02:41:11] 样本生成完成,耗时: 69.918 秒
[2025-07-25 02:41:11] ================================================================================
[2025-07-25 02:41:11] 开始计算自旋结构因子...
[2025-07-25 02:41:11] 初始化操作符缓存...
[2025-07-25 02:41:11] 预构建所有自旋相关操作符...
[2025-07-25 02:41:11] 开始计算自旋相关函数...
[2025-07-25 02:41:19] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 7.734s
[2025-07-25 02:41:27] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 8.393s
[2025-07-25 02:41:31] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 3.602s
[2025-07-25 02:41:35] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 3.598s
[2025-07-25 02:41:38] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 3.602s
[2025-07-25 02:41:42] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 3.594s
[2025-07-25 02:41:45] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 3.603s
[2025-07-25 02:41:49] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 3.598s
[2025-07-25 02:41:53] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 3.653s
[2025-07-25 02:41:56] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 3.601s
[2025-07-25 02:42:00] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 3.600s
[2025-07-25 02:42:04] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 3.597s
[2025-07-25 02:42:07] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 3.603s
[2025-07-25 02:42:11] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 3.586s
[2025-07-25 02:42:14] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 3.599s
[2025-07-25 02:42:18] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 3.601s
[2025-07-25 02:42:22] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 3.603s
[2025-07-25 02:42:25] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 3.586s
[2025-07-25 02:42:29] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 3.602s
[2025-07-25 02:42:32] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 3.598s
[2025-07-25 02:42:36] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 3.603s
[2025-07-25 02:42:40] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 3.602s
[2025-07-25 02:42:43] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 3.600s
[2025-07-25 02:42:47] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 3.586s
[2025-07-25 02:42:50] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 3.601s
[2025-07-25 02:42:54] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 3.604s
[2025-07-25 02:42:58] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 3.601s
[2025-07-25 02:43:01] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 3.599s
[2025-07-25 02:43:05] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 3.585s
[2025-07-25 02:43:08] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 3.587s
[2025-07-25 02:43:12] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 3.598s
[2025-07-25 02:43:15] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 3.596s
[2025-07-25 02:43:19] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 3.604s
[2025-07-25 02:43:23] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 3.630s
[2025-07-25 02:43:26] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 3.587s
[2025-07-25 02:43:30] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 3.603s
[2025-07-25 02:43:34] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 3.599s
[2025-07-25 02:43:37] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 3.603s
[2025-07-25 02:43:41] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 3.598s
[2025-07-25 02:43:44] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 3.586s
[2025-07-25 02:43:48] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 3.603s
[2025-07-25 02:43:51] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 3.600s
[2025-07-25 02:43:55] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 3.598s
[2025-07-25 02:43:59] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 3.594s
[2025-07-25 02:44:02] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 3.598s
[2025-07-25 02:44:06] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 3.596s
[2025-07-25 02:44:09] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 3.584s
[2025-07-25 02:44:13] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 3.599s
[2025-07-25 02:44:17] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 3.586s
[2025-07-25 02:44:20] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 3.598s
[2025-07-25 02:44:24] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 3.602s
[2025-07-25 02:44:27] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 3.601s
[2025-07-25 02:44:31] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 3.600s
[2025-07-25 02:44:35] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 3.595s
[2025-07-25 02:44:38] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 3.586s
[2025-07-25 02:44:42] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 3.600s
[2025-07-25 02:44:45] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 3.599s
[2025-07-25 02:44:49] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 3.587s
[2025-07-25 02:44:53] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 3.676s
[2025-07-25 02:44:56] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 3.589s
[2025-07-25 02:45:00] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 3.604s
[2025-07-25 02:45:04] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 3.599s
[2025-07-25 02:45:07] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 3.603s
[2025-07-25 02:45:11] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 3.586s
[2025-07-25 02:45:11] 自旋相关函数计算完成,总耗时 239.37 秒
[2025-07-25 02:45:11] 计算傅里叶变换...
[2025-07-25 02:45:11] 自旋结构因子计算完成
[2025-07-25 02:45:12] 自旋相关函数平均误差: 0.000524
[2025-07-25 02:45:12] ================================================================================
[2025-07-25 02:45:12] 开始计算对角二聚体结构因子...
[2025-07-25 02:45:12] 识别所有对角二聚体...
[2025-07-25 02:45:12] 总共找到 64 个西北-东南方向对角二聚体和 64 个西南-东北方向对角二聚体
[2025-07-25 02:45:12] 预计算对角二聚体操作符...
[2025-07-25 02:45:14] 开始计算西北-东南方向对角二聚体相关函数 (64 个算符)...
[2025-07-25 02:45:18] NW-SE对角算符进度: 1/64, 当前算符: D_(0, 14) * D_(0, 14), 耗时: 3.638s
[2025-07-25 02:45:29] NW-SE对角算符进度: 2/64, 当前算符: D_(0, 14) * D_(1, 31), 耗时: 10.963s
[2025-07-25 02:45:35] NW-SE对角算符进度: 3/64, 当前算符: D_(0, 14) * D_(2, 16), 耗时: 6.011s
[2025-07-25 02:45:41] NW-SE对角算符进度: 4/64, 当前算符: D_(0, 14) * D_(3, 1), 耗时: 6.008s
[2025-07-25 02:45:47] NW-SE对角算符进度: 5/64, 当前算符: D_(0, 14) * D_(4, 2), 耗时: 6.008s
[2025-07-25 02:45:53] NW-SE对角算符进度: 6/64, 当前算符: D_(0, 14) * D_(5, 19), 耗时: 6.018s
[2025-07-25 02:45:59] NW-SE对角算符进度: 7/64, 当前算符: D_(0, 14) * D_(6, 20), 耗时: 5.985s
[2025-07-25 02:46:05] NW-SE对角算符进度: 8/64, 当前算符: D_(0, 14) * D_(7, 5), 耗时: 6.014s
[2025-07-25 02:46:11] NW-SE对角算符进度: 9/64, 当前算符: D_(0, 14) * D_(8, 6), 耗时: 6.018s
[2025-07-25 02:46:17] NW-SE对角算符进度: 10/64, 当前算符: D_(0, 14) * D_(9, 23), 耗时: 6.010s
[2025-07-25 02:46:23] NW-SE对角算符进度: 11/64, 当前算符: D_(0, 14) * D_(10, 24), 耗时: 6.019s
[2025-07-25 02:46:29] NW-SE对角算符进度: 12/64, 当前算符: D_(0, 14) * D_(11, 9), 耗时: 6.022s
[2025-07-25 02:46:35] NW-SE对角算符进度: 13/64, 当前算符: D_(0, 14) * D_(12, 10), 耗时: 6.010s
[2025-07-25 02:46:41] NW-SE对角算符进度: 14/64, 当前算符: D_(0, 14) * D_(13, 27), 耗时: 5.989s
[2025-07-25 02:46:54] NW-SE对角算符进度: 15/64, 当前算符: D_(0, 14) * D_(14, 28), 耗时: 13.148s
[2025-07-25 02:47:00] NW-SE对角算符进度: 16/64, 当前算符: D_(0, 14) * D_(15, 13), 耗时: 6.043s
[2025-07-25 02:47:06] NW-SE对角算符进度: 17/64, 当前算符: D_(0, 14) * D_(16, 30), 耗时: 6.029s
[2025-07-25 02:47:12] NW-SE对角算符进度: 18/64, 当前算符: D_(0, 14) * D_(17, 47), 耗时: 6.006s
[2025-07-25 02:47:18] NW-SE对角算符进度: 19/64, 当前算符: D_(0, 14) * D_(18, 32), 耗时: 6.025s
[2025-07-25 02:47:24] NW-SE对角算符进度: 20/64, 当前算符: D_(0, 14) * D_(19, 17), 耗时: 6.006s
[2025-07-25 02:47:30] NW-SE对角算符进度: 21/64, 当前算符: D_(0, 14) * D_(20, 18), 耗时: 6.034s
[2025-07-25 02:47:36] NW-SE对角算符进度: 22/64, 当前算符: D_(0, 14) * D_(21, 35), 耗时: 6.041s
[2025-07-25 02:47:42] NW-SE对角算符进度: 23/64, 当前算符: D_(0, 14) * D_(22, 36), 耗时: 6.013s
[2025-07-25 02:47:48] NW-SE对角算符进度: 24/64, 当前算符: D_(0, 14) * D_(23, 21), 耗时: 6.005s
[2025-07-25 02:47:54] NW-SE对角算符进度: 25/64, 当前算符: D_(0, 14) * D_(24, 22), 耗时: 6.051s
[2025-07-25 02:48:00] NW-SE对角算符进度: 26/64, 当前算符: D_(0, 14) * D_(25, 39), 耗时: 6.034s
[2025-07-25 02:48:06] NW-SE对角算符进度: 27/64, 当前算符: D_(0, 14) * D_(26, 40), 耗时: 6.038s
[2025-07-25 02:48:12] NW-SE对角算符进度: 28/64, 当前算符: D_(0, 14) * D_(27, 25), 耗时: 6.033s
[2025-07-25 02:48:18] NW-SE对角算符进度: 29/64, 当前算符: D_(0, 14) * D_(28, 26), 耗时: 6.023s
[2025-07-25 02:48:24] NW-SE对角算符进度: 30/64, 当前算符: D_(0, 14) * D_(29, 43), 耗时: 6.003s
[2025-07-25 02:48:30] NW-SE对角算符进度: 31/64, 当前算符: D_(0, 14) * D_(30, 44), 耗时: 6.048s
[2025-07-25 02:48:36] NW-SE对角算符进度: 32/64, 当前算符: D_(0, 14) * D_(31, 29), 耗时: 6.043s
[2025-07-25 02:48:42] NW-SE对角算符进度: 33/64, 当前算符: D_(0, 14) * D_(32, 46), 耗时: 6.035s
[2025-07-25 02:48:48] NW-SE对角算符进度: 34/64, 当前算符: D_(0, 14) * D_(33, 63), 耗时: 6.023s
[2025-07-25 02:48:55] NW-SE对角算符进度: 35/64, 当前算符: D_(0, 14) * D_(34, 48), 耗时: 6.019s
[2025-07-25 02:49:01] NW-SE对角算符进度: 36/64, 当前算符: D_(0, 14) * D_(35, 33), 耗时: 6.005s
[2025-07-25 02:49:07] NW-SE对角算符进度: 37/64, 当前算符: D_(0, 14) * D_(36, 34), 耗时: 6.039s
[2025-07-25 02:49:13] NW-SE对角算符进度: 38/64, 当前算符: D_(0, 14) * D_(37, 51), 耗时: 6.045s
[2025-07-25 02:49:19] NW-SE对角算符进度: 39/64, 当前算符: D_(0, 14) * D_(38, 52), 耗时: 6.023s
[2025-07-25 02:49:25] NW-SE对角算符进度: 40/64, 当前算符: D_(0, 14) * D_(39, 37), 耗时: 6.009s
[2025-07-25 02:49:31] NW-SE对角算符进度: 41/64, 当前算符: D_(0, 14) * D_(40, 38), 耗时: 6.015s
[2025-07-25 02:49:37] NW-SE对角算符进度: 42/64, 当前算符: D_(0, 14) * D_(41, 55), 耗时: 6.047s
[2025-07-25 02:49:43] NW-SE对角算符进度: 43/64, 当前算符: D_(0, 14) * D_(42, 56), 耗时: 6.036s
[2025-07-25 02:49:49] NW-SE对角算符进度: 44/64, 当前算符: D_(0, 14) * D_(43, 41), 耗时: 6.041s
[2025-07-25 02:49:55] NW-SE对角算符进度: 45/64, 当前算符: D_(0, 14) * D_(44, 42), 耗时: 6.017s
[2025-07-25 02:50:01] NW-SE对角算符进度: 46/64, 当前算符: D_(0, 14) * D_(45, 59), 耗时: 6.010s
[2025-07-25 02:50:07] NW-SE对角算符进度: 47/64, 当前算符: D_(0, 14) * D_(46, 60), 耗时: 6.019s
[2025-07-25 02:50:13] NW-SE对角算符进度: 48/64, 当前算符: D_(0, 14) * D_(47, 45), 耗时: 6.009s
[2025-07-25 02:50:19] NW-SE对角算符进度: 49/64, 当前算符: D_(0, 14) * D_(48, 62), 耗时: 6.010s
[2025-07-25 02:50:25] NW-SE对角算符进度: 50/64, 当前算符: D_(0, 14) * D_(49, 15), 耗时: 6.027s
[2025-07-25 02:50:30] NW-SE对角算符进度: 51/64, 当前算符: D_(0, 14) * D_(50, 0), 耗时: 4.731s
[2025-07-25 02:50:36] NW-SE对角算符进度: 52/64, 当前算符: D_(0, 14) * D_(51, 49), 耗时: 6.043s
[2025-07-25 02:50:42] NW-SE对角算符进度: 53/64, 当前算符: D_(0, 14) * D_(52, 50), 耗时: 6.019s
[2025-07-25 02:50:48] NW-SE对角算符进度: 54/64, 当前算符: D_(0, 14) * D_(53, 3), 耗时: 6.005s
[2025-07-25 02:50:54] NW-SE对角算符进度: 55/64, 当前算符: D_(0, 14) * D_(54, 4), 耗时: 6.041s
[2025-07-25 02:51:00] NW-SE对角算符进度: 56/64, 当前算符: D_(0, 14) * D_(55, 53), 耗时: 6.048s
[2025-07-25 02:51:06] NW-SE对角算符进度: 57/64, 当前算符: D_(0, 14) * D_(56, 54), 耗时: 6.046s
[2025-07-25 02:51:12] NW-SE对角算符进度: 58/64, 当前算符: D_(0, 14) * D_(57, 7), 耗时: 6.013s
[2025-07-25 02:51:18] NW-SE对角算符进度: 59/64, 当前算符: D_(0, 14) * D_(58, 8), 耗时: 6.024s
[2025-07-25 02:51:24] NW-SE对角算符进度: 60/64, 当前算符: D_(0, 14) * D_(59, 57), 耗时: 6.006s
[2025-07-25 02:51:30] NW-SE对角算符进度: 61/64, 当前算符: D_(0, 14) * D_(60, 58), 耗时: 6.042s
[2025-07-25 02:51:36] NW-SE对角算符进度: 62/64, 当前算符: D_(0, 14) * D_(61, 11), 耗时: 6.039s
[2025-07-25 02:51:42] NW-SE对角算符进度: 63/64, 当前算符: D_(0, 14) * D_(62, 12), 耗时: 6.014s
[2025-07-25 02:51:48] NW-SE对角算符进度: 64/64, 当前算符: D_(0, 14) * D_(63, 61), 耗时: 6.020s
[2025-07-25 02:51:48] 西北-东南方向对角二聚体相关函数计算完成,耗时: 393.93 秒
[2025-07-25 02:51:48] ================================================================================
[2025-07-25 02:51:48] 开始计算西南-东北方向对角二聚体相关函数 (64 个算符)...
[2025-07-25 02:51:52] SW-NE对角算符进度: 1/64, 当前算符: D_(0, 2) * D_(0, 2), 耗时: 3.602s
[2025-07-25 02:51:58] SW-NE对角算符进度: 2/64, 当前算符: D_(0, 2) * D_(1, 19), 耗时: 6.045s
[2025-07-25 02:52:02] SW-NE对角算符进度: 3/64, 当前算符: D_(0, 2) * D_(2, 20), 耗时: 4.720s
[2025-07-25 02:52:08] SW-NE对角算符进度: 4/64, 当前算符: D_(0, 2) * D_(3, 5), 耗时: 6.041s
[2025-07-25 02:52:14] SW-NE对角算符进度: 5/64, 当前算符: D_(0, 2) * D_(4, 6), 耗时: 6.023s
[2025-07-25 02:52:20] SW-NE对角算符进度: 6/64, 当前算符: D_(0, 2) * D_(5, 23), 耗时: 6.008s
[2025-07-25 02:52:26] SW-NE对角算符进度: 7/64, 当前算符: D_(0, 2) * D_(6, 24), 耗时: 6.040s
[2025-07-25 02:52:33] SW-NE对角算符进度: 8/64, 当前算符: D_(0, 2) * D_(7, 9), 耗时: 6.049s
[2025-07-25 02:52:39] SW-NE对角算符进度: 9/64, 当前算符: D_(0, 2) * D_(8, 10), 耗时: 6.048s
[2025-07-25 02:52:45] SW-NE对角算符进度: 10/64, 当前算符: D_(0, 2) * D_(9, 27), 耗时: 6.015s
[2025-07-25 02:52:51] SW-NE对角算符进度: 11/64, 当前算符: D_(0, 2) * D_(10, 28), 耗时: 6.009s
[2025-07-25 02:52:57] SW-NE对角算符进度: 12/64, 当前算符: D_(0, 2) * D_(11, 13), 耗时: 6.016s
[2025-07-25 02:53:03] SW-NE对角算符进度: 13/64, 当前算符: D_(0, 2) * D_(12, 14), 耗时: 6.047s
[2025-07-25 02:53:09] SW-NE对角算符进度: 14/64, 当前算符: D_(0, 2) * D_(13, 31), 耗时: 6.045s
[2025-07-25 02:53:15] SW-NE对角算符进度: 15/64, 当前算符: D_(0, 2) * D_(14, 16), 耗时: 6.014s
[2025-07-25 02:53:21] SW-NE对角算符进度: 16/64, 当前算符: D_(0, 2) * D_(15, 1), 耗时: 6.023s
[2025-07-25 02:53:27] SW-NE对角算符进度: 17/64, 当前算符: D_(0, 2) * D_(16, 18), 耗时: 6.013s
[2025-07-25 02:53:33] SW-NE对角算符进度: 18/64, 当前算符: D_(0, 2) * D_(17, 35), 耗时: 6.044s
[2025-07-25 02:53:39] SW-NE对角算符进度: 19/64, 当前算符: D_(0, 2) * D_(18, 36), 耗时: 6.036s
[2025-07-25 02:53:45] SW-NE对角算符进度: 20/64, 当前算符: D_(0, 2) * D_(19, 21), 耗时: 6.044s
[2025-07-25 02:53:51] SW-NE对角算符进度: 21/64, 当前算符: D_(0, 2) * D_(20, 22), 耗时: 6.000s
[2025-07-25 02:53:57] SW-NE对角算符进度: 22/64, 当前算符: D_(0, 2) * D_(21, 39), 耗时: 6.010s
[2025-07-25 02:54:03] SW-NE对角算符进度: 23/64, 当前算符: D_(0, 2) * D_(22, 40), 耗时: 6.031s
[2025-07-25 02:54:09] SW-NE对角算符进度: 24/64, 当前算符: D_(0, 2) * D_(23, 25), 耗时: 6.046s
[2025-07-25 02:54:15] SW-NE对角算符进度: 25/64, 当前算符: D_(0, 2) * D_(24, 26), 耗时: 6.039s
[2025-07-25 02:54:21] SW-NE对角算符进度: 26/64, 当前算符: D_(0, 2) * D_(25, 43), 耗时: 6.039s
[2025-07-25 02:54:27] SW-NE对角算符进度: 27/64, 当前算符: D_(0, 2) * D_(26, 44), 耗时: 6.040s
[2025-07-25 02:54:33] SW-NE对角算符进度: 28/64, 当前算符: D_(0, 2) * D_(27, 29), 耗时: 6.041s
[2025-07-25 02:54:39] SW-NE对角算符进度: 29/64, 当前算符: D_(0, 2) * D_(28, 30), 耗时: 6.008s
[2025-07-25 02:54:45] SW-NE对角算符进度: 30/64, 当前算符: D_(0, 2) * D_(29, 47), 耗时: 6.020s
[2025-07-25 02:54:51] SW-NE对角算符进度: 31/64, 当前算符: D_(0, 2) * D_(30, 32), 耗时: 6.048s
[2025-07-25 02:54:57] SW-NE对角算符进度: 32/64, 当前算符: D_(0, 2) * D_(31, 17), 耗时: 6.047s
[2025-07-25 02:55:03] SW-NE对角算符进度: 33/64, 当前算符: D_(0, 2) * D_(32, 34), 耗时: 6.045s
[2025-07-25 02:55:09] SW-NE对角算符进度: 34/64, 当前算符: D_(0, 2) * D_(33, 51), 耗时: 6.028s
[2025-07-25 02:55:15] SW-NE对角算符进度: 35/64, 当前算符: D_(0, 2) * D_(34, 52), 耗时: 6.011s
[2025-07-25 02:55:21] SW-NE对角算符进度: 36/64, 当前算符: D_(0, 2) * D_(35, 37), 耗时: 6.019s
[2025-07-25 02:55:27] SW-NE对角算符进度: 37/64, 当前算符: D_(0, 2) * D_(36, 38), 耗时: 6.040s
[2025-07-25 02:55:33] SW-NE对角算符进度: 38/64, 当前算符: D_(0, 2) * D_(37, 55), 耗时: 6.035s
[2025-07-25 02:55:39] SW-NE对角算符进度: 39/64, 当前算符: D_(0, 2) * D_(38, 56), 耗时: 6.016s
[2025-07-25 02:55:45] SW-NE对角算符进度: 40/64, 当前算符: D_(0, 2) * D_(39, 41), 耗时: 6.005s
[2025-07-25 02:55:51] SW-NE对角算符进度: 41/64, 当前算符: D_(0, 2) * D_(40, 42), 耗时: 5.998s
[2025-07-25 02:55:58] SW-NE对角算符进度: 42/64, 当前算符: D_(0, 2) * D_(41, 59), 耗时: 6.040s
[2025-07-25 02:56:04] SW-NE对角算符进度: 43/64, 当前算符: D_(0, 2) * D_(42, 60), 耗时: 6.026s
[2025-07-25 02:56:10] SW-NE对角算符进度: 44/64, 当前算符: D_(0, 2) * D_(43, 45), 耗时: 6.039s
[2025-07-25 02:56:16] SW-NE对角算符进度: 45/64, 当前算符: D_(0, 2) * D_(44, 46), 耗时: 6.010s
[2025-07-25 02:56:22] SW-NE对角算符进度: 46/64, 当前算符: D_(0, 2) * D_(45, 63), 耗时: 6.012s
[2025-07-25 02:56:28] SW-NE对角算符进度: 47/64, 当前算符: D_(0, 2) * D_(46, 48), 耗时: 6.039s
[2025-07-25 02:56:34] SW-NE对角算符进度: 48/64, 当前算符: D_(0, 2) * D_(47, 33), 耗时: 6.045s
[2025-07-25 02:56:40] SW-NE对角算符进度: 49/64, 当前算符: D_(0, 2) * D_(48, 50), 耗时: 6.045s
[2025-07-25 02:56:46] SW-NE对角算符进度: 50/64, 当前算符: D_(0, 2) * D_(49, 3), 耗时: 6.019s
[2025-07-25 02:56:52] SW-NE对角算符进度: 51/64, 当前算符: D_(0, 2) * D_(50, 4), 耗时: 6.006s
[2025-07-25 02:56:58] SW-NE对角算符进度: 52/64, 当前算符: D_(0, 2) * D_(51, 53), 耗时: 6.019s
[2025-07-25 02:57:04] SW-NE对角算符进度: 53/64, 当前算符: D_(0, 2) * D_(52, 54), 耗时: 6.038s
[2025-07-25 02:57:10] SW-NE对角算符进度: 54/64, 当前算符: D_(0, 2) * D_(53, 7), 耗时: 6.029s
[2025-07-25 02:57:16] SW-NE对角算符进度: 55/64, 当前算符: D_(0, 2) * D_(54, 8), 耗时: 6.006s
[2025-07-25 02:57:22] SW-NE对角算符进度: 56/64, 当前算符: D_(0, 2) * D_(55, 57), 耗时: 6.001s
[2025-07-25 02:57:28] SW-NE对角算符进度: 57/64, 当前算符: D_(0, 2) * D_(56, 58), 耗时: 6.002s
[2025-07-25 02:57:34] SW-NE对角算符进度: 58/64, 当前算符: D_(0, 2) * D_(57, 11), 耗时: 6.041s
[2025-07-25 02:57:40] SW-NE对角算符进度: 59/64, 当前算符: D_(0, 2) * D_(58, 12), 耗时: 6.029s
[2025-07-25 02:57:46] SW-NE对角算符进度: 60/64, 当前算符: D_(0, 2) * D_(59, 61), 耗时: 6.039s
[2025-07-25 02:57:52] SW-NE对角算符进度: 61/64, 当前算符: D_(0, 2) * D_(60, 62), 耗时: 6.006s
[2025-07-25 02:57:58] SW-NE对角算符进度: 62/64, 当前算符: D_(0, 2) * D_(61, 15), 耗时: 6.020s
[2025-07-25 02:58:03] SW-NE对角算符进度: 63/64, 当前算符: D_(0, 2) * D_(62, 0), 耗时: 4.729s
[2025-07-25 02:58:09] SW-NE对角算符进度: 64/64, 当前算符: D_(0, 2) * D_(63, 49), 耗时: 6.040s
[2025-07-25 02:58:09] 西南-东北方向对角二聚体相关函数计算完成,耗时: 380.80 秒
[2025-07-25 02:58:09] 计算傅里叶变换...
[2025-07-25 02:58:09] 对角二聚体结构因子计算完成
[2025-07-25 02:58:10] 对角二聚体相关函数平均误差: 0.000115
