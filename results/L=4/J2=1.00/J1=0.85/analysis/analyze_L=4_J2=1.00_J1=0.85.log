[2025-07-25 03:35:01] ================================================================================
[2025-07-25 03:35:01] 加载量子态: L=4, J2=1.00, J1=0.85
[2025-07-25 03:35:01] 设置样本数为: 1048576
[2025-07-25 03:35:01] 开始生成共享样本集...
[2025-07-25 03:36:11] 样本生成完成,耗时: 70.106 秒
[2025-07-25 03:36:11] ================================================================================
[2025-07-25 03:36:11] 开始计算自旋结构因子...
[2025-07-25 03:36:11] 初始化操作符缓存...
[2025-07-25 03:36:11] 预构建所有自旋相关操作符...
[2025-07-25 03:36:11] 开始计算自旋相关函数...
[2025-07-25 03:36:19] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 7.726s
[2025-07-25 03:36:27] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 8.378s
[2025-07-25 03:36:31] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 3.589s
[2025-07-25 03:36:35] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 3.599s
[2025-07-25 03:36:38] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 3.605s
[2025-07-25 03:36:42] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 3.608s
[2025-07-25 03:36:45] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 3.610s
[2025-07-25 03:36:49] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 3.599s
[2025-07-25 03:36:53] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 3.648s
[2025-07-25 03:36:56] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 3.603s
[2025-07-25 03:37:00] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 3.590s
[2025-07-25 03:37:03] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 3.590s
[2025-07-25 03:37:07] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 3.611s
[2025-07-25 03:37:11] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 3.593s
[2025-07-25 03:37:14] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 3.605s
[2025-07-25 03:37:18] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 3.602s
[2025-07-25 03:37:22] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 3.590s
[2025-07-25 03:37:25] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 3.591s
[2025-07-25 03:37:29] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 3.629s
[2025-07-25 03:37:32] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 3.603s
[2025-07-25 03:37:36] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 3.593s
[2025-07-25 03:37:40] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 3.589s
[2025-07-25 03:37:43] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 3.598s
[2025-07-25 03:37:47] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 3.591s
[2025-07-25 03:37:50] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 3.605s
[2025-07-25 03:37:54] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 3.607s
[2025-07-25 03:37:58] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 3.594s
[2025-07-25 03:38:01] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 3.604s
[2025-07-25 03:38:05] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 3.590s
[2025-07-25 03:38:08] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 3.592s
[2025-07-25 03:38:12] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 3.602s
[2025-07-25 03:38:16] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 3.589s
[2025-07-25 03:38:19] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 3.602s
[2025-07-25 03:38:23] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 3.609s
[2025-07-25 03:38:26] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 3.595s
[2025-07-25 03:38:30] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 3.608s
[2025-07-25 03:38:34] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 3.590s
[2025-07-25 03:38:37] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 3.591s
[2025-07-25 03:38:41] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 3.600s
[2025-07-25 03:38:44] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 3.594s
[2025-07-25 03:38:48] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 3.610s
[2025-07-25 03:38:52] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 3.605s
[2025-07-25 03:38:55] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 3.605s
[2025-07-25 03:38:59] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 3.589s
[2025-07-25 03:39:02] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 3.602s
[2025-07-25 03:39:06] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 3.598s
[2025-07-25 03:39:10] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 3.589s
[2025-07-25 03:39:13] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 3.605s
[2025-07-25 03:39:17] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 3.593s
[2025-07-25 03:39:20] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 3.591s
[2025-07-25 03:39:24] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 3.600s
[2025-07-25 03:39:27] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 3.590s
[2025-07-25 03:39:31] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 3.605s
[2025-07-25 03:39:35] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 3.598s
[2025-07-25 03:39:38] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 3.590s
[2025-07-25 03:39:42] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 3.602s
[2025-07-25 03:39:45] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 3.592s
[2025-07-25 03:39:49] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 3.595s
[2025-07-25 03:39:53] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 3.633s
[2025-07-25 03:39:56] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 3.591s
[2025-07-25 03:40:00] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 3.599s
[2025-07-25 03:40:04] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 3.604s
[2025-07-25 03:40:07] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 3.590s
[2025-07-25 03:40:11] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 3.594s
[2025-07-25 03:40:11] 自旋相关函数计算完成,总耗时 239.36 秒
[2025-07-25 03:40:11] 计算傅里叶变换...
[2025-07-25 03:40:11] 自旋结构因子计算完成
[2025-07-25 03:40:12] 自旋相关函数平均误差: 0.000526
[2025-07-25 03:40:12] ================================================================================
[2025-07-25 03:40:12] 开始计算对角二聚体结构因子...
[2025-07-25 03:40:12] 识别所有对角二聚体...
[2025-07-25 03:40:12] 总共找到 64 个西北-东南方向对角二聚体和 64 个西南-东北方向对角二聚体
[2025-07-25 03:40:12] 预计算对角二聚体操作符...
[2025-07-25 03:40:14] 开始计算西北-东南方向对角二聚体相关函数 (64 个算符)...
[2025-07-25 03:40:18] NW-SE对角算符进度: 1/64, 当前算符: D_(0, 14) * D_(0, 14), 耗时: 3.649s
[2025-07-25 03:40:29] NW-SE对角算符进度: 2/64, 当前算符: D_(0, 14) * D_(1, 31), 耗时: 10.894s
[2025-07-25 03:40:35] NW-SE对角算符进度: 3/64, 当前算符: D_(0, 14) * D_(2, 16), 耗时: 6.030s
[2025-07-25 03:40:41] NW-SE对角算符进度: 4/64, 当前算符: D_(0, 14) * D_(3, 1), 耗时: 6.028s
[2025-07-25 03:40:47] NW-SE对角算符进度: 5/64, 当前算符: D_(0, 14) * D_(4, 2), 耗时: 6.009s
[2025-07-25 03:40:53] NW-SE对角算符进度: 6/64, 当前算符: D_(0, 14) * D_(5, 19), 耗时: 6.022s
[2025-07-25 03:40:59] NW-SE对角算符进度: 7/64, 当前算符: D_(0, 14) * D_(6, 20), 耗时: 6.010s
[2025-07-25 03:41:05] NW-SE对角算符进度: 8/64, 当前算符: D_(0, 14) * D_(7, 5), 耗时: 6.036s
[2025-07-25 03:41:11] NW-SE对角算符进度: 9/64, 当前算符: D_(0, 14) * D_(8, 6), 耗时: 6.005s
[2025-07-25 03:41:17] NW-SE对角算符进度: 10/64, 当前算符: D_(0, 14) * D_(9, 23), 耗时: 6.027s
[2025-07-25 03:41:23] NW-SE对角算符进度: 11/64, 当前算符: D_(0, 14) * D_(10, 24), 耗时: 6.007s
[2025-07-25 03:41:29] NW-SE对角算符进度: 12/64, 当前算符: D_(0, 14) * D_(11, 9), 耗时: 6.007s
[2025-07-25 03:41:35] NW-SE对角算符进度: 13/64, 当前算符: D_(0, 14) * D_(12, 10), 耗时: 6.029s
[2025-07-25 03:41:41] NW-SE对角算符进度: 14/64, 当前算符: D_(0, 14) * D_(13, 27), 耗时: 6.010s
[2025-07-25 03:41:54] NW-SE对角算符进度: 15/64, 当前算符: D_(0, 14) * D_(14, 28), 耗时: 13.220s
[2025-07-25 03:42:00] NW-SE对角算符进度: 16/64, 当前算符: D_(0, 14) * D_(15, 13), 耗时: 6.040s
[2025-07-25 03:42:06] NW-SE对角算符进度: 17/64, 当前算符: D_(0, 14) * D_(16, 30), 耗时: 6.017s
[2025-07-25 03:42:12] NW-SE对角算符进度: 18/64, 当前算符: D_(0, 14) * D_(17, 47), 耗时: 6.015s
[2025-07-25 03:42:18] NW-SE对角算符进度: 19/64, 当前算符: D_(0, 14) * D_(18, 32), 耗时: 6.048s
[2025-07-25 03:42:24] NW-SE对角算符进度: 20/64, 当前算符: D_(0, 14) * D_(19, 17), 耗时: 6.012s
[2025-07-25 03:42:30] NW-SE对角算符进度: 21/64, 当前算符: D_(0, 14) * D_(20, 18), 耗时: 6.042s
[2025-07-25 03:42:36] NW-SE对角算符进度: 22/64, 当前算符: D_(0, 14) * D_(21, 35), 耗时: 6.021s
[2025-07-25 03:42:42] NW-SE对角算符进度: 23/64, 当前算符: D_(0, 14) * D_(22, 36), 耗时: 6.030s
[2025-07-25 03:42:48] NW-SE对角算符进度: 24/64, 当前算符: D_(0, 14) * D_(23, 21), 耗时: 6.021s
[2025-07-25 03:42:54] NW-SE对角算符进度: 25/64, 当前算符: D_(0, 14) * D_(24, 22), 耗时: 6.035s
[2025-07-25 03:43:00] NW-SE对角算符进度: 26/64, 当前算符: D_(0, 14) * D_(25, 39), 耗时: 6.023s
[2025-07-25 03:43:06] NW-SE对角算符进度: 27/64, 当前算符: D_(0, 14) * D_(26, 40), 耗时: 6.038s
[2025-07-25 03:43:12] NW-SE对角算符进度: 28/64, 当前算符: D_(0, 14) * D_(27, 25), 耗时: 6.011s
[2025-07-25 03:43:18] NW-SE对角算符进度: 29/64, 当前算符: D_(0, 14) * D_(28, 26), 耗时: 6.044s
[2025-07-25 03:43:25] NW-SE对角算符进度: 30/64, 当前算符: D_(0, 14) * D_(29, 43), 耗时: 6.018s
[2025-07-25 03:43:31] NW-SE对角算符进度: 31/64, 当前算符: D_(0, 14) * D_(30, 44), 耗时: 6.034s
[2025-07-25 03:43:37] NW-SE对角算符进度: 32/64, 当前算符: D_(0, 14) * D_(31, 29), 耗时: 6.014s
[2025-07-25 03:43:43] NW-SE对角算符进度: 33/64, 当前算符: D_(0, 14) * D_(32, 46), 耗时: 6.050s
[2025-07-25 03:43:49] NW-SE对角算符进度: 34/64, 当前算符: D_(0, 14) * D_(33, 63), 耗时: 6.044s
[2025-07-25 03:43:55] NW-SE对角算符进度: 35/64, 当前算符: D_(0, 14) * D_(34, 48), 耗时: 6.037s
[2025-07-25 03:44:01] NW-SE对角算符进度: 36/64, 当前算符: D_(0, 14) * D_(35, 33), 耗时: 6.023s
[2025-07-25 03:44:07] NW-SE对角算符进度: 37/64, 当前算符: D_(0, 14) * D_(36, 34), 耗时: 6.030s
[2025-07-25 03:44:13] NW-SE对角算符进度: 38/64, 当前算符: D_(0, 14) * D_(37, 51), 耗时: 6.018s
[2025-07-25 03:44:19] NW-SE对角算符进度: 39/64, 当前算符: D_(0, 14) * D_(38, 52), 耗时: 6.044s
[2025-07-25 03:44:25] NW-SE对角算符进度: 40/64, 当前算符: D_(0, 14) * D_(39, 37), 耗时: 6.014s
[2025-07-25 03:44:31] NW-SE对角算符进度: 41/64, 当前算符: D_(0, 14) * D_(40, 38), 耗时: 6.032s
[2025-07-25 03:44:37] NW-SE对角算符进度: 42/64, 当前算符: D_(0, 14) * D_(41, 55), 耗时: 6.015s
[2025-07-25 03:44:43] NW-SE对角算符进度: 43/64, 当前算符: D_(0, 14) * D_(42, 56), 耗时: 6.047s
[2025-07-25 03:44:49] NW-SE对角算符进度: 44/64, 当前算符: D_(0, 14) * D_(43, 41), 耗时: 6.014s
[2025-07-25 03:44:55] NW-SE对角算符进度: 45/64, 当前算符: D_(0, 14) * D_(44, 42), 耗时: 6.034s
[2025-07-25 03:45:01] NW-SE对角算符进度: 46/64, 当前算符: D_(0, 14) * D_(45, 59), 耗时: 6.017s
[2025-07-25 03:45:07] NW-SE对角算符进度: 47/64, 当前算符: D_(0, 14) * D_(46, 60), 耗时: 6.035s
[2025-07-25 03:45:13] NW-SE对角算符进度: 48/64, 当前算符: D_(0, 14) * D_(47, 45), 耗时: 6.020s
[2025-07-25 03:45:19] NW-SE对角算符进度: 49/64, 当前算符: D_(0, 14) * D_(48, 62), 耗时: 6.030s
[2025-07-25 03:45:25] NW-SE对角算符进度: 50/64, 当前算符: D_(0, 14) * D_(49, 15), 耗时: 6.030s
[2025-07-25 03:45:30] NW-SE对角算符进度: 51/64, 当前算符: D_(0, 14) * D_(50, 0), 耗时: 4.708s
[2025-07-25 03:45:36] NW-SE对角算符进度: 52/64, 当前算符: D_(0, 14) * D_(51, 49), 耗时: 6.020s
[2025-07-25 03:45:42] NW-SE对角算符进度: 53/64, 当前算符: D_(0, 14) * D_(52, 50), 耗时: 6.038s
[2025-07-25 03:45:48] NW-SE对角算符进度: 54/64, 当前算符: D_(0, 14) * D_(53, 3), 耗时: 6.022s
[2025-07-25 03:45:54] NW-SE对角算符进度: 55/64, 当前算符: D_(0, 14) * D_(54, 4), 耗时: 6.048s
[2025-07-25 03:46:00] NW-SE对角算符进度: 56/64, 当前算符: D_(0, 14) * D_(55, 53), 耗时: 6.017s
[2025-07-25 03:46:06] NW-SE对角算符进度: 57/64, 当前算符: D_(0, 14) * D_(56, 54), 耗时: 6.042s
[2025-07-25 03:46:12] NW-SE对角算符进度: 58/64, 当前算符: D_(0, 14) * D_(57, 7), 耗时: 6.016s
[2025-07-25 03:46:18] NW-SE对角算符进度: 59/64, 当前算符: D_(0, 14) * D_(58, 8), 耗时: 6.048s
[2025-07-25 03:46:24] NW-SE对角算符进度: 60/64, 当前算符: D_(0, 14) * D_(59, 57), 耗时: 6.020s
[2025-07-25 03:46:30] NW-SE对角算符进度: 61/64, 当前算符: D_(0, 14) * D_(60, 58), 耗时: 6.145s
[2025-07-25 03:46:36] NW-SE对角算符进度: 62/64, 当前算符: D_(0, 14) * D_(61, 11), 耗时: 6.014s
[2025-07-25 03:46:42] NW-SE对角算符进度: 63/64, 当前算符: D_(0, 14) * D_(62, 12), 耗时: 6.012s
[2025-07-25 03:46:48] NW-SE对角算符进度: 64/64, 当前算符: D_(0, 14) * D_(63, 61), 耗时: 6.038s
[2025-07-25 03:46:48] 西北-东南方向对角二聚体相关函数计算完成,耗时: 394.24 秒
[2025-07-25 03:46:48] ================================================================================
[2025-07-25 03:46:48] 开始计算西南-东北方向对角二聚体相关函数 (64 个算符)...
[2025-07-25 03:46:52] SW-NE对角算符进度: 1/64, 当前算符: D_(0, 2) * D_(0, 2), 耗时: 3.596s
[2025-07-25 03:46:58] SW-NE对角算符进度: 2/64, 当前算符: D_(0, 2) * D_(1, 19), 耗时: 6.029s
[2025-07-25 03:47:03] SW-NE对角算符进度: 3/64, 当前算符: D_(0, 2) * D_(2, 20), 耗时: 4.712s
[2025-07-25 03:47:09] SW-NE对角算符进度: 4/64, 当前算符: D_(0, 2) * D_(3, 5), 耗时: 6.043s
[2025-07-25 03:47:15] SW-NE对角算符进度: 5/64, 当前算符: D_(0, 2) * D_(4, 6), 耗时: 6.043s
[2025-07-25 03:47:21] SW-NE对角算符进度: 6/64, 当前算符: D_(0, 2) * D_(5, 23), 耗时: 6.010s
[2025-07-25 03:47:27] SW-NE对角算符进度: 7/64, 当前算符: D_(0, 2) * D_(6, 24), 耗时: 6.034s
[2025-07-25 03:47:33] SW-NE对角算符进度: 8/64, 当前算符: D_(0, 2) * D_(7, 9), 耗时: 6.017s
[2025-07-25 03:47:39] SW-NE对角算符进度: 9/64, 当前算符: D_(0, 2) * D_(8, 10), 耗时: 6.014s
[2025-07-25 03:47:45] SW-NE对角算符进度: 10/64, 当前算符: D_(0, 2) * D_(9, 27), 耗时: 6.031s
[2025-07-25 03:47:51] SW-NE对角算符进度: 11/64, 当前算符: D_(0, 2) * D_(10, 28), 耗时: 6.018s
[2025-07-25 03:47:57] SW-NE对角算符进度: 12/64, 当前算符: D_(0, 2) * D_(11, 13), 耗时: 6.032s
[2025-07-25 03:48:03] SW-NE对角算符进度: 13/64, 当前算符: D_(0, 2) * D_(12, 14), 耗时: 6.013s
[2025-07-25 03:48:09] SW-NE对角算符进度: 14/64, 当前算符: D_(0, 2) * D_(13, 31), 耗时: 6.045s
[2025-07-25 03:48:15] SW-NE对角算符进度: 15/64, 当前算符: D_(0, 2) * D_(14, 16), 耗时: 6.010s
[2025-07-25 03:48:21] SW-NE对角算符进度: 16/64, 当前算符: D_(0, 2) * D_(15, 1), 耗时: 6.041s
[2025-07-25 03:48:27] SW-NE对角算符进度: 17/64, 当前算符: D_(0, 2) * D_(16, 18), 耗时: 6.021s
[2025-07-25 03:48:33] SW-NE对角算符进度: 18/64, 当前算符: D_(0, 2) * D_(17, 35), 耗时: 6.037s
[2025-07-25 03:48:39] SW-NE对角算符进度: 19/64, 当前算符: D_(0, 2) * D_(18, 36), 耗时: 6.020s
[2025-07-25 03:48:45] SW-NE对角算符进度: 20/64, 当前算符: D_(0, 2) * D_(19, 21), 耗时: 6.028s
[2025-07-25 03:48:51] SW-NE对角算符进度: 21/64, 当前算符: D_(0, 2) * D_(20, 22), 耗时: 6.011s
[2025-07-25 03:48:57] SW-NE对角算符进度: 22/64, 当前算符: D_(0, 2) * D_(21, 39), 耗时: 6.021s
[2025-07-25 03:49:03] SW-NE对角算符进度: 23/64, 当前算符: D_(0, 2) * D_(22, 40), 耗时: 6.029s
[2025-07-25 03:49:09] SW-NE对角算符进度: 24/64, 当前算符: D_(0, 2) * D_(23, 25), 耗时: 6.020s
[2025-07-25 03:49:15] SW-NE对角算符进度: 25/64, 当前算符: D_(0, 2) * D_(24, 26), 耗时: 6.013s
[2025-07-25 03:49:21] SW-NE对角算符进度: 26/64, 当前算符: D_(0, 2) * D_(25, 43), 耗时: 6.041s
[2025-07-25 03:49:27] SW-NE对角算符进度: 27/64, 当前算符: D_(0, 2) * D_(26, 44), 耗时: 6.010s
[2025-07-25 03:49:33] SW-NE对角算符进度: 28/64, 当前算符: D_(0, 2) * D_(27, 29), 耗时: 6.046s
[2025-07-25 03:49:39] SW-NE对角算符进度: 29/64, 当前算符: D_(0, 2) * D_(28, 30), 耗时: 6.015s
[2025-07-25 03:49:45] SW-NE对角算符进度: 30/64, 当前算符: D_(0, 2) * D_(29, 47), 耗时: 6.038s
[2025-07-25 03:49:51] SW-NE对角算符进度: 31/64, 当前算符: D_(0, 2) * D_(30, 32), 耗时: 6.013s
[2025-07-25 03:49:57] SW-NE对角算符进度: 32/64, 当前算符: D_(0, 2) * D_(31, 17), 耗时: 6.032s
[2025-07-25 03:50:03] SW-NE对角算符进度: 33/64, 当前算符: D_(0, 2) * D_(32, 34), 耗时: 6.013s
[2025-07-25 03:50:10] SW-NE对角算符进度: 34/64, 当前算符: D_(0, 2) * D_(33, 51), 耗时: 6.048s
[2025-07-25 03:50:16] SW-NE对角算符进度: 35/64, 当前算符: D_(0, 2) * D_(34, 52), 耗时: 6.017s
[2025-07-25 03:50:22] SW-NE对角算符进度: 36/64, 当前算符: D_(0, 2) * D_(35, 37), 耗时: 6.037s
[2025-07-25 03:50:28] SW-NE对角算符进度: 37/64, 当前算符: D_(0, 2) * D_(36, 38), 耗时: 6.015s
[2025-07-25 03:50:34] SW-NE对角算符进度: 38/64, 当前算符: D_(0, 2) * D_(37, 55), 耗时: 6.028s
[2025-07-25 03:50:40] SW-NE对角算符进度: 39/64, 当前算符: D_(0, 2) * D_(38, 56), 耗时: 6.031s
[2025-07-25 03:50:46] SW-NE对角算符进度: 40/64, 当前算符: D_(0, 2) * D_(39, 41), 耗时: 6.018s
[2025-07-25 03:50:52] SW-NE对角算符进度: 41/64, 当前算符: D_(0, 2) * D_(40, 42), 耗时: 6.102s
[2025-07-25 03:50:58] SW-NE对角算符进度: 42/64, 当前算符: D_(0, 2) * D_(41, 59), 耗时: 6.038s
[2025-07-25 03:51:04] SW-NE对角算符进度: 43/64, 当前算符: D_(0, 2) * D_(42, 60), 耗时: 6.013s
[2025-07-25 03:51:10] SW-NE对角算符进度: 44/64, 当前算符: D_(0, 2) * D_(43, 45), 耗时: 6.041s
[2025-07-25 03:51:16] SW-NE对角算符进度: 45/64, 当前算符: D_(0, 2) * D_(44, 46), 耗时: 6.023s
[2025-07-25 03:51:22] SW-NE对角算符进度: 46/64, 当前算符: D_(0, 2) * D_(45, 63), 耗时: 6.029s
[2025-07-25 03:51:28] SW-NE对角算符进度: 47/64, 当前算符: D_(0, 2) * D_(46, 48), 耗时: 6.014s
[2025-07-25 03:51:34] SW-NE对角算符进度: 48/64, 当前算符: D_(0, 2) * D_(47, 33), 耗时: 6.029s
[2025-07-25 03:51:40] SW-NE对角算符进度: 49/64, 当前算符: D_(0, 2) * D_(48, 50), 耗时: 6.021s
[2025-07-25 03:51:46] SW-NE对角算符进度: 50/64, 当前算符: D_(0, 2) * D_(49, 3), 耗时: 6.036s
[2025-07-25 03:51:52] SW-NE对角算符进度: 51/64, 当前算符: D_(0, 2) * D_(50, 4), 耗时: 6.015s
[2025-07-25 03:51:58] SW-NE对角算符进度: 52/64, 当前算符: D_(0, 2) * D_(51, 53), 耗时: 6.033s
[2025-07-25 03:52:04] SW-NE对角算符进度: 53/64, 当前算符: D_(0, 2) * D_(52, 54), 耗时: 6.016s
[2025-07-25 03:52:10] SW-NE对角算符进度: 54/64, 当前算符: D_(0, 2) * D_(53, 7), 耗时: 6.038s
[2025-07-25 03:52:16] SW-NE对角算符进度: 55/64, 当前算符: D_(0, 2) * D_(54, 8), 耗时: 6.014s
[2025-07-25 03:52:22] SW-NE对角算符进度: 56/64, 当前算符: D_(0, 2) * D_(55, 57), 耗时: 6.022s
[2025-07-25 03:52:28] SW-NE对角算符进度: 57/64, 当前算符: D_(0, 2) * D_(56, 58), 耗时: 6.017s
[2025-07-25 03:52:34] SW-NE对角算符进度: 58/64, 当前算符: D_(0, 2) * D_(57, 11), 耗时: 6.027s
[2025-07-25 03:52:40] SW-NE对角算符进度: 59/64, 当前算符: D_(0, 2) * D_(58, 12), 耗时: 6.015s
[2025-07-25 03:52:46] SW-NE对角算符进度: 60/64, 当前算符: D_(0, 2) * D_(59, 61), 耗时: 6.037s
[2025-07-25 03:52:52] SW-NE对角算符进度: 61/64, 当前算符: D_(0, 2) * D_(60, 62), 耗时: 6.016s
[2025-07-25 03:52:58] SW-NE对角算符进度: 62/64, 当前算符: D_(0, 2) * D_(61, 15), 耗时: 6.038s
[2025-07-25 03:53:03] SW-NE对角算符进度: 63/64, 当前算符: D_(0, 2) * D_(62, 0), 耗时: 4.707s
[2025-07-25 03:53:09] SW-NE对角算符进度: 64/64, 当前算符: D_(0, 2) * D_(63, 49), 耗时: 6.036s
[2025-07-25 03:53:09] 西南-东北方向对角二聚体相关函数计算完成,耗时: 380.74 秒
[2025-07-25 03:53:09] 计算傅里叶变换...
[2025-07-25 03:53:10] 对角二聚体结构因子计算完成
[2025-07-25 03:53:10] 对角二聚体相关函数平均误差: 0.000114
