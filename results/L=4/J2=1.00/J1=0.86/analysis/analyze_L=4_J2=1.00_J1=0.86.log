[2025-07-25 03:53:24] ================================================================================
[2025-07-25 03:53:24] 加载量子态: L=4, J2=1.00, J1=0.86
[2025-07-25 03:53:24] 设置样本数为: 1048576
[2025-07-25 03:53:24] 开始生成共享样本集...
[2025-07-25 03:54:33] 样本生成完成,耗时: 69.422 秒
[2025-07-25 03:54:33] ================================================================================
[2025-07-25 03:54:33] 开始计算自旋结构因子...
[2025-07-25 03:54:33] 初始化操作符缓存...
[2025-07-25 03:54:33] 预构建所有自旋相关操作符...
[2025-07-25 03:54:33] 开始计算自旋相关函数...
[2025-07-25 03:54:41] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 7.701s
[2025-07-25 03:54:50] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 8.365s
[2025-07-25 03:54:53] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 3.595s
[2025-07-25 03:54:57] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 3.593s
[2025-07-25 03:55:00] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 3.595s
[2025-07-25 03:55:04] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 3.593s
[2025-07-25 03:55:07] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 3.595s
[2025-07-25 03:55:11] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 3.571s
[2025-07-25 03:55:15] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 3.573s
[2025-07-25 03:55:18] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 3.593s
[2025-07-25 03:55:22] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 3.593s
[2025-07-25 03:55:25] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 3.592s
[2025-07-25 03:55:29] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 3.573s
[2025-07-25 03:55:33] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 3.571s
[2025-07-25 03:55:36] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 3.573s
[2025-07-25 03:55:40] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 3.594s
[2025-07-25 03:55:43] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 3.596s
[2025-07-25 03:55:47] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 3.572s
[2025-07-25 03:55:50] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 3.572s
[2025-07-25 03:55:54] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 3.572s
[2025-07-25 03:55:58] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 3.594s
[2025-07-25 03:56:01] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 3.594s
[2025-07-25 03:56:05] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 3.593s
[2025-07-25 03:56:08] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 3.572s
[2025-07-25 03:56:12] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 3.572s
[2025-07-25 03:56:16] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 3.595s
[2025-07-25 03:56:19] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 3.593s
[2025-07-25 03:56:23] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 3.592s
[2025-07-25 03:56:26] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 3.572s
[2025-07-25 03:56:30] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 3.572s
[2025-07-25 03:56:33] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 3.574s
[2025-07-25 03:56:37] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 3.594s
[2025-07-25 03:56:41] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 3.598s
[2025-07-25 03:56:44] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 3.572s
[2025-07-25 03:56:48] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 3.572s
[2025-07-25 03:56:51] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 3.573s
[2025-07-25 03:56:55] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 3.593s
[2025-07-25 03:56:59] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 3.595s
[2025-07-25 03:57:02] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 3.592s
[2025-07-25 03:57:06] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 3.572s
[2025-07-25 03:57:09] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 3.571s
[2025-07-25 03:57:13] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 3.591s
[2025-07-25 03:57:17] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 3.591s
[2025-07-25 03:57:20] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 3.591s
[2025-07-25 03:57:24] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 3.571s
[2025-07-25 03:57:27] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 3.571s
[2025-07-25 03:57:31] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 3.572s
[2025-07-25 03:57:34] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 3.572s
[2025-07-25 03:57:38] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 3.572s
[2025-07-25 03:57:42] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 3.592s
[2025-07-25 03:57:45] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 3.596s
[2025-07-25 03:57:49] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 3.593s
[2025-07-25 03:57:52] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 3.572s
[2025-07-25 03:57:56] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 3.572s
[2025-07-25 03:57:59] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 3.571s
[2025-07-25 03:58:03] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 3.593s
[2025-07-25 03:58:07] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 3.592s
[2025-07-25 03:58:10] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 3.572s
[2025-07-25 03:58:14] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 3.572s
[2025-07-25 03:58:17] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 3.572s
[2025-07-25 03:58:21] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 3.593s
[2025-07-25 03:58:25] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 3.591s
[2025-07-25 03:58:28] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 3.593s
[2025-07-25 03:58:32] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 3.572s
[2025-07-25 03:58:32] 自旋相关函数计算完成,总耗时 238.30 秒
[2025-07-25 03:58:32] 计算傅里叶变换...
[2025-07-25 03:58:32] 自旋结构因子计算完成
[2025-07-25 03:58:33] 自旋相关函数平均误差: 0.000525
[2025-07-25 03:58:33] ================================================================================
[2025-07-25 03:58:33] 开始计算对角二聚体结构因子...
[2025-07-25 03:58:33] 识别所有对角二聚体...
[2025-07-25 03:58:33] 总共找到 64 个西北-东南方向对角二聚体和 64 个西南-东北方向对角二聚体
[2025-07-25 03:58:33] 预计算对角二聚体操作符...
[2025-07-25 03:58:35] 开始计算西北-东南方向对角二聚体相关函数 (64 个算符)...
[2025-07-25 03:58:39] NW-SE对角算符进度: 1/64, 当前算符: D_(0, 14) * D_(0, 14), 耗时: 3.602s
[2025-07-25 03:58:50] NW-SE对角算符进度: 2/64, 当前算符: D_(0, 14) * D_(1, 31), 耗时: 10.860s
[2025-07-25 03:58:56] NW-SE对角算符进度: 3/64, 当前算符: D_(0, 14) * D_(2, 16), 耗时: 5.968s
[2025-07-25 03:59:02] NW-SE对角算符进度: 4/64, 当前算符: D_(0, 14) * D_(3, 1), 耗时: 5.968s
[2025-07-25 03:59:08] NW-SE对角算符进度: 5/64, 当前算符: D_(0, 14) * D_(4, 2), 耗时: 5.993s
[2025-07-25 03:59:13] NW-SE对角算符进度: 6/64, 当前算符: D_(0, 14) * D_(5, 19), 耗时: 5.995s
[2025-07-25 03:59:19] NW-SE对角算符进度: 7/64, 当前算符: D_(0, 14) * D_(6, 20), 耗时: 5.950s
[2025-07-25 03:59:25] NW-SE对角算符进度: 8/64, 当前算符: D_(0, 14) * D_(7, 5), 耗时: 5.968s
[2025-07-25 03:59:31] NW-SE对角算符进度: 9/64, 当前算符: D_(0, 14) * D_(8, 6), 耗时: 5.996s
[2025-07-25 03:59:37] NW-SE对角算符进度: 10/64, 当前算符: D_(0, 14) * D_(9, 23), 耗时: 5.991s
[2025-07-25 03:59:43] NW-SE对角算符进度: 11/64, 当前算符: D_(0, 14) * D_(10, 24), 耗时: 5.993s
[2025-07-25 03:59:49] NW-SE对角算符进度: 12/64, 当前算符: D_(0, 14) * D_(11, 9), 耗时: 5.993s
[2025-07-25 03:59:55] NW-SE对角算符进度: 13/64, 当前算符: D_(0, 14) * D_(12, 10), 耗时: 5.968s
[2025-07-25 04:00:01] NW-SE对角算符进度: 14/64, 当前算符: D_(0, 14) * D_(13, 27), 耗时: 5.952s
[2025-07-25 04:00:14] NW-SE对角算符进度: 15/64, 当前算符: D_(0, 14) * D_(14, 28), 耗时: 13.150s
[2025-07-25 04:00:20] NW-SE对角算符进度: 16/64, 当前算符: D_(0, 14) * D_(15, 13), 耗时: 6.019s
[2025-07-25 04:00:27] NW-SE对角算符进度: 17/64, 当前算符: D_(0, 14) * D_(16, 30), 耗时: 6.013s
[2025-07-25 04:00:32] NW-SE对角算符进度: 18/64, 当前算符: D_(0, 14) * D_(17, 47), 耗时: 5.973s
[2025-07-25 04:00:38] NW-SE对角算符进度: 19/64, 当前算符: D_(0, 14) * D_(18, 32), 耗时: 5.973s
[2025-07-25 04:00:44] NW-SE对角算符进度: 20/64, 当前算符: D_(0, 14) * D_(19, 17), 耗时: 5.972s
[2025-07-25 04:00:50] NW-SE对角算符进度: 21/64, 当前算符: D_(0, 14) * D_(20, 18), 耗时: 6.012s
[2025-07-25 04:00:56] NW-SE对角算符进度: 22/64, 当前算符: D_(0, 14) * D_(21, 35), 耗时: 6.017s
[2025-07-25 04:01:02] NW-SE对角算符进度: 23/64, 当前算符: D_(0, 14) * D_(22, 36), 耗时: 5.971s
[2025-07-25 04:01:08] NW-SE对角算符进度: 24/64, 当前算符: D_(0, 14) * D_(23, 21), 耗时: 5.976s
[2025-07-25 04:01:14] NW-SE对角算符进度: 25/64, 当前算符: D_(0, 14) * D_(24, 22), 耗时: 5.975s
[2025-07-25 04:01:20] NW-SE对角算符进度: 26/64, 当前算符: D_(0, 14) * D_(25, 39), 耗时: 6.012s
[2025-07-25 04:01:26] NW-SE对角算符进度: 27/64, 当前算符: D_(0, 14) * D_(26, 40), 耗时: 6.014s
[2025-07-25 04:01:32] NW-SE对角算符进度: 28/64, 当前算符: D_(0, 14) * D_(27, 25), 耗时: 6.012s
[2025-07-25 04:01:38] NW-SE对角算符进度: 29/64, 当前算符: D_(0, 14) * D_(28, 26), 耗时: 5.974s
[2025-07-25 04:01:44] NW-SE对角算符进度: 30/64, 当前算符: D_(0, 14) * D_(29, 43), 耗时: 5.972s
[2025-07-25 04:01:50] NW-SE对角算符进度: 31/64, 当前算符: D_(0, 14) * D_(30, 44), 耗时: 6.018s
[2025-07-25 04:01:56] NW-SE对角算符进度: 32/64, 当前算符: D_(0, 14) * D_(31, 29), 耗时: 6.015s
[2025-07-25 04:02:02] NW-SE对角算符进度: 33/64, 当前算符: D_(0, 14) * D_(32, 46), 耗时: 6.012s
[2025-07-25 04:02:08] NW-SE对角算符进度: 34/64, 当前算符: D_(0, 14) * D_(33, 63), 耗时: 5.974s
[2025-07-25 04:02:14] NW-SE对角算符进度: 35/64, 当前算符: D_(0, 14) * D_(34, 48), 耗时: 5.973s
[2025-07-25 04:02:20] NW-SE对角算符进度: 36/64, 当前算符: D_(0, 14) * D_(35, 33), 耗时: 5.973s
[2025-07-25 04:02:26] NW-SE对角算符进度: 37/64, 当前算符: D_(0, 14) * D_(36, 34), 耗时: 6.014s
[2025-07-25 04:02:32] NW-SE对角算符进度: 38/64, 当前算符: D_(0, 14) * D_(37, 51), 耗时: 6.016s
[2025-07-25 04:02:38] NW-SE对角算符进度: 39/64, 当前算符: D_(0, 14) * D_(38, 52), 耗时: 5.974s
[2025-07-25 04:02:44] NW-SE对角算符进度: 40/64, 当前算符: D_(0, 14) * D_(39, 37), 耗时: 5.976s
[2025-07-25 04:02:50] NW-SE对角算符进度: 41/64, 当前算符: D_(0, 14) * D_(40, 38), 耗时: 5.980s
[2025-07-25 04:02:56] NW-SE对角算符进度: 42/64, 当前算符: D_(0, 14) * D_(41, 55), 耗时: 6.015s
[2025-07-25 04:03:02] NW-SE对角算符进度: 43/64, 当前算符: D_(0, 14) * D_(42, 56), 耗时: 6.013s
[2025-07-25 04:03:08] NW-SE对角算符进度: 44/64, 当前算符: D_(0, 14) * D_(43, 41), 耗时: 6.013s
[2025-07-25 04:03:14] NW-SE对角算符进度: 45/64, 当前算符: D_(0, 14) * D_(44, 42), 耗时: 5.971s
[2025-07-25 04:03:20] NW-SE对角算符进度: 46/64, 当前算符: D_(0, 14) * D_(45, 59), 耗时: 5.972s
[2025-07-25 04:03:26] NW-SE对角算符进度: 47/64, 当前算符: D_(0, 14) * D_(46, 60), 耗时: 5.981s
[2025-07-25 04:03:32] NW-SE对角算符进度: 48/64, 当前算符: D_(0, 14) * D_(47, 45), 耗时: 5.972s
[2025-07-25 04:03:38] NW-SE对角算符进度: 49/64, 当前算符: D_(0, 14) * D_(48, 62), 耗时: 5.972s
[2025-07-25 04:03:44] NW-SE对角算符进度: 50/64, 当前算符: D_(0, 14) * D_(49, 15), 耗时: 6.010s
[2025-07-25 04:03:49] NW-SE对角算符进度: 51/64, 当前算符: D_(0, 14) * D_(50, 0), 耗时: 4.716s
[2025-07-25 04:03:55] NW-SE对角算符进度: 52/64, 当前算符: D_(0, 14) * D_(51, 49), 耗时: 6.013s
[2025-07-25 04:04:01] NW-SE对角算符进度: 53/64, 当前算符: D_(0, 14) * D_(52, 50), 耗时: 5.974s
[2025-07-25 04:04:07] NW-SE对角算符进度: 54/64, 当前算符: D_(0, 14) * D_(53, 3), 耗时: 5.996s
[2025-07-25 04:04:13] NW-SE对角算符进度: 55/64, 当前算符: D_(0, 14) * D_(54, 4), 耗时: 6.014s
[2025-07-25 04:04:19] NW-SE对角算符进度: 56/64, 当前算符: D_(0, 14) * D_(55, 53), 耗时: 6.015s
[2025-07-25 04:04:25] NW-SE对角算符进度: 57/64, 当前算符: D_(0, 14) * D_(56, 54), 耗时: 6.017s
[2025-07-25 04:04:31] NW-SE对角算符进度: 58/64, 当前算符: D_(0, 14) * D_(57, 7), 耗时: 5.974s
[2025-07-25 04:04:37] NW-SE对角算符进度: 59/64, 当前算符: D_(0, 14) * D_(58, 8), 耗时: 5.974s
[2025-07-25 04:04:43] NW-SE对角算符进度: 60/64, 当前算符: D_(0, 14) * D_(59, 57), 耗时: 5.972s
[2025-07-25 04:04:49] NW-SE对角算符进度: 61/64, 当前算符: D_(0, 14) * D_(60, 58), 耗时: 6.014s
[2025-07-25 04:04:55] NW-SE对角算符进度: 62/64, 当前算符: D_(0, 14) * D_(61, 11), 耗时: 6.011s
[2025-07-25 04:05:01] NW-SE对角算符进度: 63/64, 当前算符: D_(0, 14) * D_(62, 12), 耗时: 5.973s
[2025-07-25 04:05:07] NW-SE对角算符进度: 64/64, 当前算符: D_(0, 14) * D_(63, 61), 耗时: 5.973s
[2025-07-25 04:05:07] 西北-东南方向对角二聚体相关函数计算完成,耗时: 391.79 秒
[2025-07-25 04:05:07] ================================================================================
[2025-07-25 04:05:07] 开始计算西南-东北方向对角二聚体相关函数 (64 个算符)...
[2025-07-25 04:05:10] SW-NE对角算符进度: 1/64, 当前算符: D_(0, 2) * D_(0, 2), 耗时: 3.589s
[2025-07-25 04:05:17] SW-NE对角算符进度: 2/64, 当前算符: D_(0, 2) * D_(1, 19), 耗时: 6.014s
[2025-07-25 04:05:21] SW-NE对角算符进度: 3/64, 当前算符: D_(0, 2) * D_(2, 20), 耗时: 4.712s
[2025-07-25 04:05:27] SW-NE对角算符进度: 4/64, 当前算符: D_(0, 2) * D_(3, 5), 耗时: 6.014s
[2025-07-25 04:05:33] SW-NE对角算符进度: 5/64, 当前算符: D_(0, 2) * D_(4, 6), 耗时: 5.974s
[2025-07-25 04:05:39] SW-NE对角算符进度: 6/64, 当前算符: D_(0, 2) * D_(5, 23), 耗时: 5.972s
[2025-07-25 04:05:45] SW-NE对角算符进度: 7/64, 当前算符: D_(0, 2) * D_(6, 24), 耗时: 6.012s
[2025-07-25 04:05:51] SW-NE对角算符进度: 8/64, 当前算符: D_(0, 2) * D_(7, 9), 耗时: 6.018s
[2025-07-25 04:05:57] SW-NE对角算符进度: 9/64, 当前算符: D_(0, 2) * D_(8, 10), 耗时: 6.018s
[2025-07-25 04:06:03] SW-NE对角算符进度: 10/64, 当前算符: D_(0, 2) * D_(9, 27), 耗时: 5.972s
[2025-07-25 04:06:09] SW-NE对角算符进度: 11/64, 当前算符: D_(0, 2) * D_(10, 28), 耗时: 5.973s
[2025-07-25 04:06:15] SW-NE对角算符进度: 12/64, 当前算符: D_(0, 2) * D_(11, 13), 耗时: 5.972s
[2025-07-25 04:06:21] SW-NE对角算符进度: 13/64, 当前算符: D_(0, 2) * D_(12, 14), 耗时: 6.015s
[2025-07-25 04:06:27] SW-NE对角算符进度: 14/64, 当前算符: D_(0, 2) * D_(13, 31), 耗时: 6.014s
[2025-07-25 04:06:33] SW-NE对角算符进度: 15/64, 当前算符: D_(0, 2) * D_(14, 16), 耗时: 5.974s
[2025-07-25 04:06:39] SW-NE对角算符进度: 16/64, 当前算符: D_(0, 2) * D_(15, 1), 耗时: 5.975s
[2025-07-25 04:06:45] SW-NE对角算符进度: 17/64, 当前算符: D_(0, 2) * D_(16, 18), 耗时: 5.972s
[2025-07-25 04:06:51] SW-NE对角算符进度: 18/64, 当前算符: D_(0, 2) * D_(17, 35), 耗时: 6.014s
[2025-07-25 04:06:57] SW-NE对角算符进度: 19/64, 当前算符: D_(0, 2) * D_(18, 36), 耗时: 6.012s
[2025-07-25 04:07:03] SW-NE对角算符进度: 20/64, 当前算符: D_(0, 2) * D_(19, 21), 耗时: 6.016s
[2025-07-25 04:07:09] SW-NE对角算符进度: 21/64, 当前算符: D_(0, 2) * D_(20, 22), 耗时: 5.973s
[2025-07-25 04:07:15] SW-NE对角算符进度: 22/64, 当前算符: D_(0, 2) * D_(21, 39), 耗时: 5.973s
[2025-07-25 04:07:21] SW-NE对角算符进度: 23/64, 当前算符: D_(0, 2) * D_(22, 40), 耗时: 6.010s
[2025-07-25 04:07:27] SW-NE对角算符进度: 24/64, 当前算符: D_(0, 2) * D_(23, 25), 耗时: 6.016s
[2025-07-25 04:07:33] SW-NE对角算符进度: 25/64, 当前算符: D_(0, 2) * D_(24, 26), 耗时: 6.013s
[2025-07-25 04:07:39] SW-NE对角算符进度: 26/64, 当前算符: D_(0, 2) * D_(25, 43), 耗时: 6.013s
[2025-07-25 04:07:45] SW-NE对角算符进度: 27/64, 当前算符: D_(0, 2) * D_(26, 44), 耗时: 6.014s
[2025-07-25 04:07:51] SW-NE对角算符进度: 28/64, 当前算符: D_(0, 2) * D_(27, 29), 耗时: 6.014s
[2025-07-25 04:07:57] SW-NE对角算符进度: 29/64, 当前算符: D_(0, 2) * D_(28, 30), 耗时: 5.975s
[2025-07-25 04:08:03] SW-NE对角算符进度: 30/64, 当前算符: D_(0, 2) * D_(29, 47), 耗时: 5.976s
[2025-07-25 04:08:09] SW-NE对角算符进度: 31/64, 当前算符: D_(0, 2) * D_(30, 32), 耗时: 6.019s
[2025-07-25 04:08:15] SW-NE对角算符进度: 32/64, 当前算符: D_(0, 2) * D_(31, 17), 耗时: 6.018s
[2025-07-25 04:08:21] SW-NE对角算符进度: 33/64, 当前算符: D_(0, 2) * D_(32, 34), 耗时: 6.016s
[2025-07-25 04:08:27] SW-NE对角算符进度: 34/64, 当前算符: D_(0, 2) * D_(33, 51), 耗时: 5.973s
[2025-07-25 04:08:33] SW-NE对角算符进度: 35/64, 当前算符: D_(0, 2) * D_(34, 52), 耗时: 5.972s
[2025-07-25 04:08:39] SW-NE对角算符进度: 36/64, 当前算符: D_(0, 2) * D_(35, 37), 耗时: 5.973s
[2025-07-25 04:08:45] SW-NE对角算符进度: 37/64, 当前算符: D_(0, 2) * D_(36, 38), 耗时: 6.016s
[2025-07-25 04:08:51] SW-NE对角算符进度: 38/64, 当前算符: D_(0, 2) * D_(37, 55), 耗时: 6.012s
[2025-07-25 04:08:57] SW-NE对角算符进度: 39/64, 当前算符: D_(0, 2) * D_(38, 56), 耗时: 5.974s
[2025-07-25 04:09:03] SW-NE对角算符进度: 40/64, 当前算符: D_(0, 2) * D_(39, 41), 耗时: 5.974s
[2025-07-25 04:09:09] SW-NE对角算符进度: 41/64, 当前算符: D_(0, 2) * D_(40, 42), 耗时: 5.973s
[2025-07-25 04:09:15] SW-NE对角算符进度: 42/64, 当前算符: D_(0, 2) * D_(41, 59), 耗时: 6.018s
[2025-07-25 04:09:21] SW-NE对角算符进度: 43/64, 当前算符: D_(0, 2) * D_(42, 60), 耗时: 6.011s
[2025-07-25 04:09:27] SW-NE对角算符进度: 44/64, 当前算符: D_(0, 2) * D_(43, 45), 耗时: 6.016s
[2025-07-25 04:09:33] SW-NE对角算符进度: 45/64, 当前算符: D_(0, 2) * D_(44, 46), 耗时: 5.972s
[2025-07-25 04:09:39] SW-NE对角算符进度: 46/64, 当前算符: D_(0, 2) * D_(45, 63), 耗时: 5.973s
[2025-07-25 04:09:45] SW-NE对角算符进度: 47/64, 当前算符: D_(0, 2) * D_(46, 48), 耗时: 6.013s
[2025-07-25 04:09:51] SW-NE对角算符进度: 48/64, 当前算符: D_(0, 2) * D_(47, 33), 耗时: 6.015s
[2025-07-25 04:09:57] SW-NE对角算符进度: 49/64, 当前算符: D_(0, 2) * D_(48, 50), 耗时: 6.016s
[2025-07-25 04:10:03] SW-NE对角算符进度: 50/64, 当前算符: D_(0, 2) * D_(49, 3), 耗时: 5.973s
[2025-07-25 04:10:09] SW-NE对角算符进度: 51/64, 当前算符: D_(0, 2) * D_(50, 4), 耗时: 5.972s
[2025-07-25 04:10:15] SW-NE对角算符进度: 52/64, 当前算符: D_(0, 2) * D_(51, 53), 耗时: 5.972s
[2025-07-25 04:10:21] SW-NE对角算符进度: 53/64, 当前算符: D_(0, 2) * D_(52, 54), 耗时: 6.013s
[2025-07-25 04:10:27] SW-NE对角算符进度: 54/64, 当前算符: D_(0, 2) * D_(53, 7), 耗时: 6.011s
[2025-07-25 04:10:33] SW-NE对角算符进度: 55/64, 当前算符: D_(0, 2) * D_(54, 8), 耗时: 5.972s
[2025-07-25 04:10:39] SW-NE对角算符进度: 56/64, 当前算符: D_(0, 2) * D_(55, 57), 耗时: 5.972s
[2025-07-25 04:10:45] SW-NE对角算符进度: 57/64, 当前算符: D_(0, 2) * D_(56, 58), 耗时: 5.971s
[2025-07-25 04:10:51] SW-NE对角算符进度: 58/64, 当前算符: D_(0, 2) * D_(57, 11), 耗时: 6.015s
[2025-07-25 04:10:57] SW-NE对角算符进度: 59/64, 当前算符: D_(0, 2) * D_(58, 12), 耗时: 6.011s
[2025-07-25 04:11:03] SW-NE对角算符进度: 60/64, 当前算符: D_(0, 2) * D_(59, 61), 耗时: 6.014s
[2025-07-25 04:11:09] SW-NE对角算符进度: 61/64, 当前算符: D_(0, 2) * D_(60, 62), 耗时: 5.972s
[2025-07-25 04:11:15] SW-NE对角算符进度: 62/64, 当前算符: D_(0, 2) * D_(61, 15), 耗时: 5.974s
[2025-07-25 04:11:20] SW-NE对角算符进度: 63/64, 当前算符: D_(0, 2) * D_(62, 0), 耗时: 4.716s
[2025-07-25 04:11:26] SW-NE对角算符进度: 64/64, 当前算符: D_(0, 2) * D_(63, 49), 耗时: 6.015s
[2025-07-25 04:11:26] 西南-东北方向对角二聚体相关函数计算完成,耗时: 378.81 秒
[2025-07-25 04:11:26] 计算傅里叶变换...
[2025-07-25 04:11:26] 对角二聚体结构因子计算完成
[2025-07-25 04:11:27] 对角二聚体相关函数平均误差: 0.000114
