[2025-07-25 02:58:24] ================================================================================
[2025-07-25 02:58:24] 加载量子态: L=4, J2=1.00, J1=0.83
[2025-07-25 02:58:24] 设置样本数为: 1048576
[2025-07-25 02:58:24] 开始生成共享样本集...
[2025-07-25 02:59:33] 样本生成完成,耗时: 69.611 秒
[2025-07-25 02:59:33] ================================================================================
[2025-07-25 02:59:33] 开始计算自旋结构因子...
[2025-07-25 02:59:33] 初始化操作符缓存...
[2025-07-25 02:59:33] 预构建所有自旋相关操作符...
[2025-07-25 02:59:34] 开始计算自旋相关函数...
[2025-07-25 02:59:41] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 7.695s
[2025-07-25 02:59:50] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 8.356s
[2025-07-25 02:59:53] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 3.587s
[2025-07-25 02:59:57] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 3.587s
[2025-07-25 03:00:00] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 3.586s
[2025-07-25 03:00:04] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 3.585s
[2025-07-25 03:00:08] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 3.586s
[2025-07-25 03:00:11] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 3.567s
[2025-07-25 03:00:15] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 3.578s
[2025-07-25 03:00:18] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 3.586s
[2025-07-25 03:00:22] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 3.586s
[2025-07-25 03:00:26] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 3.586s
[2025-07-25 03:00:29] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 3.577s
[2025-07-25 03:00:33] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 3.567s
[2025-07-25 03:00:36] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 3.577s
[2025-07-25 03:00:40] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 3.587s
[2025-07-25 03:00:43] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 3.586s
[2025-07-25 03:00:47] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 3.574s
[2025-07-25 03:00:51] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 3.574s
[2025-07-25 03:00:54] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 3.647s
[2025-07-25 03:00:58] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 3.586s
[2025-07-25 03:01:01] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 3.586s
[2025-07-25 03:01:05] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 3.587s
[2025-07-25 03:01:09] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 3.573s
[2025-07-25 03:01:12] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 3.574s
[2025-07-25 03:01:16] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 3.586s
[2025-07-25 03:01:19] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 3.586s
[2025-07-25 03:01:23] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 3.586s
[2025-07-25 03:01:26] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 3.574s
[2025-07-25 03:01:30] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 3.574s
[2025-07-25 03:01:34] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 3.578s
[2025-07-25 03:01:37] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 3.585s
[2025-07-25 03:01:41] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 3.586s
[2025-07-25 03:01:44] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 3.572s
[2025-07-25 03:01:48] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 3.575s
[2025-07-25 03:01:52] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 3.577s
[2025-07-25 03:01:55] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 3.586s
[2025-07-25 03:01:59] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 3.586s
[2025-07-25 03:02:02] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 3.586s
[2025-07-25 03:02:06] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 3.570s
[2025-07-25 03:02:09] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 3.576s
[2025-07-25 03:02:13] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 3.587s
[2025-07-25 03:02:17] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 3.585s
[2025-07-25 03:02:20] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 3.585s
[2025-07-25 03:02:24] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 3.571s
[2025-07-25 03:02:27] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 3.572s
[2025-07-25 03:02:31] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 3.575s
[2025-07-25 03:02:34] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 3.577s
[2025-07-25 03:02:38] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 3.577s
[2025-07-25 03:02:42] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 3.586s
[2025-07-25 03:02:45] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 3.586s
[2025-07-25 03:02:49] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 3.588s
[2025-07-25 03:02:52] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 3.578s
[2025-07-25 03:02:56] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 3.576s
[2025-07-25 03:03:00] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 3.570s
[2025-07-25 03:03:03] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 3.590s
[2025-07-25 03:03:07] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 3.590s
[2025-07-25 03:03:10] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 3.578s
[2025-07-25 03:03:14] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 3.576s
[2025-07-25 03:03:17] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 3.577s
[2025-07-25 03:03:21] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 3.588s
[2025-07-25 03:03:25] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 3.589s
[2025-07-25 03:03:28] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 3.590s
[2025-07-25 03:03:32] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 3.568s
[2025-07-25 03:03:32] 自旋相关函数计算完成,总耗时 238.20 秒
[2025-07-25 03:03:32] 计算傅里叶变换...
[2025-07-25 03:03:32] 自旋结构因子计算完成
[2025-07-25 03:03:33] 自旋相关函数平均误差: 0.000532
[2025-07-25 03:03:33] ================================================================================
[2025-07-25 03:03:33] 开始计算对角二聚体结构因子...
[2025-07-25 03:03:33] 识别所有对角二聚体...
[2025-07-25 03:03:33] 总共找到 64 个西北-东南方向对角二聚体和 64 个西南-东北方向对角二聚体
[2025-07-25 03:03:33] 预计算对角二聚体操作符...
[2025-07-25 03:03:35] 开始计算西北-东南方向对角二聚体相关函数 (64 个算符)...
[2025-07-25 03:03:39] NW-SE对角算符进度: 1/64, 当前算符: D_(0, 14) * D_(0, 14), 耗时: 3.616s
[2025-07-25 03:03:50] NW-SE对角算符进度: 2/64, 当前算符: D_(0, 14) * D_(1, 31), 耗时: 10.879s
[2025-07-25 03:03:56] NW-SE对角算符进度: 3/64, 当前算符: D_(0, 14) * D_(2, 16), 耗时: 5.973s
[2025-07-25 03:04:02] NW-SE对角算符进度: 4/64, 当前算符: D_(0, 14) * D_(3, 1), 耗时: 5.969s
[2025-07-25 03:04:08] NW-SE对角算符进度: 5/64, 当前算符: D_(0, 14) * D_(4, 2), 耗时: 5.981s
[2025-07-25 03:04:14] NW-SE对角算符进度: 6/64, 当前算符: D_(0, 14) * D_(5, 19), 耗时: 5.981s
[2025-07-25 03:04:20] NW-SE对角算符进度: 7/64, 当前算符: D_(0, 14) * D_(6, 20), 耗时: 5.954s
[2025-07-25 03:04:25] NW-SE对角算符进度: 8/64, 当前算符: D_(0, 14) * D_(7, 5), 耗时: 5.975s
[2025-07-25 03:04:31] NW-SE对角算符进度: 9/64, 当前算符: D_(0, 14) * D_(8, 6), 耗时: 5.982s
[2025-07-25 03:04:37] NW-SE对角算符进度: 10/64, 当前算符: D_(0, 14) * D_(9, 23), 耗时: 5.979s
[2025-07-25 03:04:43] NW-SE对角算符进度: 11/64, 当前算符: D_(0, 14) * D_(10, 24), 耗时: 5.981s
[2025-07-25 03:04:49] NW-SE对角算符进度: 12/64, 当前算符: D_(0, 14) * D_(11, 9), 耗时: 5.981s
[2025-07-25 03:04:55] NW-SE对角算符进度: 13/64, 当前算符: D_(0, 14) * D_(12, 10), 耗时: 5.970s
[2025-07-25 03:05:01] NW-SE对角算符进度: 14/64, 当前算符: D_(0, 14) * D_(13, 27), 耗时: 5.950s
[2025-07-25 03:05:15] NW-SE对角算符进度: 15/64, 当前算符: D_(0, 14) * D_(14, 28), 耗时: 13.163s
[2025-07-25 03:05:21] NW-SE对角算符进度: 16/64, 当前算符: D_(0, 14) * D_(15, 13), 耗时: 6.000s
[2025-07-25 03:05:27] NW-SE对角算符进度: 17/64, 当前算符: D_(0, 14) * D_(16, 30), 耗时: 5.997s
[2025-07-25 03:05:32] NW-SE对角算符进度: 18/64, 当前算符: D_(0, 14) * D_(17, 47), 耗时: 5.975s
[2025-07-25 03:05:38] NW-SE对角算符进度: 19/64, 当前算符: D_(0, 14) * D_(18, 32), 耗时: 5.979s
[2025-07-25 03:05:44] NW-SE对角算符进度: 20/64, 当前算符: D_(0, 14) * D_(19, 17), 耗时: 5.974s
[2025-07-25 03:05:50] NW-SE对角算符进度: 21/64, 当前算符: D_(0, 14) * D_(20, 18), 耗时: 5.998s
[2025-07-25 03:05:56] NW-SE对角算符进度: 22/64, 当前算符: D_(0, 14) * D_(21, 35), 耗时: 5.997s
[2025-07-25 03:06:02] NW-SE对角算符进度: 23/64, 当前算符: D_(0, 14) * D_(22, 36), 耗时: 5.973s
[2025-07-25 03:06:08] NW-SE对角算符进度: 24/64, 当前算符: D_(0, 14) * D_(23, 21), 耗时: 5.979s
[2025-07-25 03:06:14] NW-SE对角算符进度: 25/64, 当前算符: D_(0, 14) * D_(24, 22), 耗时: 5.979s
[2025-07-25 03:06:20] NW-SE对角算符进度: 26/64, 当前算符: D_(0, 14) * D_(25, 39), 耗时: 6.001s
[2025-07-25 03:06:26] NW-SE对角算符进度: 27/64, 当前算符: D_(0, 14) * D_(26, 40), 耗时: 6.000s
[2025-07-25 03:06:32] NW-SE对角算符进度: 28/64, 当前算符: D_(0, 14) * D_(27, 25), 耗时: 5.998s
[2025-07-25 03:06:38] NW-SE对角算符进度: 29/64, 当前算符: D_(0, 14) * D_(28, 26), 耗时: 5.976s
[2025-07-25 03:06:44] NW-SE对角算符进度: 30/64, 当前算符: D_(0, 14) * D_(29, 43), 耗时: 5.959s
[2025-07-25 03:06:50] NW-SE对角算符进度: 31/64, 当前算符: D_(0, 14) * D_(30, 44), 耗时: 5.999s
[2025-07-25 03:06:56] NW-SE对角算符进度: 32/64, 当前算符: D_(0, 14) * D_(31, 29), 耗时: 5.997s
[2025-07-25 03:07:02] NW-SE对角算符进度: 33/64, 当前算符: D_(0, 14) * D_(32, 46), 耗时: 6.001s
[2025-07-25 03:07:08] NW-SE对角算符进度: 34/64, 当前算符: D_(0, 14) * D_(33, 63), 耗时: 5.975s
[2025-07-25 03:07:14] NW-SE对角算符进度: 35/64, 当前算符: D_(0, 14) * D_(34, 48), 耗时: 5.971s
[2025-07-25 03:07:20] NW-SE对角算符进度: 36/64, 当前算符: D_(0, 14) * D_(35, 33), 耗时: 5.977s
[2025-07-25 03:07:26] NW-SE对角算符进度: 37/64, 当前算符: D_(0, 14) * D_(36, 34), 耗时: 6.002s
[2025-07-25 03:07:32] NW-SE对角算符进度: 38/64, 当前算符: D_(0, 14) * D_(37, 51), 耗时: 5.999s
[2025-07-25 03:07:38] NW-SE对角算符进度: 39/64, 当前算符: D_(0, 14) * D_(38, 52), 耗时: 5.977s
[2025-07-25 03:07:44] NW-SE对角算符进度: 40/64, 当前算符: D_(0, 14) * D_(39, 37), 耗时: 5.983s
[2025-07-25 03:07:50] NW-SE对角算符进度: 41/64, 当前算符: D_(0, 14) * D_(40, 38), 耗时: 5.983s
[2025-07-25 03:07:56] NW-SE对角算符进度: 42/64, 当前算符: D_(0, 14) * D_(41, 55), 耗时: 5.997s
[2025-07-25 03:08:02] NW-SE对角算符进度: 43/64, 当前算符: D_(0, 14) * D_(42, 56), 耗时: 6.000s
[2025-07-25 03:08:08] NW-SE对角算符进度: 44/64, 当前算符: D_(0, 14) * D_(43, 41), 耗时: 5.998s
[2025-07-25 03:08:14] NW-SE对角算符进度: 45/64, 当前算符: D_(0, 14) * D_(44, 42), 耗时: 5.967s
[2025-07-25 03:08:20] NW-SE对角算符进度: 46/64, 当前算符: D_(0, 14) * D_(45, 59), 耗时: 5.966s
[2025-07-25 03:08:26] NW-SE对角算符进度: 47/64, 当前算符: D_(0, 14) * D_(46, 60), 耗时: 5.984s
[2025-07-25 03:08:32] NW-SE对角算符进度: 48/64, 当前算符: D_(0, 14) * D_(47, 45), 耗时: 5.964s
[2025-07-25 03:08:38] NW-SE对角算符进度: 49/64, 当前算符: D_(0, 14) * D_(48, 62), 耗时: 5.978s
[2025-07-25 03:08:44] NW-SE对角算符进度: 50/64, 当前算符: D_(0, 14) * D_(49, 15), 耗时: 5.999s
[2025-07-25 03:08:49] NW-SE对角算符进度: 51/64, 当前算符: D_(0, 14) * D_(50, 0), 耗时: 4.695s
[2025-07-25 03:08:55] NW-SE对角算符进度: 52/64, 当前算符: D_(0, 14) * D_(51, 49), 耗时: 5.999s
[2025-07-25 03:09:01] NW-SE对角算符进度: 53/64, 当前算符: D_(0, 14) * D_(52, 50), 耗时: 5.976s
[2025-07-25 03:09:07] NW-SE对角算符进度: 54/64, 当前算符: D_(0, 14) * D_(53, 3), 耗时: 5.976s
[2025-07-25 03:09:13] NW-SE对角算符进度: 55/64, 当前算符: D_(0, 14) * D_(54, 4), 耗时: 5.998s
[2025-07-25 03:09:19] NW-SE对角算符进度: 56/64, 当前算符: D_(0, 14) * D_(55, 53), 耗时: 6.000s
[2025-07-25 03:09:25] NW-SE对角算符进度: 57/64, 当前算符: D_(0, 14) * D_(56, 54), 耗时: 5.998s
[2025-07-25 03:09:31] NW-SE对角算符进度: 58/64, 当前算符: D_(0, 14) * D_(57, 7), 耗时: 5.980s
[2025-07-25 03:09:37] NW-SE对角算符进度: 59/64, 当前算符: D_(0, 14) * D_(58, 8), 耗时: 5.978s
[2025-07-25 03:09:43] NW-SE对角算符进度: 60/64, 当前算符: D_(0, 14) * D_(59, 57), 耗时: 5.961s
[2025-07-25 03:09:49] NW-SE对角算符进度: 61/64, 当前算符: D_(0, 14) * D_(60, 58), 耗时: 6.003s
[2025-07-25 03:09:55] NW-SE对角算符进度: 62/64, 当前算符: D_(0, 14) * D_(61, 11), 耗时: 6.004s
[2025-07-25 03:10:01] NW-SE对角算符进度: 63/64, 当前算符: D_(0, 14) * D_(62, 12), 耗时: 5.979s
[2025-07-25 03:10:07] NW-SE对角算符进度: 64/64, 当前算符: D_(0, 14) * D_(63, 61), 耗时: 5.975s
[2025-07-25 03:10:07] 西北-东南方向对角二聚体相关函数计算完成,耗时: 391.44 秒
[2025-07-25 03:10:07] ================================================================================
[2025-07-25 03:10:07] 开始计算西南-东北方向对角二聚体相关函数 (64 个算符)...
[2025-07-25 03:10:10] SW-NE对角算符进度: 1/64, 当前算符: D_(0, 2) * D_(0, 2), 耗时: 3.583s
[2025-07-25 03:10:16] SW-NE对角算符进度: 2/64, 当前算符: D_(0, 2) * D_(1, 19), 耗时: 5.998s
[2025-07-25 03:10:21] SW-NE对角算符进度: 3/64, 当前算符: D_(0, 2) * D_(2, 20), 耗时: 4.700s
[2025-07-25 03:10:27] SW-NE对角算符进度: 4/64, 当前算符: D_(0, 2) * D_(3, 5), 耗时: 6.000s
[2025-07-25 03:10:33] SW-NE对角算符进度: 5/64, 当前算符: D_(0, 2) * D_(4, 6), 耗时: 5.979s
[2025-07-25 03:10:39] SW-NE对角算符进度: 6/64, 当前算符: D_(0, 2) * D_(5, 23), 耗时: 5.979s
[2025-07-25 03:10:45] SW-NE对角算符进度: 7/64, 当前算符: D_(0, 2) * D_(6, 24), 耗时: 6.001s
[2025-07-25 03:10:51] SW-NE对角算符进度: 8/64, 当前算符: D_(0, 2) * D_(7, 9), 耗时: 6.003s
[2025-07-25 03:10:57] SW-NE对角算符进度: 9/64, 当前算符: D_(0, 2) * D_(8, 10), 耗时: 5.997s
[2025-07-25 03:11:03] SW-NE对角算符进度: 10/64, 当前算符: D_(0, 2) * D_(9, 27), 耗时: 5.973s
[2025-07-25 03:11:09] SW-NE对角算符进度: 11/64, 当前算符: D_(0, 2) * D_(10, 28), 耗时: 5.974s
[2025-07-25 03:11:15] SW-NE对角算符进度: 12/64, 当前算符: D_(0, 2) * D_(11, 13), 耗时: 5.970s
[2025-07-25 03:11:21] SW-NE对角算符进度: 13/64, 当前算符: D_(0, 2) * D_(12, 14), 耗时: 5.999s
[2025-07-25 03:11:27] SW-NE对角算符进度: 14/64, 当前算符: D_(0, 2) * D_(13, 31), 耗时: 6.000s
[2025-07-25 03:11:33] SW-NE对角算符进度: 15/64, 当前算符: D_(0, 2) * D_(14, 16), 耗时: 5.976s
[2025-07-25 03:11:39] SW-NE对角算符进度: 16/64, 当前算符: D_(0, 2) * D_(15, 1), 耗时: 5.972s
[2025-07-25 03:11:45] SW-NE对角算符进度: 17/64, 当前算符: D_(0, 2) * D_(16, 18), 耗时: 5.978s
[2025-07-25 03:11:51] SW-NE对角算符进度: 18/64, 当前算符: D_(0, 2) * D_(17, 35), 耗时: 6.002s
[2025-07-25 03:11:57] SW-NE对角算符进度: 19/64, 当前算符: D_(0, 2) * D_(18, 36), 耗时: 6.004s
[2025-07-25 03:12:03] SW-NE对角算符进度: 20/64, 当前算符: D_(0, 2) * D_(19, 21), 耗时: 6.002s
[2025-07-25 03:12:09] SW-NE对角算符进度: 21/64, 当前算符: D_(0, 2) * D_(20, 22), 耗时: 5.964s
[2025-07-25 03:12:15] SW-NE对角算符进度: 22/64, 当前算符: D_(0, 2) * D_(21, 39), 耗时: 5.982s
[2025-07-25 03:12:21] SW-NE对角算符进度: 23/64, 当前算符: D_(0, 2) * D_(22, 40), 耗时: 6.020s
[2025-07-25 03:12:27] SW-NE对角算符进度: 24/64, 当前算符: D_(0, 2) * D_(23, 25), 耗时: 6.002s
[2025-07-25 03:12:33] SW-NE对角算符进度: 25/64, 当前算符: D_(0, 2) * D_(24, 26), 耗时: 6.004s
[2025-07-25 03:12:39] SW-NE对角算符进度: 26/64, 当前算符: D_(0, 2) * D_(25, 43), 耗时: 6.003s
[2025-07-25 03:12:45] SW-NE对角算符进度: 27/64, 当前算符: D_(0, 2) * D_(26, 44), 耗时: 6.004s
[2025-07-25 03:12:51] SW-NE对角算符进度: 28/64, 当前算符: D_(0, 2) * D_(27, 29), 耗时: 6.005s
[2025-07-25 03:12:57] SW-NE对角算符进度: 29/64, 当前算符: D_(0, 2) * D_(28, 30), 耗时: 5.985s
[2025-07-25 03:13:03] SW-NE对角算符进度: 30/64, 当前算符: D_(0, 2) * D_(29, 47), 耗时: 5.985s
[2025-07-25 03:13:09] SW-NE对角算符进度: 31/64, 当前算符: D_(0, 2) * D_(30, 32), 耗时: 6.003s
[2025-07-25 03:13:15] SW-NE对角算符进度: 32/64, 当前算符: D_(0, 2) * D_(31, 17), 耗时: 6.002s
[2025-07-25 03:13:21] SW-NE对角算符进度: 33/64, 当前算符: D_(0, 2) * D_(32, 34), 耗时: 6.003s
[2025-07-25 03:13:27] SW-NE对角算符进度: 34/64, 当前算符: D_(0, 2) * D_(33, 51), 耗时: 5.981s
[2025-07-25 03:13:33] SW-NE对角算符进度: 35/64, 当前算符: D_(0, 2) * D_(34, 52), 耗时: 5.977s
[2025-07-25 03:13:39] SW-NE对角算符进度: 36/64, 当前算符: D_(0, 2) * D_(35, 37), 耗时: 5.967s
[2025-07-25 03:13:45] SW-NE对角算符进度: 37/64, 当前算符: D_(0, 2) * D_(36, 38), 耗时: 5.998s
[2025-07-25 03:13:51] SW-NE对角算符进度: 38/64, 当前算符: D_(0, 2) * D_(37, 55), 耗时: 6.002s
[2025-07-25 03:13:57] SW-NE对角算符进度: 39/64, 当前算符: D_(0, 2) * D_(38, 56), 耗时: 5.977s
[2025-07-25 03:14:03] SW-NE对角算符进度: 40/64, 当前算符: D_(0, 2) * D_(39, 41), 耗时: 5.978s
[2025-07-25 03:14:09] SW-NE对角算符进度: 41/64, 当前算符: D_(0, 2) * D_(40, 42), 耗时: 5.961s
[2025-07-25 03:14:15] SW-NE对角算符进度: 42/64, 当前算符: D_(0, 2) * D_(41, 59), 耗时: 6.004s
[2025-07-25 03:14:21] SW-NE对角算符进度: 43/64, 当前算符: D_(0, 2) * D_(42, 60), 耗时: 6.002s
[2025-07-25 03:14:27] SW-NE对角算符进度: 44/64, 当前算符: D_(0, 2) * D_(43, 45), 耗时: 6.001s
[2025-07-25 03:14:33] SW-NE对角算符进度: 45/64, 当前算符: D_(0, 2) * D_(44, 46), 耗时: 5.977s
[2025-07-25 03:14:39] SW-NE对角算符进度: 46/64, 当前算符: D_(0, 2) * D_(45, 63), 耗时: 5.980s
[2025-07-25 03:14:45] SW-NE对角算符进度: 47/64, 当前算符: D_(0, 2) * D_(46, 48), 耗时: 6.001s
[2025-07-25 03:14:51] SW-NE对角算符进度: 48/64, 当前算符: D_(0, 2) * D_(47, 33), 耗时: 5.998s
[2025-07-25 03:14:57] SW-NE对角算符进度: 49/64, 当前算符: D_(0, 2) * D_(48, 50), 耗时: 5.999s
[2025-07-25 03:15:02] SW-NE对角算符进度: 50/64, 当前算符: D_(0, 2) * D_(49, 3), 耗时: 5.976s
[2025-07-25 03:15:08] SW-NE对角算符进度: 51/64, 当前算符: D_(0, 2) * D_(50, 4), 耗时: 5.977s
[2025-07-25 03:15:14] SW-NE对角算符进度: 52/64, 当前算符: D_(0, 2) * D_(51, 53), 耗时: 5.968s
[2025-07-25 03:15:20] SW-NE对角算符进度: 53/64, 当前算符: D_(0, 2) * D_(52, 54), 耗时: 6.004s
[2025-07-25 03:15:26] SW-NE对角算符进度: 54/64, 当前算符: D_(0, 2) * D_(53, 7), 耗时: 6.003s
[2025-07-25 03:15:32] SW-NE对角算符进度: 55/64, 当前算符: D_(0, 2) * D_(54, 8), 耗时: 5.981s
[2025-07-25 03:15:38] SW-NE对角算符进度: 56/64, 当前算符: D_(0, 2) * D_(55, 57), 耗时: 5.966s
[2025-07-25 03:15:44] SW-NE对角算符进度: 57/64, 当前算符: D_(0, 2) * D_(56, 58), 耗时: 5.968s
[2025-07-25 03:15:50] SW-NE对角算符进度: 58/64, 当前算符: D_(0, 2) * D_(57, 11), 耗时: 6.002s
[2025-07-25 03:15:56] SW-NE对角算符进度: 59/64, 当前算符: D_(0, 2) * D_(58, 12), 耗时: 6.004s
[2025-07-25 03:16:02] SW-NE对角算符进度: 60/64, 当前算符: D_(0, 2) * D_(59, 61), 耗时: 6.000s
[2025-07-25 03:16:08] SW-NE对角算符进度: 61/64, 当前算符: D_(0, 2) * D_(60, 62), 耗时: 5.976s
[2025-07-25 03:16:14] SW-NE对角算符进度: 62/64, 当前算符: D_(0, 2) * D_(61, 15), 耗时: 5.977s
[2025-07-25 03:16:19] SW-NE对角算符进度: 63/64, 当前算符: D_(0, 2) * D_(62, 0), 耗时: 4.695s
[2025-07-25 03:16:25] SW-NE对角算符进度: 64/64, 当前算符: D_(0, 2) * D_(63, 49), 耗时: 5.999s
[2025-07-25 03:16:25] 西南-东北方向对角二聚体相关函数计算完成,耗时: 378.42 秒
[2025-07-25 03:16:25] 计算傅里叶变换...
[2025-07-25 03:16:26] 对角二聚体结构因子计算完成
[2025-07-25 03:16:27] 对角二聚体相关函数平均误差: 0.000116
