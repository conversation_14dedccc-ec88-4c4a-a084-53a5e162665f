[2025-07-25 01:45:16] ================================================================================
[2025-07-25 01:45:16] 加载量子态: L=4, J2=1.00, J1=0.79
[2025-07-25 01:45:16] 设置样本数为: 1048576
[2025-07-25 01:45:16] 开始生成共享样本集...
[2025-07-25 01:46:26] 样本生成完成,耗时: 69.855 秒
[2025-07-25 01:46:26] ================================================================================
[2025-07-25 01:46:26] 开始计算自旋结构因子...
[2025-07-25 01:46:26] 初始化操作符缓存...
[2025-07-25 01:46:26] 预构建所有自旋相关操作符...
[2025-07-25 01:46:26] 开始计算自旋相关函数...
[2025-07-25 01:46:34] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 7.782s
[2025-07-25 01:46:42] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 8.352s
[2025-07-25 01:46:45] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 3.571s
[2025-07-25 01:46:49] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 3.587s
[2025-07-25 01:46:53] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 3.587s
[2025-07-25 01:46:56] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 3.670s
[2025-07-25 01:47:00] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 3.586s
[2025-07-25 01:47:03] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 3.585s
[2025-07-25 01:47:07] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 3.564s
[2025-07-25 01:47:11] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 3.587s
[2025-07-25 01:47:14] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 3.570s
[2025-07-25 01:47:18] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 3.568s
[2025-07-25 01:47:21] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 3.588s
[2025-07-25 01:47:25] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 3.575s
[2025-07-25 01:47:28] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 3.588s
[2025-07-25 01:47:32] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 3.587s
[2025-07-25 01:47:36] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 3.575s
[2025-07-25 01:47:39] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 3.573s
[2025-07-25 01:47:43] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 3.587s
[2025-07-25 01:47:46] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 3.587s
[2025-07-25 01:47:50] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 3.573s
[2025-07-25 01:47:54] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 3.571s
[2025-07-25 01:47:57] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 3.585s
[2025-07-25 01:48:01] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 3.571s
[2025-07-25 01:48:04] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 3.587s
[2025-07-25 01:48:08] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 3.587s
[2025-07-25 01:48:11] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 3.575s
[2025-07-25 01:48:15] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 3.587s
[2025-07-25 01:48:19] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 3.569s
[2025-07-25 01:48:22] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 3.572s
[2025-07-25 01:48:26] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 3.587s
[2025-07-25 01:48:29] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 3.569s
[2025-07-25 01:48:33] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 3.587s
[2025-07-25 01:48:37] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 3.587s
[2025-07-25 01:48:40] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 3.577s
[2025-07-25 01:48:44] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 3.587s
[2025-07-25 01:48:47] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 3.572s
[2025-07-25 01:48:51] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 3.575s
[2025-07-25 01:48:54] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 3.587s
[2025-07-25 01:48:58] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 3.576s
[2025-07-25 01:49:02] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 3.587s
[2025-07-25 01:49:05] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 3.587s
[2025-07-25 01:49:09] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 3.588s
[2025-07-25 01:49:12] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 3.570s
[2025-07-25 01:49:16] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 3.588s
[2025-07-25 01:49:20] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 3.585s
[2025-07-25 01:49:23] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 3.566s
[2025-07-25 01:49:27] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 3.588s
[2025-07-25 01:49:30] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 3.575s
[2025-07-25 01:49:34] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 3.572s
[2025-07-25 01:49:37] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 3.588s
[2025-07-25 01:49:41] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 3.574s
[2025-07-25 01:49:45] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 3.587s
[2025-07-25 01:49:48] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 3.585s
[2025-07-25 01:49:52] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 3.575s
[2025-07-25 01:49:55] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 3.588s
[2025-07-25 01:49:59] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 3.572s
[2025-07-25 01:50:02] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 3.577s
[2025-07-25 01:50:06] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 3.589s
[2025-07-25 01:50:10] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 3.565s
[2025-07-25 01:50:13] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 3.586s
[2025-07-25 01:50:17] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 3.588s
[2025-07-25 01:50:20] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 3.573s
[2025-07-25 01:50:24] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 3.576s
[2025-07-25 01:50:24] 自旋相关函数计算完成,总耗时 238.26 秒
[2025-07-25 01:50:24] 计算傅里叶变换...
[2025-07-25 01:50:25] 自旋结构因子计算完成
[2025-07-25 01:50:25] 自旋相关函数平均误差: 0.000534
[2025-07-25 01:50:25] ================================================================================
[2025-07-25 01:50:25] 开始计算对角二聚体结构因子...
[2025-07-25 01:50:25] 识别所有对角二聚体...
[2025-07-25 01:50:25] 总共找到 64 个西北-东南方向对角二聚体和 64 个西南-东北方向对角二聚体
[2025-07-25 01:50:25] 预计算对角二聚体操作符...
[2025-07-25 01:50:27] 开始计算西北-东南方向对角二聚体相关函数 (64 个算符)...
[2025-07-25 01:50:31] NW-SE对角算符进度: 1/64, 当前算符: D_(0, 14) * D_(0, 14), 耗时: 3.614s
[2025-07-25 01:50:42] NW-SE对角算符进度: 2/64, 当前算符: D_(0, 14) * D_(1, 31), 耗时: 10.948s
[2025-07-25 01:50:48] NW-SE对角算符进度: 3/64, 当前算符: D_(0, 14) * D_(2, 16), 耗时: 6.001s
[2025-07-25 01:50:54] NW-SE对角算符进度: 4/64, 当前算符: D_(0, 14) * D_(3, 1), 耗时: 6.000s
[2025-07-25 01:51:00] NW-SE对角算符进度: 5/64, 当前算符: D_(0, 14) * D_(4, 2), 耗时: 5.976s
[2025-07-25 01:51:06] NW-SE对角算符进度: 6/64, 当前算符: D_(0, 14) * D_(5, 19), 耗时: 5.999s
[2025-07-25 01:51:12] NW-SE对角算符进度: 7/64, 当前算符: D_(0, 14) * D_(6, 20), 耗时: 5.980s
[2025-07-25 01:51:18] NW-SE对角算符进度: 8/64, 当前算符: D_(0, 14) * D_(7, 5), 耗时: 5.999s
[2025-07-25 01:51:24] NW-SE对角算符进度: 9/64, 当前算符: D_(0, 14) * D_(8, 6), 耗时: 5.958s
[2025-07-25 01:51:30] NW-SE对角算符进度: 10/64, 当前算符: D_(0, 14) * D_(9, 23), 耗时: 6.001s
[2025-07-25 01:51:36] NW-SE对角算符进度: 11/64, 当前算符: D_(0, 14) * D_(10, 24), 耗时: 5.970s
[2025-07-25 01:51:42] NW-SE对角算符进度: 12/64, 当前算符: D_(0, 14) * D_(11, 9), 耗时: 5.973s
[2025-07-25 01:51:48] NW-SE对角算符进度: 13/64, 当前算符: D_(0, 14) * D_(12, 10), 耗时: 6.000s
[2025-07-25 01:51:54] NW-SE对角算符进度: 14/64, 当前算符: D_(0, 14) * D_(13, 27), 耗时: 5.980s
[2025-07-25 01:52:07] NW-SE对角算符进度: 15/64, 当前算符: D_(0, 14) * D_(14, 28), 耗时: 13.184s
[2025-07-25 01:52:13] NW-SE对角算符进度: 16/64, 当前算符: D_(0, 14) * D_(15, 13), 耗时: 6.006s
[2025-07-25 01:52:19] NW-SE对角算符进度: 17/64, 当前算符: D_(0, 14) * D_(16, 30), 耗时: 5.971s
[2025-07-25 01:52:25] NW-SE对角算符进度: 18/64, 当前算符: D_(0, 14) * D_(17, 47), 耗时: 5.967s
[2025-07-25 01:52:31] NW-SE对角算符进度: 19/64, 当前算符: D_(0, 14) * D_(18, 32), 耗时: 6.005s
[2025-07-25 01:52:37] NW-SE对角算符进度: 20/64, 当前算符: D_(0, 14) * D_(19, 17), 耗时: 5.957s
[2025-07-25 01:52:43] NW-SE对角算符进度: 21/64, 当前算符: D_(0, 14) * D_(20, 18), 耗时: 6.004s
[2025-07-25 01:52:49] NW-SE对角算符进度: 22/64, 当前算符: D_(0, 14) * D_(21, 35), 耗时: 5.977s
[2025-07-25 01:52:55] NW-SE对角算符进度: 23/64, 当前算符: D_(0, 14) * D_(22, 36), 耗时: 5.999s
[2025-07-25 01:53:01] NW-SE对角算符进度: 24/64, 当前算符: D_(0, 14) * D_(23, 21), 耗时: 5.976s
[2025-07-25 01:53:07] NW-SE对角算符进度: 25/64, 当前算符: D_(0, 14) * D_(24, 22), 耗时: 6.001s
[2025-07-25 01:53:13] NW-SE对角算符进度: 26/64, 当前算符: D_(0, 14) * D_(25, 39), 耗时: 5.982s
[2025-07-25 01:53:19] NW-SE对角算符进度: 27/64, 当前算符: D_(0, 14) * D_(26, 40), 耗时: 6.004s
[2025-07-25 01:53:25] NW-SE对角算符进度: 28/64, 当前算符: D_(0, 14) * D_(27, 25), 耗时: 5.963s
[2025-07-25 01:53:31] NW-SE对角算符进度: 29/64, 当前算符: D_(0, 14) * D_(28, 26), 耗时: 6.005s
[2025-07-25 01:53:37] NW-SE对角算符进度: 30/64, 当前算符: D_(0, 14) * D_(29, 43), 耗时: 5.974s
[2025-07-25 01:53:43] NW-SE对角算符进度: 31/64, 当前算符: D_(0, 14) * D_(30, 44), 耗时: 6.001s
[2025-07-25 01:53:49] NW-SE对角算符进度: 32/64, 当前算符: D_(0, 14) * D_(31, 29), 耗时: 5.975s
[2025-07-25 01:53:55] NW-SE对角算符进度: 33/64, 当前算符: D_(0, 14) * D_(32, 46), 耗时: 6.004s
[2025-07-25 01:54:01] NW-SE对角算符进度: 34/64, 当前算符: D_(0, 14) * D_(33, 63), 耗时: 6.004s
[2025-07-25 01:54:07] NW-SE对角算符进度: 35/64, 当前算符: D_(0, 14) * D_(34, 48), 耗时: 6.002s
[2025-07-25 01:54:13] NW-SE对角算符进度: 36/64, 当前算符: D_(0, 14) * D_(35, 33), 耗时: 5.981s
[2025-07-25 01:54:19] NW-SE对角算符进度: 37/64, 当前算符: D_(0, 14) * D_(36, 34), 耗时: 5.997s
[2025-07-25 01:54:25] NW-SE对角算符进度: 38/64, 当前算符: D_(0, 14) * D_(37, 51), 耗时: 5.977s
[2025-07-25 01:54:31] NW-SE对角算符进度: 39/64, 当前算符: D_(0, 14) * D_(38, 52), 耗时: 6.004s
[2025-07-25 01:54:37] NW-SE对角算符进度: 40/64, 当前算符: D_(0, 14) * D_(39, 37), 耗时: 5.969s
[2025-07-25 01:54:43] NW-SE对角算符进度: 41/64, 当前算符: D_(0, 14) * D_(40, 38), 耗时: 5.999s
[2025-07-25 01:54:49] NW-SE对角算符进度: 42/64, 当前算符: D_(0, 14) * D_(41, 55), 耗时: 5.977s
[2025-07-25 01:54:55] NW-SE对角算符进度: 43/64, 当前算符: D_(0, 14) * D_(42, 56), 耗时: 6.004s
[2025-07-25 01:55:01] NW-SE对角算符进度: 44/64, 当前算符: D_(0, 14) * D_(43, 41), 耗时: 5.967s
[2025-07-25 01:55:07] NW-SE对角算符进度: 45/64, 当前算符: D_(0, 14) * D_(44, 42), 耗时: 6.002s
[2025-07-25 01:55:13] NW-SE对角算符进度: 46/64, 当前算符: D_(0, 14) * D_(45, 59), 耗时: 5.971s
[2025-07-25 01:55:19] NW-SE对角算符进度: 47/64, 当前算符: D_(0, 14) * D_(46, 60), 耗时: 6.002s
[2025-07-25 01:55:25] NW-SE对角算符进度: 48/64, 当前算符: D_(0, 14) * D_(47, 45), 耗时: 5.976s
[2025-07-25 01:55:31] NW-SE对角算符进度: 49/64, 当前算符: D_(0, 14) * D_(48, 62), 耗时: 5.998s
[2025-07-25 01:55:37] NW-SE对角算符进度: 50/64, 当前算符: D_(0, 14) * D_(49, 15), 耗时: 5.998s
[2025-07-25 01:55:41] NW-SE对角算符进度: 51/64, 当前算符: D_(0, 14) * D_(50, 0), 耗时: 4.683s
[2025-07-25 01:55:47] NW-SE对角算符进度: 52/64, 当前算符: D_(0, 14) * D_(51, 49), 耗时: 5.977s
[2025-07-25 01:55:53] NW-SE对角算符进度: 53/64, 当前算符: D_(0, 14) * D_(52, 50), 耗时: 6.003s
[2025-07-25 01:55:59] NW-SE对角算符进度: 54/64, 当前算符: D_(0, 14) * D_(53, 3), 耗时: 5.977s
[2025-07-25 01:56:05] NW-SE对角算符进度: 55/64, 当前算符: D_(0, 14) * D_(54, 4), 耗时: 6.005s
[2025-07-25 01:56:11] NW-SE对角算符进度: 56/64, 当前算符: D_(0, 14) * D_(55, 53), 耗时: 5.975s
[2025-07-25 01:56:17] NW-SE对角算符进度: 57/64, 当前算符: D_(0, 14) * D_(56, 54), 耗时: 6.004s
[2025-07-25 01:56:23] NW-SE对角算符进度: 58/64, 当前算符: D_(0, 14) * D_(57, 7), 耗时: 5.970s
[2025-07-25 01:56:29] NW-SE对角算符进度: 59/64, 当前算符: D_(0, 14) * D_(58, 8), 耗时: 6.004s
[2025-07-25 01:56:35] NW-SE对角算符进度: 60/64, 当前算符: D_(0, 14) * D_(59, 57), 耗时: 5.976s
[2025-07-25 01:56:41] NW-SE对角算符进度: 61/64, 当前算符: D_(0, 14) * D_(60, 58), 耗时: 6.003s
[2025-07-25 01:56:47] NW-SE对角算符进度: 62/64, 当前算符: D_(0, 14) * D_(61, 11), 耗时: 5.969s
[2025-07-25 01:56:53] NW-SE对角算符进度: 63/64, 当前算符: D_(0, 14) * D_(62, 12), 耗时: 5.954s
[2025-07-25 01:56:59] NW-SE对角算符进度: 64/64, 当前算符: D_(0, 14) * D_(63, 61), 耗时: 6.002s
[2025-07-25 01:56:59] 西北-东南方向对角二聚体相关函数计算完成,耗时: 391.77 秒
[2025-07-25 01:56:59] ================================================================================
[2025-07-25 01:56:59] 开始计算西南-东北方向对角二聚体相关函数 (64 个算符)...
[2025-07-25 01:57:03] SW-NE对角算符进度: 1/64, 当前算符: D_(0, 2) * D_(0, 2), 耗时: 3.585s
[2025-07-25 01:57:09] SW-NE对角算符进度: 2/64, 当前算符: D_(0, 2) * D_(1, 19), 耗时: 5.997s
[2025-07-25 01:57:13] SW-NE对角算符进度: 3/64, 当前算符: D_(0, 2) * D_(2, 20), 耗时: 4.691s
[2025-07-25 01:57:19] SW-NE对角算符进度: 4/64, 当前算符: D_(0, 2) * D_(3, 5), 耗时: 6.004s
[2025-07-25 01:57:25] SW-NE对角算符进度: 5/64, 当前算符: D_(0, 2) * D_(4, 6), 耗时: 6.002s
[2025-07-25 01:57:31] SW-NE对角算符进度: 6/64, 当前算符: D_(0, 2) * D_(5, 23), 耗时: 5.957s
[2025-07-25 01:57:37] SW-NE对角算符进度: 7/64, 当前算符: D_(0, 2) * D_(6, 24), 耗时: 6.001s
[2025-07-25 01:57:43] SW-NE对角算符进度: 8/64, 当前算符: D_(0, 2) * D_(7, 9), 耗时: 5.980s
[2025-07-25 01:57:49] SW-NE对角算符进度: 9/64, 当前算符: D_(0, 2) * D_(8, 10), 耗时: 5.980s
[2025-07-25 01:57:55] SW-NE对角算符进度: 10/64, 当前算符: D_(0, 2) * D_(9, 27), 耗时: 6.000s
[2025-07-25 01:58:01] SW-NE对角算符进度: 11/64, 当前算符: D_(0, 2) * D_(10, 28), 耗时: 5.973s
[2025-07-25 01:58:07] SW-NE对角算符进度: 12/64, 当前算符: D_(0, 2) * D_(11, 13), 耗时: 6.000s
[2025-07-25 01:58:13] SW-NE对角算符进度: 13/64, 当前算符: D_(0, 2) * D_(12, 14), 耗时: 5.981s
[2025-07-25 01:58:19] SW-NE对角算符进度: 14/64, 当前算符: D_(0, 2) * D_(13, 31), 耗时: 6.004s
[2025-07-25 01:58:25] SW-NE对角算符进度: 15/64, 当前算符: D_(0, 2) * D_(14, 16), 耗时: 5.955s
[2025-07-25 01:58:31] SW-NE对角算符进度: 16/64, 当前算符: D_(0, 2) * D_(15, 1), 耗时: 6.003s
[2025-07-25 01:58:37] SW-NE对角算符进度: 17/64, 当前算符: D_(0, 2) * D_(16, 18), 耗时: 5.979s
[2025-07-25 01:58:43] SW-NE对角算符进度: 18/64, 当前算符: D_(0, 2) * D_(17, 35), 耗时: 6.002s
[2025-07-25 01:58:49] SW-NE对角算符进度: 19/64, 当前算符: D_(0, 2) * D_(18, 36), 耗时: 5.976s
[2025-07-25 01:58:55] SW-NE对角算符进度: 20/64, 当前算符: D_(0, 2) * D_(19, 21), 耗时: 5.998s
[2025-07-25 01:59:01] SW-NE对角算符进度: 21/64, 当前算符: D_(0, 2) * D_(20, 22), 耗时: 5.964s
[2025-07-25 01:59:07] SW-NE对角算符进度: 22/64, 当前算符: D_(0, 2) * D_(21, 39), 耗时: 5.977s
[2025-07-25 01:59:13] SW-NE对角算符进度: 23/64, 当前算符: D_(0, 2) * D_(22, 40), 耗时: 5.998s
[2025-07-25 01:59:19] SW-NE对角算符进度: 24/64, 当前算符: D_(0, 2) * D_(23, 25), 耗时: 5.982s
[2025-07-25 01:59:25] SW-NE对角算符进度: 25/64, 当前算符: D_(0, 2) * D_(24, 26), 耗时: 5.972s
[2025-07-25 01:59:31] SW-NE对角算符进度: 26/64, 当前算符: D_(0, 2) * D_(25, 43), 耗时: 6.003s
[2025-07-25 01:59:37] SW-NE对角算符进度: 27/64, 当前算符: D_(0, 2) * D_(26, 44), 耗时: 5.972s
[2025-07-25 01:59:43] SW-NE对角算符进度: 28/64, 当前算符: D_(0, 2) * D_(27, 29), 耗时: 6.003s
[2025-07-25 01:59:49] SW-NE对角算符进度: 29/64, 当前算符: D_(0, 2) * D_(28, 30), 耗时: 5.971s
[2025-07-25 01:59:55] SW-NE对角算符进度: 30/64, 当前算符: D_(0, 2) * D_(29, 47), 耗时: 6.002s
[2025-07-25 02:00:01] SW-NE对角算符进度: 31/64, 当前算符: D_(0, 2) * D_(30, 32), 耗时: 5.977s
[2025-07-25 02:00:07] SW-NE对角算符进度: 32/64, 当前算符: D_(0, 2) * D_(31, 17), 耗时: 5.999s
[2025-07-25 02:00:13] SW-NE对角算符进度: 33/64, 当前算符: D_(0, 2) * D_(32, 34), 耗时: 5.977s
[2025-07-25 02:00:19] SW-NE对角算符进度: 34/64, 当前算符: D_(0, 2) * D_(33, 51), 耗时: 6.004s
[2025-07-25 02:00:25] SW-NE对角算符进度: 35/64, 当前算符: D_(0, 2) * D_(34, 52), 耗时: 5.972s
[2025-07-25 02:00:31] SW-NE对角算符进度: 36/64, 当前算符: D_(0, 2) * D_(35, 37), 耗时: 6.002s
[2025-07-25 02:00:37] SW-NE对角算符进度: 37/64, 当前算符: D_(0, 2) * D_(36, 38), 耗时: 5.976s
[2025-07-25 02:00:43] SW-NE对角算符进度: 38/64, 当前算符: D_(0, 2) * D_(37, 55), 耗时: 5.998s
[2025-07-25 02:00:49] SW-NE对角算符进度: 39/64, 当前算符: D_(0, 2) * D_(38, 56), 耗时: 5.999s
[2025-07-25 02:00:55] SW-NE对角算符进度: 40/64, 当前算符: D_(0, 2) * D_(39, 41), 耗时: 5.975s
[2025-07-25 02:01:01] SW-NE对角算符进度: 41/64, 当前算符: D_(0, 2) * D_(40, 42), 耗时: 5.969s
[2025-07-25 02:01:07] SW-NE对角算符进度: 42/64, 当前算符: D_(0, 2) * D_(41, 59), 耗时: 6.003s
[2025-07-25 02:01:13] SW-NE对角算符进度: 43/64, 当前算符: D_(0, 2) * D_(42, 60), 耗时: 5.963s
[2025-07-25 02:01:19] SW-NE对角算符进度: 44/64, 当前算符: D_(0, 2) * D_(43, 45), 耗时: 6.003s
[2025-07-25 02:01:25] SW-NE对角算符进度: 45/64, 当前算符: D_(0, 2) * D_(44, 46), 耗时: 5.982s
[2025-07-25 02:01:31] SW-NE对角算符进度: 46/64, 当前算符: D_(0, 2) * D_(45, 63), 耗时: 5.998s
[2025-07-25 02:01:37] SW-NE对角算符进度: 47/64, 当前算符: D_(0, 2) * D_(46, 48), 耗时: 5.970s
[2025-07-25 02:01:43] SW-NE对角算符进度: 48/64, 当前算符: D_(0, 2) * D_(47, 33), 耗时: 5.998s
[2025-07-25 02:01:49] SW-NE对角算符进度: 49/64, 当前算符: D_(0, 2) * D_(48, 50), 耗时: 5.977s
[2025-07-25 02:01:55] SW-NE对角算符进度: 50/64, 当前算符: D_(0, 2) * D_(49, 3), 耗时: 6.003s
[2025-07-25 02:02:01] SW-NE对角算符进度: 51/64, 当前算符: D_(0, 2) * D_(50, 4), 耗时: 5.972s
[2025-07-25 02:02:07] SW-NE对角算符进度: 52/64, 当前算符: D_(0, 2) * D_(51, 53), 耗时: 6.002s
[2025-07-25 02:02:13] SW-NE对角算符进度: 53/64, 当前算符: D_(0, 2) * D_(52, 54), 耗时: 5.973s
[2025-07-25 02:02:19] SW-NE对角算符进度: 54/64, 当前算符: D_(0, 2) * D_(53, 7), 耗时: 6.002s
[2025-07-25 02:02:25] SW-NE对角算符进度: 55/64, 当前算符: D_(0, 2) * D_(54, 8), 耗时: 5.970s
[2025-07-25 02:02:31] SW-NE对角算符进度: 56/64, 当前算符: D_(0, 2) * D_(55, 57), 耗时: 5.979s
[2025-07-25 02:02:37] SW-NE对角算符进度: 57/64, 当前算符: D_(0, 2) * D_(56, 58), 耗时: 5.973s
[2025-07-25 02:02:43] SW-NE对角算符进度: 58/64, 当前算符: D_(0, 2) * D_(57, 11), 耗时: 5.996s
[2025-07-25 02:02:49] SW-NE对角算符进度: 59/64, 当前算符: D_(0, 2) * D_(58, 12), 耗时: 5.970s
[2025-07-25 02:02:55] SW-NE对角算符进度: 60/64, 当前算符: D_(0, 2) * D_(59, 61), 耗时: 6.002s
[2025-07-25 02:03:01] SW-NE对角算符进度: 61/64, 当前算符: D_(0, 2) * D_(60, 62), 耗时: 5.971s
[2025-07-25 02:03:07] SW-NE对角算符进度: 62/64, 当前算符: D_(0, 2) * D_(61, 15), 耗时: 6.004s
[2025-07-25 02:03:11] SW-NE对角算符进度: 63/64, 当前算符: D_(0, 2) * D_(62, 0), 耗时: 4.683s
[2025-07-25 02:03:17] SW-NE对角算符进度: 64/64, 当前算符: D_(0, 2) * D_(63, 49), 耗时: 6.002s
[2025-07-25 02:03:17] 西南-东北方向对角二聚体相关函数计算完成,耗时: 378.24 秒
[2025-07-25 02:03:17] 计算傅里叶变换...
[2025-07-25 02:03:18] 对角二聚体结构因子计算完成
[2025-07-25 02:03:19] 对角二聚体相关函数平均误差: 0.000117
