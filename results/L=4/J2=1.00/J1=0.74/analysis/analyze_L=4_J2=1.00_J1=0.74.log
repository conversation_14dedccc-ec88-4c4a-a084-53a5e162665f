[2025-07-25 00:13:35] ================================================================================
[2025-07-25 00:13:35] 加载量子态: L=4, J2=1.00, J1=0.74
[2025-07-25 00:13:35] 设置样本数为: 1048576
[2025-07-25 00:13:35] 开始生成共享样本集...
[2025-07-25 00:14:45] 样本生成完成,耗时: 69.924 秒
[2025-07-25 00:14:45] ================================================================================
[2025-07-25 00:14:45] 开始计算自旋结构因子...
[2025-07-25 00:14:45] 初始化操作符缓存...
[2025-07-25 00:14:45] 预构建所有自旋相关操作符...
[2025-07-25 00:14:45] 开始计算自旋相关函数...
[2025-07-25 00:14:54] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 8.377s
[2025-07-25 00:15:02] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 8.462s
[2025-07-25 00:15:06] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 3.571s
[2025-07-25 00:15:09] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 3.584s
[2025-07-25 00:15:13] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 3.585s
[2025-07-25 00:15:17] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 3.579s
[2025-07-25 00:15:20] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 3.585s
[2025-07-25 00:15:24] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 3.584s
[2025-07-25 00:15:27] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 3.569s
[2025-07-25 00:15:31] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 3.586s
[2025-07-25 00:15:34] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 3.570s
[2025-07-25 00:15:38] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 3.569s
[2025-07-25 00:15:42] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 3.585s
[2025-07-25 00:15:45] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 3.574s
[2025-07-25 00:15:49] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 3.587s
[2025-07-25 00:15:52] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 3.588s
[2025-07-25 00:15:56] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 3.574s
[2025-07-25 00:15:59] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 3.575s
[2025-07-25 00:16:03] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 3.588s
[2025-07-25 00:16:07] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 3.588s
[2025-07-25 00:16:10] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 3.577s
[2025-07-25 00:16:14] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 3.570s
[2025-07-25 00:16:17] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 3.588s
[2025-07-25 00:16:21] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 3.575s
[2025-07-25 00:16:25] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 3.589s
[2025-07-25 00:16:28] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 3.588s
[2025-07-25 00:16:32] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 3.578s
[2025-07-25 00:16:35] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 3.589s
[2025-07-25 00:16:39] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 3.574s
[2025-07-25 00:16:42] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 3.576s
[2025-07-25 00:16:46] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 3.588s
[2025-07-25 00:16:50] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 3.572s
[2025-07-25 00:16:53] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 3.589s
[2025-07-25 00:16:57] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 3.589s
[2025-07-25 00:17:00] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 3.580s
[2025-07-25 00:17:04] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 3.589s
[2025-07-25 00:17:08] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 3.574s
[2025-07-25 00:17:11] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 3.575s
[2025-07-25 00:17:15] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 3.588s
[2025-07-25 00:17:18] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 3.579s
[2025-07-25 00:17:22] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 3.588s
[2025-07-25 00:17:25] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 3.588s
[2025-07-25 00:17:29] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 3.588s
[2025-07-25 00:17:33] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 3.571s
[2025-07-25 00:17:36] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 3.589s
[2025-07-25 00:17:40] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 3.588s
[2025-07-25 00:17:43] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 3.572s
[2025-07-25 00:17:47] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 3.588s
[2025-07-25 00:17:51] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 3.577s
[2025-07-25 00:17:54] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 3.593s
[2025-07-25 00:17:58] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 3.588s
[2025-07-25 00:18:01] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 3.569s
[2025-07-25 00:18:05] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 3.587s
[2025-07-25 00:18:09] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 3.588s
[2025-07-25 00:18:12] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 3.569s
[2025-07-25 00:18:16] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 3.588s
[2025-07-25 00:18:19] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 3.575s
[2025-07-25 00:18:23] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 3.580s
[2025-07-25 00:18:26] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 3.589s
[2025-07-25 00:18:30] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 3.567s
[2025-07-25 00:18:34] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 3.588s
[2025-07-25 00:18:37] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 3.589s
[2025-07-25 00:18:41] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 3.574s
[2025-07-25 00:18:44] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 3.577s
[2025-07-25 00:18:44] 自旋相关函数计算完成,总耗时 238.96 秒
[2025-07-25 00:18:44] 计算傅里叶变换...
[2025-07-25 00:18:45] 自旋结构因子计算完成
[2025-07-25 00:18:46] 自旋相关函数平均误差: 0.000559
[2025-07-25 00:18:46] ================================================================================
[2025-07-25 00:18:46] 开始计算对角二聚体结构因子...
[2025-07-25 00:18:46] 识别所有对角二聚体...
[2025-07-25 00:18:46] 总共找到 64 个西北-东南方向对角二聚体和 64 个西南-东北方向对角二聚体
[2025-07-25 00:18:46] 预计算对角二聚体操作符...
[2025-07-25 00:18:48] 开始计算西北-东南方向对角二聚体相关函数 (64 个算符)...
[2025-07-25 00:18:52] NW-SE对角算符进度: 1/64, 当前算符: D_(0, 14) * D_(0, 14), 耗时: 3.610s
[2025-07-25 00:19:03] NW-SE对角算符进度: 2/64, 当前算符: D_(0, 14) * D_(1, 31), 耗时: 10.992s
[2025-07-25 00:19:09] NW-SE对角算符进度: 3/64, 当前算符: D_(0, 14) * D_(2, 16), 耗时: 5.997s
[2025-07-25 00:19:15] NW-SE对角算符进度: 4/64, 当前算符: D_(0, 14) * D_(3, 1), 耗时: 5.998s
[2025-07-25 00:19:21] NW-SE对角算符进度: 5/64, 当前算符: D_(0, 14) * D_(4, 2), 耗时: 5.978s
[2025-07-25 00:19:27] NW-SE对角算符进度: 6/64, 当前算符: D_(0, 14) * D_(5, 19), 耗时: 5.999s
[2025-07-25 00:19:33] NW-SE对角算符进度: 7/64, 当前算符: D_(0, 14) * D_(6, 20), 耗时: 5.980s
[2025-07-25 00:19:39] NW-SE对角算符进度: 8/64, 当前算符: D_(0, 14) * D_(7, 5), 耗时: 5.999s
[2025-07-25 00:19:45] NW-SE对角算符进度: 9/64, 当前算符: D_(0, 14) * D_(8, 6), 耗时: 5.965s
[2025-07-25 00:19:51] NW-SE对角算符进度: 10/64, 当前算符: D_(0, 14) * D_(9, 23), 耗时: 5.998s
[2025-07-25 00:19:57] NW-SE对角算符进度: 11/64, 当前算符: D_(0, 14) * D_(10, 24), 耗时: 5.974s
[2025-07-25 00:20:03] NW-SE对角算符进度: 12/64, 当前算符: D_(0, 14) * D_(11, 9), 耗时: 5.976s
[2025-07-25 00:20:09] NW-SE对角算符进度: 13/64, 当前算符: D_(0, 14) * D_(12, 10), 耗时: 5.996s
[2025-07-25 00:20:15] NW-SE对角算符进度: 14/64, 当前算符: D_(0, 14) * D_(13, 27), 耗时: 5.981s
[2025-07-25 00:20:28] NW-SE对角算符进度: 15/64, 当前算符: D_(0, 14) * D_(14, 28), 耗时: 13.293s
[2025-07-25 00:20:34] NW-SE对角算符进度: 16/64, 当前算符: D_(0, 14) * D_(15, 13), 耗时: 6.004s
[2025-07-25 00:20:40] NW-SE对角算符进度: 17/64, 当前算符: D_(0, 14) * D_(16, 30), 耗时: 5.973s
[2025-07-25 00:20:46] NW-SE对角算符进度: 18/64, 当前算符: D_(0, 14) * D_(17, 47), 耗时: 5.970s
[2025-07-25 00:20:52] NW-SE对角算符进度: 19/64, 当前算符: D_(0, 14) * D_(18, 32), 耗时: 6.002s
[2025-07-25 00:20:58] NW-SE对角算符进度: 20/64, 当前算符: D_(0, 14) * D_(19, 17), 耗时: 5.982s
[2025-07-25 00:21:04] NW-SE对角算符进度: 21/64, 当前算符: D_(0, 14) * D_(20, 18), 耗时: 6.004s
[2025-07-25 00:21:10] NW-SE对角算符进度: 22/64, 当前算符: D_(0, 14) * D_(21, 35), 耗时: 5.980s
[2025-07-25 00:21:16] NW-SE对角算符进度: 23/64, 当前算符: D_(0, 14) * D_(22, 36), 耗时: 5.998s
[2025-07-25 00:21:22] NW-SE对角算符进度: 24/64, 当前算符: D_(0, 14) * D_(23, 21), 耗时: 5.978s
[2025-07-25 00:21:28] NW-SE对角算符进度: 25/64, 当前算符: D_(0, 14) * D_(24, 22), 耗时: 6.002s
[2025-07-25 00:21:34] NW-SE对角算符进度: 26/64, 当前算符: D_(0, 14) * D_(25, 39), 耗时: 5.983s
[2025-07-25 00:21:40] NW-SE对角算符进度: 27/64, 当前算符: D_(0, 14) * D_(26, 40), 耗时: 6.002s
[2025-07-25 00:21:46] NW-SE对角算符进度: 28/64, 当前算符: D_(0, 14) * D_(27, 25), 耗时: 5.971s
[2025-07-25 00:21:52] NW-SE对角算符进度: 29/64, 当前算符: D_(0, 14) * D_(28, 26), 耗时: 6.004s
[2025-07-25 00:21:58] NW-SE对角算符进度: 30/64, 当前算符: D_(0, 14) * D_(29, 43), 耗时: 5.974s
[2025-07-25 00:22:04] NW-SE对角算符进度: 31/64, 当前算符: D_(0, 14) * D_(30, 44), 耗时: 6.000s
[2025-07-25 00:22:10] NW-SE对角算符进度: 32/64, 当前算符: D_(0, 14) * D_(31, 29), 耗时: 5.975s
[2025-07-25 00:22:16] NW-SE对角算符进度: 33/64, 当前算符: D_(0, 14) * D_(32, 46), 耗时: 6.002s
[2025-07-25 00:22:22] NW-SE对角算符进度: 34/64, 当前算符: D_(0, 14) * D_(33, 63), 耗时: 6.003s
[2025-07-25 00:22:28] NW-SE对角算符进度: 35/64, 当前算符: D_(0, 14) * D_(34, 48), 耗时: 6.002s
[2025-07-25 00:22:34] NW-SE对角算符进度: 36/64, 当前算符: D_(0, 14) * D_(35, 33), 耗时: 5.983s
[2025-07-25 00:22:40] NW-SE对角算符进度: 37/64, 当前算符: D_(0, 14) * D_(36, 34), 耗时: 5.997s
[2025-07-25 00:22:46] NW-SE对角算符进度: 38/64, 当前算符: D_(0, 14) * D_(37, 51), 耗时: 5.978s
[2025-07-25 00:22:52] NW-SE对角算符进度: 39/64, 当前算符: D_(0, 14) * D_(38, 52), 耗时: 6.002s
[2025-07-25 00:22:58] NW-SE对角算符进度: 40/64, 当前算符: D_(0, 14) * D_(39, 37), 耗时: 5.974s
[2025-07-25 00:23:04] NW-SE对角算符进度: 41/64, 当前算符: D_(0, 14) * D_(40, 38), 耗时: 5.999s
[2025-07-25 00:23:10] NW-SE对角算符进度: 42/64, 当前算符: D_(0, 14) * D_(41, 55), 耗时: 5.980s
[2025-07-25 00:23:16] NW-SE对角算符进度: 43/64, 当前算符: D_(0, 14) * D_(42, 56), 耗时: 6.003s
[2025-07-25 00:23:22] NW-SE对角算符进度: 44/64, 当前算符: D_(0, 14) * D_(43, 41), 耗时: 5.987s
[2025-07-25 00:23:28] NW-SE对角算符进度: 45/64, 当前算符: D_(0, 14) * D_(44, 42), 耗时: 6.001s
[2025-07-25 00:23:34] NW-SE对角算符进度: 46/64, 当前算符: D_(0, 14) * D_(45, 59), 耗时: 5.974s
[2025-07-25 00:23:40] NW-SE对角算符进度: 47/64, 当前算符: D_(0, 14) * D_(46, 60), 耗时: 6.001s
[2025-07-25 00:23:46] NW-SE对角算符进度: 48/64, 当前算符: D_(0, 14) * D_(47, 45), 耗时: 5.977s
[2025-07-25 00:23:52] NW-SE对角算符进度: 49/64, 当前算符: D_(0, 14) * D_(48, 62), 耗时: 6.048s
[2025-07-25 00:23:58] NW-SE对角算符进度: 50/64, 当前算符: D_(0, 14) * D_(49, 15), 耗时: 5.998s
[2025-07-25 00:24:02] NW-SE对角算符进度: 51/64, 当前算符: D_(0, 14) * D_(50, 0), 耗时: 4.682s
[2025-07-25 00:24:08] NW-SE对角算符进度: 52/64, 当前算符: D_(0, 14) * D_(51, 49), 耗时: 5.978s
[2025-07-25 00:24:14] NW-SE对角算符进度: 53/64, 当前算符: D_(0, 14) * D_(52, 50), 耗时: 6.002s
[2025-07-25 00:24:20] NW-SE对角算符进度: 54/64, 当前算符: D_(0, 14) * D_(53, 3), 耗时: 5.980s
[2025-07-25 00:24:26] NW-SE对角算符进度: 55/64, 当前算符: D_(0, 14) * D_(54, 4), 耗时: 6.003s
[2025-07-25 00:24:32] NW-SE对角算符进度: 56/64, 当前算符: D_(0, 14) * D_(55, 53), 耗时: 5.979s
[2025-07-25 00:24:38] NW-SE对角算符进度: 57/64, 当前算符: D_(0, 14) * D_(56, 54), 耗时: 6.003s
[2025-07-25 00:24:44] NW-SE对角算符进度: 58/64, 当前算符: D_(0, 14) * D_(57, 7), 耗时: 5.975s
[2025-07-25 00:24:50] NW-SE对角算符进度: 59/64, 当前算符: D_(0, 14) * D_(58, 8), 耗时: 6.003s
[2025-07-25 00:24:56] NW-SE对角算符进度: 60/64, 当前算符: D_(0, 14) * D_(59, 57), 耗时: 5.976s
[2025-07-25 00:25:02] NW-SE对角算符进度: 61/64, 当前算符: D_(0, 14) * D_(60, 58), 耗时: 6.001s
[2025-07-25 00:25:08] NW-SE对角算符进度: 62/64, 当前算符: D_(0, 14) * D_(61, 11), 耗时: 5.974s
[2025-07-25 00:25:14] NW-SE对角算符进度: 63/64, 当前算符: D_(0, 14) * D_(62, 12), 耗时: 5.969s
[2025-07-25 00:25:20] NW-SE对角算符进度: 64/64, 当前算符: D_(0, 14) * D_(63, 61), 耗时: 6.002s
[2025-07-25 00:25:20] 西北-东南方向对角二聚体相关函数计算完成,耗时: 392.05 秒
[2025-07-25 00:25:20] ================================================================================
[2025-07-25 00:25:20] 开始计算西南-东北方向对角二聚体相关函数 (64 个算符)...
[2025-07-25 00:25:24] SW-NE对角算符进度: 1/64, 当前算符: D_(0, 2) * D_(0, 2), 耗时: 3.588s
[2025-07-25 00:25:30] SW-NE对角算符进度: 2/64, 当前算符: D_(0, 2) * D_(1, 19), 耗时: 5.997s
[2025-07-25 00:25:35] SW-NE对角算符进度: 3/64, 当前算符: D_(0, 2) * D_(2, 20), 耗时: 4.692s
[2025-07-25 00:25:41] SW-NE对角算符进度: 4/64, 当前算符: D_(0, 2) * D_(3, 5), 耗时: 6.002s
[2025-07-25 00:25:47] SW-NE对角算符进度: 5/64, 当前算符: D_(0, 2) * D_(4, 6), 耗时: 6.001s
[2025-07-25 00:25:53] SW-NE对角算符进度: 6/64, 当前算符: D_(0, 2) * D_(5, 23), 耗时: 5.965s
[2025-07-25 00:25:59] SW-NE对角算符进度: 7/64, 当前算符: D_(0, 2) * D_(6, 24), 耗时: 6.000s
[2025-07-25 00:26:05] SW-NE对角算符进度: 8/64, 当前算符: D_(0, 2) * D_(7, 9), 耗时: 5.990s
[2025-07-25 00:26:11] SW-NE对角算符进度: 9/64, 当前算符: D_(0, 2) * D_(8, 10), 耗时: 5.987s
[2025-07-25 00:26:17] SW-NE对角算符进度: 10/64, 当前算符: D_(0, 2) * D_(9, 27), 耗时: 5.998s
[2025-07-25 00:26:22] SW-NE对角算符进度: 11/64, 当前算符: D_(0, 2) * D_(10, 28), 耗时: 5.975s
[2025-07-25 00:26:28] SW-NE对角算符进度: 12/64, 当前算符: D_(0, 2) * D_(11, 13), 耗时: 5.998s
[2025-07-25 00:26:34] SW-NE对角算符进度: 13/64, 当前算符: D_(0, 2) * D_(12, 14), 耗时: 5.979s
[2025-07-25 00:26:40] SW-NE对角算符进度: 14/64, 当前算符: D_(0, 2) * D_(13, 31), 耗时: 6.002s
[2025-07-25 00:26:46] SW-NE对角算符进度: 15/64, 当前算符: D_(0, 2) * D_(14, 16), 耗时: 5.975s
[2025-07-25 00:26:52] SW-NE对角算符进度: 16/64, 当前算符: D_(0, 2) * D_(15, 1), 耗时: 6.002s
[2025-07-25 00:26:58] SW-NE对角算符进度: 17/64, 当前算符: D_(0, 2) * D_(16, 18), 耗时: 5.982s
[2025-07-25 00:27:04] SW-NE对角算符进度: 18/64, 当前算符: D_(0, 2) * D_(17, 35), 耗时: 6.002s
[2025-07-25 00:27:10] SW-NE对角算符进度: 19/64, 当前算符: D_(0, 2) * D_(18, 36), 耗时: 5.978s
[2025-07-25 00:27:16] SW-NE对角算符进度: 20/64, 当前算符: D_(0, 2) * D_(19, 21), 耗时: 5.998s
[2025-07-25 00:27:22] SW-NE对角算符进度: 21/64, 当前算符: D_(0, 2) * D_(20, 22), 耗时: 5.968s
[2025-07-25 00:27:28] SW-NE对角算符进度: 22/64, 当前算符: D_(0, 2) * D_(21, 39), 耗时: 5.979s
[2025-07-25 00:27:34] SW-NE对角算符进度: 23/64, 当前算符: D_(0, 2) * D_(22, 40), 耗时: 5.998s
[2025-07-25 00:27:40] SW-NE对角算符进度: 24/64, 当前算符: D_(0, 2) * D_(23, 25), 耗时: 5.984s
[2025-07-25 00:27:46] SW-NE对角算符进度: 25/64, 当前算符: D_(0, 2) * D_(24, 26), 耗时: 5.981s
[2025-07-25 00:27:52] SW-NE对角算符进度: 26/64, 当前算符: D_(0, 2) * D_(25, 43), 耗时: 6.001s
[2025-07-25 00:27:58] SW-NE对角算符进度: 27/64, 当前算符: D_(0, 2) * D_(26, 44), 耗时: 5.980s
[2025-07-25 00:28:04] SW-NE对角算符进度: 28/64, 当前算符: D_(0, 2) * D_(27, 29), 耗时: 6.001s
[2025-07-25 00:28:10] SW-NE对角算符进度: 29/64, 当前算符: D_(0, 2) * D_(28, 30), 耗时: 5.975s
[2025-07-25 00:28:16] SW-NE对角算符进度: 30/64, 当前算符: D_(0, 2) * D_(29, 47), 耗时: 6.000s
[2025-07-25 00:28:22] SW-NE对角算符进度: 31/64, 当前算符: D_(0, 2) * D_(30, 32), 耗时: 5.982s
[2025-07-25 00:28:28] SW-NE对角算符进度: 32/64, 当前算符: D_(0, 2) * D_(31, 17), 耗时: 5.998s
[2025-07-25 00:28:34] SW-NE对角算符进度: 33/64, 当前算符: D_(0, 2) * D_(32, 34), 耗时: 5.988s
[2025-07-25 00:28:40] SW-NE对角算符进度: 34/64, 当前算符: D_(0, 2) * D_(33, 51), 耗时: 6.002s
[2025-07-25 00:28:46] SW-NE对角算符进度: 35/64, 当前算符: D_(0, 2) * D_(34, 52), 耗时: 5.974s
[2025-07-25 00:28:52] SW-NE对角算符进度: 36/64, 当前算符: D_(0, 2) * D_(35, 37), 耗时: 6.001s
[2025-07-25 00:28:58] SW-NE对角算符进度: 37/64, 当前算符: D_(0, 2) * D_(36, 38), 耗时: 5.983s
[2025-07-25 00:29:04] SW-NE对角算符进度: 38/64, 当前算符: D_(0, 2) * D_(37, 55), 耗时: 5.997s
[2025-07-25 00:29:10] SW-NE对角算符进度: 39/64, 当前算符: D_(0, 2) * D_(38, 56), 耗时: 5.998s
[2025-07-25 00:29:16] SW-NE对角算符进度: 40/64, 当前算符: D_(0, 2) * D_(39, 41), 耗时: 5.975s
[2025-07-25 00:29:22] SW-NE对角算符进度: 41/64, 当前算符: D_(0, 2) * D_(40, 42), 耗时: 5.968s
[2025-07-25 00:29:28] SW-NE对角算符进度: 42/64, 当前算符: D_(0, 2) * D_(41, 59), 耗时: 6.000s
[2025-07-25 00:29:34] SW-NE对角算符进度: 43/64, 当前算符: D_(0, 2) * D_(42, 60), 耗时: 5.975s
[2025-07-25 00:29:40] SW-NE对角算符进度: 44/64, 当前算符: D_(0, 2) * D_(43, 45), 耗时: 6.001s
[2025-07-25 00:29:46] SW-NE对角算符进度: 45/64, 当前算符: D_(0, 2) * D_(44, 46), 耗时: 5.982s
[2025-07-25 00:29:52] SW-NE对角算符进度: 46/64, 当前算符: D_(0, 2) * D_(45, 63), 耗时: 6.002s
[2025-07-25 00:29:58] SW-NE对角算符进度: 47/64, 当前算符: D_(0, 2) * D_(46, 48), 耗时: 5.981s
[2025-07-25 00:30:04] SW-NE对角算符进度: 48/64, 当前算符: D_(0, 2) * D_(47, 33), 耗时: 5.996s
[2025-07-25 00:30:10] SW-NE对角算符进度: 49/64, 当前算符: D_(0, 2) * D_(48, 50), 耗时: 5.982s
[2025-07-25 00:30:16] SW-NE对角算符进度: 50/64, 当前算符: D_(0, 2) * D_(49, 3), 耗时: 6.000s
[2025-07-25 00:30:22] SW-NE对角算符进度: 51/64, 当前算符: D_(0, 2) * D_(50, 4), 耗时: 6.036s
[2025-07-25 00:30:28] SW-NE对角算符进度: 52/64, 当前算符: D_(0, 2) * D_(51, 53), 耗时: 5.999s
[2025-07-25 00:30:34] SW-NE对角算符进度: 53/64, 当前算符: D_(0, 2) * D_(52, 54), 耗时: 5.977s
[2025-07-25 00:30:40] SW-NE对角算符进度: 54/64, 当前算符: D_(0, 2) * D_(53, 7), 耗时: 6.000s
[2025-07-25 00:30:46] SW-NE对角算符进度: 55/64, 当前算符: D_(0, 2) * D_(54, 8), 耗时: 5.969s
[2025-07-25 00:30:52] SW-NE对角算符进度: 56/64, 当前算符: D_(0, 2) * D_(55, 57), 耗时: 6.044s
[2025-07-25 00:30:58] SW-NE对角算符进度: 57/64, 当前算符: D_(0, 2) * D_(56, 58), 耗时: 5.974s
[2025-07-25 00:31:04] SW-NE对角算符进度: 58/64, 当前算符: D_(0, 2) * D_(57, 11), 耗时: 5.995s
[2025-07-25 00:31:10] SW-NE对角算符进度: 59/64, 当前算符: D_(0, 2) * D_(58, 12), 耗时: 5.975s
[2025-07-25 00:31:16] SW-NE对角算符进度: 60/64, 当前算符: D_(0, 2) * D_(59, 61), 耗时: 5.999s
[2025-07-25 00:31:22] SW-NE对角算符进度: 61/64, 当前算符: D_(0, 2) * D_(60, 62), 耗时: 6.056s
[2025-07-25 00:31:28] SW-NE对角算符进度: 62/64, 当前算符: D_(0, 2) * D_(61, 15), 耗时: 6.000s
[2025-07-25 00:31:33] SW-NE对角算符进度: 63/64, 当前算符: D_(0, 2) * D_(62, 0), 耗时: 4.682s
[2025-07-25 00:31:39] SW-NE对角算符进度: 64/64, 当前算符: D_(0, 2) * D_(63, 49), 耗时: 5.999s
[2025-07-25 00:31:39] 西南-东北方向对角二聚体相关函数计算完成,耗时: 378.55 秒
[2025-07-25 00:31:39] 计算傅里叶变换...
[2025-07-25 00:31:39] 对角二聚体结构因子计算完成
[2025-07-25 00:31:40] 对角二聚体相关函数平均误差: 0.000125
