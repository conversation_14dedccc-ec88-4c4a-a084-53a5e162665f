[2025-07-25 01:08:48] ================================================================================
[2025-07-25 01:08:48] 加载量子态: L=4, J2=1.00, J1=0.77
[2025-07-25 01:08:48] 设置样本数为: 1048576
[2025-07-25 01:08:48] 开始生成共享样本集...
[2025-07-25 01:09:58] 样本生成完成,耗时: 69.617 秒
[2025-07-25 01:09:58] ================================================================================
[2025-07-25 01:09:58] 开始计算自旋结构因子...
[2025-07-25 01:09:58] 初始化操作符缓存...
[2025-07-25 01:09:58] 预构建所有自旋相关操作符...
[2025-07-25 01:09:58] 开始计算自旋相关函数...
[2025-07-25 01:10:06] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 7.958s
[2025-07-25 01:10:14] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 8.422s
[2025-07-25 01:10:18] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 3.591s
[2025-07-25 01:10:22] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 3.590s
[2025-07-25 01:10:25] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 3.591s
[2025-07-25 01:10:29] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 3.589s
[2025-07-25 01:10:32] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 3.591s
[2025-07-25 01:10:36] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 3.569s
[2025-07-25 01:10:39] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 3.578s
[2025-07-25 01:10:43] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 3.590s
[2025-07-25 01:10:47] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 3.590s
[2025-07-25 01:10:50] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 3.590s
[2025-07-25 01:10:54] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 3.577s
[2025-07-25 01:10:57] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 3.570s
[2025-07-25 01:11:01] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 3.577s
[2025-07-25 01:11:05] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 3.591s
[2025-07-25 01:11:08] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 3.592s
[2025-07-25 01:11:12] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 3.572s
[2025-07-25 01:11:15] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 3.574s
[2025-07-25 01:11:19] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 3.576s
[2025-07-25 01:11:22] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 3.591s
[2025-07-25 01:11:26] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 3.590s
[2025-07-25 01:11:30] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 3.589s
[2025-07-25 01:11:33] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 3.570s
[2025-07-25 01:11:37] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 3.572s
[2025-07-25 01:11:40] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 3.591s
[2025-07-25 01:11:44] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 3.590s
[2025-07-25 01:11:48] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 3.590s
[2025-07-25 01:11:51] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 3.572s
[2025-07-25 01:11:55] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 3.571s
[2025-07-25 01:11:58] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 3.579s
[2025-07-25 01:12:02] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 3.590s
[2025-07-25 01:12:05] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 3.592s
[2025-07-25 01:12:09] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 3.573s
[2025-07-25 01:12:13] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 3.572s
[2025-07-25 01:12:16] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 3.577s
[2025-07-25 01:12:20] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 3.592s
[2025-07-25 01:12:23] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 3.592s
[2025-07-25 01:12:27] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 3.590s
[2025-07-25 01:12:31] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 3.571s
[2025-07-25 01:12:34] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 3.576s
[2025-07-25 01:12:38] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 3.591s
[2025-07-25 01:12:41] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 3.592s
[2025-07-25 01:12:45] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 3.590s
[2025-07-25 01:12:48] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 3.571s
[2025-07-25 01:12:52] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 3.572s
[2025-07-25 01:12:56] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 3.673s
[2025-07-25 01:12:59] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 3.576s
[2025-07-25 01:13:03] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 3.575s
[2025-07-25 01:13:06] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 3.590s
[2025-07-25 01:13:10] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 3.592s
[2025-07-25 01:13:14] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 3.592s
[2025-07-25 01:13:17] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 3.574s
[2025-07-25 01:13:21] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 3.574s
[2025-07-25 01:13:24] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 3.573s
[2025-07-25 01:13:28] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 3.593s
[2025-07-25 01:13:32] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 3.593s
[2025-07-25 01:13:35] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 3.580s
[2025-07-25 01:13:39] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 3.571s
[2025-07-25 01:13:42] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 3.574s
[2025-07-25 01:13:46] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 3.593s
[2025-07-25 01:13:49] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 3.593s
[2025-07-25 01:13:53] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 3.593s
[2025-07-25 01:13:57] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 3.573s
[2025-07-25 01:13:57] 自旋相关函数计算完成,总耗时 238.68 秒
[2025-07-25 01:13:57] 计算傅里叶变换...
[2025-07-25 01:13:57] 自旋结构因子计算完成
[2025-07-25 01:13:58] 自旋相关函数平均误差: 0.000545
[2025-07-25 01:13:58] ================================================================================
[2025-07-25 01:13:58] 开始计算对角二聚体结构因子...
[2025-07-25 01:13:58] 识别所有对角二聚体...
[2025-07-25 01:13:58] 总共找到 64 个西北-东南方向对角二聚体和 64 个西南-东北方向对角二聚体
[2025-07-25 01:13:58] 预计算对角二聚体操作符...
[2025-07-25 01:14:00] 开始计算西北-东南方向对角二聚体相关函数 (64 个算符)...
[2025-07-25 01:14:04] NW-SE对角算符进度: 1/64, 当前算符: D_(0, 14) * D_(0, 14), 耗时: 3.611s
[2025-07-25 01:14:15] NW-SE对角算符进度: 2/64, 当前算符: D_(0, 14) * D_(1, 31), 耗时: 11.300s
[2025-07-25 01:14:21] NW-SE对角算符进度: 3/64, 当前算符: D_(0, 14) * D_(2, 16), 耗时: 5.975s
[2025-07-25 01:14:27] NW-SE对角算符进度: 4/64, 当前算符: D_(0, 14) * D_(3, 1), 耗时: 5.972s
[2025-07-25 01:14:33] NW-SE对角算符进度: 5/64, 当前算符: D_(0, 14) * D_(4, 2), 耗时: 5.984s
[2025-07-25 01:14:39] NW-SE对角算符进度: 6/64, 当前算符: D_(0, 14) * D_(5, 19), 耗时: 5.989s
[2025-07-25 01:14:45] NW-SE对角算符进度: 7/64, 当前算符: D_(0, 14) * D_(6, 20), 耗时: 5.950s
[2025-07-25 01:14:51] NW-SE对角算符进度: 8/64, 当前算符: D_(0, 14) * D_(7, 5), 耗时: 5.978s
[2025-07-25 01:14:57] NW-SE对角算符进度: 9/64, 当前算符: D_(0, 14) * D_(8, 6), 耗时: 5.990s
[2025-07-25 01:15:03] NW-SE对角算符进度: 10/64, 当前算符: D_(0, 14) * D_(9, 23), 耗时: 5.986s
[2025-07-25 01:15:09] NW-SE对角算符进度: 11/64, 当前算符: D_(0, 14) * D_(10, 24), 耗时: 5.993s
[2025-07-25 01:15:15] NW-SE对角算符进度: 12/64, 当前算符: D_(0, 14) * D_(11, 9), 耗时: 5.993s
[2025-07-25 01:15:21] NW-SE对角算符进度: 13/64, 当前算符: D_(0, 14) * D_(12, 10), 耗时: 5.972s
[2025-07-25 01:15:27] NW-SE对角算符进度: 14/64, 当前算符: D_(0, 14) * D_(13, 27), 耗时: 5.947s
[2025-07-25 01:15:40] NW-SE对角算符进度: 15/64, 当前算符: D_(0, 14) * D_(14, 28), 耗时: 13.280s
[2025-07-25 01:15:46] NW-SE对角算符进度: 16/64, 当前算符: D_(0, 14) * D_(15, 13), 耗时: 6.011s
[2025-07-25 01:15:52] NW-SE对角算符进度: 17/64, 当前算符: D_(0, 14) * D_(16, 30), 耗时: 6.004s
[2025-07-25 01:15:58] NW-SE对角算符进度: 18/64, 当前算符: D_(0, 14) * D_(17, 47), 耗时: 5.975s
[2025-07-25 01:16:04] NW-SE对角算符进度: 19/64, 当前算符: D_(0, 14) * D_(18, 32), 耗时: 5.982s
[2025-07-25 01:16:10] NW-SE对角算符进度: 20/64, 当前算符: D_(0, 14) * D_(19, 17), 耗时: 5.975s
[2025-07-25 01:16:16] NW-SE对角算符进度: 21/64, 当前算符: D_(0, 14) * D_(20, 18), 耗时: 6.007s
[2025-07-25 01:16:22] NW-SE对角算符进度: 22/64, 当前算符: D_(0, 14) * D_(21, 35), 耗时: 6.010s
[2025-07-25 01:16:28] NW-SE对角算符进度: 23/64, 当前算符: D_(0, 14) * D_(22, 36), 耗时: 5.970s
[2025-07-25 01:16:34] NW-SE对角算符进度: 24/64, 当前算符: D_(0, 14) * D_(23, 21), 耗时: 5.983s
[2025-07-25 01:16:40] NW-SE对角算符进度: 25/64, 当前算符: D_(0, 14) * D_(24, 22), 耗时: 5.983s
[2025-07-25 01:16:46] NW-SE对角算符进度: 26/64, 当前算符: D_(0, 14) * D_(25, 39), 耗时: 6.006s
[2025-07-25 01:16:52] NW-SE对角算符进度: 27/64, 当前算符: D_(0, 14) * D_(26, 40), 耗时: 6.009s
[2025-07-25 01:16:58] NW-SE对角算符进度: 28/64, 当前算符: D_(0, 14) * D_(27, 25), 耗时: 6.005s
[2025-07-25 01:17:04] NW-SE对角算符进度: 29/64, 当前算符: D_(0, 14) * D_(28, 26), 耗时: 5.983s
[2025-07-25 01:17:10] NW-SE对角算符进度: 30/64, 当前算符: D_(0, 14) * D_(29, 43), 耗时: 5.968s
[2025-07-25 01:17:16] NW-SE对角算符进度: 31/64, 当前算符: D_(0, 14) * D_(30, 44), 耗时: 6.015s
[2025-07-25 01:17:22] NW-SE对角算符进度: 32/64, 当前算符: D_(0, 14) * D_(31, 29), 耗时: 6.014s
[2025-07-25 01:17:28] NW-SE对角算符进度: 33/64, 当前算符: D_(0, 14) * D_(32, 46), 耗时: 6.009s
[2025-07-25 01:17:34] NW-SE对角算符进度: 34/64, 当前算符: D_(0, 14) * D_(33, 63), 耗时: 5.977s
[2025-07-25 01:17:40] NW-SE对角算符进度: 35/64, 当前算符: D_(0, 14) * D_(34, 48), 耗时: 5.972s
[2025-07-25 01:17:46] NW-SE对角算符进度: 36/64, 当前算符: D_(0, 14) * D_(35, 33), 耗时: 5.982s
[2025-07-25 01:17:52] NW-SE对角算符进度: 37/64, 当前算符: D_(0, 14) * D_(36, 34), 耗时: 6.009s
[2025-07-25 01:17:58] NW-SE对角算符进度: 38/64, 当前算符: D_(0, 14) * D_(37, 51), 耗时: 6.012s
[2025-07-25 01:18:04] NW-SE对角算符进度: 39/64, 当前算符: D_(0, 14) * D_(38, 52), 耗时: 5.979s
[2025-07-25 01:18:10] NW-SE对角算符进度: 40/64, 当前算符: D_(0, 14) * D_(39, 37), 耗时: 5.986s
[2025-07-25 01:18:16] NW-SE对角算符进度: 41/64, 当前算符: D_(0, 14) * D_(40, 38), 耗时: 5.988s
[2025-07-25 01:18:22] NW-SE对角算符进度: 42/64, 当前算符: D_(0, 14) * D_(41, 55), 耗时: 6.028s
[2025-07-25 01:18:28] NW-SE对角算符进度: 43/64, 当前算符: D_(0, 14) * D_(42, 56), 耗时: 6.012s
[2025-07-25 01:18:34] NW-SE对角算符进度: 44/64, 当前算符: D_(0, 14) * D_(43, 41), 耗时: 6.012s
[2025-07-25 01:18:40] NW-SE对角算符进度: 45/64, 当前算符: D_(0, 14) * D_(44, 42), 耗时: 5.970s
[2025-07-25 01:18:46] NW-SE对角算符进度: 46/64, 当前算符: D_(0, 14) * D_(45, 59), 耗时: 5.972s
[2025-07-25 01:18:52] NW-SE对角算符进度: 47/64, 当前算符: D_(0, 14) * D_(46, 60), 耗时: 6.028s
[2025-07-25 01:18:58] NW-SE对角算符进度: 48/64, 当前算符: D_(0, 14) * D_(47, 45), 耗时: 5.972s
[2025-07-25 01:19:04] NW-SE对角算符进度: 49/64, 当前算符: D_(0, 14) * D_(48, 62), 耗时: 5.970s
[2025-07-25 01:19:10] NW-SE对角算符进度: 50/64, 当前算符: D_(0, 14) * D_(49, 15), 耗时: 6.007s
[2025-07-25 01:19:15] NW-SE对角算符进度: 51/64, 当前算符: D_(0, 14) * D_(50, 0), 耗时: 4.706s
[2025-07-25 01:19:21] NW-SE对角算符进度: 52/64, 当前算符: D_(0, 14) * D_(51, 49), 耗时: 6.013s
[2025-07-25 01:19:27] NW-SE对角算符进度: 53/64, 当前算符: D_(0, 14) * D_(52, 50), 耗时: 5.980s
[2025-07-25 01:19:33] NW-SE对角算符进度: 54/64, 当前算符: D_(0, 14) * D_(53, 3), 耗时: 5.981s
[2025-07-25 01:19:39] NW-SE对角算符进度: 55/64, 当前算符: D_(0, 14) * D_(54, 4), 耗时: 6.010s
[2025-07-25 01:19:45] NW-SE对角算符进度: 56/64, 当前算符: D_(0, 14) * D_(55, 53), 耗时: 6.015s
[2025-07-25 01:19:51] NW-SE对角算符进度: 57/64, 当前算符: D_(0, 14) * D_(56, 54), 耗时: 6.015s
[2025-07-25 01:19:57] NW-SE对角算符进度: 58/64, 当前算符: D_(0, 14) * D_(57, 7), 耗时: 5.985s
[2025-07-25 01:20:03] NW-SE对角算符进度: 59/64, 当前算符: D_(0, 14) * D_(58, 8), 耗时: 5.983s
[2025-07-25 01:20:09] NW-SE对角算符进度: 60/64, 当前算符: D_(0, 14) * D_(59, 57), 耗时: 5.970s
[2025-07-25 01:20:15] NW-SE对角算符进度: 61/64, 当前算符: D_(0, 14) * D_(60, 58), 耗时: 6.012s
[2025-07-25 01:20:21] NW-SE对角算符进度: 62/64, 当前算符: D_(0, 14) * D_(61, 11), 耗时: 6.006s
[2025-07-25 01:20:27] NW-SE对角算符进度: 63/64, 当前算符: D_(0, 14) * D_(62, 12), 耗时: 5.978s
[2025-07-25 01:20:33] NW-SE对角算符进度: 64/64, 当前算符: D_(0, 14) * D_(63, 61), 耗时: 5.975s
[2025-07-25 01:20:33] 西北-东南方向对角二聚体相关函数计算完成,耗时: 392.41 秒
[2025-07-25 01:20:33] ================================================================================
[2025-07-25 01:20:33] 开始计算西南-东北方向对角二聚体相关函数 (64 个算符)...
[2025-07-25 01:20:36] SW-NE对角算符进度: 1/64, 当前算符: D_(0, 2) * D_(0, 2), 耗时: 3.583s
[2025-07-25 01:20:42] SW-NE对角算符进度: 2/64, 当前算符: D_(0, 2) * D_(1, 19), 耗时: 6.013s
[2025-07-25 01:20:47] SW-NE对角算符进度: 3/64, 当前算符: D_(0, 2) * D_(2, 20), 耗时: 4.706s
[2025-07-25 01:20:53] SW-NE对角算符进度: 4/64, 当前算符: D_(0, 2) * D_(3, 5), 耗时: 6.012s
[2025-07-25 01:20:59] SW-NE对角算符进度: 5/64, 当前算符: D_(0, 2) * D_(4, 6), 耗时: 5.978s
[2025-07-25 01:21:05] SW-NE对角算符进度: 6/64, 当前算符: D_(0, 2) * D_(5, 23), 耗时: 5.974s
[2025-07-25 01:21:11] SW-NE对角算符进度: 7/64, 当前算符: D_(0, 2) * D_(6, 24), 耗时: 6.012s
[2025-07-25 01:21:17] SW-NE对角算符进度: 8/64, 当前算符: D_(0, 2) * D_(7, 9), 耗时: 6.016s
[2025-07-25 01:21:23] SW-NE对角算符进度: 9/64, 当前算符: D_(0, 2) * D_(8, 10), 耗时: 6.011s
[2025-07-25 01:21:29] SW-NE对角算符进度: 10/64, 当前算符: D_(0, 2) * D_(9, 27), 耗时: 5.977s
[2025-07-25 01:21:35] SW-NE对角算符进度: 11/64, 当前算符: D_(0, 2) * D_(10, 28), 耗时: 5.979s
[2025-07-25 01:21:41] SW-NE对角算符进度: 12/64, 当前算符: D_(0, 2) * D_(11, 13), 耗时: 5.969s
[2025-07-25 01:21:47] SW-NE对角算符进度: 13/64, 当前算符: D_(0, 2) * D_(12, 14), 耗时: 6.015s
[2025-07-25 01:21:53] SW-NE对角算符进度: 14/64, 当前算符: D_(0, 2) * D_(13, 31), 耗时: 6.011s
[2025-07-25 01:21:59] SW-NE对角算符进度: 15/64, 当前算符: D_(0, 2) * D_(14, 16), 耗时: 5.982s
[2025-07-25 01:22:05] SW-NE对角算符进度: 16/64, 当前算符: D_(0, 2) * D_(15, 1), 耗时: 5.976s
[2025-07-25 01:22:11] SW-NE对角算符进度: 17/64, 当前算符: D_(0, 2) * D_(16, 18), 耗时: 5.977s
[2025-07-25 01:22:17] SW-NE对角算符进度: 18/64, 当前算符: D_(0, 2) * D_(17, 35), 耗时: 6.013s
[2025-07-25 01:22:23] SW-NE对角算符进度: 19/64, 当前算符: D_(0, 2) * D_(18, 36), 耗时: 6.013s
[2025-07-25 01:22:29] SW-NE对角算符进度: 20/64, 当前算符: D_(0, 2) * D_(19, 21), 耗时: 6.014s
[2025-07-25 01:22:35] SW-NE对角算符进度: 21/64, 当前算符: D_(0, 2) * D_(20, 22), 耗时: 5.968s
[2025-07-25 01:22:41] SW-NE对角算符进度: 22/64, 当前算符: D_(0, 2) * D_(21, 39), 耗时: 5.984s
[2025-07-25 01:22:47] SW-NE对角算符进度: 23/64, 当前算符: D_(0, 2) * D_(22, 40), 耗时: 6.008s
[2025-07-25 01:22:53] SW-NE对角算符进度: 24/64, 当前算符: D_(0, 2) * D_(23, 25), 耗时: 6.014s
[2025-07-25 01:22:59] SW-NE对角算符进度: 25/64, 当前算符: D_(0, 2) * D_(24, 26), 耗时: 6.009s
[2025-07-25 01:23:05] SW-NE对角算符进度: 26/64, 当前算符: D_(0, 2) * D_(25, 43), 耗时: 6.008s
[2025-07-25 01:23:11] SW-NE对角算符进度: 27/64, 当前算符: D_(0, 2) * D_(26, 44), 耗时: 6.009s
[2025-07-25 01:23:17] SW-NE对角算符进度: 28/64, 当前算符: D_(0, 2) * D_(27, 29), 耗时: 6.012s
[2025-07-25 01:23:23] SW-NE对角算符进度: 29/64, 当前算符: D_(0, 2) * D_(28, 30), 耗时: 5.986s
[2025-07-25 01:23:29] SW-NE对角算符进度: 30/64, 当前算符: D_(0, 2) * D_(29, 47), 耗时: 5.985s
[2025-07-25 01:23:35] SW-NE对角算符进度: 31/64, 当前算符: D_(0, 2) * D_(30, 32), 耗时: 6.009s
[2025-07-25 01:23:41] SW-NE对角算符进度: 32/64, 当前算符: D_(0, 2) * D_(31, 17), 耗时: 6.013s
[2025-07-25 01:23:47] SW-NE对角算符进度: 33/64, 当前算符: D_(0, 2) * D_(32, 34), 耗时: 6.011s
[2025-07-25 01:23:53] SW-NE对角算符进度: 34/64, 当前算符: D_(0, 2) * D_(33, 51), 耗时: 5.984s
[2025-07-25 01:23:59] SW-NE对角算符进度: 35/64, 当前算符: D_(0, 2) * D_(34, 52), 耗时: 5.977s
[2025-07-25 01:24:05] SW-NE对角算符进度: 36/64, 当前算符: D_(0, 2) * D_(35, 37), 耗时: 5.971s
[2025-07-25 01:24:11] SW-NE对角算符进度: 37/64, 当前算符: D_(0, 2) * D_(36, 38), 耗时: 6.015s
[2025-07-25 01:24:17] SW-NE对角算符进度: 38/64, 当前算符: D_(0, 2) * D_(37, 55), 耗时: 6.013s
[2025-07-25 01:24:23] SW-NE对角算符进度: 39/64, 当前算符: D_(0, 2) * D_(38, 56), 耗时: 5.985s
[2025-07-25 01:24:29] SW-NE对角算符进度: 40/64, 当前算符: D_(0, 2) * D_(39, 41), 耗时: 5.982s
[2025-07-25 01:24:35] SW-NE对角算符进度: 41/64, 当前算符: D_(0, 2) * D_(40, 42), 耗时: 5.966s
[2025-07-25 01:24:41] SW-NE对角算符进度: 42/64, 当前算符: D_(0, 2) * D_(41, 59), 耗时: 6.013s
[2025-07-25 01:24:47] SW-NE对角算符进度: 43/64, 当前算符: D_(0, 2) * D_(42, 60), 耗时: 6.007s
[2025-07-25 01:24:53] SW-NE对角算符进度: 44/64, 当前算符: D_(0, 2) * D_(43, 45), 耗时: 6.015s
[2025-07-25 01:24:59] SW-NE对角算符进度: 45/64, 当前算符: D_(0, 2) * D_(44, 46), 耗时: 5.975s
[2025-07-25 01:25:05] SW-NE对角算符进度: 46/64, 当前算符: D_(0, 2) * D_(45, 63), 耗时: 5.981s
[2025-07-25 01:25:11] SW-NE对角算符进度: 47/64, 当前算符: D_(0, 2) * D_(46, 48), 耗时: 6.010s
[2025-07-25 01:25:17] SW-NE对角算符进度: 48/64, 当前算符: D_(0, 2) * D_(47, 33), 耗时: 6.013s
[2025-07-25 01:25:23] SW-NE对角算符进度: 49/64, 当前算符: D_(0, 2) * D_(48, 50), 耗时: 6.016s
[2025-07-25 01:25:29] SW-NE对角算符进度: 50/64, 当前算符: D_(0, 2) * D_(49, 3), 耗时: 5.972s
[2025-07-25 01:25:35] SW-NE对角算符进度: 51/64, 当前算符: D_(0, 2) * D_(50, 4), 耗时: 5.970s
[2025-07-25 01:25:41] SW-NE对角算符进度: 52/64, 当前算符: D_(0, 2) * D_(51, 53), 耗时: 5.971s
[2025-07-25 01:25:47] SW-NE对角算符进度: 53/64, 当前算符: D_(0, 2) * D_(52, 54), 耗时: 6.013s
[2025-07-25 01:25:53] SW-NE对角算符进度: 54/64, 当前算符: D_(0, 2) * D_(53, 7), 耗时: 6.007s
[2025-07-25 01:25:59] SW-NE对角算符进度: 55/64, 当前算符: D_(0, 2) * D_(54, 8), 耗时: 5.979s
[2025-07-25 01:26:05] SW-NE对角算符进度: 56/64, 当前算符: D_(0, 2) * D_(55, 57), 耗时: 5.972s
[2025-07-25 01:26:11] SW-NE对角算符进度: 57/64, 当前算符: D_(0, 2) * D_(56, 58), 耗时: 5.971s
[2025-07-25 01:26:17] SW-NE对角算符进度: 58/64, 当前算符: D_(0, 2) * D_(57, 11), 耗时: 6.017s
[2025-07-25 01:26:23] SW-NE对角算符进度: 59/64, 当前算符: D_(0, 2) * D_(58, 12), 耗时: 6.011s
[2025-07-25 01:26:29] SW-NE对角算符进度: 60/64, 当前算符: D_(0, 2) * D_(59, 61), 耗时: 6.014s
[2025-07-25 01:26:35] SW-NE对角算符进度: 61/64, 当前算符: D_(0, 2) * D_(60, 62), 耗时: 5.971s
[2025-07-25 01:26:41] SW-NE对角算符进度: 62/64, 当前算符: D_(0, 2) * D_(61, 15), 耗时: 5.981s
[2025-07-25 01:26:46] SW-NE对角算符进度: 63/64, 当前算符: D_(0, 2) * D_(62, 0), 耗时: 4.705s
[2025-07-25 01:26:52] SW-NE对角算符进度: 64/64, 当前算符: D_(0, 2) * D_(63, 49), 耗时: 6.013s
[2025-07-25 01:26:52] 西南-东北方向对角二聚体相关函数计算完成,耗时: 378.81 秒
[2025-07-25 01:26:52] 计算傅里叶变换...
[2025-07-25 01:26:52] 对角二聚体结构因子计算完成
[2025-07-25 01:26:53] 对角二聚体相关函数平均误差: 0.000122
