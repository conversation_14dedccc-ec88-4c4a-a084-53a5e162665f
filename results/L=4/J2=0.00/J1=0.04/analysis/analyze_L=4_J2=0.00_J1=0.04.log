[2025-08-01 01:56:22] 使用checkpoint文件: results/L=4/J2=0.00/J1=0.04/training/checkpoints/final_GCNN.pkl
[2025-08-01 01:56:30] ✓ 从checkpoint加载参数: final
[2025-08-01 01:56:30]   - 能量: -54.663975-0.000269j ± 0.043090
[2025-08-01 01:56:30] ================================================================================
[2025-08-01 01:56:30] 加载量子态: L=4, J2=0.00, J1=0.04
[2025-08-01 01:56:30] 设置样本数为: 1048576
[2025-08-01 01:56:30] 开始生成共享样本集...
[2025-08-01 01:59:02] 样本生成完成,耗时: 151.717 秒
[2025-08-01 01:59:02] ================================================================================
[2025-08-01 01:59:02] 开始计算自旋结构因子...
[2025-08-01 01:59:02] 初始化操作符缓存...
[2025-08-01 01:59:02] 预构建所有自旋相关操作符...
[2025-08-01 01:59:02] 开始计算自旋相关函数...
[2025-08-01 01:59:13] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 10.895s
[2025-08-01 01:59:27] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 13.633s
[2025-08-01 01:59:35] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 8.133s
[2025-08-01 01:59:43] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 8.105s
[2025-08-01 01:59:51] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 8.144s
[2025-08-01 01:59:59] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 8.145s
[2025-08-01 02:00:07] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 8.103s
[2025-08-01 02:00:16] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 8.129s
[2025-08-01 02:00:24] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 8.103s
[2025-08-01 02:00:32] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 8.105s
[2025-08-01 02:00:40] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 8.128s
[2025-08-01 02:00:48] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 8.108s
[2025-08-01 02:00:56] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 8.138s
[2025-08-01 02:01:04] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 8.107s
[2025-08-01 02:01:12] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 8.151s
[2025-08-01 02:01:21] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 8.105s
[2025-08-01 02:01:29] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 8.156s
[2025-08-01 02:01:37] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 8.146s
[2025-08-01 02:01:45] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 8.103s
[2025-08-01 02:01:53] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 8.124s
[2025-08-01 02:02:01] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 8.105s
[2025-08-01 02:02:09] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 8.106s
[2025-08-01 02:02:17] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 8.148s
[2025-08-01 02:02:26] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 8.105s
[2025-08-01 02:02:34] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 8.136s
[2025-08-01 02:02:42] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 8.134s
[2025-08-01 02:02:50] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 8.104s
[2025-08-01 02:02:58] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 8.137s
[2025-08-01 02:03:06] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 8.103s
[2025-08-01 02:03:14] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 8.106s
[2025-08-01 02:03:22] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 8.145s
[2025-08-01 02:03:31] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 8.136s
[2025-08-01 02:03:39] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 8.102s
[2025-08-01 02:03:47] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 8.106s
[2025-08-01 02:03:55] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 8.148s
[2025-08-01 02:04:03] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 8.104s
[2025-08-01 02:04:11] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 8.162s
[2025-08-01 02:04:19] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 8.143s
[2025-08-01 02:04:27] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 8.105s
[2025-08-01 02:04:36] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 8.150s
[2025-08-01 02:04:44] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 8.104s
[2025-08-01 02:04:52] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 8.103s
[2025-08-01 02:05:00] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 8.150s
[2025-08-01 02:05:08] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 8.102s
[2025-08-01 02:05:16] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 8.157s
[2025-08-01 02:05:24] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 8.136s
[2025-08-01 02:05:32] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 8.106s
[2025-08-01 02:05:41] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 8.147s
[2025-08-01 02:05:49] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 8.103s
[2025-08-01 02:05:57] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 8.103s
[2025-08-01 02:06:05] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 8.146s
[2025-08-01 02:06:13] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 8.106s
[2025-08-01 02:06:21] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 8.103s
[2025-08-01 02:06:29] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 8.105s
[2025-08-01 02:06:37] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 8.140s
[2025-08-01 02:06:46] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 8.104s
[2025-08-01 02:06:54] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 8.148s
[2025-08-01 02:07:02] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 8.143s
[2025-08-01 02:07:10] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 8.104s
[2025-08-01 02:07:18] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 8.151s
[2025-08-01 02:07:26] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 8.106s
[2025-08-01 02:07:34] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 8.105s
[2025-08-01 02:07:42] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 8.128s
[2025-08-01 02:07:51] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 8.105s
[2025-08-01 02:07:51] 自旋相关函数计算完成,总耗时 528.22 秒
[2025-08-01 02:07:51] 计算傅里叶变换...
[2025-08-01 02:07:51] 自旋结构因子计算完成
[2025-08-01 02:07:52] 自旋相关函数平均误差: 0.000664
