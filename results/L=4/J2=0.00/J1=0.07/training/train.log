[2025-08-04 23:21:22] ✓ 从checkpoint恢复: results/L=4/J2=0.00/J1=0.06/training/checkpoints/final_GCNN.pkl
[2025-08-04 23:21:22]   - 迭代次数: final
[2025-08-04 23:21:22]   - 能量: -55.374490+0.000258j ± 0.042658
[2025-08-04 23:21:22]   - 时间戳: 2025-08-01T00:53:56.073477+08:00
[2025-08-04 23:21:30] ✓ 变分状态参数已从checkpoint恢复
[2025-08-04 23:21:30] ✓ 从final状态恢复, 重置迭代计数为0
[2025-08-04 23:21:30] ==================================================
[2025-08-04 23:21:30] GCNN for Shastry-Sutherland Model
[2025-08-04 23:21:30] ==================================================
[2025-08-04 23:21:30] System parameters:
[2025-08-04 23:21:30]   - System size: L=4, N=64
[2025-08-04 23:21:30]   - System parameters: J1=0.07, J2=0.0, Q=1.0
[2025-08-04 23:21:30] --------------------------------------------------
[2025-08-04 23:21:30] Model parameters:
[2025-08-04 23:21:30]   - Number of layers = 4
[2025-08-04 23:21:30]   - Number of features = 4
[2025-08-04 23:21:30]   - Total parameters = 12572
[2025-08-04 23:21:30] --------------------------------------------------
[2025-08-04 23:21:30] Training parameters:
[2025-08-04 23:21:30]   - Learning rate: 0.015
[2025-08-04 23:21:30]   - Total iterations: 1050
[2025-08-04 23:21:30]   - Annealing cycles: 3
[2025-08-04 23:21:30]   - Initial period: 150
[2025-08-04 23:21:30]   - Period multiplier: 2.0
[2025-08-04 23:21:30]   - Temperature range: 0.0-1.0
[2025-08-04 23:21:30]   - Samples: 16384
[2025-08-04 23:21:30]   - Discarded samples: 0
[2025-08-04 23:21:30]   - Chunk size: 2048
[2025-08-04 23:21:30]   - Diagonal shift: 0.2
[2025-08-04 23:21:30]   - Gradient clipping: 1.0
[2025-08-04 23:21:30]   - Checkpoint enabled: interval=500
[2025-08-04 23:21:30]   - Checkpoint directory: results/L=4/J2=0.00/J1=0.07/training/checkpoints
[2025-08-04 23:21:30] --------------------------------------------------
[2025-08-04 23:21:30] Device status:
[2025-08-04 23:21:30]   - Devices model: A100
[2025-08-04 23:21:30]   - Number of devices: 1
[2025-08-04 23:21:30]   - Sharding: True
[2025-08-04 23:21:30] ============================================================
[2025-08-04 23:22:18] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -55.742032+0.004416j
[2025-08-04 23:22:52] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -55.744696-0.003150j
[2025-08-04 23:23:11] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -55.767186-0.001261j
[2025-08-04 23:23:30] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -55.780264-0.006338j
[2025-08-04 23:23:49] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -55.735812-0.001908j
[2025-08-04 23:24:08] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -55.737078-0.002834j
[2025-08-04 23:24:27] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -55.773635-0.001960j
[2025-08-04 23:24:46] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -55.761515-0.001470j
[2025-08-04 23:25:05] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -55.724343-0.000617j
[2025-08-04 23:25:24] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -55.774887-0.001994j
[2025-08-04 23:25:43] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -55.786010-0.000349j
[2025-08-04 23:26:02] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -55.712170+0.000671j
[2025-08-04 23:26:21] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -55.753714-0.000538j
[2025-08-04 23:26:40] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -55.797464+0.000045j
[2025-08-04 23:26:59] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -55.837658+0.000578j
[2025-08-04 23:27:19] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -55.790778-0.002487j
[2025-08-04 23:27:38] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -55.754681-0.000336j
[2025-08-04 23:27:57] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -55.805884-0.000160j
[2025-08-04 23:28:16] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -55.785086-0.001522j
[2025-08-04 23:28:35] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -55.763165+0.000246j
[2025-08-04 23:28:54] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -55.768815-0.000307j
[2025-08-04 23:29:13] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -55.814033-0.001840j
[2025-08-04 23:29:32] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -55.815180-0.001425j
[2025-08-04 23:29:51] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -55.801546-0.000871j
[2025-08-04 23:30:10] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -55.811623-0.001837j
[2025-08-04 23:30:29] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -55.815529+0.000845j
[2025-08-04 23:30:48] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -55.799135+0.000331j
[2025-08-04 23:31:07] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -55.775839-0.000465j
[2025-08-04 23:31:26] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -55.838938-0.001581j
[2025-08-04 23:31:45] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -55.793424-0.002189j
[2025-08-04 23:32:04] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -55.826573-0.000482j
[2025-08-04 23:32:23] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -55.823051+0.000136j
[2025-08-04 23:32:42] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -55.794421-0.000652j
[2025-08-04 23:33:01] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -55.792189+0.000182j
[2025-08-04 23:33:20] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -55.788465+0.000055j
[2025-08-04 23:33:39] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -55.842478-0.001225j
[2025-08-04 23:33:58] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -55.759891-0.003151j
[2025-08-04 23:34:17] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -55.833082-0.000560j
[2025-08-04 23:34:36] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -55.781318-0.001034j
[2025-08-04 23:34:55] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -55.820362+0.000738j
[2025-08-04 23:35:14] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -55.828921-0.000639j
[2025-08-04 23:35:34] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -55.835964+0.000019j
[2025-08-04 23:35:53] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -55.856678+0.000320j
[2025-08-04 23:36:12] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -55.768554+0.000270j
[2025-08-04 23:36:31] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -55.779072+0.000591j
[2025-08-04 23:36:50] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -55.780658-0.001437j
[2025-08-04 23:37:09] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -55.802923-0.000402j
[2025-08-04 23:37:28] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -55.904444-0.000946j
[2025-08-04 23:37:47] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -55.801913+0.000912j
[2025-08-04 23:38:06] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -55.824313+0.000834j
[2025-08-04 23:38:25] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -55.813779+0.000584j
[2025-08-04 23:38:44] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -55.830671-0.000886j
[2025-08-04 23:39:03] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -55.842338+0.000180j
[2025-08-04 23:39:22] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -55.864850-0.000891j
[2025-08-04 23:39:41] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -55.845544+0.001697j
[2025-08-04 23:40:00] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -55.889735+0.000837j
[2025-08-04 23:40:19] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -55.849356-0.001211j
[2025-08-04 23:40:38] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -55.826501-0.001625j
[2025-08-04 23:40:57] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -55.793376+0.001055j
[2025-08-04 23:41:16] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -55.836764+0.000017j
[2025-08-04 23:41:35] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -55.803003+0.000001j
[2025-08-04 23:41:54] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -55.774752+0.001041j
[2025-08-04 23:42:13] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -55.798274-0.001232j
[2025-08-04 23:42:32] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -55.818716-0.000148j
[2025-08-04 23:42:51] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -55.724095-0.001607j
[2025-08-04 23:43:10] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -55.798077+0.000526j
[2025-08-04 23:43:29] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -55.834868+0.000118j
[2025-08-04 23:43:48] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -55.837620+0.000834j
[2025-08-04 23:44:07] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -55.857297+0.000178j
[2025-08-04 23:44:26] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -55.873682-0.000913j
[2025-08-04 23:44:45] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -55.835565-0.000263j
[2025-08-04 23:45:04] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -55.873257+0.000643j
[2025-08-04 23:45:23] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -55.884199+0.000154j
[2025-08-04 23:45:43] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -55.857089-0.000499j
[2025-08-04 23:46:02] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -55.889542+0.002247j
[2025-08-04 23:46:21] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -55.912616-0.000569j
[2025-08-04 23:46:40] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -55.902714-0.000733j
[2025-08-04 23:46:59] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -55.827528-0.000528j
[2025-08-04 23:47:18] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -55.832184-0.000109j
[2025-08-04 23:47:37] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -55.818130+0.002732j
[2025-08-04 23:47:56] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -55.815681+0.000567j
[2025-08-04 23:48:15] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -55.813902+0.001135j
[2025-08-04 23:48:34] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -55.846727-0.000810j
[2025-08-04 23:48:53] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -55.814018+0.000302j
[2025-08-04 23:49:12] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -55.771737-0.000997j
[2025-08-04 23:49:31] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -55.817715-0.000199j
[2025-08-04 23:49:50] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -55.817126-0.000943j
[2025-08-04 23:50:09] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -55.765923-0.000293j
[2025-08-04 23:50:28] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -55.772781-0.000998j
[2025-08-04 23:50:47] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -55.811473+0.000110j
[2025-08-04 23:51:06] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -55.803534-0.001351j
[2025-08-04 23:51:25] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -55.839781+0.001557j
[2025-08-04 23:51:44] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -55.835950+0.000228j
[2025-08-04 23:52:03] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -55.836732+0.000122j
[2025-08-04 23:52:22] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -55.826511+0.000523j
[2025-08-04 23:52:41] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -55.795490-0.001336j
[2025-08-04 23:53:00] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -55.839522-0.000746j
[2025-08-04 23:53:19] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -55.805736+0.002412j
[2025-08-04 23:53:38] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -55.865758+0.001329j
[2025-08-04 23:53:57] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -55.855704-0.001476j
[2025-08-04 23:54:16] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -55.867034-0.000730j
[2025-08-04 23:54:35] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -55.756864-0.001250j
[2025-08-04 23:54:55] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -55.819981+0.002044j
[2025-08-04 23:55:14] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -55.800665-0.001332j
[2025-08-04 23:55:33] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -55.771646+0.001549j
[2025-08-04 23:55:52] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -55.775674+0.001486j
[2025-08-04 23:56:11] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -55.852933-0.000462j
[2025-08-04 23:56:30] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -55.890579-0.000409j
[2025-08-04 23:56:49] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -55.793620+0.001166j
[2025-08-04 23:57:08] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -55.767180+0.001973j
[2025-08-04 23:57:27] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -55.821014-0.000378j
[2025-08-04 23:57:46] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -55.797921+0.003775j
[2025-08-04 23:58:05] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -55.825565-0.000922j
[2025-08-04 23:58:24] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -55.825818-0.001520j
[2025-08-04 23:58:43] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -55.814030-0.000102j
[2025-08-04 23:59:02] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -55.865464-0.001745j
[2025-08-04 23:59:21] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -55.822944+0.001635j
[2025-08-04 23:59:40] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -55.824344-0.001295j
[2025-08-04 23:59:59] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -55.820711-0.000590j
[2025-08-05 00:00:18] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -55.809722-0.000566j
[2025-08-05 00:00:37] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -55.816614-0.001257j
[2025-08-05 00:00:56] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -55.793642-0.000923j
[2025-08-05 00:01:15] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -55.780774+0.000402j
[2025-08-05 00:01:34] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -55.799823-0.001055j
[2025-08-05 00:01:53] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -55.848937-0.000064j
[2025-08-05 00:02:12] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -55.860345-0.000717j
[2025-08-05 00:02:31] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -55.940692+0.000074j
[2025-08-05 00:02:50] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -55.864469+0.001774j
[2025-08-05 00:03:09] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -55.890620+0.000078j
[2025-08-05 00:03:28] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -55.897946+0.002568j
[2025-08-05 00:03:48] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -55.855442+0.001457j
[2025-08-05 00:04:07] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -55.860564+0.000759j
[2025-08-05 00:04:26] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -55.875455+0.000970j
[2025-08-05 00:04:45] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -55.910169-0.002118j
[2025-08-05 00:05:04] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -55.932027-0.000392j
[2025-08-05 00:05:23] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -55.845745+0.000768j
[2025-08-05 00:05:42] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -55.909971+0.000770j
[2025-08-05 00:06:01] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -55.883185+0.002337j
[2025-08-05 00:06:20] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -55.888394+0.001293j
[2025-08-05 00:06:39] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -55.873487-0.000615j
[2025-08-05 00:06:58] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -55.912882+0.000510j
[2025-08-05 00:07:17] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -55.872685+0.000196j
[2025-08-05 00:07:36] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -55.902166-0.000756j
[2025-08-05 00:07:55] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -55.840247+0.000977j
[2025-08-05 00:08:14] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -55.779701+0.000329j
[2025-08-05 00:08:33] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -55.759059+0.000716j
[2025-08-05 00:08:52] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -55.866446+0.002076j
[2025-08-05 00:09:11] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -55.881421+0.000445j
[2025-08-05 00:09:30] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -55.880572-0.000784j
[2025-08-05 00:09:49] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -55.881970+0.001365j
[2025-08-05 00:09:49] RESTART #1 | Period: 300
[2025-08-05 00:10:08] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -55.909965+0.000031j
[2025-08-05 00:10:27] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -55.931454+0.002111j
[2025-08-05 00:10:46] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -55.929064-0.002483j
[2025-08-05 00:11:05] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -55.948904+0.000009j
[2025-08-05 00:11:24] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -55.929649+0.000976j
[2025-08-05 00:11:43] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -55.889707+0.000753j
[2025-08-05 00:12:02] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -55.876504-0.001405j
[2025-08-05 00:12:21] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -55.863595+0.000235j
[2025-08-05 00:12:40] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -55.861972-0.001120j
[2025-08-05 00:12:59] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -55.869311-0.002457j
[2025-08-05 00:13:18] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -55.841724+0.000298j
[2025-08-05 00:13:37] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -55.817167+0.000388j
[2025-08-05 00:13:56] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -55.838398+0.001506j
[2025-08-05 00:14:15] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -55.884255-0.000259j
[2025-08-05 00:14:35] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -55.855418-0.000363j
[2025-08-05 00:14:54] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -55.896086-0.001183j
[2025-08-05 00:15:13] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -55.887259-0.001400j
[2025-08-05 00:15:32] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -55.906739+0.000172j
[2025-08-05 00:15:51] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -55.873513+0.001099j
[2025-08-05 00:16:10] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -55.906439-0.001305j
[2025-08-05 00:16:29] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -55.832979-0.000254j
[2025-08-05 00:16:48] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -55.833620+0.000896j
[2025-08-05 00:17:07] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -55.848944-0.000449j
[2025-08-05 00:17:26] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -55.878112-0.000003j
[2025-08-05 00:17:45] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -55.897579-0.000501j
[2025-08-05 00:18:04] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -55.861275+0.001276j
[2025-08-05 00:18:23] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -55.836933-0.001927j
[2025-08-05 00:18:42] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -55.811134+0.000497j
[2025-08-05 00:19:01] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -55.794694+0.001638j
[2025-08-05 00:19:20] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -55.785882-0.000013j
[2025-08-05 00:19:39] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -55.800895+0.000950j
[2025-08-05 00:19:58] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -55.835746-0.000637j
[2025-08-05 00:20:17] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -55.829581-0.000252j
[2025-08-05 00:20:36] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -55.831075+0.002725j
[2025-08-05 00:20:55] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -55.864079-0.002398j
[2025-08-05 00:21:14] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -55.829054-0.000746j
[2025-08-05 00:21:33] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -55.803189-0.001283j
[2025-08-05 00:21:52] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -55.830338-0.000416j
[2025-08-05 00:22:11] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -55.848235+0.000770j
[2025-08-05 00:22:30] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -55.832687-0.000320j
[2025-08-05 00:22:49] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -55.850504+0.000550j
[2025-08-05 00:23:08] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -55.833257+0.000212j
[2025-08-05 00:23:27] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -55.859152-0.000455j
[2025-08-05 00:23:46] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -55.807929+0.001008j
[2025-08-05 00:24:05] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -55.816925-0.000324j
[2025-08-05 00:24:25] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -55.889853+0.000057j
[2025-08-05 00:24:44] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -55.788969+0.002218j
[2025-08-05 00:25:03] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -55.856800+0.000423j
[2025-08-05 00:25:22] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -55.824194-0.000215j
[2025-08-05 00:25:41] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -55.771398+0.000434j
[2025-08-05 00:26:00] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -55.795046-0.000223j
[2025-08-05 00:26:19] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -55.765730+0.000275j
[2025-08-05 00:26:38] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -55.805280-0.001465j
[2025-08-05 00:26:57] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -55.820804-0.001373j
[2025-08-05 00:27:16] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -55.865429+0.000316j
[2025-08-05 00:27:35] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -55.874202-0.001691j
[2025-08-05 00:27:54] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -55.908958+0.000506j
[2025-08-05 00:28:13] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -55.906404-0.000089j
[2025-08-05 00:28:32] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -55.881558+0.000849j
[2025-08-05 00:28:51] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -55.846980-0.000868j
[2025-08-05 00:29:10] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -55.836640+0.001104j
[2025-08-05 00:29:29] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -55.774864-0.001352j
[2025-08-05 00:29:48] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -55.807140+0.001224j
[2025-08-05 00:30:07] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -55.809129-0.000115j
[2025-08-05 00:30:26] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -55.797004+0.003444j
[2025-08-05 00:30:45] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -55.806020+0.001194j
[2025-08-05 00:31:04] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -55.816017-0.001898j
[2025-08-05 00:31:23] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -55.773013+0.000930j
[2025-08-05 00:31:42] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -55.787967-0.000513j
[2025-08-05 00:32:01] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -55.817492+0.001706j
[2025-08-05 00:32:20] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -55.844687-0.000376j
[2025-08-05 00:32:39] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -55.866994-0.001178j
[2025-08-05 00:32:58] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -55.857089-0.000743j
[2025-08-05 00:33:18] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -55.860344-0.001151j
[2025-08-05 00:33:37] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -55.801165-0.000427j
[2025-08-05 00:33:56] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -55.741316-0.000851j
[2025-08-05 00:34:15] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -55.718428+0.000620j
[2025-08-05 00:34:34] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -55.765261-0.001669j
[2025-08-05 00:34:53] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -55.816636+0.000381j
[2025-08-05 00:35:12] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -55.783459+0.000411j
[2025-08-05 00:35:31] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -55.791545-0.001738j
[2025-08-05 00:35:50] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -55.783335+0.000920j
[2025-08-05 00:36:09] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -55.798632-0.000107j
[2025-08-05 00:36:28] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -55.774486+0.000295j
[2025-08-05 00:36:47] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -55.817741+0.000748j
[2025-08-05 00:37:06] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -55.779762-0.001832j
[2025-08-05 00:37:25] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -55.780483+0.001432j
[2025-08-05 00:37:44] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -55.777007-0.001206j
[2025-08-05 00:38:03] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -55.848863-0.001343j
[2025-08-05 00:38:22] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -55.829692+0.000414j
[2025-08-05 00:38:41] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -55.849649-0.000506j
[2025-08-05 00:39:00] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -55.831123+0.000925j
[2025-08-05 00:39:19] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -55.835863-0.001827j
[2025-08-05 00:39:38] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -55.846840-0.000079j
[2025-08-05 00:39:57] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -55.844589-0.002196j
[2025-08-05 00:40:16] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -55.843289-0.000240j
[2025-08-05 00:40:35] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -55.825795+0.001128j
[2025-08-05 00:40:54] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -55.800465+0.001246j
[2025-08-05 00:41:13] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -55.823072+0.001751j
[2025-08-05 00:41:32] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -55.866159+0.000751j
[2025-08-05 00:41:51] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -55.850393-0.000490j
[2025-08-05 00:42:10] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -55.829322-0.000384j
[2025-08-05 00:42:29] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -55.798128-0.000526j
[2025-08-05 00:42:48] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -55.833199+0.001388j
[2025-08-05 00:43:07] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -55.812907+0.002424j
[2025-08-05 00:43:26] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -55.862598-0.001007j
[2025-08-05 00:43:45] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -55.908522+0.000826j
[2025-08-05 00:44:04] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -55.892509+0.003198j
[2025-08-05 00:44:23] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -55.881262-0.000254j
[2025-08-05 00:44:43] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -55.869048+0.000495j
[2025-08-05 00:45:02] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -55.862739+0.001791j
[2025-08-05 00:45:21] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -55.850203-0.001203j
[2025-08-05 00:45:40] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -55.839439-0.000327j
[2025-08-05 00:45:59] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -55.826393-0.001804j
[2025-08-05 00:46:18] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -55.818202+0.000279j
[2025-08-05 00:46:37] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -55.821254-0.000193j
[2025-08-05 00:46:56] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -55.825867+0.000174j
[2025-08-05 00:47:15] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -55.867334+0.000449j
[2025-08-05 00:47:34] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -55.792132-0.000449j
[2025-08-05 00:47:53] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -55.815845+0.000026j
[2025-08-05 00:48:12] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -55.855854-0.000809j
[2025-08-05 00:48:31] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -55.892999+0.001132j
[2025-08-05 00:48:50] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -55.859227+0.001729j
[2025-08-05 00:49:09] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -55.849083-0.000440j
[2025-08-05 00:49:28] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -55.891389-0.002120j
[2025-08-05 00:49:47] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -55.879541-0.000155j
[2025-08-05 00:50:06] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -55.880340+0.000188j
[2025-08-05 00:50:25] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -55.887005-0.000592j
[2025-08-05 00:50:44] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -55.847723+0.000099j
[2025-08-05 00:51:03] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -55.825318-0.001022j
[2025-08-05 00:51:22] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -55.832821+0.000973j
[2025-08-05 00:51:41] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -55.781657-0.002081j
[2025-08-05 00:52:00] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -55.816581+0.000891j
[2025-08-05 00:52:19] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -55.778565+0.001778j
[2025-08-05 00:52:38] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -55.736489+0.000576j
[2025-08-05 00:52:57] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -55.832016-0.000456j
[2025-08-05 00:53:16] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -55.773571-0.001375j
[2025-08-05 00:53:35] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -55.796691+0.000653j
[2025-08-05 00:53:54] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -55.830465+0.000766j
[2025-08-05 00:54:13] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -55.863352+0.000483j
[2025-08-05 00:54:32] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -55.776417+0.000149j
[2025-08-05 00:54:51] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -55.752445-0.002167j
[2025-08-05 00:55:10] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -55.770724+0.000776j
[2025-08-05 00:55:30] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -55.765998+0.000043j
[2025-08-05 00:55:49] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -55.778214-0.000026j
[2025-08-05 00:56:08] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -55.839545-0.000455j
[2025-08-05 00:56:27] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -55.818468-0.001361j
[2025-08-05 00:56:46] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -55.796100-0.000276j
[2025-08-05 00:57:05] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -55.781893+0.000558j
[2025-08-05 00:57:24] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -55.863586-0.001818j
[2025-08-05 00:57:43] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -55.823305+0.000129j
[2025-08-05 00:58:02] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -55.846073-0.000223j
[2025-08-05 00:58:21] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -55.838713+0.001345j
[2025-08-05 00:58:40] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -55.831279+0.000147j
[2025-08-05 00:58:59] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -55.858722+0.001746j
[2025-08-05 00:59:18] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -55.843941+0.000026j
[2025-08-05 00:59:37] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -55.783432+0.001127j
[2025-08-05 00:59:56] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -55.824722+0.001351j
[2025-08-05 01:00:15] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -55.823890-0.000164j
[2025-08-05 01:00:34] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -55.782033-0.001587j
[2025-08-05 01:00:53] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -55.796365+0.001795j
[2025-08-05 01:01:12] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -55.835495+0.000508j
[2025-08-05 01:01:31] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -55.828368+0.000988j
[2025-08-05 01:01:50] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -55.866988+0.003012j
[2025-08-05 01:02:09] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -55.858697+0.000226j
[2025-08-05 01:02:28] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -55.843871-0.001540j
[2025-08-05 01:02:47] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -55.835912+0.000710j
[2025-08-05 01:03:06] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -55.855394-0.000823j
[2025-08-05 01:03:25] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -55.819282+0.001644j
[2025-08-05 01:03:44] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -55.785104-0.001861j
[2025-08-05 01:04:04] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -55.855965+0.000375j
[2025-08-05 01:04:23] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -55.812030+0.001931j
[2025-08-05 01:04:42] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -55.818099-0.001351j
[2025-08-05 01:05:01] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -55.862446+0.000834j
[2025-08-05 01:05:20] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -55.847846-0.002432j
[2025-08-05 01:05:39] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -55.816148+0.000456j
[2025-08-05 01:05:58] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -55.795471+0.000030j
[2025-08-05 01:06:17] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -55.835951+0.001677j
[2025-08-05 01:06:36] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -55.766486-0.000504j
[2025-08-05 01:06:55] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -55.846193-0.001821j
[2025-08-05 01:07:14] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -55.800707-0.000540j
[2025-08-05 01:07:33] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -55.858391-0.000808j
[2025-08-05 01:07:52] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -55.842933+0.000714j
[2025-08-05 01:08:11] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -55.845729+0.000840j
[2025-08-05 01:08:30] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -55.775828+0.000544j
[2025-08-05 01:08:49] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -55.828143+0.000569j
[2025-08-05 01:09:08] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -55.801682-0.000339j
[2025-08-05 01:09:27] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -55.784817+0.000167j
[2025-08-05 01:09:46] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -55.822091-0.000280j
[2025-08-05 01:10:05] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -55.852332-0.002518j
[2025-08-05 01:10:24] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -55.880612-0.000457j
[2025-08-05 01:10:43] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -55.903857+0.001012j
[2025-08-05 01:11:02] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -55.838823+0.001170j
[2025-08-05 01:11:21] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -55.891352-0.001403j
[2025-08-05 01:11:40] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -55.845349-0.001027j
[2025-08-05 01:11:59] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -55.845642-0.000596j
[2025-08-05 01:12:18] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -55.852302+0.000467j
[2025-08-05 01:12:37] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -55.798124+0.001987j
[2025-08-05 01:12:56] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -55.810265+0.001213j
[2025-08-05 01:13:15] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -55.842390-0.000969j
[2025-08-05 01:13:34] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -55.841174-0.000595j
[2025-08-05 01:13:53] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -55.851728+0.000344j
[2025-08-05 01:14:13] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -55.852598+0.001580j
[2025-08-05 01:14:32] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -55.824449+0.002723j
[2025-08-05 01:14:51] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -55.810580-0.001150j
[2025-08-05 01:15:10] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -55.795774+0.000686j
[2025-08-05 01:15:29] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -55.809366+0.000019j
[2025-08-05 01:15:48] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -55.773567-0.000824j
[2025-08-05 01:16:07] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -55.815284-0.000828j
[2025-08-05 01:16:26] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -55.903936+0.000748j
[2025-08-05 01:16:45] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -55.870524+0.001027j
[2025-08-05 01:17:04] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -55.817163+0.001495j
[2025-08-05 01:17:23] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -55.802736+0.000794j
[2025-08-05 01:17:42] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -55.832866+0.000523j
[2025-08-05 01:18:01] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -55.748781-0.000180j
[2025-08-05 01:18:20] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -55.788959+0.001314j
[2025-08-05 01:18:39] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -55.779811+0.000556j
[2025-08-05 01:18:58] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -55.744043-0.000284j
[2025-08-05 01:19:17] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -55.779727+0.001592j
[2025-08-05 01:19:36] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -55.800555+0.000021j
[2025-08-05 01:19:55] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -55.821477+0.000056j
[2025-08-05 01:20:14] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -55.883411-0.000018j
[2025-08-05 01:20:33] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -55.918506+0.000108j
[2025-08-05 01:20:52] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -55.958732-0.000276j
[2025-08-05 01:21:11] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -55.949360+0.003904j
[2025-08-05 01:21:30] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -55.902705+0.000166j
[2025-08-05 01:21:49] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -55.845005+0.002891j
[2025-08-05 01:22:08] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -55.843361-0.001745j
[2025-08-05 01:22:27] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -55.846871+0.000058j
[2025-08-05 01:22:46] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -55.828898-0.000499j
[2025-08-05 01:23:05] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -55.794241-0.000746j
[2025-08-05 01:23:24] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -55.896785-0.000090j
[2025-08-05 01:23:43] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -55.906457-0.000555j
[2025-08-05 01:24:02] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -55.911442-0.000356j
[2025-08-05 01:24:21] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -55.901577+0.000251j
[2025-08-05 01:24:40] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -55.882556-0.001779j
[2025-08-05 01:24:59] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -55.923470+0.000596j
[2025-08-05 01:25:19] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -55.883732+0.001168j
[2025-08-05 01:25:38] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -55.857619+0.000515j
[2025-08-05 01:25:57] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -55.889512-0.001659j
[2025-08-05 01:26:16] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -55.875452-0.001562j
[2025-08-05 01:26:35] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -55.848741-0.000479j
[2025-08-05 01:26:54] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -55.842108+0.000091j
[2025-08-05 01:27:13] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -55.819262-0.000558j
[2025-08-05 01:27:32] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -55.793766+0.000382j
[2025-08-05 01:27:51] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -55.857736-0.000471j
[2025-08-05 01:28:10] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -55.827278-0.000005j
[2025-08-05 01:28:29] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -55.824116-0.000136j
[2025-08-05 01:28:48] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -55.794567+0.000092j
[2025-08-05 01:29:07] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -55.800050+0.001947j
[2025-08-05 01:29:26] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -55.810738-0.000052j
[2025-08-05 01:29:45] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -55.853494-0.000455j
[2025-08-05 01:30:04] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -55.811951+0.001094j
[2025-08-05 01:30:23] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -55.847850+0.001235j
[2025-08-05 01:30:42] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -55.876895+0.001415j
[2025-08-05 01:31:01] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -55.887307+0.001258j
[2025-08-05 01:31:20] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -55.831444+0.002004j
[2025-08-05 01:31:39] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -55.782415+0.000729j
[2025-08-05 01:31:58] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -55.766829+0.000338j
[2025-08-05 01:32:17] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -55.811376+0.000747j
[2025-08-05 01:32:36] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -55.820129+0.001631j
[2025-08-05 01:32:55] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -55.799692-0.001414j
[2025-08-05 01:33:14] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -55.766940-0.000835j
[2025-08-05 01:33:33] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -55.747725-0.000704j
[2025-08-05 01:33:52] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -55.776570-0.000046j
[2025-08-05 01:34:11] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -55.784228-0.000430j
[2025-08-05 01:34:30] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -55.793728+0.000499j
[2025-08-05 01:34:49] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -55.841906+0.000423j
[2025-08-05 01:35:08] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -55.779608-0.002001j
[2025-08-05 01:35:27] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -55.762011+0.000371j
[2025-08-05 01:35:46] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -55.850835+0.000186j
[2025-08-05 01:36:06] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -55.776927+0.000043j
[2025-08-05 01:36:25] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -55.798669-0.000535j
[2025-08-05 01:36:44] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -55.790211+0.000221j
[2025-08-05 01:37:03] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -55.807150+0.000051j
[2025-08-05 01:37:22] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -55.788447+0.000363j
[2025-08-05 01:37:41] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -55.825567+0.000576j
[2025-08-05 01:38:00] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -55.792874-0.000841j
[2025-08-05 01:38:19] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -55.804817-0.000926j
[2025-08-05 01:38:38] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -55.856908+0.002718j
[2025-08-05 01:38:57] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -55.872325+0.001079j
[2025-08-05 01:39:16] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -55.858310-0.002631j
[2025-08-05 01:39:35] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -55.770743+0.000885j
[2025-08-05 01:39:54] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -55.771179-0.001058j
[2025-08-05 01:40:13] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -55.761691-0.000502j
[2025-08-05 01:40:32] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -55.825265-0.000011j
[2025-08-05 01:40:51] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -55.844547-0.000493j
[2025-08-05 01:41:10] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -55.820161-0.000667j
[2025-08-05 01:41:29] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -55.861076-0.000021j
[2025-08-05 01:41:48] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -55.869456+0.001820j
[2025-08-05 01:42:07] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -55.830769+0.000788j
[2025-08-05 01:42:26] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -55.831936+0.000422j
[2025-08-05 01:42:45] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -55.789672-0.000991j
[2025-08-05 01:43:04] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -55.850874-0.001404j
[2025-08-05 01:43:23] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -55.844741+0.000520j
[2025-08-05 01:43:42] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -55.865617-0.000869j
[2025-08-05 01:44:01] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -55.772626+0.000063j
[2025-08-05 01:44:20] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -55.782839+0.000384j
[2025-08-05 01:44:39] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -55.798240+0.000065j
[2025-08-05 01:44:58] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -55.820554-0.001399j
[2025-08-05 01:44:58] RESTART #2 | Period: 600
[2025-08-05 01:45:18] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -55.849012+0.001961j
[2025-08-05 01:45:37] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -55.817605-0.000841j
[2025-08-05 01:45:56] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -55.830890-0.002309j
[2025-08-05 01:46:15] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -55.792787-0.000107j
[2025-08-05 01:46:34] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -55.817009-0.000135j
[2025-08-05 01:46:53] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -55.829209+0.003635j
[2025-08-05 01:47:12] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -55.879394-0.002278j
[2025-08-05 01:47:31] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -55.852523+0.000903j
[2025-08-05 01:47:50] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -55.913813+0.000744j
[2025-08-05 01:48:09] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -55.848051+0.000259j
[2025-08-05 01:48:28] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -55.836506+0.001032j
[2025-08-05 01:48:47] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -55.890246+0.000244j
[2025-08-05 01:49:06] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -55.823369-0.000839j
[2025-08-05 01:49:25] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -55.844742-0.000184j
[2025-08-05 01:49:44] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -55.846631-0.001139j
[2025-08-05 01:50:03] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -55.795436-0.000353j
[2025-08-05 01:50:22] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -55.843366-0.000580j
[2025-08-05 01:50:41] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -55.801852-0.001039j
[2025-08-05 01:51:00] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -55.737956+0.000993j
[2025-08-05 01:51:19] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -55.726477+0.000754j
[2025-08-05 01:51:38] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -55.776188+0.000001j
[2025-08-05 01:51:57] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -55.770052+0.000928j
[2025-08-05 01:52:16] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -55.720708-0.000250j
[2025-08-05 01:52:35] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -55.752852-0.000619j
[2025-08-05 01:52:54] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -55.842580-0.000209j
[2025-08-05 01:53:13] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -55.818658-0.000130j
[2025-08-05 01:53:32] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -55.889927+0.000091j
[2025-08-05 01:53:51] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -55.922077-0.000787j
[2025-08-05 01:54:10] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -55.927086-0.001660j
[2025-08-05 01:54:29] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -55.944635-0.000958j
[2025-08-05 01:54:49] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -55.936514-0.000196j
[2025-08-05 01:55:08] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -55.867168-0.002819j
[2025-08-05 01:55:27] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -55.903708-0.000302j
[2025-08-05 01:55:46] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -55.884329-0.000524j
[2025-08-05 01:56:05] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -55.870549+0.000137j
[2025-08-05 01:56:24] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -55.798781+0.000321j
[2025-08-05 01:56:43] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -55.812357-0.000436j
[2025-08-05 01:57:02] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -55.861659-0.001406j
[2025-08-05 01:57:21] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -55.884091+0.001233j
[2025-08-05 01:57:40] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -55.962132+0.000656j
[2025-08-05 01:57:59] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -55.867673+0.001532j
[2025-08-05 01:58:18] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -55.863346+0.000166j
[2025-08-05 01:58:37] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -55.892189+0.000037j
[2025-08-05 01:58:56] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -55.882326-0.000160j
[2025-08-05 01:59:15] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -55.869265+0.000089j
[2025-08-05 01:59:34] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -55.844903+0.000345j
[2025-08-05 01:59:53] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -55.815263-0.001477j
[2025-08-05 02:00:12] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -55.796296-0.001940j
[2025-08-05 02:00:31] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -55.781823-0.000053j
[2025-08-05 02:00:50] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -55.810770+0.000228j
[2025-08-05 02:00:50] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-08-05 02:01:09] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -55.734488-0.000595j
[2025-08-05 02:01:28] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -55.785378+0.000405j
[2025-08-05 02:01:47] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -55.766491+0.001405j
[2025-08-05 02:02:06] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -55.817151-0.000548j
[2025-08-05 02:02:25] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -55.881646+0.000841j
[2025-08-05 02:02:44] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -55.881219+0.000915j
[2025-08-05 02:03:03] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -55.827265+0.000915j
[2025-08-05 02:03:22] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -55.829682-0.000610j
[2025-08-05 02:03:41] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -55.917987+0.001118j
[2025-08-05 02:04:00] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -55.811152+0.001024j
[2025-08-05 02:04:19] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -55.813241-0.000203j
[2025-08-05 02:04:38] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -55.853027-0.000275j
[2025-08-05 02:04:57] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -55.794476+0.000787j
[2025-08-05 02:05:16] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -55.802415-0.001628j
[2025-08-05 02:05:36] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -55.778016-0.001050j
[2025-08-05 02:05:55] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -55.737879-0.000174j
[2025-08-05 02:06:14] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -55.750622-0.000017j
[2025-08-05 02:06:33] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -55.807816+0.000029j
[2025-08-05 02:06:52] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -55.793057-0.001265j
[2025-08-05 02:07:11] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -55.763453+0.000729j
[2025-08-05 02:07:30] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -55.797867+0.000700j
[2025-08-05 02:07:49] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -55.774730-0.000382j
[2025-08-05 02:08:08] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -55.816757+0.000282j
[2025-08-05 02:08:27] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -55.846000+0.000387j
[2025-08-05 02:08:46] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -55.854771-0.000792j
[2025-08-05 02:09:05] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -55.786896-0.001614j
[2025-08-05 02:09:24] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -55.778906-0.001066j
[2025-08-05 02:09:43] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -55.805401-0.000436j
[2025-08-05 02:10:02] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -55.830611+0.000759j
[2025-08-05 02:10:21] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -55.837751-0.000523j
[2025-08-05 02:10:40] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -55.799717+0.000035j
[2025-08-05 02:10:59] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -55.780278+0.000514j
[2025-08-05 02:11:18] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -55.747794+0.000338j
[2025-08-05 02:11:37] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -55.755157+0.001838j
[2025-08-05 02:11:56] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -55.738382+0.000620j
[2025-08-05 02:12:15] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -55.739403-0.000069j
[2025-08-05 02:12:34] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -55.801473-0.001036j
[2025-08-05 02:12:53] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -55.795429+0.000731j
[2025-08-05 02:13:12] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -55.818169-0.002159j
[2025-08-05 02:13:31] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -55.785806-0.000992j
[2025-08-05 02:13:50] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -55.817287-0.000251j
[2025-08-05 02:14:09] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -55.815029+0.002357j
[2025-08-05 02:14:28] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -55.819682-0.000935j
[2025-08-05 02:14:48] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -55.751937-0.002135j
[2025-08-05 02:15:07] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -55.803405-0.001465j
[2025-08-05 02:15:26] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -55.811180-0.001940j
[2025-08-05 02:15:45] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -55.782969-0.001478j
[2025-08-05 02:16:04] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -55.798207-0.000482j
[2025-08-05 02:16:23] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -55.868284-0.001259j
[2025-08-05 02:16:42] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -55.835039+0.001392j
[2025-08-05 02:17:01] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -55.850511+0.000909j
[2025-08-05 02:17:20] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -55.831522+0.001237j
[2025-08-05 02:17:39] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -55.788610-0.000948j
[2025-08-05 02:17:58] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -55.871322-0.000505j
[2025-08-05 02:18:17] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -55.789584+0.002023j
[2025-08-05 02:18:36] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -55.846251-0.001399j
[2025-08-05 02:18:55] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -55.749516+0.002476j
[2025-08-05 02:19:14] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -55.817110+0.000115j
[2025-08-05 02:19:33] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -55.763034+0.000082j
[2025-08-05 02:19:52] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -55.766497+0.000408j
[2025-08-05 02:20:11] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -55.732191-0.000492j
[2025-08-05 02:20:30] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -55.758120-0.000733j
[2025-08-05 02:20:49] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -55.750005+0.000243j
[2025-08-05 02:21:08] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -55.834585-0.000009j
[2025-08-05 02:21:27] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -55.813185+0.001736j
[2025-08-05 02:21:46] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -55.761897+0.000820j
[2025-08-05 02:22:05] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -55.791819+0.001357j
[2025-08-05 02:22:24] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -55.814914-0.002557j
[2025-08-05 02:22:43] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -55.812859+0.000852j
[2025-08-05 02:23:02] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -55.815933-0.001737j
[2025-08-05 02:23:21] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -55.830402+0.002032j
[2025-08-05 02:23:40] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -55.842397-0.001468j
[2025-08-05 02:23:59] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -55.806601-0.002055j
[2025-08-05 02:24:18] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -55.802840-0.000331j
[2025-08-05 02:24:37] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -55.826127-0.000178j
[2025-08-05 02:24:56] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -55.828375-0.002139j
[2025-08-05 02:25:15] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -55.837165-0.002037j
[2025-08-05 02:25:35] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -55.714658-0.000476j
[2025-08-05 02:25:54] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -55.780217-0.000070j
[2025-08-05 02:26:13] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -55.738172+0.000512j
[2025-08-05 02:26:32] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -55.759344+0.001167j
[2025-08-05 02:26:51] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -55.769317-0.000181j
[2025-08-05 02:27:10] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -55.820703-0.001679j
[2025-08-05 02:27:29] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -55.786160+0.001147j
[2025-08-05 02:27:48] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -55.810444-0.001266j
[2025-08-05 02:28:07] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -55.776457-0.000003j
[2025-08-05 02:28:26] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -55.822315-0.000879j
[2025-08-05 02:28:45] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -55.815245-0.000505j
[2025-08-05 02:29:04] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -55.903962-0.001287j
[2025-08-05 02:29:23] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -55.862933-0.000444j
[2025-08-05 02:29:42] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -55.859789-0.001375j
[2025-08-05 02:30:01] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -55.806213-0.001023j
[2025-08-05 02:30:20] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -55.821860-0.000761j
[2025-08-05 02:30:39] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -55.867816+0.000394j
[2025-08-05 02:30:58] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -55.912898+0.000313j
[2025-08-05 02:31:17] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -55.871275-0.000783j
[2025-08-05 02:31:36] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -55.894020-0.001087j
[2025-08-05 02:31:55] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -55.929177+0.001143j
[2025-08-05 02:32:14] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -55.808703+0.000519j
[2025-08-05 02:32:33] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -55.836600+0.001155j
[2025-08-05 02:32:52] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -55.848306+0.001289j
[2025-08-05 02:33:11] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -55.812180+0.001217j
[2025-08-05 02:33:30] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -55.861717+0.000323j
[2025-08-05 02:33:49] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -55.831129+0.000444j
[2025-08-05 02:34:08] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -55.810745-0.000178j
[2025-08-05 02:34:27] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -55.729678-0.000830j
[2025-08-05 02:34:46] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -55.788197+0.000841j
[2025-08-05 02:35:06] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -55.786515-0.001572j
[2025-08-05 02:35:25] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -55.796768-0.000483j
[2025-08-05 02:35:44] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -55.838493-0.000288j
[2025-08-05 02:36:03] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -55.822925-0.000182j
[2025-08-05 02:36:22] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -55.877348+0.001517j
[2025-08-05 02:36:41] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -55.848749+0.000364j
[2025-08-05 02:37:00] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -55.847181+0.001826j
[2025-08-05 02:37:19] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -55.856599+0.001207j
[2025-08-05 02:37:38] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -55.831279+0.001798j
[2025-08-05 02:37:57] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -55.822824-0.002332j
[2025-08-05 02:38:16] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -55.848834+0.000955j
[2025-08-05 02:38:35] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -55.847684-0.001854j
[2025-08-05 02:38:54] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -55.848393+0.001531j
[2025-08-05 02:39:13] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -55.875360+0.001172j
[2025-08-05 02:39:32] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -55.852850+0.000001j
[2025-08-05 02:39:51] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -55.814567+0.000331j
[2025-08-05 02:40:10] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -55.852213+0.001239j
[2025-08-05 02:40:29] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -55.820010-0.000699j
[2025-08-05 02:40:48] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -55.815832+0.001001j
[2025-08-05 02:41:07] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -55.838274-0.001005j
[2025-08-05 02:41:26] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -55.855929+0.000141j
[2025-08-05 02:41:45] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -55.823349-0.000690j
[2025-08-05 02:42:04] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -55.836388+0.000381j
[2025-08-05 02:42:23] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -55.863708-0.000216j
[2025-08-05 02:42:42] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -55.841158-0.001738j
[2025-08-05 02:43:01] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -55.791599-0.001199j
[2025-08-05 02:43:20] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -55.792415+0.000062j
[2025-08-05 02:43:39] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -55.820259+0.000432j
[2025-08-05 02:43:58] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -55.803876-0.000231j
[2025-08-05 02:44:18] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -55.774576-0.001001j
[2025-08-05 02:44:37] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -55.753640+0.000238j
[2025-08-05 02:44:56] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -55.796015-0.000776j
[2025-08-05 02:45:15] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -55.829096-0.000516j
[2025-08-05 02:45:34] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -55.761874-0.000077j
[2025-08-05 02:45:53] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -55.816622-0.001757j
[2025-08-05 02:46:12] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -55.807085-0.001214j
[2025-08-05 02:46:31] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -55.763360+0.000329j
[2025-08-05 02:46:50] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -55.859188+0.002217j
[2025-08-05 02:47:09] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -55.758443+0.001691j
[2025-08-05 02:47:28] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -55.764464+0.000441j
[2025-08-05 02:47:47] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -55.790927+0.000558j
[2025-08-05 02:48:06] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -55.734553-0.001107j
[2025-08-05 02:48:25] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -55.756551+0.000334j
[2025-08-05 02:48:44] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -55.785501-0.000766j
[2025-08-05 02:49:03] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -55.760299-0.000908j
[2025-08-05 02:49:22] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -55.798173-0.000991j
[2025-08-05 02:49:41] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -55.818019+0.000340j
[2025-08-05 02:50:00] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -55.783797-0.000221j
[2025-08-05 02:50:19] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -55.839676-0.001319j
[2025-08-05 02:50:38] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -55.818173-0.000927j
[2025-08-05 02:50:57] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -55.829008+0.000021j
[2025-08-05 02:51:16] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -55.768796+0.000749j
[2025-08-05 02:51:35] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -55.857092+0.000556j
[2025-08-05 02:51:54] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -55.817043-0.000203j
[2025-08-05 02:52:13] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -55.891906-0.000032j
[2025-08-05 02:52:32] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -55.870048+0.000347j
[2025-08-05 02:52:51] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -55.855529+0.003132j
[2025-08-05 02:53:10] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -55.861974+0.000101j
[2025-08-05 02:53:29] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -55.858961+0.000079j
[2025-08-05 02:53:48] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -55.858307-0.000138j
[2025-08-05 02:54:07] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -55.822142+0.000808j
[2025-08-05 02:54:26] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -55.765715+0.001746j
[2025-08-05 02:54:45] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -55.792508+0.001037j
[2025-08-05 02:55:05] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -55.788684-0.000155j
[2025-08-05 02:55:24] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -55.797438-0.000214j
[2025-08-05 02:55:43] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -55.779960+0.000506j
[2025-08-05 02:56:02] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -55.796790+0.000549j
[2025-08-05 02:56:21] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -55.821652-0.000326j
[2025-08-05 02:56:40] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -55.805958-0.001182j
[2025-08-05 02:56:59] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -55.757353-0.000498j
[2025-08-05 02:57:18] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -55.821321-0.001045j
[2025-08-05 02:57:37] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -55.824366-0.000176j
[2025-08-05 02:57:56] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -55.791961-0.000951j
[2025-08-05 02:58:15] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -55.806011-0.002401j
[2025-08-05 02:58:34] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -55.806462+0.000530j
[2025-08-05 02:58:53] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -55.750499+0.001635j
[2025-08-05 02:59:12] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -55.811147-0.000912j
[2025-08-05 02:59:31] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -55.730733+0.000188j
[2025-08-05 02:59:50] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -55.724484+0.001423j
[2025-08-05 03:00:09] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -55.788997+0.000481j
[2025-08-05 03:00:28] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -55.811136+0.000041j
[2025-08-05 03:00:47] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -55.776744+0.000388j
[2025-08-05 03:01:06] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -55.828943-0.000037j
[2025-08-05 03:01:25] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -55.858227+0.001665j
[2025-08-05 03:01:44] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -55.837278+0.002118j
[2025-08-05 03:02:03] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -55.821542+0.000788j
[2025-08-05 03:02:22] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -55.860550+0.000063j
[2025-08-05 03:02:41] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -55.776343+0.000821j
[2025-08-05 03:03:00] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -55.778536-0.000394j
[2025-08-05 03:03:19] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -55.836904+0.000230j
[2025-08-05 03:03:38] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -55.801804-0.001732j
[2025-08-05 03:03:57] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -55.853116-0.000105j
[2025-08-05 03:04:16] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -55.806970+0.001821j
[2025-08-05 03:04:36] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -55.857112+0.000398j
[2025-08-05 03:04:55] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -55.818980+0.000376j
[2025-08-05 03:05:14] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -55.785554+0.000069j
[2025-08-05 03:05:33] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -55.878879-0.000527j
[2025-08-05 03:05:52] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -55.838256-0.000106j
[2025-08-05 03:06:11] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -55.855736-0.000240j
[2025-08-05 03:06:30] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -55.871979+0.000559j
[2025-08-05 03:06:49] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -55.825528-0.000686j
[2025-08-05 03:07:08] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -55.818505+0.002409j
[2025-08-05 03:07:27] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -55.838558+0.001874j
[2025-08-05 03:07:46] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -55.799574+0.001183j
[2025-08-05 03:08:05] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -55.865708-0.000287j
[2025-08-05 03:08:24] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -55.753356+0.000610j
[2025-08-05 03:08:43] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -55.800514-0.000341j
[2025-08-05 03:09:02] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -55.776088-0.000753j
[2025-08-05 03:09:21] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -55.873223+0.001341j
[2025-08-05 03:09:40] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -55.853261-0.000428j
[2025-08-05 03:09:59] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -55.862781-0.000166j
[2025-08-05 03:10:18] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -55.851971-0.000505j
[2025-08-05 03:10:37] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -55.862947+0.001286j
[2025-08-05 03:10:56] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -55.859846-0.000204j
[2025-08-05 03:11:15] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -55.854132-0.000574j
[2025-08-05 03:11:34] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -55.857108-0.001693j
[2025-08-05 03:11:53] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -55.851692+0.001556j
[2025-08-05 03:12:12] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -55.855673+0.001430j
[2025-08-05 03:12:31] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -55.857567+0.000357j
[2025-08-05 03:12:50] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -55.839560+0.000303j
[2025-08-05 03:13:09] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -55.878138-0.000269j
[2025-08-05 03:13:28] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -55.874383+0.002606j
[2025-08-05 03:13:48] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -55.824769+0.000590j
[2025-08-05 03:14:07] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -55.853696-0.001454j
[2025-08-05 03:14:26] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -55.798590-0.002002j
[2025-08-05 03:14:45] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -55.860107+0.001338j
[2025-08-05 03:15:04] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -55.854200+0.000106j
[2025-08-05 03:15:23] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -55.886383-0.000484j
[2025-08-05 03:15:42] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -55.810623+0.000253j
[2025-08-05 03:16:01] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -55.815510+0.000786j
[2025-08-05 03:16:20] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -55.755495-0.000895j
[2025-08-05 03:16:39] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -55.803089+0.000225j
[2025-08-05 03:16:58] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -55.796552-0.000392j
[2025-08-05 03:17:17] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -55.784352-0.000578j
[2025-08-05 03:17:36] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -55.841140+0.000000j
[2025-08-05 03:17:55] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -55.795058-0.001498j
[2025-08-05 03:18:14] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -55.766297-0.001067j
[2025-08-05 03:18:33] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -55.837896+0.001521j
[2025-08-05 03:18:52] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -55.848555-0.001145j
[2025-08-05 03:19:11] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -55.808013+0.000848j
[2025-08-05 03:19:30] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -55.820104+0.000580j
[2025-08-05 03:19:49] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -55.857583+0.001033j
[2025-08-05 03:20:08] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -55.801624-0.000430j
[2025-08-05 03:20:27] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -55.743990+0.000555j
[2025-08-05 03:20:46] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -55.828189-0.000016j
[2025-08-05 03:21:05] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -55.829252+0.001224j
[2025-08-05 03:21:24] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -55.823498+0.000784j
[2025-08-05 03:21:43] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -55.760272+0.001256j
[2025-08-05 03:22:02] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -55.752018-0.001313j
[2025-08-05 03:22:21] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -55.746203+0.002263j
[2025-08-05 03:22:40] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -55.815126+0.000753j
[2025-08-05 03:22:59] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -55.801586+0.000345j
[2025-08-05 03:23:18] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -55.837148+0.000770j
[2025-08-05 03:23:37] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -55.839832-0.001352j
[2025-08-05 03:23:56] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -55.787759-0.001178j
[2025-08-05 03:24:15] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -55.821456+0.002114j
[2025-08-05 03:24:35] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -55.854983+0.000144j
[2025-08-05 03:24:54] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -55.853298+0.000152j
[2025-08-05 03:25:13] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -55.840009-0.002453j
[2025-08-05 03:25:32] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -55.802560-0.002057j
[2025-08-05 03:25:51] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -55.890113-0.000824j
[2025-08-05 03:26:10] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -55.857406+0.000682j
[2025-08-05 03:26:29] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -55.889591+0.000552j
[2025-08-05 03:26:48] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -55.857501+0.000619j
[2025-08-05 03:27:07] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -55.916453-0.000847j
[2025-08-05 03:27:26] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -55.889195-0.000767j
[2025-08-05 03:27:45] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -55.835826-0.001404j
[2025-08-05 03:28:04] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -55.921914-0.001081j
[2025-08-05 03:28:23] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -55.879667+0.000394j
[2025-08-05 03:28:42] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -55.905344-0.000143j
[2025-08-05 03:29:01] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -55.810186+0.000171j
[2025-08-05 03:29:20] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -55.872479-0.000328j
[2025-08-05 03:29:39] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -55.900653-0.001169j
[2025-08-05 03:29:58] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -55.889868-0.001352j
[2025-08-05 03:30:17] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -55.846140-0.000914j
[2025-08-05 03:30:36] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -55.806385+0.001215j
[2025-08-05 03:30:55] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -55.871680-0.000459j
[2025-08-05 03:31:14] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -55.810140-0.000992j
[2025-08-05 03:31:33] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -55.798772+0.000195j
[2025-08-05 03:31:52] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -55.773105-0.000227j
[2025-08-05 03:32:11] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -55.760629-0.000888j
[2025-08-05 03:32:30] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -55.758898-0.000695j
[2025-08-05 03:32:49] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -55.779894-0.001572j
[2025-08-05 03:33:08] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -55.749697-0.001666j
[2025-08-05 03:33:27] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -55.849487+0.000460j
[2025-08-05 03:33:46] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -55.811435+0.000390j
[2025-08-05 03:34:06] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -55.769785-0.000786j
[2025-08-05 03:34:25] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -55.787258+0.000690j
[2025-08-05 03:34:44] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -55.841214+0.000159j
[2025-08-05 03:35:03] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -55.866061+0.000693j
[2025-08-05 03:35:22] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -55.842177-0.000237j
[2025-08-05 03:35:41] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -55.831944+0.000266j
[2025-08-05 03:36:00] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -55.842404+0.000425j
[2025-08-05 03:36:19] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -55.823585+0.000079j
[2025-08-05 03:36:38] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -55.886294+0.000731j
[2025-08-05 03:36:57] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -55.877314+0.000935j
[2025-08-05 03:37:16] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -55.890830-0.000831j
[2025-08-05 03:37:35] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -55.913605+0.000256j
[2025-08-05 03:37:54] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -55.990860-0.001221j
[2025-08-05 03:38:13] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -55.929395+0.001092j
[2025-08-05 03:38:32] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -55.890151-0.002287j
[2025-08-05 03:38:51] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -55.815560+0.001530j
[2025-08-05 03:39:10] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -55.813907+0.001361j
[2025-08-05 03:39:29] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -55.805596+0.001432j
[2025-08-05 03:39:48] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -55.899619+0.000799j
[2025-08-05 03:40:07] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -55.908439-0.002043j
[2025-08-05 03:40:26] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -55.857470+0.000923j
[2025-08-05 03:40:45] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -55.816852-0.000919j
[2025-08-05 03:41:04] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -55.794814+0.000799j
[2025-08-05 03:41:23] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -55.825418+0.001226j
[2025-08-05 03:41:42] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -55.814225-0.000231j
[2025-08-05 03:42:01] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -55.823119+0.000178j
[2025-08-05 03:42:20] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -55.776593+0.000972j
[2025-08-05 03:42:39] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -55.760610+0.000026j
[2025-08-05 03:42:58] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -55.775811+0.001210j
[2025-08-05 03:43:18] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -55.799278-0.000818j
[2025-08-05 03:43:37] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -55.810548+0.000518j
[2025-08-05 03:43:56] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -55.863396-0.000533j
[2025-08-05 03:44:15] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -55.815154-0.000340j
[2025-08-05 03:44:34] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -55.793559-0.000693j
[2025-08-05 03:44:53] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -55.824500-0.001662j
[2025-08-05 03:45:12] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -55.854723-0.000745j
[2025-08-05 03:45:31] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -55.828259-0.002395j
[2025-08-05 03:45:50] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -55.819220-0.002424j
[2025-08-05 03:46:09] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -55.781594+0.000648j
[2025-08-05 03:46:28] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -55.770264-0.000827j
[2025-08-05 03:46:47] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -55.801727+0.001463j
[2025-08-05 03:47:06] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -55.771356-0.001577j
[2025-08-05 03:47:25] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -55.785412-0.000552j
[2025-08-05 03:47:44] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -55.842525-0.001418j
[2025-08-05 03:48:03] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -55.865893-0.000079j
[2025-08-05 03:48:22] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -55.885673+0.001782j
[2025-08-05 03:48:41] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -55.871197+0.002016j
[2025-08-05 03:49:00] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -55.879884-0.001811j
[2025-08-05 03:49:19] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -55.822099+0.001725j
[2025-08-05 03:49:38] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -55.832873+0.000404j
[2025-08-05 03:49:57] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -55.829417+0.000395j
[2025-08-05 03:50:16] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -55.867609-0.000521j
[2025-08-05 03:50:35] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -55.900086+0.000094j
[2025-08-05 03:50:54] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -55.858718-0.000616j
[2025-08-05 03:51:13] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -55.899206-0.000173j
[2025-08-05 03:51:32] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -55.874304-0.002605j
[2025-08-05 03:51:51] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -55.907602+0.000697j
[2025-08-05 03:52:10] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -55.872799-0.000366j
[2025-08-05 03:52:29] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -55.891059-0.002129j
[2025-08-05 03:52:48] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -55.849785-0.002980j
[2025-08-05 03:53:07] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -55.765364-0.000912j
[2025-08-05 03:53:26] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -55.829445-0.001561j
[2025-08-05 03:53:46] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -55.828263-0.002124j
[2025-08-05 03:54:05] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -55.813549-0.000569j
[2025-08-05 03:54:24] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -55.841535-0.001506j
[2025-08-05 03:54:43] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -55.813393-0.000177j
[2025-08-05 03:55:02] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -55.748847-0.001653j
[2025-08-05 03:55:21] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -55.756480-0.001570j
[2025-08-05 03:55:40] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -55.716901+0.000617j
[2025-08-05 03:55:59] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -55.749854-0.000770j
[2025-08-05 03:56:18] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -55.788052-0.001241j
[2025-08-05 03:56:37] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -55.797052+0.001146j
[2025-08-05 03:56:56] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -55.825287-0.001759j
[2025-08-05 03:57:15] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -55.807771-0.000648j
[2025-08-05 03:57:34] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -55.816258+0.000531j
[2025-08-05 03:57:53] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -55.832902+0.000235j
[2025-08-05 03:58:12] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -55.855874-0.000106j
[2025-08-05 03:58:31] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -55.864438+0.000124j
[2025-08-05 03:58:50] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -55.772273+0.000651j
[2025-08-05 03:59:09] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -55.787061+0.000220j
[2025-08-05 03:59:28] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -55.801073-0.000039j
[2025-08-05 03:59:47] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -55.813486-0.002038j
[2025-08-05 04:00:06] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -55.826908-0.001722j
[2025-08-05 04:00:25] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -55.830502+0.000808j
[2025-08-05 04:00:44] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -55.864523-0.000378j
[2025-08-05 04:01:03] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -55.901211+0.001317j
[2025-08-05 04:01:22] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -55.890530-0.000427j
[2025-08-05 04:01:41] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -55.867147-0.000104j
[2025-08-05 04:02:00] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -55.860529-0.000313j
[2025-08-05 04:02:19] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -55.830819-0.000344j
[2025-08-05 04:02:38] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -55.817651+0.003036j
[2025-08-05 04:02:57] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -55.773892-0.000340j
[2025-08-05 04:03:16] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -55.821476-0.000706j
[2025-08-05 04:03:36] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -55.816508-0.000126j
[2025-08-05 04:03:55] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -55.781708+0.000983j
[2025-08-05 04:04:14] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -55.799661-0.001168j
[2025-08-05 04:04:33] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -55.793765+0.000810j
[2025-08-05 04:04:52] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -55.870916-0.001508j
[2025-08-05 04:05:11] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -55.849604-0.001471j
[2025-08-05 04:05:30] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -55.831923-0.001616j
[2025-08-05 04:05:49] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -55.817588-0.000403j
[2025-08-05 04:06:08] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -55.850941+0.000284j
[2025-08-05 04:06:27] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -55.825234+0.001463j
[2025-08-05 04:06:46] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -55.832603-0.000871j
[2025-08-05 04:07:05] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -55.842892+0.000283j
[2025-08-05 04:07:24] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -55.881288+0.000977j
[2025-08-05 04:07:43] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -55.854773+0.002702j
[2025-08-05 04:08:02] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -55.845679+0.000445j
[2025-08-05 04:08:21] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -55.824769+0.001147j
[2025-08-05 04:08:40] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -55.859784+0.000401j
[2025-08-05 04:08:59] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -55.833937+0.000411j
[2025-08-05 04:09:18] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -55.879567+0.000632j
[2025-08-05 04:09:37] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -55.867232-0.000264j
[2025-08-05 04:09:56] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -55.890957+0.000461j
[2025-08-05 04:10:15] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -55.840781+0.000910j
[2025-08-05 04:10:34] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -55.851379+0.001765j
[2025-08-05 04:10:53] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -55.911651+0.000084j
[2025-08-05 04:11:12] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -55.885284-0.000057j
[2025-08-05 04:11:31] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -55.891325-0.000504j
[2025-08-05 04:11:50] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -55.904898+0.000655j
[2025-08-05 04:12:09] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -55.849708+0.002540j
[2025-08-05 04:12:28] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -55.791623-0.000739j
[2025-08-05 04:12:48] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -55.828462-0.000539j
[2025-08-05 04:13:07] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -55.899417-0.000226j
[2025-08-05 04:13:26] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -55.881763+0.001091j
[2025-08-05 04:13:45] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -55.854270-0.000388j
[2025-08-05 04:14:04] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -55.848411+0.001076j
[2025-08-05 04:14:23] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -55.866730-0.001780j
[2025-08-05 04:14:42] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -55.810591+0.000378j
[2025-08-05 04:15:01] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -55.772179+0.001596j
[2025-08-05 04:15:20] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -55.824962+0.000363j
[2025-08-05 04:15:39] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -55.837659-0.001062j
[2025-08-05 04:15:58] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -55.807263-0.000219j
[2025-08-05 04:16:17] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -55.859031+0.000261j
[2025-08-05 04:16:36] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -55.807738+0.001614j
[2025-08-05 04:16:55] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -55.862718-0.000794j
[2025-08-05 04:17:14] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -55.887626-0.000320j
[2025-08-05 04:17:33] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -55.911262-0.000538j
[2025-08-05 04:17:52] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -55.829475+0.000962j
[2025-08-05 04:18:11] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -55.802716-0.000908j
[2025-08-05 04:18:30] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -55.827191+0.000401j
[2025-08-05 04:18:49] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -55.801902-0.001713j
[2025-08-05 04:19:08] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -55.811206-0.001713j
[2025-08-05 04:19:27] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -55.854363-0.001012j
[2025-08-05 04:19:46] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -55.799544+0.001033j
[2025-08-05 04:20:05] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -55.856312-0.000277j
[2025-08-05 04:20:24] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -55.839760+0.000045j
[2025-08-05 04:20:43] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -55.879953-0.000842j
[2025-08-05 04:21:02] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -55.835439-0.000602j
[2025-08-05 04:21:21] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -55.844710+0.001435j
[2025-08-05 04:21:40] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -55.887067-0.001751j
[2025-08-05 04:22:00] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -55.856347+0.000888j
[2025-08-05 04:22:19] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -55.807418-0.000711j
[2025-08-05 04:22:38] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -55.824874-0.001144j
[2025-08-05 04:22:57] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -55.833398-0.000315j
[2025-08-05 04:23:16] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -55.827241+0.001025j
[2025-08-05 04:23:35] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -55.892956-0.000184j
[2025-08-05 04:23:54] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -55.884048+0.000601j
[2025-08-05 04:24:13] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -55.847126-0.001146j
[2025-08-05 04:24:32] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -55.889078+0.001967j
[2025-08-05 04:24:51] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -55.904737-0.000602j
[2025-08-05 04:25:10] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -55.909174-0.000373j
[2025-08-05 04:25:29] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -55.895343+0.000763j
[2025-08-05 04:25:48] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -55.823578+0.001108j
[2025-08-05 04:26:07] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -55.792175-0.000428j
[2025-08-05 04:26:26] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -55.797744-0.000924j
[2025-08-05 04:26:45] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -55.747497-0.001966j
[2025-08-05 04:27:04] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -55.810238-0.001463j
[2025-08-05 04:27:23] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -55.869481-0.000105j
[2025-08-05 04:27:42] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -55.803035+0.000075j
[2025-08-05 04:28:01] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -55.760979+0.000310j
[2025-08-05 04:28:20] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -55.767592+0.000867j
[2025-08-05 04:28:39] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -55.739782+0.000248j
[2025-08-05 04:28:58] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -55.760780+0.000449j
[2025-08-05 04:29:17] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -55.717454-0.000838j
[2025-08-05 04:29:36] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -55.707288+0.000648j
[2025-08-05 04:29:55] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -55.774318-0.000341j
[2025-08-05 04:30:14] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -55.817287+0.000086j
[2025-08-05 04:30:34] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -55.834016+0.000457j
[2025-08-05 04:30:53] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -55.826125-0.000151j
[2025-08-05 04:31:12] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -55.809354+0.000242j
[2025-08-05 04:31:31] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -55.828779-0.001799j
[2025-08-05 04:31:50] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -55.883354+0.001268j
[2025-08-05 04:32:09] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -55.791116+0.001781j
[2025-08-05 04:32:28] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -55.827801+0.001368j
[2025-08-05 04:32:47] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -55.867756+0.001301j
[2025-08-05 04:33:06] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -55.845161+0.000584j
[2025-08-05 04:33:25] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -55.813114+0.000919j
[2025-08-05 04:33:44] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -55.816899+0.000213j
[2025-08-05 04:34:03] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -55.833790-0.001593j
[2025-08-05 04:34:22] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -55.893760-0.000444j
[2025-08-05 04:34:41] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -55.894380-0.001341j
[2025-08-05 04:35:00] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -55.850621+0.001121j
[2025-08-05 04:35:19] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -55.822287+0.000033j
[2025-08-05 04:35:38] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -55.856585-0.000370j
[2025-08-05 04:35:57] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -55.795962-0.000081j
[2025-08-05 04:36:16] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -55.787056+0.003233j
[2025-08-05 04:36:35] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -55.798957+0.001890j
[2025-08-05 04:36:54] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -55.835482+0.002106j
[2025-08-05 04:37:13] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -55.771253-0.000263j
[2025-08-05 04:37:32] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -55.791691-0.001441j
[2025-08-05 04:37:51] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -55.752866+0.001340j
[2025-08-05 04:38:10] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -55.809969-0.002661j
[2025-08-05 04:38:29] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -55.795002-0.000844j
[2025-08-05 04:38:48] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -55.808463+0.000885j
[2025-08-05 04:39:07] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -55.782513+0.001298j
[2025-08-05 04:39:26] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -55.775170-0.000444j
[2025-08-05 04:39:26] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-08-05 04:39:46] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -55.737492+0.001468j
[2025-08-05 04:40:05] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -55.788105-0.000821j
[2025-08-05 04:40:24] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -55.822279+0.001256j
[2025-08-05 04:40:43] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -55.821273-0.000210j
[2025-08-05 04:41:02] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -55.815843+0.000576j
[2025-08-05 04:41:21] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -55.789958+0.000805j
[2025-08-05 04:41:40] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -55.780929+0.001555j
[2025-08-05 04:41:59] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -55.807482-0.000628j
[2025-08-05 04:42:18] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -55.810726-0.000683j
[2025-08-05 04:42:37] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -55.808039-0.001442j
[2025-08-05 04:42:56] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -55.881798-0.000519j
[2025-08-05 04:43:15] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -55.879418+0.000413j
[2025-08-05 04:43:34] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -55.848482-0.002173j
[2025-08-05 04:43:53] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -55.831886-0.000496j
[2025-08-05 04:44:12] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -55.776987+0.000472j
[2025-08-05 04:44:31] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -55.770822+0.000184j
[2025-08-05 04:44:50] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -55.761489-0.000097j
[2025-08-05 04:45:09] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -55.762286+0.001238j
[2025-08-05 04:45:28] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -55.743463-0.000342j
[2025-08-05 04:45:47] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -55.789076-0.000812j
[2025-08-05 04:46:06] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -55.756501-0.000832j
[2025-08-05 04:46:25] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -55.770406+0.000206j
[2025-08-05 04:46:44] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -55.758710-0.000093j
[2025-08-05 04:47:03] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -55.819647+0.000324j
[2025-08-05 04:47:22] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -55.815048-0.000163j
[2025-08-05 04:47:41] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -55.757699+0.000777j
[2025-08-05 04:48:00] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -55.783799-0.000311j
[2025-08-05 04:48:19] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -55.806187-0.001340j
[2025-08-05 04:48:38] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -55.731498+0.000834j
[2025-08-05 04:48:57] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -55.812290+0.001313j
[2025-08-05 04:49:17] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -55.785974-0.001162j
[2025-08-05 04:49:36] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -55.821290+0.001058j
[2025-08-05 04:49:55] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -55.850871-0.001106j
[2025-08-05 04:50:14] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -55.792008+0.000260j
[2025-08-05 04:50:33] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -55.817885-0.000474j
[2025-08-05 04:50:52] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -55.816749+0.001269j
[2025-08-05 04:51:11] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -55.834061+0.003580j
[2025-08-05 04:51:30] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -55.830580-0.003375j
[2025-08-05 04:51:49] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -55.855289-0.001581j
[2025-08-05 04:52:08] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -55.844911+0.000197j
[2025-08-05 04:52:27] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -55.775723-0.000482j
[2025-08-05 04:52:46] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -55.829484+0.000116j
[2025-08-05 04:53:05] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -55.861401+0.000455j
[2025-08-05 04:53:24] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -55.785244-0.000684j
[2025-08-05 04:53:43] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -55.738070-0.000134j
[2025-08-05 04:54:02] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -55.846397-0.000524j
[2025-08-05 04:54:21] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -55.796446-0.000525j
[2025-08-05 04:54:40] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -55.775542+0.001456j
[2025-08-05 04:54:59] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -55.792781+0.000898j
[2025-08-05 04:55:18] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -55.825176+0.001908j
[2025-08-05 04:55:18] ✅ Training completed | Restarts: 2
[2025-08-05 04:55:18] ============================================================
[2025-08-05 04:55:18] Training completed | Runtime: 20027.8s
[2025-08-05 04:55:43] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-08-05 04:55:43] ============================================================
