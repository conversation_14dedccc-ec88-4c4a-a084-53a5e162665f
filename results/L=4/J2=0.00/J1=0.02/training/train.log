[2025-08-04 23:18:20] ✓ 从checkpoint恢复: results/L=4/J2=0.00/J1=0.03/training/checkpoints/final_GCNN.pkl
[2025-08-04 23:18:20]   - 迭代次数: final
[2025-08-04 23:18:20]   - 能量: -54.205744+0.001305j ± 0.042579
[2025-08-04 23:18:20]   - 时间戳: 2025-07-31T22:01:13.500318+08:00
[2025-08-04 23:18:28] ✓ 变分状态参数已从checkpoint恢复
[2025-08-04 23:18:28] ✓ 从final状态恢复, 重置迭代计数为0
[2025-08-04 23:18:28] ==================================================
[2025-08-04 23:18:28] GCNN for Shastry-Sutherland Model
[2025-08-04 23:18:28] ==================================================
[2025-08-04 23:18:28] System parameters:
[2025-08-04 23:18:28]   - System size: L=4, N=64
[2025-08-04 23:18:28]   - System parameters: J1=0.02, J2=0.0, Q=1.0
[2025-08-04 23:18:28] --------------------------------------------------
[2025-08-04 23:18:28] Model parameters:
[2025-08-04 23:18:28]   - Number of layers = 4
[2025-08-04 23:18:28]   - Number of features = 4
[2025-08-04 23:18:28]   - Total parameters = 12572
[2025-08-04 23:18:28] --------------------------------------------------
[2025-08-04 23:18:28] Training parameters:
[2025-08-04 23:18:28]   - Learning rate: 0.015
[2025-08-04 23:18:28]   - Total iterations: 1050
[2025-08-04 23:18:28]   - Annealing cycles: 3
[2025-08-04 23:18:28]   - Initial period: 150
[2025-08-04 23:18:28]   - Period multiplier: 2.0
[2025-08-04 23:18:28]   - Temperature range: 0.0-1.0
[2025-08-04 23:18:28]   - Samples: 16384
[2025-08-04 23:18:28]   - Discarded samples: 0
[2025-08-04 23:18:28]   - Chunk size: 2048
[2025-08-04 23:18:28]   - Diagonal shift: 0.2
[2025-08-04 23:18:28]   - Gradient clipping: 1.0
[2025-08-04 23:18:28]   - Checkpoint enabled: interval=500
[2025-08-04 23:18:28]   - Checkpoint directory: results/L=4/J2=0.00/J1=0.02/training/checkpoints
[2025-08-04 23:18:28] --------------------------------------------------
[2025-08-04 23:18:28] Device status:
[2025-08-04 23:18:28]   - Devices model: A100
[2025-08-04 23:18:28]   - Number of devices: 1
[2025-08-04 23:18:28]   - Sharding: True
[2025-08-04 23:18:28] ============================================================
[2025-08-04 23:19:18] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -53.877021-0.002880j
[2025-08-04 23:19:52] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -53.843126-0.003663j
[2025-08-04 23:20:11] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -53.854603-0.004807j
[2025-08-04 23:20:31] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -53.825707-0.003142j
[2025-08-04 23:20:50] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -53.900043-0.001602j
[2025-08-04 23:21:09] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -53.892366-0.004277j
[2025-08-04 23:21:28] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -53.838920-0.002591j
[2025-08-04 23:21:47] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -53.887342-0.002524j
[2025-08-04 23:22:06] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -53.827010-0.002069j
[2025-08-04 23:22:25] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -53.866301-0.002711j
[2025-08-04 23:22:45] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -53.877920-0.001629j
[2025-08-04 23:23:04] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -53.919166-0.001074j
[2025-08-04 23:23:23] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -53.871964-0.000084j
[2025-08-04 23:23:42] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -53.911494-0.001154j
[2025-08-04 23:24:01] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -53.925495+0.000202j
[2025-08-04 23:24:20] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -53.967851+0.001035j
[2025-08-04 23:24:39] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -53.946084-0.001470j
[2025-08-04 23:24:58] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -53.945399-0.001630j
[2025-08-04 23:25:18] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -53.877413-0.001515j
[2025-08-04 23:25:37] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -53.836903-0.000475j
[2025-08-04 23:25:56] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -53.911324-0.001641j
[2025-08-04 23:26:15] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -53.898773-0.001317j
[2025-08-04 23:26:34] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -53.889589-0.000999j
[2025-08-04 23:26:53] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -53.921146-0.000691j
[2025-08-04 23:27:12] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -53.899057+0.000721j
[2025-08-04 23:27:32] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -53.852659-0.000224j
[2025-08-04 23:27:51] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -53.868555-0.000479j
[2025-08-04 23:28:10] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -53.865983+0.000321j
[2025-08-04 23:28:29] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -53.839976-0.000202j
[2025-08-04 23:28:48] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -53.865360-0.000573j
[2025-08-04 23:29:07] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -53.777659+0.000408j
[2025-08-04 23:29:26] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -53.923105-0.001508j
[2025-08-04 23:29:45] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -53.868492-0.001179j
[2025-08-04 23:30:05] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -53.806344+0.001209j
[2025-08-04 23:30:24] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -53.915869+0.000386j
[2025-08-04 23:30:43] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -53.900802+0.000326j
[2025-08-04 23:31:02] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -53.887598-0.000446j
[2025-08-04 23:31:21] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -53.907299+0.000013j
[2025-08-04 23:31:40] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -53.889492-0.000538j
[2025-08-04 23:31:59] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -53.893716+0.002030j
[2025-08-04 23:32:19] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -53.893730-0.002541j
[2025-08-04 23:32:38] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -53.940346+0.002225j
[2025-08-04 23:32:57] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -53.891686-0.000484j
[2025-08-04 23:33:16] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -53.872087+0.001010j
[2025-08-04 23:33:35] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -53.903557-0.000702j
[2025-08-04 23:33:54] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -53.847634-0.000787j
[2025-08-04 23:34:13] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -53.874617+0.000464j
[2025-08-04 23:34:32] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -53.842295-0.000741j
[2025-08-04 23:34:52] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -53.808027+0.000121j
[2025-08-04 23:35:11] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -53.759634+0.000651j
[2025-08-04 23:35:30] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -53.787031+0.000762j
[2025-08-04 23:35:49] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -53.865606+0.000498j
[2025-08-04 23:36:08] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -53.825905+0.000644j
[2025-08-04 23:36:27] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -53.794370+0.001038j
[2025-08-04 23:36:46] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -53.773942+0.002178j
[2025-08-04 23:37:05] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -53.835336-0.000164j
[2025-08-04 23:37:25] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -53.852263-0.001003j
[2025-08-04 23:37:44] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -53.838733-0.001478j
[2025-08-04 23:38:03] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -53.805198-0.000197j
[2025-08-04 23:38:22] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -53.794774+0.001535j
[2025-08-04 23:38:41] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -53.787481+0.000169j
[2025-08-04 23:39:00] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -53.826952-0.000109j
[2025-08-04 23:39:19] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -53.760758-0.000460j
[2025-08-04 23:39:38] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -53.761622+0.001228j
[2025-08-04 23:39:58] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -53.735179-0.000239j
[2025-08-04 23:40:17] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -53.751762+0.000395j
[2025-08-04 23:40:36] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -53.768945-0.000368j
[2025-08-04 23:40:55] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -53.798086-0.001337j
[2025-08-04 23:41:14] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -53.772800-0.000355j
[2025-08-04 23:41:33] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -53.822974+0.000533j
[2025-08-04 23:41:52] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -53.827236-0.000150j
[2025-08-04 23:42:11] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -53.876644+0.000727j
[2025-08-04 23:42:31] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -53.910390+0.000807j
[2025-08-04 23:42:50] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -53.859801+0.001801j
[2025-08-04 23:43:09] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -53.844218+0.000305j
[2025-08-04 23:43:28] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -53.870782-0.001661j
[2025-08-04 23:43:47] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -53.906821+0.000492j
[2025-08-04 23:44:06] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -53.949659+0.002096j
[2025-08-04 23:44:25] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -53.898232-0.001828j
[2025-08-04 23:44:45] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -53.935591+0.000673j
[2025-08-04 23:45:04] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -53.947562-0.000017j
[2025-08-04 23:45:23] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -53.896837+0.000345j
[2025-08-04 23:45:42] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -53.898861-0.000944j
[2025-08-04 23:46:01] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -53.957025-0.001378j
[2025-08-04 23:46:20] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -53.957201-0.000350j
[2025-08-04 23:46:39] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -54.000253-0.001283j
[2025-08-04 23:46:58] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -53.926871-0.000206j
[2025-08-04 23:47:18] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -53.936652-0.001898j
[2025-08-04 23:47:37] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -53.955085-0.000007j
[2025-08-04 23:47:56] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -53.885398+0.000015j
[2025-08-04 23:48:15] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -53.844599+0.002706j
[2025-08-04 23:48:34] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -53.865504-0.000875j
[2025-08-04 23:48:53] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -53.866608+0.000590j
[2025-08-04 23:49:12] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -53.860292+0.000230j
[2025-08-04 23:49:31] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -53.842084-0.001427j
[2025-08-04 23:49:51] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -53.823137-0.000792j
[2025-08-04 23:50:10] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -53.845059+0.001105j
[2025-08-04 23:50:29] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -53.821408-0.000398j
[2025-08-04 23:50:48] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -53.880807-0.000756j
[2025-08-04 23:51:07] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -53.846194-0.000459j
[2025-08-04 23:51:26] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -53.820355+0.002382j
[2025-08-04 23:51:45] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -53.908581-0.000682j
[2025-08-04 23:52:05] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -53.878103+0.001375j
[2025-08-04 23:52:24] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -53.851903+0.000803j
[2025-08-04 23:52:43] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -53.866116+0.000426j
[2025-08-04 23:53:02] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -53.838991+0.000105j
[2025-08-04 23:53:21] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -53.802764-0.002071j
[2025-08-04 23:53:40] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -53.748151-0.000602j
[2025-08-04 23:53:59] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -53.776522+0.000158j
[2025-08-04 23:54:18] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -53.799683+0.002863j
[2025-08-04 23:54:38] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -53.872167+0.000998j
[2025-08-04 23:54:57] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -53.816339+0.002894j
[2025-08-04 23:55:16] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -53.830294-0.000878j
[2025-08-04 23:55:35] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -53.818503-0.001454j
[2025-08-04 23:55:54] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -53.855716+0.001151j
[2025-08-04 23:56:13] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -53.828157-0.000374j
[2025-08-04 23:56:32] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -53.856709-0.000300j
[2025-08-04 23:56:51] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -53.800961-0.000332j
[2025-08-04 23:57:11] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -53.834112+0.001233j
[2025-08-04 23:57:30] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -53.897613+0.000144j
[2025-08-04 23:57:49] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -53.900249+0.000837j
[2025-08-04 23:58:08] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -53.850102-0.000272j
[2025-08-04 23:58:27] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -53.882071-0.001442j
[2025-08-04 23:58:46] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -53.833989-0.000367j
[2025-08-04 23:59:05] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -53.828893+0.000930j
[2025-08-04 23:59:24] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -53.780483+0.002167j
[2025-08-04 23:59:44] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -53.810126+0.000938j
[2025-08-05 00:00:03] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -53.792862+0.001469j
[2025-08-05 00:00:22] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -53.807801+0.001514j
[2025-08-05 00:00:41] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -53.822283+0.000547j
[2025-08-05 00:01:00] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -53.820323+0.001628j
[2025-08-05 00:01:19] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -53.814306+0.000312j
[2025-08-05 00:01:38] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -53.809895-0.001524j
[2025-08-05 00:01:57] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -53.741705-0.000686j
[2025-08-05 00:02:17] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -53.812550+0.000264j
[2025-08-05 00:02:36] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -53.837915+0.000829j
[2025-08-05 00:02:55] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -53.831227+0.000056j
[2025-08-05 00:03:14] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -53.815104+0.001903j
[2025-08-05 00:03:33] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -53.804683+0.001454j
[2025-08-05 00:03:52] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -53.804623-0.000387j
[2025-08-05 00:04:11] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -53.909755-0.001882j
[2025-08-05 00:04:30] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -53.866108+0.000778j
[2025-08-05 00:04:50] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -53.846494+0.001250j
[2025-08-05 00:05:09] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -53.818424+0.001720j
[2025-08-05 00:05:28] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -53.804348+0.001365j
[2025-08-05 00:05:47] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -53.793214+0.000604j
[2025-08-05 00:06:06] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -53.842170+0.001814j
[2025-08-05 00:06:25] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -53.793771-0.000194j
[2025-08-05 00:06:44] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -53.867310+0.000042j
[2025-08-05 00:07:04] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -53.809148+0.000389j
[2025-08-05 00:07:04] RESTART #1 | Period: 300
[2025-08-05 00:07:23] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -53.857319+0.001157j
[2025-08-05 00:07:42] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -53.817047+0.001626j
[2025-08-05 00:08:01] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -53.869756-0.001182j
[2025-08-05 00:08:20] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -53.807961-0.000906j
[2025-08-05 00:08:39] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -53.787435-0.000894j
[2025-08-05 00:08:58] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -53.771447+0.000683j
[2025-08-05 00:09:17] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -53.785906-0.000209j
[2025-08-05 00:09:37] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -53.782274+0.000883j
[2025-08-05 00:09:56] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -53.773557-0.001763j
[2025-08-05 00:10:15] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -53.784549-0.001345j
[2025-08-05 00:10:34] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -53.794932-0.001015j
[2025-08-05 00:10:53] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -53.744694-0.000375j
[2025-08-05 00:11:12] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -53.852174-0.000538j
[2025-08-05 00:11:32] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -53.872030+0.000195j
[2025-08-05 00:11:51] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -53.841417+0.000549j
[2025-08-05 00:12:10] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -53.861266+0.000449j
[2025-08-05 00:12:29] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -53.818370-0.002575j
[2025-08-05 00:12:48] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -53.813336-0.000413j
[2025-08-05 00:13:07] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -53.792555+0.001243j
[2025-08-05 00:13:26] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -53.782881+0.001278j
[2025-08-05 00:13:45] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -53.848470+0.001178j
[2025-08-05 00:14:05] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -53.807384-0.000645j
[2025-08-05 00:14:24] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -53.849924-0.001070j
[2025-08-05 00:14:43] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -53.851500-0.000985j
[2025-08-05 00:15:02] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -53.850757-0.001111j
[2025-08-05 00:15:21] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -53.860753+0.001109j
[2025-08-05 00:15:40] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -53.832449-0.000482j
[2025-08-05 00:15:59] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -53.816366-0.001939j
[2025-08-05 00:16:19] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -53.771802+0.001238j
[2025-08-05 00:16:38] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -53.819774+0.000629j
[2025-08-05 00:16:57] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -53.789915-0.000771j
[2025-08-05 00:17:16] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -53.778467-0.000374j
[2025-08-05 00:17:35] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -53.845405-0.000530j
[2025-08-05 00:17:54] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -53.826481-0.000704j
[2025-08-05 00:18:13] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -53.853312+0.000048j
[2025-08-05 00:18:32] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -53.835790+0.001688j
[2025-08-05 00:18:52] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -53.827077-0.000890j
[2025-08-05 00:19:11] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -53.793012+0.000991j
[2025-08-05 00:19:30] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -53.814390+0.000797j
[2025-08-05 00:19:49] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -53.775284+0.002608j
[2025-08-05 00:20:08] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -53.797904+0.000395j
[2025-08-05 00:20:27] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -53.794683+0.001739j
[2025-08-05 00:20:46] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -53.813234-0.000535j
[2025-08-05 00:21:06] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -53.834622+0.001507j
[2025-08-05 00:21:25] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -53.786616+0.000265j
[2025-08-05 00:21:44] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -53.826761-0.001486j
[2025-08-05 00:22:03] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -53.862387+0.000183j
[2025-08-05 00:22:22] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -53.809133-0.000033j
[2025-08-05 00:22:41] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -53.828065-0.001615j
[2025-08-05 00:23:00] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -53.825193-0.001132j
[2025-08-05 00:23:19] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -53.790918-0.001707j
[2025-08-05 00:23:38] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -53.761369+0.002464j
[2025-08-05 00:23:58] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -53.790824+0.000394j
[2025-08-05 00:24:17] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -53.777450-0.000324j
[2025-08-05 00:24:36] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -53.779609-0.000109j
[2025-08-05 00:24:55] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -53.773789-0.000759j
[2025-08-05 00:25:14] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -53.785936-0.002215j
[2025-08-05 00:25:33] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -53.798958+0.000835j
[2025-08-05 00:25:52] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -53.812494+0.002281j
[2025-08-05 00:26:11] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -53.830556-0.000349j
[2025-08-05 00:26:31] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -53.839084+0.001048j
[2025-08-05 00:26:50] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -53.845141-0.000032j
[2025-08-05 00:27:09] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -53.814253+0.000637j
[2025-08-05 00:27:28] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -53.841428+0.000784j
[2025-08-05 00:27:47] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -53.848532+0.001421j
[2025-08-05 00:28:06] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -53.842418+0.000970j
[2025-08-05 00:28:25] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -53.849533-0.000762j
[2025-08-05 00:28:45] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -53.885894+0.001186j
[2025-08-05 00:29:04] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -53.852438+0.001382j
[2025-08-05 00:29:23] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -53.823486+0.000446j
[2025-08-05 00:29:42] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -53.863107-0.000404j
[2025-08-05 00:30:01] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -53.842723+0.001998j
[2025-08-05 00:30:20] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -53.785974+0.002790j
[2025-08-05 00:30:39] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -53.808238-0.001538j
[2025-08-05 00:30:59] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -53.831860-0.001123j
[2025-08-05 00:31:18] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -53.815139-0.001136j
[2025-08-05 00:31:37] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -53.853802-0.001631j
[2025-08-05 00:31:56] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -53.839137-0.001654j
[2025-08-05 00:32:15] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -53.800590-0.000042j
[2025-08-05 00:32:34] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -53.812172+0.001582j
[2025-08-05 00:32:53] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -53.833032+0.000855j
[2025-08-05 00:33:12] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -53.875585-0.000477j
[2025-08-05 00:33:32] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -53.847537-0.000784j
[2025-08-05 00:33:51] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -53.910955+0.000718j
[2025-08-05 00:34:10] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -53.880672-0.000894j
[2025-08-05 00:34:29] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -53.846285-0.000493j
[2025-08-05 00:34:48] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -53.817599-0.000132j
[2025-08-05 00:35:07] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -53.861447+0.000217j
[2025-08-05 00:35:26] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -53.832431+0.000166j
[2025-08-05 00:35:45] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -53.852126-0.002045j
[2025-08-05 00:36:05] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -53.820269-0.001449j
[2025-08-05 00:36:24] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -53.840400+0.001185j
[2025-08-05 00:36:43] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -53.844249-0.001184j
[2025-08-05 00:37:02] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -53.850270-0.001010j
[2025-08-05 00:37:21] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -53.838886-0.000419j
[2025-08-05 00:37:40] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -53.894775+0.001556j
[2025-08-05 00:37:59] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -53.869065+0.000044j
[2025-08-05 00:38:19] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -53.879706+0.000986j
[2025-08-05 00:38:38] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -53.879683-0.000691j
[2025-08-05 00:38:57] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -53.857884-0.000190j
[2025-08-05 00:39:16] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -53.848994+0.000234j
[2025-08-05 00:39:35] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -53.793478-0.001284j
[2025-08-05 00:39:54] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -53.837971+0.000845j
[2025-08-05 00:40:13] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -53.857410+0.000560j
[2025-08-05 00:40:32] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -53.890668-0.000555j
[2025-08-05 00:40:51] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -53.858321-0.002124j
[2025-08-05 00:41:11] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -53.862651-0.000239j
[2025-08-05 00:41:30] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -53.819582-0.001808j
[2025-08-05 00:41:49] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -53.846492-0.000090j
[2025-08-05 00:42:08] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -53.843501-0.001869j
[2025-08-05 00:42:27] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -53.884612+0.001065j
[2025-08-05 00:42:46] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -53.838157+0.000244j
[2025-08-05 00:43:05] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -53.855114+0.001077j
[2025-08-05 00:43:24] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -53.915172+0.001857j
[2025-08-05 00:43:44] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -53.914855+0.000183j
[2025-08-05 00:44:03] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -53.890431+0.000115j
[2025-08-05 00:44:22] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -53.876185+0.000946j
[2025-08-05 00:44:41] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -53.914026+0.000720j
[2025-08-05 00:45:00] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -53.891996+0.001149j
[2025-08-05 00:45:19] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -53.833148+0.000224j
[2025-08-05 00:45:38] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -53.885730-0.000961j
[2025-08-05 00:45:57] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -53.858455-0.001148j
[2025-08-05 00:46:17] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -53.825744-0.000435j
[2025-08-05 00:46:36] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -53.850887+0.000694j
[2025-08-05 00:46:55] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -53.725411+0.000869j
[2025-08-05 00:47:14] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -53.760235+0.000137j
[2025-08-05 00:47:33] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -53.767855+0.000068j
[2025-08-05 00:47:52] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -53.823471+0.001196j
[2025-08-05 00:48:11] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -53.864888-0.000718j
[2025-08-05 00:48:30] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -53.891448-0.001571j
[2025-08-05 00:48:50] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -53.809117-0.001725j
[2025-08-05 00:49:09] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -53.807638-0.000151j
[2025-08-05 00:49:28] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -53.836830-0.001680j
[2025-08-05 00:49:47] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -53.858468+0.000605j
[2025-08-05 00:50:06] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -53.806387+0.000562j
[2025-08-05 00:50:25] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -53.797043+0.000019j
[2025-08-05 00:50:44] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -53.859157-0.000390j
[2025-08-05 00:51:03] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -53.853025+0.000507j
[2025-08-05 00:51:23] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -53.855363-0.000031j
[2025-08-05 00:51:42] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -53.837604-0.001605j
[2025-08-05 00:52:01] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -53.908653-0.000586j
[2025-08-05 00:52:20] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -53.878879-0.000570j
[2025-08-05 00:52:39] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -53.823333-0.000475j
[2025-08-05 00:52:58] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -53.811337+0.001627j
[2025-08-05 00:53:17] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -53.833818+0.001880j
[2025-08-05 00:53:37] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -53.891227-0.000793j
[2025-08-05 00:53:56] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -53.839537-0.000978j
[2025-08-05 00:54:15] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -53.834372+0.000275j
[2025-08-05 00:54:34] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -53.819250-0.000670j
[2025-08-05 00:54:53] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -53.860314-0.002270j
[2025-08-05 00:55:12] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -53.870017-0.000025j
[2025-08-05 00:55:32] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -53.862109-0.000396j
[2025-08-05 00:55:51] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -53.873057-0.000811j
[2025-08-05 00:56:10] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -53.804871-0.002424j
[2025-08-05 00:56:29] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -53.779017-0.000388j
[2025-08-05 00:56:48] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -53.828519+0.002328j
[2025-08-05 00:57:07] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -53.832812-0.000946j
[2025-08-05 00:57:26] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -53.807211+0.000418j
[2025-08-05 00:57:45] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -53.836607-0.000817j
[2025-08-05 00:58:05] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -53.817128-0.000713j
[2025-08-05 00:58:24] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -53.766831+0.001218j
[2025-08-05 00:58:43] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -53.768507-0.001160j
[2025-08-05 00:59:02] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -53.794165+0.001355j
[2025-08-05 00:59:21] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -53.787735-0.000977j
[2025-08-05 00:59:40] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -53.814609-0.000246j
[2025-08-05 00:59:59] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -53.893989-0.002368j
[2025-08-05 01:00:18] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -53.872128-0.000894j
[2025-08-05 01:00:38] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -53.924920-0.000406j
[2025-08-05 01:00:57] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -53.943445+0.002587j
[2025-08-05 01:01:16] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -53.904080+0.001715j
[2025-08-05 01:01:35] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -53.878377-0.002096j
[2025-08-05 01:01:54] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -53.905907-0.000064j
[2025-08-05 01:02:13] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -53.906032-0.000223j
[2025-08-05 01:02:32] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -53.894673-0.000602j
[2025-08-05 01:02:51] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -53.955045-0.000595j
[2025-08-05 01:03:11] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -53.847383-0.000044j
[2025-08-05 01:03:30] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -53.857737-0.000339j
[2025-08-05 01:03:49] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -53.844026+0.000654j
[2025-08-05 01:04:08] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -53.871562-0.000028j
[2025-08-05 01:04:27] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -53.875293+0.000460j
[2025-08-05 01:04:46] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -53.839592-0.001724j
[2025-08-05 01:05:05] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -53.889533-0.002229j
[2025-08-05 01:05:24] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -53.851539+0.000599j
[2025-08-05 01:05:44] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -53.837278-0.000812j
[2025-08-05 01:06:03] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -53.817693+0.001323j
[2025-08-05 01:06:22] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -53.826187+0.000216j
[2025-08-05 01:06:41] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -53.831616-0.000701j
[2025-08-05 01:07:00] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -53.816697+0.001079j
[2025-08-05 01:07:19] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -53.792833-0.000687j
[2025-08-05 01:07:38] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -53.819588-0.000198j
[2025-08-05 01:07:57] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -53.795548+0.000104j
[2025-08-05 01:08:17] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -53.872915+0.001087j
[2025-08-05 01:08:36] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -53.828644-0.001144j
[2025-08-05 01:08:55] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -53.854219-0.000763j
[2025-08-05 01:09:14] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -53.863481-0.001872j
[2025-08-05 01:09:33] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -53.847129-0.001531j
[2025-08-05 01:09:52] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -53.835372+0.000340j
[2025-08-05 01:10:11] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -53.810868-0.000310j
[2025-08-05 01:10:30] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -53.866485-0.000625j
[2025-08-05 01:10:50] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -53.825797+0.001492j
[2025-08-05 01:11:09] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -53.774289+0.001850j
[2025-08-05 01:11:28] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -53.811100+0.000804j
[2025-08-05 01:11:47] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -53.777946+0.000306j
[2025-08-05 01:12:06] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -53.816247+0.000220j
[2025-08-05 01:12:25] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -53.766611+0.002252j
[2025-08-05 01:12:44] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -53.787151+0.001280j
[2025-08-05 01:13:03] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -53.855233-0.000451j
[2025-08-05 01:13:23] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -53.828236+0.003789j
[2025-08-05 01:13:42] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -53.855118+0.001164j
[2025-08-05 01:14:01] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -53.807180+0.001890j
[2025-08-05 01:14:20] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -53.836397-0.000508j
[2025-08-05 01:14:39] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -53.865339+0.001591j
[2025-08-05 01:14:58] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -53.814359-0.000068j
[2025-08-05 01:15:17] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -53.770338+0.000339j
[2025-08-05 01:15:37] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -53.789395-0.000122j
[2025-08-05 01:15:56] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -53.795761+0.000007j
[2025-08-05 01:16:15] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -53.771052+0.001437j
[2025-08-05 01:16:34] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -53.750400-0.000265j
[2025-08-05 01:16:53] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -53.721611+0.000702j
[2025-08-05 01:17:12] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -53.758504+0.000992j
[2025-08-05 01:17:32] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -53.770949-0.000873j
[2025-08-05 01:17:51] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -53.828210-0.000148j
[2025-08-05 01:18:10] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -53.782086-0.001331j
[2025-08-05 01:18:29] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -53.867622+0.000183j
[2025-08-05 01:18:48] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -53.846685-0.000632j
[2025-08-05 01:19:07] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -53.868059+0.001198j
[2025-08-05 01:19:26] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -53.854413-0.000770j
[2025-08-05 01:19:45] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -53.832556+0.000725j
[2025-08-05 01:20:05] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -53.847129+0.001612j
[2025-08-05 01:20:24] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -53.853914-0.000163j
[2025-08-05 01:20:43] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -53.832401+0.001186j
[2025-08-05 01:21:02] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -53.824025-0.004414j
[2025-08-05 01:21:21] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -53.834962-0.000555j
[2025-08-05 01:21:40] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -53.791894+0.000697j
[2025-08-05 01:21:59] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -53.807324+0.001359j
[2025-08-05 01:22:18] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -53.787457+0.000188j
[2025-08-05 01:22:38] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -53.805690-0.001471j
[2025-08-05 01:22:57] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -53.777264-0.000230j
[2025-08-05 01:23:16] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -53.847835+0.001791j
[2025-08-05 01:23:35] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -53.768596-0.000981j
[2025-08-05 01:23:54] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -53.823778+0.000014j
[2025-08-05 01:24:13] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -53.830547+0.002642j
[2025-08-05 01:24:32] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -53.845876+0.000171j
[2025-08-05 01:24:51] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -53.853694+0.000999j
[2025-08-05 01:25:11] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -53.882865+0.001380j
[2025-08-05 01:25:30] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -53.824492+0.000453j
[2025-08-05 01:25:49] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -53.845165+0.000861j
[2025-08-05 01:26:08] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -53.798206+0.001662j
[2025-08-05 01:26:27] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -53.749170+0.000257j
[2025-08-05 01:26:46] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -53.808010+0.001608j
[2025-08-05 01:27:05] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -53.804841-0.000204j
[2025-08-05 01:27:24] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -53.850166-0.000452j
[2025-08-05 01:27:43] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -53.834275-0.000437j
[2025-08-05 01:28:03] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -53.808068+0.001939j
[2025-08-05 01:28:22] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -53.765110+0.001516j
[2025-08-05 01:28:41] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -53.815378+0.000158j
[2025-08-05 01:29:00] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -53.854349+0.001474j
[2025-08-05 01:29:19] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -53.807497+0.001076j
[2025-08-05 01:29:38] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -53.864926-0.000646j
[2025-08-05 01:29:57] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -53.892948+0.000285j
[2025-08-05 01:30:17] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -53.816576-0.001274j
[2025-08-05 01:30:36] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -53.867378+0.000695j
[2025-08-05 01:30:55] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -53.791971+0.001099j
[2025-08-05 01:31:14] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -53.862553-0.000936j
[2025-08-05 01:31:33] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -53.791837-0.000723j
[2025-08-05 01:31:52] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -53.755333-0.001029j
[2025-08-05 01:32:11] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -53.791446-0.001195j
[2025-08-05 01:32:30] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -53.790395-0.001457j
[2025-08-05 01:32:50] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -53.826687+0.001226j
[2025-08-05 01:33:09] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -53.745098-0.000790j
[2025-08-05 01:33:28] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -53.815947+0.001998j
[2025-08-05 01:33:47] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -53.855890+0.001764j
[2025-08-05 01:34:06] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -53.799513+0.000116j
[2025-08-05 01:34:25] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -53.785980+0.000122j
[2025-08-05 01:34:44] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -53.772389+0.000048j
[2025-08-05 01:35:03] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -53.818000-0.002095j
[2025-08-05 01:35:23] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -53.848818-0.001114j
[2025-08-05 01:35:42] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -53.835368+0.001343j
[2025-08-05 01:36:01] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -53.826278-0.000934j
[2025-08-05 01:36:20] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -53.880396-0.000002j
[2025-08-05 01:36:39] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -53.821037+0.001298j
[2025-08-05 01:36:58] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -53.879466-0.000547j
[2025-08-05 01:37:17] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -53.818555-0.002433j
[2025-08-05 01:37:37] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -53.878151-0.000378j
[2025-08-05 01:37:56] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -53.828682-0.001443j
[2025-08-05 01:38:15] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -53.880832-0.000373j
[2025-08-05 01:38:34] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -53.853135+0.000194j
[2025-08-05 01:38:53] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -53.836361+0.001501j
[2025-08-05 01:39:12] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -53.830424-0.002214j
[2025-08-05 01:39:31] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -53.792083+0.000778j
[2025-08-05 01:39:50] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -53.862472-0.001160j
[2025-08-05 01:40:10] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -53.869751+0.000197j
[2025-08-05 01:40:29] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -53.879835+0.000538j
[2025-08-05 01:40:48] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -53.874909+0.001435j
[2025-08-05 01:41:07] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -53.877456-0.000599j
[2025-08-05 01:41:26] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -53.886247-0.000115j
[2025-08-05 01:41:45] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -53.864912+0.000419j
[2025-08-05 01:42:04] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -53.851006-0.000403j
[2025-08-05 01:42:24] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -53.843258-0.001279j
[2025-08-05 01:42:43] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -53.908481-0.001148j
[2025-08-05 01:42:43] RESTART #2 | Period: 600
[2025-08-05 01:43:02] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -53.914609+0.000527j
[2025-08-05 01:43:21] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -53.906485+0.001054j
[2025-08-05 01:43:40] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -53.876057-0.001208j
[2025-08-05 01:43:59] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -53.858940+0.001205j
[2025-08-05 01:44:18] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -53.853588-0.000662j
[2025-08-05 01:44:37] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -53.884828-0.002414j
[2025-08-05 01:44:57] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -53.889608-0.001628j
[2025-08-05 01:45:16] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -53.909380-0.000307j
[2025-08-05 01:45:35] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -53.882874-0.001972j
[2025-08-05 01:45:54] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -53.862812-0.000815j
[2025-08-05 01:46:13] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -53.854260+0.000351j
[2025-08-05 01:46:32] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -53.908924-0.001707j
[2025-08-05 01:46:51] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -53.915313+0.000581j
[2025-08-05 01:47:11] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -53.876673+0.000493j
[2025-08-05 01:47:30] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -53.854616-0.000825j
[2025-08-05 01:47:49] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -53.840546+0.001121j
[2025-08-05 01:48:08] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -53.849868-0.000272j
[2025-08-05 01:48:27] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -53.840375+0.000082j
[2025-08-05 01:48:46] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -53.877083+0.002500j
[2025-08-05 01:49:05] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -53.887201+0.002105j
[2025-08-05 01:49:24] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -53.888924+0.001036j
[2025-08-05 01:49:43] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -53.886071-0.000844j
[2025-08-05 01:50:03] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -53.850426-0.001777j
[2025-08-05 01:50:22] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -53.838174-0.001302j
[2025-08-05 01:50:41] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -53.833005-0.000417j
[2025-08-05 01:51:00] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -53.795022-0.000417j
[2025-08-05 01:51:19] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -53.811992-0.001123j
[2025-08-05 01:51:38] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -53.799121-0.000002j
[2025-08-05 01:51:57] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -53.882993-0.000883j
[2025-08-05 01:52:17] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -53.872499-0.000521j
[2025-08-05 01:52:36] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -53.902367+0.001187j
[2025-08-05 01:52:55] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -53.887878+0.000938j
[2025-08-05 01:53:14] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -53.862599+0.000554j
[2025-08-05 01:53:33] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -53.933305+0.002624j
[2025-08-05 01:53:52] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -53.878308+0.001424j
[2025-08-05 01:54:11] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -53.877356-0.000085j
[2025-08-05 01:54:30] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -53.917960+0.002055j
[2025-08-05 01:54:50] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -53.852924+0.000999j
[2025-08-05 01:55:09] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -53.850042+0.000871j
[2025-08-05 01:55:28] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -53.905479+0.001594j
[2025-08-05 01:55:47] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -53.849651+0.000516j
[2025-08-05 01:56:06] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -53.796790+0.000340j
[2025-08-05 01:56:25] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -53.823912+0.000522j
[2025-08-05 01:56:44] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -53.830710-0.000908j
[2025-08-05 01:57:03] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -53.823131-0.000434j
[2025-08-05 01:57:23] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -53.834796+0.001325j
[2025-08-05 01:57:42] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -53.905124+0.002165j
[2025-08-05 01:58:01] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -53.836234-0.001431j
[2025-08-05 01:58:20] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -53.867423+0.000650j
[2025-08-05 01:58:39] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -53.900890-0.001440j
[2025-08-05 01:58:39] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-08-05 01:58:58] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -53.900328+0.000585j
[2025-08-05 01:59:17] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -53.941108+0.000728j
[2025-08-05 01:59:37] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -53.936570+0.000344j
[2025-08-05 01:59:56] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -53.893382+0.000087j
[2025-08-05 02:00:15] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -53.862625-0.001497j
[2025-08-05 02:00:34] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -53.891778-0.000656j
[2025-08-05 02:00:53] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -53.850568-0.000212j
[2025-08-05 02:01:12] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -53.891853-0.001080j
[2025-08-05 02:01:31] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -53.879161-0.000766j
[2025-08-05 02:01:50] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -53.847787-0.000205j
[2025-08-05 02:02:10] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -53.834760-0.000204j
[2025-08-05 02:02:29] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -53.816983+0.001260j
[2025-08-05 02:02:48] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -53.806731-0.000640j
[2025-08-05 02:03:07] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -53.824149+0.000264j
[2025-08-05 02:03:26] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -53.838585+0.000861j
[2025-08-05 02:03:45] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -53.787118+0.000678j
[2025-08-05 02:04:04] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -53.781138+0.000676j
[2025-08-05 02:04:24] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -53.816626+0.000727j
[2025-08-05 02:04:43] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -53.846169-0.000417j
[2025-08-05 02:05:02] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -53.801524-0.005045j
[2025-08-05 02:05:21] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -53.828133+0.000717j
[2025-08-05 02:05:40] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -53.841193+0.000018j
[2025-08-05 02:05:59] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -53.885693+0.000403j
[2025-08-05 02:06:18] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -53.856814-0.000096j
[2025-08-05 02:06:37] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -53.832599+0.002655j
[2025-08-05 02:06:57] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -53.777172+0.002660j
[2025-08-05 02:07:16] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -53.810861-0.000764j
[2025-08-05 02:07:35] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -53.779830-0.000520j
[2025-08-05 02:07:54] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -53.842125-0.001327j
[2025-08-05 02:08:13] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -53.815085-0.000373j
[2025-08-05 02:08:32] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -53.835075-0.000205j
[2025-08-05 02:08:51] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -53.786002-0.000143j
[2025-08-05 02:09:10] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -53.859622+0.000970j
[2025-08-05 02:09:30] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -53.893009-0.002425j
[2025-08-05 02:09:49] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -53.861911+0.000553j
[2025-08-05 02:10:08] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -53.789836+0.002269j
[2025-08-05 02:10:27] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -53.870371-0.000488j
[2025-08-05 02:10:46] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -53.903164+0.000873j
[2025-08-05 02:11:05] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -53.862030-0.000108j
[2025-08-05 02:11:24] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -53.828495+0.000188j
[2025-08-05 02:11:43] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -53.859713-0.000374j
[2025-08-05 02:12:03] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -53.868224+0.001206j
[2025-08-05 02:12:22] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -53.857227+0.000849j
[2025-08-05 02:12:41] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -53.843160-0.000043j
[2025-08-05 02:13:00] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -53.834422+0.001097j
[2025-08-05 02:13:19] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -53.790125+0.000041j
[2025-08-05 02:13:38] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -53.842675-0.001445j
[2025-08-05 02:13:57] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -53.884871-0.001296j
[2025-08-05 02:14:17] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -53.831705+0.001102j
[2025-08-05 02:14:36] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -53.786621-0.002230j
[2025-08-05 02:14:55] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -53.787031+0.000687j
[2025-08-05 02:15:14] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -53.810842+0.001262j
[2025-08-05 02:15:33] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -53.896267-0.002255j
[2025-08-05 02:15:52] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -53.842148-0.000838j
[2025-08-05 02:16:11] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -53.824679-0.000690j
[2025-08-05 02:16:30] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -53.806538-0.001002j
[2025-08-05 02:16:50] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -53.785294-0.001462j
[2025-08-05 02:17:09] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -53.776075+0.000836j
[2025-08-05 02:17:28] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -53.804423+0.001390j
[2025-08-05 02:17:47] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -53.843394-0.000044j
[2025-08-05 02:18:06] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -53.860332-0.000895j
[2025-08-05 02:18:25] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -53.846229-0.000972j
[2025-08-05 02:18:44] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -53.845203+0.000895j
[2025-08-05 02:19:04] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -53.870853-0.000429j
[2025-08-05 02:19:23] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -53.857257-0.001021j
[2025-08-05 02:19:42] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -53.841443+0.002407j
[2025-08-05 02:20:01] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -53.913188-0.000222j
[2025-08-05 02:20:20] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -53.923842-0.001468j
[2025-08-05 02:20:39] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -53.876118+0.000766j
[2025-08-05 02:20:58] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -53.886977+0.000640j
[2025-08-05 02:21:17] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -53.934728+0.000741j
[2025-08-05 02:21:37] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -53.896281-0.000593j
[2025-08-05 02:21:56] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -53.876525+0.000114j
[2025-08-05 02:22:15] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -53.835522+0.001599j
[2025-08-05 02:22:34] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -53.877450+0.001213j
[2025-08-05 02:22:53] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -53.795483+0.001232j
[2025-08-05 02:23:12] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -53.837604+0.000005j
[2025-08-05 02:23:32] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -53.826559+0.000144j
[2025-08-05 02:23:51] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -53.824978-0.000542j
[2025-08-05 02:24:10] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -53.822063+0.002508j
[2025-08-05 02:24:29] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -53.846977+0.000534j
[2025-08-05 02:24:48] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -53.848014-0.001991j
[2025-08-05 02:25:07] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -53.870342-0.000792j
[2025-08-05 02:25:26] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -53.919298+0.000241j
[2025-08-05 02:25:45] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -53.948606+0.000295j
[2025-08-05 02:26:05] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -53.852998+0.000446j
[2025-08-05 02:26:24] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -53.866648-0.000683j
[2025-08-05 02:26:43] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -53.837463+0.001231j
[2025-08-05 02:27:02] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -53.893674-0.000737j
[2025-08-05 02:27:21] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -53.828183-0.000134j
[2025-08-05 02:27:40] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -53.813660-0.002014j
[2025-08-05 02:27:59] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -53.813927-0.001303j
[2025-08-05 02:28:18] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -53.808136-0.001259j
[2025-08-05 02:28:38] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -53.844032+0.001147j
[2025-08-05 02:28:57] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -53.763864+0.002249j
[2025-08-05 02:29:16] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -53.824137+0.001667j
[2025-08-05 02:29:35] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -53.791850+0.001673j
[2025-08-05 02:29:54] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -53.812018+0.001831j
[2025-08-05 02:30:13] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -53.850634+0.000970j
[2025-08-05 02:30:32] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -53.858872-0.000166j
[2025-08-05 02:30:51] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -53.816949+0.000255j
[2025-08-05 02:31:11] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -53.828018+0.000299j
[2025-08-05 02:31:30] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -53.806912-0.000856j
[2025-08-05 02:31:49] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -53.822403-0.000195j
[2025-08-05 02:32:08] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -53.868104+0.001267j
[2025-08-05 02:32:27] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -53.858652+0.001186j
[2025-08-05 02:32:46] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -53.895889-0.001284j
[2025-08-05 02:33:05] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -53.852616+0.001951j
[2025-08-05 02:33:24] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -53.763857+0.001245j
[2025-08-05 02:33:44] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -53.795697+0.000384j
[2025-08-05 02:34:03] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -53.827346+0.000146j
[2025-08-05 02:34:22] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -53.789335-0.000431j
[2025-08-05 02:34:41] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -53.855055-0.000802j
[2025-08-05 02:35:00] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -53.808104-0.000054j
[2025-08-05 02:35:19] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -53.827841-0.000185j
[2025-08-05 02:35:38] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -53.885841+0.001687j
[2025-08-05 02:35:57] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -53.908307+0.002400j
[2025-08-05 02:36:17] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -53.885771-0.001856j
[2025-08-05 02:36:36] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -53.841527-0.000206j
[2025-08-05 02:36:55] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -53.849404-0.000434j
[2025-08-05 02:37:14] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -53.858239-0.000979j
[2025-08-05 02:37:33] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -53.830337-0.002965j
[2025-08-05 02:37:52] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -53.827960+0.000918j
[2025-08-05 02:38:11] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -53.852852+0.001225j
[2025-08-05 02:38:30] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -53.825551+0.000795j
[2025-08-05 02:38:50] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -53.836828-0.000729j
[2025-08-05 02:39:09] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -53.887904-0.000189j
[2025-08-05 02:39:28] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -53.883319-0.001917j
[2025-08-05 02:39:47] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -53.827405-0.001623j
[2025-08-05 02:40:06] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -53.849218+0.000095j
[2025-08-05 02:40:25] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -53.833448-0.001290j
[2025-08-05 02:40:44] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -53.823471-0.000611j
[2025-08-05 02:41:04] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -53.848537-0.000012j
[2025-08-05 02:41:23] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -53.854009-0.000334j
[2025-08-05 02:41:42] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -53.859224-0.000498j
[2025-08-05 02:42:01] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -53.875219-0.005033j
[2025-08-05 02:42:20] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -53.880653+0.002135j
[2025-08-05 02:42:39] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -53.830328+0.003427j
[2025-08-05 02:42:58] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -53.834421-0.000025j
[2025-08-05 02:43:17] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -53.822413-0.001479j
[2025-08-05 02:43:37] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -53.799526-0.001944j
[2025-08-05 02:43:56] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -53.792790+0.000134j
[2025-08-05 02:44:15] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -53.787501-0.002567j
[2025-08-05 02:44:34] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -53.856415+0.000526j
[2025-08-05 02:44:53] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -53.768825+0.001306j
[2025-08-05 02:45:12] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -53.818501-0.000313j
[2025-08-05 02:45:31] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -53.817665-0.000627j
[2025-08-05 02:45:50] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -53.847836+0.000442j
[2025-08-05 02:46:10] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -53.856259-0.000066j
[2025-08-05 02:46:29] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -53.886707+0.000427j
[2025-08-05 02:46:48] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -53.862894-0.000518j
[2025-08-05 02:47:07] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -53.839211+0.000392j
[2025-08-05 02:47:26] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -53.846495+0.001183j
[2025-08-05 02:47:45] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -53.862361-0.000304j
[2025-08-05 02:48:04] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -53.837451-0.000226j
[2025-08-05 02:48:24] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -53.883614-0.000297j
[2025-08-05 02:48:43] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -53.894752-0.000346j
[2025-08-05 02:49:02] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -53.893746-0.000361j
[2025-08-05 02:49:21] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -53.897509-0.001319j
[2025-08-05 02:49:40] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -53.910453-0.001476j
[2025-08-05 02:49:59] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -53.853623-0.002469j
[2025-08-05 02:50:18] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -53.882605-0.001088j
[2025-08-05 02:50:37] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -53.904243-0.001645j
[2025-08-05 02:50:57] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -53.857956+0.003121j
[2025-08-05 02:51:16] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -53.882014-0.000660j
[2025-08-05 02:51:35] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -53.857840-0.001441j
[2025-08-05 02:51:54] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -53.827197+0.001274j
[2025-08-05 02:52:13] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -53.825895+0.000622j
[2025-08-05 02:52:32] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -53.815398-0.003378j
[2025-08-05 02:52:51] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -53.821228+0.002296j
[2025-08-05 02:53:11] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -53.883044-0.001647j
[2025-08-05 02:53:30] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -53.876055-0.000760j
[2025-08-05 02:53:49] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -53.813685-0.001656j
[2025-08-05 02:54:08] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -53.852347-0.002096j
[2025-08-05 02:54:27] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -53.833320+0.000144j
[2025-08-05 02:54:46] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -53.829999-0.000926j
[2025-08-05 02:55:05] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -53.802801+0.002878j
[2025-08-05 02:55:24] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -53.812769+0.000650j
[2025-08-05 02:55:43] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -53.815904+0.001359j
[2025-08-05 02:56:03] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -53.815614+0.001244j
[2025-08-05 02:56:22] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -53.823737-0.000158j
[2025-08-05 02:56:41] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -53.796403-0.000561j
[2025-08-05 02:57:00] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -53.861569+0.000344j
[2025-08-05 02:57:19] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -53.854441+0.000468j
[2025-08-05 02:57:38] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -53.817381-0.001084j
[2025-08-05 02:57:57] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -53.876650+0.000429j
[2025-08-05 02:58:17] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -53.845088+0.000340j
[2025-08-05 02:58:36] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -53.834718+0.002544j
[2025-08-05 02:58:55] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -53.880431-0.000481j
[2025-08-05 02:59:14] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -53.837569+0.002587j
[2025-08-05 02:59:33] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -53.889263-0.001529j
[2025-08-05 02:59:52] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -53.853939-0.001980j
[2025-08-05 03:00:11] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -53.874728-0.000512j
[2025-08-05 03:00:30] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -53.811378-0.002124j
[2025-08-05 03:00:50] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -53.807916-0.000515j
[2025-08-05 03:01:09] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -53.820102+0.000856j
[2025-08-05 03:01:28] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -53.792274+0.001015j
[2025-08-05 03:01:47] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -53.829798+0.000228j
[2025-08-05 03:02:06] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -53.848647+0.001218j
[2025-08-05 03:02:25] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -53.817019+0.000790j
[2025-08-05 03:02:44] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -53.819939+0.000801j
[2025-08-05 03:03:03] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -53.837010-0.000645j
[2025-08-05 03:03:23] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -53.856845-0.002199j
[2025-08-05 03:03:42] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -53.872109-0.000577j
[2025-08-05 03:04:01] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -53.849778-0.001140j
[2025-08-05 03:04:20] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -53.841092+0.000803j
[2025-08-05 03:04:39] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -53.869948-0.000417j
[2025-08-05 03:04:58] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -53.795664+0.000748j
[2025-08-05 03:05:17] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -53.852788-0.001375j
[2025-08-05 03:05:37] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -53.846683-0.000359j
[2025-08-05 03:05:56] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -53.883454-0.000911j
[2025-08-05 03:06:15] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -53.919011+0.000783j
[2025-08-05 03:06:34] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -53.888355-0.001928j
[2025-08-05 03:06:53] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -53.899810-0.001248j
[2025-08-05 03:07:12] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -53.854905+0.000310j
[2025-08-05 03:07:31] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -53.902176+0.000109j
[2025-08-05 03:07:50] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -53.869437-0.001085j
[2025-08-05 03:08:10] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -53.815394+0.000854j
[2025-08-05 03:08:29] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -53.856134-0.000381j
[2025-08-05 03:08:48] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -53.857097-0.000317j
[2025-08-05 03:09:07] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -53.880608+0.000620j
[2025-08-05 03:09:26] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -53.852895+0.000443j
[2025-08-05 03:09:45] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -53.823739+0.000189j
[2025-08-05 03:10:04] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -53.868554+0.001212j
[2025-08-05 03:10:24] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -53.864042+0.000335j
[2025-08-05 03:10:43] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -53.863382+0.000235j
[2025-08-05 03:11:02] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -53.858895-0.001816j
[2025-08-05 03:11:21] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -53.838907-0.000467j
[2025-08-05 03:11:40] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -53.850317-0.001027j
[2025-08-05 03:11:59] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -53.826898-0.001492j
[2025-08-05 03:12:18] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -53.810172-0.002201j
[2025-08-05 03:12:37] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -53.827287+0.001441j
[2025-08-05 03:12:57] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -53.830218-0.000194j
[2025-08-05 03:13:16] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -53.824407+0.001194j
[2025-08-05 03:13:35] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -53.815994-0.001457j
[2025-08-05 03:13:54] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -53.844657+0.000837j
[2025-08-05 03:14:13] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -53.878094-0.001415j
[2025-08-05 03:14:32] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -53.858915+0.002045j
[2025-08-05 03:14:51] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -53.915456+0.000287j
[2025-08-05 03:15:10] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -53.936611-0.000847j
[2025-08-05 03:15:30] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -53.866798-0.001351j
[2025-08-05 03:15:49] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -53.882857-0.000048j
[2025-08-05 03:16:08] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -53.889333-0.000798j
[2025-08-05 03:16:27] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -53.872508-0.000863j
[2025-08-05 03:16:46] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -53.828640+0.001506j
[2025-08-05 03:17:05] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -53.863279-0.001416j
[2025-08-05 03:17:24] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -53.881292-0.000731j
[2025-08-05 03:17:43] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -53.856242+0.001738j
[2025-08-05 03:18:03] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -53.809735-0.001092j
[2025-08-05 03:18:22] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -53.842620+0.000386j
[2025-08-05 03:18:41] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -53.830583+0.000295j
[2025-08-05 03:19:00] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -53.855142-0.001559j
[2025-08-05 03:19:19] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -53.819594+0.000179j
[2025-08-05 03:19:38] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -53.861945+0.000121j
[2025-08-05 03:19:57] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -53.827042-0.000438j
[2025-08-05 03:20:17] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -53.787028+0.002593j
[2025-08-05 03:20:36] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -53.803079-0.001368j
[2025-08-05 03:20:55] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -53.807674-0.001301j
[2025-08-05 03:21:14] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -53.773702-0.000172j
[2025-08-05 03:21:33] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -53.830656+0.000678j
[2025-08-05 03:21:52] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -53.858227-0.000876j
[2025-08-05 03:22:11] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -53.804194+0.000508j
[2025-08-05 03:22:30] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -53.831198-0.000000j
[2025-08-05 03:22:50] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -53.815731-0.001239j
[2025-08-05 03:23:09] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -53.823537-0.000244j
[2025-08-05 03:23:28] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -53.866134+0.000396j
[2025-08-05 03:23:47] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -53.840082-0.000920j
[2025-08-05 03:24:06] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -53.838822-0.002489j
[2025-08-05 03:24:25] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -53.827762-0.001955j
[2025-08-05 03:24:44] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -53.824534-0.000443j
[2025-08-05 03:25:03] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -53.854940-0.000824j
[2025-08-05 03:25:23] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -53.833335-0.000730j
[2025-08-05 03:25:42] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -53.811440-0.000167j
[2025-08-05 03:26:01] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -53.784971-0.002697j
[2025-08-05 03:26:20] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -53.800921-0.000524j
[2025-08-05 03:26:39] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -53.818677-0.000141j
[2025-08-05 03:26:58] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -53.860308+0.000521j
[2025-08-05 03:27:17] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -53.883020-0.000152j
[2025-08-05 03:27:37] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -53.900080-0.001507j
[2025-08-05 03:27:56] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -53.868926+0.003688j
[2025-08-05 03:28:15] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -53.829456+0.000869j
[2025-08-05 03:28:34] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -53.830883+0.001305j
[2025-08-05 03:28:53] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -53.851908+0.000357j
[2025-08-05 03:29:12] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -53.824645-0.001388j
[2025-08-05 03:29:31] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -53.871553-0.001113j
[2025-08-05 03:29:50] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -53.863689+0.000838j
[2025-08-05 03:30:10] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -53.852504+0.001424j
[2025-08-05 03:30:29] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -53.835594-0.000181j
[2025-08-05 03:30:48] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -53.828510-0.000005j
[2025-08-05 03:31:07] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -53.821014+0.000437j
[2025-08-05 03:31:26] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -53.856931-0.001611j
[2025-08-05 03:31:45] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -53.848132-0.001751j
[2025-08-05 03:32:04] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -53.795579-0.001130j
[2025-08-05 03:32:24] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -53.875866-0.000795j
[2025-08-05 03:32:43] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -53.848749-0.002024j
[2025-08-05 03:33:02] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -53.884011+0.000986j
[2025-08-05 03:33:21] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -53.877710-0.000744j
[2025-08-05 03:33:40] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -53.861388+0.002481j
[2025-08-05 03:33:59] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -53.866810+0.002552j
[2025-08-05 03:34:18] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -53.832923-0.000585j
[2025-08-05 03:34:37] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -53.786372+0.000305j
[2025-08-05 03:34:56] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -53.808136+0.000491j
[2025-08-05 03:35:16] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -53.872763-0.001307j
[2025-08-05 03:35:35] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -53.817167+0.002733j
[2025-08-05 03:35:54] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -53.796578+0.001817j
[2025-08-05 03:36:13] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -53.783164+0.000897j
[2025-08-05 03:36:32] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -53.779337+0.001738j
[2025-08-05 03:36:51] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -53.819701+0.000511j
[2025-08-05 03:37:10] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -53.838713+0.000396j
[2025-08-05 03:37:29] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -53.812103+0.000108j
[2025-08-05 03:37:49] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -53.829962-0.001812j
[2025-08-05 03:38:08] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -53.798580-0.000191j
[2025-08-05 03:38:27] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -53.783617-0.000041j
[2025-08-05 03:38:46] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -53.842729-0.000881j
[2025-08-05 03:39:05] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -53.864014-0.000975j
[2025-08-05 03:39:24] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -53.876258+0.001723j
[2025-08-05 03:39:43] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -53.827488+0.000098j
[2025-08-05 03:40:02] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -53.890500-0.000873j
[2025-08-05 03:40:22] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -53.886164+0.002447j
[2025-08-05 03:40:41] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -53.957523+0.000946j
[2025-08-05 03:41:00] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -53.915745-0.000776j
[2025-08-05 03:41:19] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -53.903499-0.001002j
[2025-08-05 03:41:38] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -53.931136-0.000672j
[2025-08-05 03:41:57] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -53.909348+0.000273j
[2025-08-05 03:42:16] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -53.932010+0.001322j
[2025-08-05 03:42:35] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -53.945323+0.000702j
[2025-08-05 03:42:55] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -53.933282+0.000310j
[2025-08-05 03:43:14] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -53.945952-0.001280j
[2025-08-05 03:43:33] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -53.928549-0.001255j
[2025-08-05 03:43:52] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -53.930001-0.001492j
[2025-08-05 03:44:11] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -53.974860-0.000907j
[2025-08-05 03:44:30] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -53.949943+0.003252j
[2025-08-05 03:44:49] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -53.897629-0.001944j
[2025-08-05 03:45:08] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -53.906644-0.001191j
[2025-08-05 03:45:27] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -53.885660+0.000157j
[2025-08-05 03:45:47] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -53.859637-0.000652j
[2025-08-05 03:46:06] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -53.803518+0.000187j
[2025-08-05 03:46:25] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -53.816794+0.000984j
[2025-08-05 03:46:44] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -53.752529+0.000791j
[2025-08-05 03:47:03] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -53.761789+0.000398j
[2025-08-05 03:47:22] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -53.872900-0.002172j
[2025-08-05 03:47:41] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -53.826613-0.000912j
[2025-08-05 03:48:00] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -53.803183+0.000441j
[2025-08-05 03:48:20] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -53.861405-0.000779j
[2025-08-05 03:48:39] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -53.839995+0.000717j
[2025-08-05 03:48:58] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -53.880598-0.001638j
[2025-08-05 03:49:17] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -53.916977-0.000629j
[2025-08-05 03:49:36] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -53.921285-0.000544j
[2025-08-05 03:49:55] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -53.907194-0.000903j
[2025-08-05 03:50:14] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -53.898702-0.000633j
[2025-08-05 03:50:33] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -53.869684-0.000268j
[2025-08-05 03:50:53] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -53.827780-0.000765j
[2025-08-05 03:51:12] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -53.850702-0.000028j
[2025-08-05 03:51:31] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -53.788494-0.003218j
[2025-08-05 03:51:50] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -53.806578+0.000709j
[2025-08-05 03:52:09] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -53.798334-0.000977j
[2025-08-05 03:52:28] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -53.791772-0.002165j
[2025-08-05 03:52:47] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -53.780486+0.000975j
[2025-08-05 03:53:07] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -53.798480-0.000151j
[2025-08-05 03:53:26] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -53.748040+0.000798j
[2025-08-05 03:53:45] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -53.773858+0.000388j
[2025-08-05 03:54:04] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -53.819410-0.000285j
[2025-08-05 03:54:23] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -53.787131-0.002468j
[2025-08-05 03:54:42] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -53.775290-0.000112j
[2025-08-05 03:55:01] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -53.822363+0.001397j
[2025-08-05 03:55:20] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -53.920550-0.000272j
[2025-08-05 03:55:39] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -53.841497-0.002134j
[2025-08-05 03:55:59] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -53.833304-0.000269j
[2025-08-05 03:56:18] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -53.855824+0.000195j
[2025-08-05 03:56:37] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -53.847934+0.000717j
[2025-08-05 03:56:56] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -53.764693+0.000603j
[2025-08-05 03:57:15] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -53.840800+0.000398j
[2025-08-05 03:57:34] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -53.853834+0.000925j
[2025-08-05 03:57:54] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -53.860176+0.000057j
[2025-08-05 03:58:13] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -53.819337+0.001726j
[2025-08-05 03:58:32] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -53.816602-0.001295j
[2025-08-05 03:58:51] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -53.776616-0.001818j
[2025-08-05 03:59:10] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -53.776691-0.000264j
[2025-08-05 03:59:29] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -53.818230+0.000238j
[2025-08-05 03:59:48] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -53.789535-0.001575j
[2025-08-05 04:00:07] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -53.800605+0.001568j
[2025-08-05 04:00:27] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -53.802450-0.000536j
[2025-08-05 04:00:46] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -53.786401+0.000312j
[2025-08-05 04:01:05] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -53.794081-0.000677j
[2025-08-05 04:01:24] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -53.743670-0.001561j
[2025-08-05 04:01:43] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -53.788300+0.000108j
[2025-08-05 04:02:02] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -53.767467+0.000730j
[2025-08-05 04:02:21] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -53.819544-0.000441j
[2025-08-05 04:02:40] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -53.873276-0.000813j
[2025-08-05 04:03:00] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -53.862978+0.000910j
[2025-08-05 04:03:19] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -53.806484+0.000211j
[2025-08-05 04:03:38] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -53.839128-0.000642j
[2025-08-05 04:03:57] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -53.819941+0.001214j
[2025-08-05 04:04:16] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -53.813479-0.000357j
[2025-08-05 04:04:35] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -53.851541-0.001098j
[2025-08-05 04:04:54] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -53.844092+0.000205j
[2025-08-05 04:05:13] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -53.840949-0.000707j
[2025-08-05 04:05:33] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -53.871625-0.000797j
[2025-08-05 04:05:52] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -53.838187+0.001962j
[2025-08-05 04:06:11] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -53.831925+0.000765j
[2025-08-05 04:06:30] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -53.914358+0.000596j
[2025-08-05 04:06:49] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -53.857752+0.000573j
[2025-08-05 04:07:08] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -53.866604+0.000422j
[2025-08-05 04:07:27] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -53.905371+0.002127j
[2025-08-05 04:07:47] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -53.854537+0.001668j
[2025-08-05 04:08:06] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -53.833491-0.002544j
[2025-08-05 04:08:25] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -53.851544-0.000824j
[2025-08-05 04:08:44] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -53.824090+0.000386j
[2025-08-05 04:09:03] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -53.830524-0.000646j
[2025-08-05 04:09:22] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -53.826086-0.001550j
[2025-08-05 04:09:41] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -53.833250+0.001185j
[2025-08-05 04:10:00] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -53.888932-0.001894j
[2025-08-05 04:10:20] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -53.891688-0.000382j
[2025-08-05 04:10:39] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -53.919944+0.000141j
[2025-08-05 04:10:58] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -53.942191+0.001131j
[2025-08-05 04:11:17] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -53.870740-0.001509j
[2025-08-05 04:11:36] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -53.850251+0.000173j
[2025-08-05 04:11:55] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -53.881439+0.000220j
[2025-08-05 04:12:14] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -53.835524+0.000005j
[2025-08-05 04:12:33] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -53.842912-0.000116j
[2025-08-05 04:12:53] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -53.824632-0.001832j
[2025-08-05 04:13:12] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -53.876126+0.001257j
[2025-08-05 04:13:31] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -53.854780+0.000444j
[2025-08-05 04:13:50] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -53.930330-0.001645j
[2025-08-05 04:14:09] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -53.879553-0.001352j
[2025-08-05 04:14:28] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -53.900284-0.001556j
[2025-08-05 04:14:47] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -53.885339-0.000478j
[2025-08-05 04:15:07] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -53.875666+0.002991j
[2025-08-05 04:15:26] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -53.836919+0.001722j
[2025-08-05 04:15:45] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -53.878721+0.001796j
[2025-08-05 04:16:04] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -53.773090+0.000658j
[2025-08-05 04:16:23] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -53.782022+0.001010j
[2025-08-05 04:16:42] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -53.767971-0.000203j
[2025-08-05 04:17:01] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -53.836237+0.001214j
[2025-08-05 04:17:20] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -53.867394+0.000478j
[2025-08-05 04:17:40] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -53.897159+0.000582j
[2025-08-05 04:17:59] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -53.869835-0.000526j
[2025-08-05 04:18:18] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -53.915450-0.001041j
[2025-08-05 04:18:37] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -53.926626+0.000819j
[2025-08-05 04:18:56] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -53.948623-0.000666j
[2025-08-05 04:19:15] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -53.957359+0.000252j
[2025-08-05 04:19:34] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -53.897708-0.000869j
[2025-08-05 04:19:54] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -53.951505+0.000720j
[2025-08-05 04:20:13] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -53.860051+0.000081j
[2025-08-05 04:20:32] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -53.830167+0.001213j
[2025-08-05 04:20:51] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -53.801884+0.000410j
[2025-08-05 04:21:10] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -53.855616-0.000093j
[2025-08-05 04:21:29] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -53.814746+0.001981j
[2025-08-05 04:21:48] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -53.853305+0.000582j
[2025-08-05 04:22:07] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -53.812057-0.000109j
[2025-08-05 04:22:27] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -53.776731-0.000751j
[2025-08-05 04:22:46] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -53.794040-0.000690j
[2025-08-05 04:23:05] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -53.875178-0.001405j
[2025-08-05 04:23:24] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -53.871121-0.000031j
[2025-08-05 04:23:43] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -53.877244+0.002724j
[2025-08-05 04:24:02] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -53.895306-0.000400j
[2025-08-05 04:24:21] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -53.922646+0.001525j
[2025-08-05 04:24:40] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -53.984355-0.000117j
[2025-08-05 04:25:00] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -53.847299-0.000079j
[2025-08-05 04:25:19] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -53.852633+0.000600j
[2025-08-05 04:25:38] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -53.820376-0.000061j
[2025-08-05 04:25:57] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -53.870365-0.000110j
[2025-08-05 04:26:16] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -53.804090+0.002040j
[2025-08-05 04:26:35] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -53.784521+0.001518j
[2025-08-05 04:26:54] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -53.827543-0.001210j
[2025-08-05 04:27:13] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -53.868608+0.001386j
[2025-08-05 04:27:33] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -53.873624-0.000002j
[2025-08-05 04:27:52] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -53.878043+0.002097j
[2025-08-05 04:28:11] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -53.870046-0.000303j
[2025-08-05 04:28:30] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -53.826847-0.001219j
[2025-08-05 04:28:49] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -53.877282-0.000150j
[2025-08-05 04:29:08] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -53.803878+0.000609j
[2025-08-05 04:29:27] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -53.775928+0.000665j
[2025-08-05 04:29:47] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -53.761049+0.000983j
[2025-08-05 04:30:06] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -53.818529-0.000368j
[2025-08-05 04:30:25] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -53.796677+0.000850j
[2025-08-05 04:30:44] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -53.805371-0.001348j
[2025-08-05 04:31:03] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -53.810363+0.001210j
[2025-08-05 04:31:22] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -53.824755-0.002489j
[2025-08-05 04:31:41] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -53.793223-0.000521j
[2025-08-05 04:32:00] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -53.784308-0.001814j
[2025-08-05 04:32:20] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -53.797376-0.002009j
[2025-08-05 04:32:39] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -53.790073-0.001634j
[2025-08-05 04:32:58] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -53.794790-0.000741j
[2025-08-05 04:33:17] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -53.859239+0.000515j
[2025-08-05 04:33:36] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -53.823833+0.002028j
[2025-08-05 04:33:55] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -53.800764-0.000671j
[2025-08-05 04:34:14] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -53.888542+0.000012j
[2025-08-05 04:34:34] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -53.833783+0.000323j
[2025-08-05 04:34:53] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -53.877290+0.000385j
[2025-08-05 04:35:12] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -53.848144-0.000563j
[2025-08-05 04:35:31] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -53.808083-0.000976j
[2025-08-05 04:35:50] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -53.833549-0.002177j
[2025-08-05 04:36:09] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -53.843558-0.000688j
[2025-08-05 04:36:28] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -53.776618+0.000710j
[2025-08-05 04:36:47] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -53.824093+0.000838j
[2025-08-05 04:37:07] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -53.840584+0.000178j
[2025-08-05 04:37:26] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -53.893114-0.001835j
[2025-08-05 04:37:45] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -53.826677-0.001179j
[2025-08-05 04:38:04] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -53.831558-0.000091j
[2025-08-05 04:38:04] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-08-05 04:38:23] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -53.870016-0.001697j
[2025-08-05 04:38:42] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -53.889636-0.000027j
[2025-08-05 04:39:01] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -53.827369+0.000680j
[2025-08-05 04:39:20] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -53.843289+0.000804j
[2025-08-05 04:39:40] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -53.848188+0.002549j
[2025-08-05 04:39:59] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -53.849789+0.000795j
[2025-08-05 04:40:18] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -53.838157+0.000127j
[2025-08-05 04:40:37] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -53.840600+0.001867j
[2025-08-05 04:40:56] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -53.849241+0.001572j
[2025-08-05 04:41:15] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -53.833643-0.001961j
[2025-08-05 04:41:34] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -53.836417-0.000255j
[2025-08-05 04:41:54] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -53.847760-0.000353j
[2025-08-05 04:42:13] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -53.839304+0.000485j
[2025-08-05 04:42:32] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -53.829276-0.000102j
[2025-08-05 04:42:51] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -53.805007+0.000163j
[2025-08-05 04:43:10] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -53.813745+0.003410j
[2025-08-05 04:43:29] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -53.780839-0.000680j
[2025-08-05 04:43:48] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -53.794790+0.000481j
[2025-08-05 04:44:07] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -53.866209-0.000318j
[2025-08-05 04:44:27] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -53.834984+0.001434j
[2025-08-05 04:44:46] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -53.876424-0.000723j
[2025-08-05 04:45:05] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -53.917404+0.001001j
[2025-08-05 04:45:24] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -53.866693-0.000099j
[2025-08-05 04:45:43] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -53.903046+0.000437j
[2025-08-05 04:46:02] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -53.868959-0.000484j
[2025-08-05 04:46:21] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -53.895392-0.000859j
[2025-08-05 04:46:40] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -53.881297+0.000315j
[2025-08-05 04:47:00] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -53.885206-0.000520j
[2025-08-05 04:47:19] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -53.896705-0.002068j
[2025-08-05 04:47:38] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -53.858721-0.000396j
[2025-08-05 04:47:57] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -53.860352-0.000107j
[2025-08-05 04:48:16] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -53.884753-0.000877j
[2025-08-05 04:48:35] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -53.870531+0.000832j
[2025-08-05 04:48:54] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -53.893443+0.000724j
[2025-08-05 04:49:13] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -53.880476+0.001351j
[2025-08-05 04:49:33] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -53.908750+0.000445j
[2025-08-05 04:49:52] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -53.901258+0.002836j
[2025-08-05 04:50:11] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -53.885088-0.001639j
[2025-08-05 04:50:30] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -53.896873+0.001517j
[2025-08-05 04:50:49] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -53.810032+0.000279j
[2025-08-05 04:51:08] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -53.840588-0.002358j
[2025-08-05 04:51:27] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -53.842201+0.000538j
[2025-08-05 04:51:47] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -53.848011+0.000547j
[2025-08-05 04:52:06] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -53.868138+0.000248j
[2025-08-05 04:52:25] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -53.861701-0.000753j
[2025-08-05 04:52:44] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -53.822108-0.000195j
[2025-08-05 04:53:03] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -53.901193+0.000459j
[2025-08-05 04:53:22] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -53.860539+0.000017j
[2025-08-05 04:53:41] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -53.818464+0.000476j
[2025-08-05 04:54:00] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -53.850026-0.001004j
[2025-08-05 04:54:00] ✅ Training completed | Restarts: 2
[2025-08-05 04:54:00] ============================================================
[2025-08-05 04:54:00] Training completed | Runtime: 20132.2s
[2025-08-05 04:54:26] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-08-05 04:54:26] ============================================================
