[2025-07-31 19:19:04] ✓ 从checkpoint恢复: results/L=4/J2=0.00/J1=0.05/training/checkpoints/final_GCNN.pkl
[2025-07-31 19:19:04]   - 迭代次数: final
[2025-07-31 19:19:04]   - 能量: -55.045696+0.000398j ± 0.042838
[2025-07-31 19:19:04]   - 时间戳: 2025-07-31T18:38:37.726587+08:00
[2025-07-31 19:19:13] ✓ 变分状态参数已从checkpoint恢复
[2025-07-31 19:19:13] ✓ 从final状态恢复, 重置迭代计数为0
[2025-07-31 19:19:13] ==================================================
[2025-07-31 19:19:13] GCNN for Shastry-Sutherland Model
[2025-07-31 19:19:13] ==================================================
[2025-07-31 19:19:13] System parameters:
[2025-07-31 19:19:13]   - System size: L=4, N=64
[2025-07-31 19:19:13]   - System parameters: J1=0.06, J2=0.0, Q=1.0
[2025-07-31 19:19:13] --------------------------------------------------
[2025-07-31 19:19:13] Model parameters:
[2025-07-31 19:19:13]   - Number of layers = 4
[2025-07-31 19:19:13]   - Number of features = 4
[2025-07-31 19:19:13]   - Total parameters = 12572
[2025-07-31 19:19:13] --------------------------------------------------
[2025-07-31 19:19:13] Training parameters:
[2025-07-31 19:19:13]   - Learning rate: 0.015
[2025-07-31 19:19:13]   - Total iterations: 1050
[2025-07-31 19:19:13]   - Annealing cycles: 3
[2025-07-31 19:19:13]   - Initial period: 150
[2025-07-31 19:19:13]   - Period multiplier: 2.0
[2025-07-31 19:19:13]   - Temperature range: 0.0-1.0
[2025-07-31 19:19:13]   - Samples: 16384
[2025-07-31 19:19:13]   - Discarded samples: 0
[2025-07-31 19:19:13]   - Chunk size: 2048
[2025-07-31 19:19:13]   - Diagonal shift: 0.2
[2025-07-31 19:19:13]   - Gradient clipping: 1.0
[2025-07-31 19:19:13]   - Checkpoint enabled: interval=500
[2025-07-31 19:19:13]   - Checkpoint directory: results/L=4/J2=0.00/J1=0.06/training/checkpoints
[2025-07-31 19:19:13] --------------------------------------------------
[2025-07-31 19:19:13] Device status:
[2025-07-31 19:19:13]   - Devices model: A100
[2025-07-31 19:19:13]   - Number of devices: 1
[2025-07-31 19:19:13]   - Sharding: True
[2025-07-31 19:19:13] ============================================================
[2025-07-31 19:20:03] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -55.447964+0.002918j
[2025-07-31 19:20:37] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -55.420379-0.001761j
[2025-07-31 19:20:56] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -55.439650-0.000783j
[2025-07-31 19:21:15] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -55.454988-0.001672j
[2025-07-31 19:21:34] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -55.449712-0.002690j
[2025-07-31 19:21:54] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -55.440257-0.002927j
[2025-07-31 19:22:13] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -55.466460+0.000419j
[2025-07-31 19:22:32] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -55.445919-0.001720j
[2025-07-31 19:22:51] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -55.465904-0.000984j
[2025-07-31 19:23:10] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -55.400496-0.000735j
[2025-07-31 19:23:29] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -55.393630-0.000860j
[2025-07-31 19:23:48] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -55.420812+0.001126j
[2025-07-31 19:24:08] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -55.435810-0.001342j
[2025-07-31 19:24:27] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -55.448040-0.001404j
[2025-07-31 19:24:46] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -55.415785-0.000157j
[2025-07-31 19:25:05] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -55.410884+0.000521j
[2025-07-31 19:25:24] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -55.401764+0.000946j
[2025-07-31 19:25:43] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -55.447755+0.001700j
[2025-07-31 19:26:02] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -55.433499-0.000287j
[2025-07-31 19:26:21] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -55.432378+0.000798j
[2025-07-31 19:26:41] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -55.489152-0.000802j
[2025-07-31 19:27:00] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -55.455599+0.000185j
[2025-07-31 19:27:19] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -55.512720+0.000128j
[2025-07-31 19:27:38] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -55.457885-0.000484j
[2025-07-31 19:27:57] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -55.442982+0.001614j
[2025-07-31 19:28:16] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -55.369889+0.003627j
[2025-07-31 19:28:35] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -55.375665+0.000825j
[2025-07-31 19:28:54] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -55.405508-0.000519j
[2025-07-31 19:29:13] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -55.381573+0.000357j
[2025-07-31 19:29:32] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -55.390762-0.000954j
[2025-07-31 19:29:52] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -55.384483+0.000727j
[2025-07-31 19:30:11] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -55.341425+0.000025j
[2025-07-31 19:30:30] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -55.377825+0.000283j
[2025-07-31 19:30:49] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -55.423431+0.000247j
[2025-07-31 19:31:08] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -55.434426-0.000926j
[2025-07-31 19:31:27] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -55.438749-0.002108j
[2025-07-31 19:31:46] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -55.433999-0.001311j
[2025-07-31 19:32:06] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -55.492813-0.000324j
[2025-07-31 19:32:25] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -55.417885-0.002308j
[2025-07-31 19:32:44] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -55.420198-0.001047j
[2025-07-31 19:33:03] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -55.406485-0.001155j
[2025-07-31 19:33:22] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -55.421013-0.000093j
[2025-07-31 19:33:41] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -55.372178+0.000231j
[2025-07-31 19:34:00] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -55.362799+0.000135j
[2025-07-31 19:34:19] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -55.386523+0.000592j
[2025-07-31 19:34:39] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -55.417792-0.000675j
[2025-07-31 19:34:58] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -55.385403-0.000116j
[2025-07-31 19:35:17] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -55.395054+0.001705j
[2025-07-31 19:35:36] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -55.387777+0.001170j
[2025-07-31 19:35:55] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -55.421567+0.000192j
[2025-07-31 19:36:14] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -55.422474-0.000136j
[2025-07-31 19:36:33] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -55.425938+0.000663j
[2025-07-31 19:36:52] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -55.477555-0.001147j
[2025-07-31 19:37:11] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -55.468719+0.000168j
[2025-07-31 19:37:31] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -55.426968-0.000739j
[2025-07-31 19:37:50] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -55.433354+0.000096j
[2025-07-31 19:38:09] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -55.402980-0.000146j
[2025-07-31 19:38:28] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -55.428783-0.000297j
[2025-07-31 19:38:47] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -55.436216-0.001601j
[2025-07-31 19:39:06] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -55.479145+0.000182j
[2025-07-31 19:39:25] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -55.479595+0.001242j
[2025-07-31 19:39:45] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -55.477425-0.001618j
[2025-07-31 19:40:04] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -55.417426+0.001767j
[2025-07-31 19:40:23] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -55.404432+0.000428j
[2025-07-31 19:40:42] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -55.443940-0.001253j
[2025-07-31 19:41:01] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -55.431954-0.000865j
[2025-07-31 19:41:20] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -55.438222+0.000753j
[2025-07-31 19:41:39] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -55.435906+0.000405j
[2025-07-31 19:41:58] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -55.437639-0.001000j
[2025-07-31 19:42:18] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -55.467352+0.000657j
[2025-07-31 19:42:37] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -55.439332-0.000846j
[2025-07-31 19:42:56] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -55.382799-0.000753j
[2025-07-31 19:43:15] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -55.417283+0.000015j
[2025-07-31 19:43:34] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -55.423139+0.001884j
[2025-07-31 19:43:53] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -55.412805+0.000954j
[2025-07-31 19:44:12] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -55.444410+0.002304j
[2025-07-31 19:44:32] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -55.431984+0.000390j
[2025-07-31 19:44:51] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -55.472329-0.000999j
[2025-07-31 19:45:10] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -55.490374-0.001848j
[2025-07-31 19:45:29] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -55.479874+0.000348j
[2025-07-31 19:45:48] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -55.457846-0.000523j
[2025-07-31 19:46:07] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -55.493993+0.001285j
[2025-07-31 19:46:26] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -55.454707+0.000475j
[2025-07-31 19:46:45] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -55.456005-0.000900j
[2025-07-31 19:47:04] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -55.478758+0.000637j
[2025-07-31 19:47:23] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -55.356519-0.000436j
[2025-07-31 19:47:43] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -55.372230-0.001067j
[2025-07-31 19:48:02] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -55.346777-0.000536j
[2025-07-31 19:48:21] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -55.378083-0.000819j
[2025-07-31 19:48:40] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -55.392321-0.000027j
[2025-07-31 19:48:59] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -55.409087+0.002383j
[2025-07-31 19:49:18] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -55.420077+0.001010j
[2025-07-31 19:49:37] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -55.400105+0.000353j
[2025-07-31 19:49:56] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -55.359787+0.000661j
[2025-07-31 19:50:15] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -55.333823+0.000027j
[2025-07-31 19:50:35] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -55.400543-0.001598j
[2025-07-31 19:50:54] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -55.413192+0.000035j
[2025-07-31 19:51:13] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -55.393106-0.000297j
[2025-07-31 19:51:32] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -55.384745-0.000067j
[2025-07-31 19:51:51] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -55.376149-0.001231j
[2025-07-31 19:52:10] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -55.447793-0.000674j
[2025-07-31 19:52:29] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -55.477223+0.001138j
[2025-07-31 19:52:48] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -55.425418-0.000605j
[2025-07-31 19:53:07] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -55.373270+0.000803j
[2025-07-31 19:53:27] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -55.472535-0.001681j
[2025-07-31 19:53:46] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -55.348091+0.002082j
[2025-07-31 19:54:05] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -55.382956+0.000069j
[2025-07-31 19:54:24] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -55.351754-0.000919j
[2025-07-31 19:54:43] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -55.413657+0.000966j
[2025-07-31 19:55:02] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -55.389787-0.002936j
[2025-07-31 19:55:21] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -55.438641-0.000158j
[2025-07-31 19:55:40] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -55.431045-0.000276j
[2025-07-31 19:55:59] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -55.407732+0.000874j
[2025-07-31 19:56:18] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -55.401609-0.000270j
[2025-07-31 19:56:38] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -55.344767+0.000670j
[2025-07-31 19:56:57] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -55.388273+0.001169j
[2025-07-31 19:57:16] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -55.393549-0.000004j
[2025-07-31 19:57:35] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -55.411343+0.001097j
[2025-07-31 19:57:54] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -55.377613+0.000456j
[2025-07-31 19:58:13] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -55.430065+0.001218j
[2025-07-31 19:58:32] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -55.419891+0.000514j
[2025-07-31 19:58:51] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -55.416918-0.000206j
[2025-07-31 19:59:11] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -55.397432-0.000223j
[2025-07-31 19:59:30] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -55.395391-0.001158j
[2025-07-31 19:59:49] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -55.437031-0.001914j
[2025-07-31 20:00:08] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -55.409254-0.000243j
[2025-07-31 20:00:27] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -55.471354+0.000188j
[2025-07-31 20:00:46] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -55.470439-0.001274j
[2025-07-31 20:01:05] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -55.421902-0.001199j
[2025-07-31 20:01:24] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -55.428063+0.000014j
[2025-07-31 20:01:43] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -55.399617-0.001719j
[2025-07-31 20:02:03] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -55.456422-0.000703j
[2025-07-31 20:02:22] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -55.459441-0.000492j
[2025-07-31 20:02:41] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -55.376190-0.002082j
[2025-07-31 20:03:00] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -55.416871-0.000872j
[2025-07-31 20:03:19] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -55.413278-0.000429j
[2025-07-31 20:03:38] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -55.446910-0.000459j
[2025-07-31 20:03:57] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -55.418587-0.001910j
[2025-07-31 20:04:17] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -55.481002-0.000440j
[2025-07-31 20:04:36] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -55.414436-0.000666j
[2025-07-31 20:04:55] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -55.477171-0.002475j
[2025-07-31 20:05:14] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -55.439749+0.000790j
[2025-07-31 20:05:33] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -55.408336+0.000766j
[2025-07-31 20:05:52] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -55.442437-0.002077j
[2025-07-31 20:06:11] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -55.431403+0.000982j
[2025-07-31 20:06:31] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -55.437979-0.000636j
[2025-07-31 20:06:50] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -55.394360-0.000252j
[2025-07-31 20:07:09] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -55.393262+0.000365j
[2025-07-31 20:07:28] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -55.425231-0.001853j
[2025-07-31 20:07:47] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -55.440687-0.000040j
[2025-07-31 20:07:47] RESTART #1 | Period: 300
[2025-07-31 20:08:06] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -55.462332-0.000782j
[2025-07-31 20:08:25] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -55.454575+0.000938j
[2025-07-31 20:08:45] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -55.403226-0.000017j
[2025-07-31 20:09:04] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -55.402164-0.000466j
[2025-07-31 20:09:23] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -55.468426-0.001130j
[2025-07-31 20:09:42] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -55.456058+0.000654j
[2025-07-31 20:10:01] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -55.436592-0.000701j
[2025-07-31 20:10:20] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -55.470377+0.000091j
[2025-07-31 20:10:39] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -55.490070+0.000614j
[2025-07-31 20:10:59] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -55.490717+0.000205j
[2025-07-31 20:11:18] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -55.434279+0.001406j
[2025-07-31 20:11:37] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -55.429138-0.000705j
[2025-07-31 20:11:56] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -55.454937-0.001836j
[2025-07-31 20:12:15] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -55.398274-0.001322j
[2025-07-31 20:12:34] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -55.423505+0.000820j
[2025-07-31 20:12:53] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -55.447232+0.000816j
[2025-07-31 20:13:12] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -55.364852+0.000157j
[2025-07-31 20:13:32] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -55.419238+0.001240j
[2025-07-31 20:13:51] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -55.388361-0.000335j
[2025-07-31 20:14:10] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -55.450384-0.001794j
[2025-07-31 20:14:29] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -55.490579-0.001177j
[2025-07-31 20:14:48] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -55.449928-0.000933j
[2025-07-31 20:15:07] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -55.429951-0.000299j
[2025-07-31 20:15:26] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -55.428784+0.000124j
[2025-07-31 20:15:46] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -55.433515-0.000407j
[2025-07-31 20:16:05] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -55.395754-0.000691j
[2025-07-31 20:16:24] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -55.458893+0.000249j
[2025-07-31 20:16:43] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -55.390980-0.000608j
[2025-07-31 20:17:02] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -55.456508-0.001985j
[2025-07-31 20:17:21] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -55.435563+0.001914j
[2025-07-31 20:17:40] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -55.426695-0.001133j
[2025-07-31 20:18:00] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -55.443559+0.000017j
[2025-07-31 20:18:19] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -55.542544+0.002055j
[2025-07-31 20:18:38] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -55.470633-0.001868j
[2025-07-31 20:18:57] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -55.506267-0.000759j
[2025-07-31 20:19:16] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -55.436189-0.002678j
[2025-07-31 20:19:35] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -55.413839+0.000942j
[2025-07-31 20:19:54] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -55.354694+0.000001j
[2025-07-31 20:20:13] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -55.397644-0.000149j
[2025-07-31 20:20:32] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -55.397330-0.000423j
[2025-07-31 20:20:51] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -55.435883-0.000962j
[2025-07-31 20:21:10] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -55.438017-0.000073j
[2025-07-31 20:21:29] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -55.414697-0.001555j
[2025-07-31 20:21:48] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -55.396894-0.000809j
[2025-07-31 20:22:07] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -55.387047-0.001765j
[2025-07-31 20:22:26] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -55.334199-0.001011j
[2025-07-31 20:22:46] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -55.440907+0.001888j
[2025-07-31 20:23:05] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -55.432219+0.000953j
[2025-07-31 20:23:24] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -55.405703+0.000504j
[2025-07-31 20:23:43] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -55.372520-0.000576j
[2025-07-31 20:24:02] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -55.435930+0.000423j
[2025-07-31 20:24:21] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -55.416600+0.000109j
[2025-07-31 20:24:40] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -55.494246+0.001043j
[2025-07-31 20:24:59] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -55.479193-0.000835j
[2025-07-31 20:25:18] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -55.477498-0.001866j
[2025-07-31 20:25:37] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -55.467441-0.000488j
[2025-07-31 20:25:56] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -55.426914+0.000149j
[2025-07-31 20:26:15] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -55.465174+0.000953j
[2025-07-31 20:26:34] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -55.435341+0.000538j
[2025-07-31 20:26:53] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -55.448983+0.002452j
[2025-07-31 20:27:12] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -55.446538+0.001059j
[2025-07-31 20:27:31] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -55.457011-0.000905j
[2025-07-31 20:27:50] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -55.454230+0.000375j
[2025-07-31 20:28:09] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -55.470218+0.000253j
[2025-07-31 20:28:28] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -55.478490+0.001582j
[2025-07-31 20:28:48] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -55.424891+0.000639j
[2025-07-31 20:29:07] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -55.429245-0.002167j
[2025-07-31 20:29:26] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -55.441007+0.000381j
[2025-07-31 20:29:45] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -55.428437-0.001416j
[2025-07-31 20:30:04] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -55.431447-0.000297j
[2025-07-31 20:30:23] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -55.469305+0.002175j
[2025-07-31 20:30:42] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -55.450826+0.001933j
[2025-07-31 20:31:01] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -55.483179-0.001287j
[2025-07-31 20:31:20] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -55.455508+0.001757j
[2025-07-31 20:31:39] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -55.416549+0.001306j
[2025-07-31 20:31:58] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -55.443694-0.001035j
[2025-07-31 20:32:17] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -55.438394-0.000133j
[2025-07-31 20:32:36] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -55.467450-0.000621j
[2025-07-31 20:32:55] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -55.426128-0.001952j
[2025-07-31 20:33:14] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -55.459829-0.000992j
[2025-07-31 20:33:33] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -55.439011-0.001953j
[2025-07-31 20:33:52] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -55.440810-0.000176j
[2025-07-31 20:34:11] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -55.435438+0.002109j
[2025-07-31 20:34:30] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -55.407593+0.000938j
[2025-07-31 20:34:50] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -55.474686+0.001508j
[2025-07-31 20:35:09] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -55.361462+0.000552j
[2025-07-31 20:35:28] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -55.346897+0.000507j
[2025-07-31 20:35:47] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -55.366158+0.000106j
[2025-07-31 20:36:06] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -55.410872+0.000251j
[2025-07-31 20:36:25] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -55.408567+0.002284j
[2025-07-31 20:36:44] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -55.404276-0.000291j
[2025-07-31 20:37:03] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -55.451884+0.000077j
[2025-07-31 20:37:22] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -55.428320-0.002805j
[2025-07-31 20:37:41] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -55.418094+0.000125j
[2025-07-31 20:38:00] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -55.366227+0.000383j
[2025-07-31 20:38:19] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -55.394785-0.001017j
[2025-07-31 20:38:38] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -55.395807-0.001527j
[2025-07-31 20:38:57] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -55.340678-0.001095j
[2025-07-31 20:39:16] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -55.394072+0.000338j
[2025-07-31 20:39:35] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -55.465717+0.001403j
[2025-07-31 20:39:54] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -55.468849-0.000954j
[2025-07-31 20:40:13] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -55.463136-0.000738j
[2025-07-31 20:40:32] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -55.489556-0.000164j
[2025-07-31 20:40:52] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -55.480946+0.001102j
[2025-07-31 20:41:11] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -55.470763-0.001059j
[2025-07-31 20:41:30] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -55.520767-0.000874j
[2025-07-31 20:41:49] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -55.483683-0.000011j
[2025-07-31 20:42:08] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -55.485009+0.001312j
[2025-07-31 20:42:27] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -55.498581+0.003980j
[2025-07-31 20:42:46] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -55.452570+0.002814j
[2025-07-31 20:43:05] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -55.465387-0.000312j
[2025-07-31 20:43:24] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -55.508236-0.001524j
[2025-07-31 20:43:43] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -55.476507+0.000893j
[2025-07-31 20:44:02] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -55.482768+0.000576j
[2025-07-31 20:44:21] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -55.471075+0.000585j
[2025-07-31 20:44:40] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -55.428363+0.001306j
[2025-07-31 20:44:59] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -55.453525+0.000495j
[2025-07-31 20:45:18] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -55.418885-0.000719j
[2025-07-31 20:45:37] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -55.448817-0.001266j
[2025-07-31 20:45:56] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -55.425810-0.000798j
[2025-07-31 20:46:15] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -55.431606-0.000408j
[2025-07-31 20:46:34] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -55.436657+0.001570j
[2025-07-31 20:46:53] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -55.448140-0.000223j
[2025-07-31 20:47:13] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -55.453951+0.002765j
[2025-07-31 20:47:32] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -55.427334+0.000298j
[2025-07-31 20:47:51] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -55.444075+0.002229j
[2025-07-31 20:48:10] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -55.444823+0.000679j
[2025-07-31 20:48:29] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -55.462558-0.002114j
[2025-07-31 20:48:48] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -55.428809-0.000824j
[2025-07-31 20:49:07] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -55.528838+0.000165j
[2025-07-31 20:49:26] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -55.489834-0.001223j
[2025-07-31 20:49:45] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -55.511992-0.002275j
[2025-07-31 20:50:04] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -55.502435-0.002246j
[2025-07-31 20:50:23] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -55.482496+0.001731j
[2025-07-31 20:50:42] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -55.491985-0.000194j
[2025-07-31 20:51:01] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -55.450153+0.000209j
[2025-07-31 20:51:20] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -55.427243+0.000229j
[2025-07-31 20:51:39] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -55.398187-0.001497j
[2025-07-31 20:51:58] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -55.434786-0.000121j
[2025-07-31 20:52:17] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -55.454632-0.000952j
[2025-07-31 20:52:36] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -55.411020+0.001532j
[2025-07-31 20:52:55] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -55.403640+0.000804j
[2025-07-31 20:53:14] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -55.409982-0.000196j
[2025-07-31 20:53:33] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -55.450731-0.001317j
[2025-07-31 20:53:53] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -55.441480+0.000675j
[2025-07-31 20:54:12] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -55.443133-0.001311j
[2025-07-31 20:54:31] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -55.430613+0.003294j
[2025-07-31 20:54:50] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -55.421697+0.000339j
[2025-07-31 20:55:09] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -55.463659-0.000492j
[2025-07-31 20:55:28] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -55.439658-0.000270j
[2025-07-31 20:55:47] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -55.486690-0.001671j
[2025-07-31 20:56:06] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -55.491616-0.002460j
[2025-07-31 20:56:25] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -55.433733-0.001871j
[2025-07-31 20:56:44] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -55.452597-0.002028j
[2025-07-31 20:57:03] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -55.453882-0.000673j
[2025-07-31 20:57:22] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -55.424227+0.001347j
[2025-07-31 20:57:41] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -55.377374-0.001184j
[2025-07-31 20:58:00] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -55.377636+0.001777j
[2025-07-31 20:58:19] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -55.429599+0.001787j
[2025-07-31 20:58:38] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -55.456570-0.002083j
[2025-07-31 20:58:57] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -55.472549-0.001693j
[2025-07-31 20:59:16] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -55.460711-0.000539j
[2025-07-31 20:59:35] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -55.435896+0.001731j
[2025-07-31 20:59:54] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -55.450237+0.000910j
[2025-07-31 21:00:14] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -55.461923+0.000569j
[2025-07-31 21:00:33] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -55.444346+0.002469j
[2025-07-31 21:00:52] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -55.476764-0.000259j
[2025-07-31 21:01:11] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -55.473894-0.001796j
[2025-07-31 21:01:30] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -55.436467-0.002090j
[2025-07-31 21:01:49] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -55.464718-0.001112j
[2025-07-31 21:02:08] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -55.404441+0.001866j
[2025-07-31 21:02:27] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -55.405099+0.001414j
[2025-07-31 21:02:46] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -55.384597+0.000086j
[2025-07-31 21:03:05] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -55.402749+0.000465j
[2025-07-31 21:03:24] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -55.393272-0.001264j
[2025-07-31 21:03:43] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -55.358982-0.001190j
[2025-07-31 21:04:02] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -55.418095+0.000896j
[2025-07-31 21:04:21] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -55.411822+0.001038j
[2025-07-31 21:04:40] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -55.401006-0.000257j
[2025-07-31 21:04:59] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -55.421455+0.000288j
[2025-07-31 21:05:18] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -55.439159-0.000563j
[2025-07-31 21:05:37] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -55.414694+0.000694j
[2025-07-31 21:05:56] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -55.340651+0.000590j
[2025-07-31 21:06:15] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -55.358322+0.000282j
[2025-07-31 21:06:34] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -55.323625+0.000533j
[2025-07-31 21:06:54] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -55.358048-0.000989j
[2025-07-31 21:07:13] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -55.358825-0.001094j
[2025-07-31 21:07:32] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -55.316405-0.000773j
[2025-07-31 21:07:51] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -55.340340+0.000118j
[2025-07-31 21:08:10] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -55.310011-0.001265j
[2025-07-31 21:08:29] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -55.384459+0.001058j
[2025-07-31 21:08:48] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -55.367693+0.002267j
[2025-07-31 21:09:07] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -55.379673+0.000638j
[2025-07-31 21:09:26] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -55.361822-0.000467j
[2025-07-31 21:09:45] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -55.352245-0.001825j
[2025-07-31 21:10:04] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -55.353073+0.004922j
[2025-07-31 21:10:23] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -55.379651+0.001725j
[2025-07-31 21:10:42] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -55.378154-0.001143j
[2025-07-31 21:11:01] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -55.423722+0.001249j
[2025-07-31 21:11:20] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -55.457104+0.000125j
[2025-07-31 21:11:39] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -55.402404-0.000103j
[2025-07-31 21:11:58] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -55.397913+0.001751j
[2025-07-31 21:12:18] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -55.443755+0.001749j
[2025-07-31 21:12:37] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -55.362309+0.000712j
[2025-07-31 21:12:56] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -55.376026+0.000718j
[2025-07-31 21:13:15] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -55.402534-0.000351j
[2025-07-31 21:13:34] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -55.412573-0.001855j
[2025-07-31 21:13:53] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -55.351857-0.002195j
[2025-07-31 21:14:12] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -55.408263-0.000432j
[2025-07-31 21:14:31] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -55.418926-0.000975j
[2025-07-31 21:14:50] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -55.412923+0.003597j
[2025-07-31 21:15:09] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -55.401567+0.000406j
[2025-07-31 21:15:28] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -55.365554+0.001358j
[2025-07-31 21:15:47] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -55.397560+0.000101j
[2025-07-31 21:16:06] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -55.420717+0.003424j
[2025-07-31 21:16:25] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -55.441971-0.000041j
[2025-07-31 21:16:44] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -55.400673-0.000841j
[2025-07-31 21:17:03] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -55.404324-0.000193j
[2025-07-31 21:17:22] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -55.409098+0.000251j
[2025-07-31 21:17:41] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -55.471891+0.000778j
[2025-07-31 21:18:00] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -55.459441-0.001888j
[2025-07-31 21:18:20] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -55.483585-0.000180j
[2025-07-31 21:18:39] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -55.475076-0.001141j
[2025-07-31 21:18:58] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -55.453025-0.000918j
[2025-07-31 21:19:17] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -55.421349+0.001088j
[2025-07-31 21:19:36] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -55.464270+0.001416j
[2025-07-31 21:19:55] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -55.468988-0.000010j
[2025-07-31 21:20:14] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -55.442307-0.000725j
[2025-07-31 21:20:33] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -55.491472-0.001168j
[2025-07-31 21:20:52] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -55.466239+0.000322j
[2025-07-31 21:21:11] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -55.476642-0.000441j
[2025-07-31 21:21:30] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -55.497332+0.000675j
[2025-07-31 21:21:49] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -55.412374+0.001032j
[2025-07-31 21:22:08] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -55.435817-0.001940j
[2025-07-31 21:22:27] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -55.425424+0.000176j
[2025-07-31 21:22:46] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -55.448321+0.000098j
[2025-07-31 21:23:05] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -55.450114+0.000813j
[2025-07-31 21:23:24] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -55.431690+0.001663j
[2025-07-31 21:23:43] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -55.473980-0.000908j
[2025-07-31 21:24:02] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -55.418510-0.000240j
[2025-07-31 21:24:21] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -55.438489-0.000240j
[2025-07-31 21:24:41] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -55.459697-0.000102j
[2025-07-31 21:25:00] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -55.464912+0.000662j
[2025-07-31 21:25:19] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -55.487364-0.000723j
[2025-07-31 21:25:38] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -55.453648-0.000171j
[2025-07-31 21:25:57] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -55.408291-0.001401j
[2025-07-31 21:26:16] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -55.459074-0.004596j
[2025-07-31 21:26:35] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -55.400512-0.000680j
[2025-07-31 21:26:54] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -55.443497-0.000246j
[2025-07-31 21:27:13] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -55.408479+0.000837j
[2025-07-31 21:27:32] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -55.459889+0.001499j
[2025-07-31 21:27:51] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -55.366993-0.000152j
[2025-07-31 21:28:10] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -55.371670-0.000689j
[2025-07-31 21:28:29] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -55.391161-0.000529j
[2025-07-31 21:28:48] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -55.378781+0.001350j
[2025-07-31 21:29:07] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -55.370505+0.000218j
[2025-07-31 21:29:26] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -55.355820-0.001295j
[2025-07-31 21:29:45] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -55.393709-0.000066j
[2025-07-31 21:30:04] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -55.426745-0.000243j
[2025-07-31 21:30:23] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -55.403046+0.000869j
[2025-07-31 21:30:42] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -55.342059-0.000307j
[2025-07-31 21:31:01] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -55.408740-0.001896j
[2025-07-31 21:31:20] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -55.418474-0.001331j
[2025-07-31 21:31:40] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -55.442204+0.000748j
[2025-07-31 21:31:59] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -55.466354+0.000086j
[2025-07-31 21:32:18] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -55.427684-0.000376j
[2025-07-31 21:32:37] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -55.445372-0.000295j
[2025-07-31 21:32:56] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -55.456636-0.002410j
[2025-07-31 21:33:15] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -55.469560-0.002233j
[2025-07-31 21:33:34] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -55.431400-0.002091j
[2025-07-31 21:33:53] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -55.407932-0.001363j
[2025-07-31 21:34:12] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -55.385907-0.001593j
[2025-07-31 21:34:31] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -55.385314-0.001065j
[2025-07-31 21:34:50] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -55.437411-0.002218j
[2025-07-31 21:35:09] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -55.416904-0.001191j
[2025-07-31 21:35:28] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -55.371299-0.001234j
[2025-07-31 21:35:47] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -55.411130-0.001002j
[2025-07-31 21:36:06] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -55.448104-0.000686j
[2025-07-31 21:36:25] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -55.389459-0.001045j
[2025-07-31 21:36:44] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -55.415619+0.000055j
[2025-07-31 21:37:03] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -55.449742+0.001182j
[2025-07-31 21:37:22] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -55.441837-0.001727j
[2025-07-31 21:37:41] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -55.451229+0.000544j
[2025-07-31 21:38:01] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -55.395353-0.000888j
[2025-07-31 21:38:20] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -55.375736-0.000036j
[2025-07-31 21:38:39] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -55.427784-0.000421j
[2025-07-31 21:38:58] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -55.391864+0.000620j
[2025-07-31 21:39:17] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -55.385636+0.000831j
[2025-07-31 21:39:36] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -55.342694-0.000056j
[2025-07-31 21:39:55] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -55.398809-0.000477j
[2025-07-31 21:40:14] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -55.399742-0.000625j
[2025-07-31 21:40:33] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -55.439241-0.001189j
[2025-07-31 21:40:52] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -55.417001-0.000620j
[2025-07-31 21:41:11] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -55.367758+0.000579j
[2025-07-31 21:41:30] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -55.373539+0.000249j
[2025-07-31 21:41:49] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -55.445941-0.000650j
[2025-07-31 21:42:08] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -55.465622-0.000082j
[2025-07-31 21:42:27] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -55.463990-0.000088j
[2025-07-31 21:42:46] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -55.475908+0.001773j
[2025-07-31 21:43:05] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -55.450402-0.000736j
[2025-07-31 21:43:05] RESTART #2 | Period: 600
[2025-07-31 21:43:24] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -55.471288+0.000668j
[2025-07-31 21:43:43] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -55.477674-0.000609j
[2025-07-31 21:44:02] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -55.446917-0.000679j
[2025-07-31 21:44:21] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -55.452169-0.000886j
[2025-07-31 21:44:41] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -55.420709+0.000791j
[2025-07-31 21:45:00] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -55.452666-0.001710j
[2025-07-31 21:45:19] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -55.421865+0.000512j
[2025-07-31 21:45:38] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -55.355862+0.000230j
[2025-07-31 21:45:57] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -55.397699-0.000845j
[2025-07-31 21:46:16] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -55.427551-0.001018j
[2025-07-31 21:46:35] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -55.391507-0.001506j
[2025-07-31 21:46:54] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -55.436296-0.000051j
[2025-07-31 21:47:13] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -55.404784-0.000537j
[2025-07-31 21:47:32] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -55.430299+0.000893j
[2025-07-31 21:47:51] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -55.442294+0.000497j
[2025-07-31 21:48:10] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -55.476890-0.003058j
[2025-07-31 21:48:29] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -55.403759+0.000358j
[2025-07-31 21:48:48] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -55.413171+0.000954j
[2025-07-31 21:49:07] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -55.349970+0.000408j
[2025-07-31 21:49:26] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -55.350569-0.000304j
[2025-07-31 21:49:45] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -55.386953+0.000437j
[2025-07-31 21:50:04] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -55.408349+0.000927j
[2025-07-31 21:50:23] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -55.419560-0.000044j
[2025-07-31 21:50:42] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -55.334475+0.002260j
[2025-07-31 21:51:01] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -55.428067+0.000619j
[2025-07-31 21:51:20] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -55.441002+0.000897j
[2025-07-31 21:51:40] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -55.366807+0.000732j
[2025-07-31 21:51:59] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -55.404495+0.000216j
[2025-07-31 21:52:18] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -55.373939+0.003471j
[2025-07-31 21:52:37] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -55.404330-0.000347j
[2025-07-31 21:52:56] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -55.403351+0.000542j
[2025-07-31 21:53:15] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -55.425759-0.001326j
[2025-07-31 21:53:34] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -55.405851-0.000256j
[2025-07-31 21:53:53] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -55.460347-0.002661j
[2025-07-31 21:54:12] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -55.454483-0.000072j
[2025-07-31 21:54:31] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -55.470707-0.000500j
[2025-07-31 21:54:50] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -55.417451+0.000878j
[2025-07-31 21:55:09] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -55.442962+0.000884j
[2025-07-31 21:55:28] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -55.480954+0.000344j
[2025-07-31 21:55:47] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -55.495993+0.000090j
[2025-07-31 21:56:06] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -55.441652+0.000186j
[2025-07-31 21:56:25] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -55.387924-0.000450j
[2025-07-31 21:56:44] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -55.343357-0.000582j
[2025-07-31 21:57:03] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -55.429037+0.000688j
[2025-07-31 21:57:22] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -55.429643-0.001055j
[2025-07-31 21:57:42] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -55.415757+0.000856j
[2025-07-31 21:58:01] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -55.411932-0.001349j
[2025-07-31 21:58:20] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -55.453853+0.000613j
[2025-07-31 21:58:39] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -55.471696-0.001077j
[2025-07-31 21:58:58] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -55.479973-0.000315j
[2025-07-31 21:58:58] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-07-31 21:59:17] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -55.480746+0.001845j
[2025-07-31 21:59:36] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -55.504849+0.000151j
[2025-07-31 21:59:55] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -55.428105+0.001591j
[2025-07-31 22:00:14] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -55.417503+0.000556j
[2025-07-31 22:00:33] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -55.423284+0.001443j
[2025-07-31 22:00:52] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -55.449606-0.001040j
[2025-07-31 22:01:11] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -55.461684-0.000403j
[2025-07-31 22:01:30] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -55.438009+0.000405j
[2025-07-31 22:01:49] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -55.453887+0.001581j
[2025-07-31 22:02:08] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -55.437214+0.000400j
[2025-07-31 22:02:27] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -55.443782-0.002894j
[2025-07-31 22:02:46] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -55.474558-0.001780j
[2025-07-31 22:03:05] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -55.447744-0.000794j
[2025-07-31 22:03:24] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -55.435196+0.000348j
[2025-07-31 22:03:43] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -55.490991-0.000185j
[2025-07-31 22:04:02] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -55.469523+0.000918j
[2025-07-31 22:04:21] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -55.449372-0.001295j
[2025-07-31 22:04:41] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -55.457338+0.000449j
[2025-07-31 22:05:00] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -55.403203+0.000323j
[2025-07-31 22:05:19] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -55.363449-0.002663j
[2025-07-31 22:05:38] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -55.364717+0.002374j
[2025-07-31 22:05:57] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -55.394772-0.000871j
[2025-07-31 22:06:16] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -55.384946+0.000500j
[2025-07-31 22:06:35] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -55.412889+0.000257j
[2025-07-31 22:06:54] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -55.413908-0.000175j
[2025-07-31 22:07:13] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -55.443637+0.000755j
[2025-07-31 22:07:32] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -55.440358+0.000192j
[2025-07-31 22:07:51] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -55.458537-0.000917j
[2025-07-31 22:08:10] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -55.448243+0.000821j
[2025-07-31 22:08:29] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -55.455963-0.000451j
[2025-07-31 22:08:48] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -55.507746+0.000839j
[2025-07-31 22:09:07] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -55.445404-0.000223j
[2025-07-31 22:09:26] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -55.379745+0.000113j
[2025-07-31 22:09:45] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -55.338285+0.000739j
[2025-07-31 22:10:04] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -55.383020-0.000376j
[2025-07-31 22:10:24] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -55.421231+0.001003j
[2025-07-31 22:10:43] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -55.431528+0.001183j
[2025-07-31 22:11:02] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -55.498616+0.002124j
[2025-07-31 22:11:21] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -55.456040+0.000540j
[2025-07-31 22:11:40] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -55.405157-0.000050j
[2025-07-31 22:11:59] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -55.402272-0.000258j
[2025-07-31 22:12:18] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -55.391413-0.000461j
[2025-07-31 22:12:37] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -55.458917-0.000996j
[2025-07-31 22:12:56] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -55.491789+0.001242j
[2025-07-31 22:13:15] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -55.456408-0.000216j
[2025-07-31 22:13:34] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -55.411075-0.000203j
[2025-07-31 22:13:53] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -55.380311+0.001031j
[2025-07-31 22:14:12] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -55.414838-0.002376j
[2025-07-31 22:14:31] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -55.449196-0.000292j
[2025-07-31 22:14:50] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -55.463618+0.001143j
[2025-07-31 22:15:09] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -55.442043+0.001853j
[2025-07-31 22:15:28] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -55.397653+0.000344j
[2025-07-31 22:15:47] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -55.422737-0.003099j
[2025-07-31 22:16:06] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -55.423420-0.000544j
[2025-07-31 22:16:26] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -55.361850-0.001846j
[2025-07-31 22:16:45] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -55.369218-0.000018j
[2025-07-31 22:17:04] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -55.384148+0.000325j
[2025-07-31 22:17:23] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -55.384523+0.001057j
[2025-07-31 22:17:42] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -55.394664+0.001152j
[2025-07-31 22:18:01] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -55.443167-0.000324j
[2025-07-31 22:18:20] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -55.416102+0.000544j
[2025-07-31 22:18:39] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -55.401225-0.000125j
[2025-07-31 22:18:58] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -55.478314+0.000051j
[2025-07-31 22:19:17] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -55.441112-0.000240j
[2025-07-31 22:19:36] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -55.443075-0.000407j
[2025-07-31 22:19:55] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -55.446369+0.001303j
[2025-07-31 22:20:14] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -55.438155+0.000328j
[2025-07-31 22:20:33] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -55.468048+0.000408j
[2025-07-31 22:20:52] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -55.471108+0.000322j
[2025-07-31 22:21:11] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -55.541055-0.000886j
[2025-07-31 22:21:30] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -55.551114-0.001694j
[2025-07-31 22:21:49] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -55.465978-0.000366j
[2025-07-31 22:22:08] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -55.480506-0.000426j
[2025-07-31 22:22:28] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -55.390101+0.000020j
[2025-07-31 22:22:47] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -55.420444+0.001377j
[2025-07-31 22:23:06] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -55.444166+0.000528j
[2025-07-31 22:23:25] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -55.449242+0.000540j
[2025-07-31 22:23:44] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -55.432430-0.001562j
[2025-07-31 22:24:03] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -55.438731+0.001069j
[2025-07-31 22:24:22] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -55.460222-0.000936j
[2025-07-31 22:24:41] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -55.508988+0.000874j
[2025-07-31 22:25:00] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -55.452385-0.000625j
[2025-07-31 22:25:19] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -55.432391-0.000279j
[2025-07-31 22:25:38] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -55.443062+0.000751j
[2025-07-31 22:25:57] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -55.413974-0.001513j
[2025-07-31 22:26:16] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -55.500170+0.001602j
[2025-07-31 22:26:35] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -55.506985+0.000360j
[2025-07-31 22:26:54] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -55.470110+0.001917j
[2025-07-31 22:27:13] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -55.519400+0.000764j
[2025-07-31 22:27:32] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -55.490173+0.000281j
[2025-07-31 22:27:51] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -55.523701-0.000058j
[2025-07-31 22:28:10] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -55.520952+0.000601j
[2025-07-31 22:28:29] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -55.478833+0.000160j
[2025-07-31 22:28:48] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -55.460700+0.000552j
[2025-07-31 22:29:07] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -55.458996+0.000264j
[2025-07-31 22:29:27] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -55.448103+0.000033j
[2025-07-31 22:29:46] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -55.442070-0.001686j
[2025-07-31 22:30:05] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -55.459898+0.000277j
[2025-07-31 22:30:24] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -55.464786-0.000996j
[2025-07-31 22:30:43] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -55.458630-0.000969j
[2025-07-31 22:31:02] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -55.448139+0.002383j
[2025-07-31 22:31:21] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -55.455448+0.001634j
[2025-07-31 22:31:40] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -55.407810-0.000281j
[2025-07-31 22:31:59] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -55.429230+0.000182j
[2025-07-31 22:32:18] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -55.418179-0.000310j
[2025-07-31 22:32:37] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -55.424527-0.000802j
[2025-07-31 22:32:56] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -55.467362+0.001339j
[2025-07-31 22:33:15] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -55.464563+0.000865j
[2025-07-31 22:33:34] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -55.449233+0.000535j
[2025-07-31 22:33:53] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -55.463387-0.000671j
[2025-07-31 22:34:12] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -55.414691+0.000613j
[2025-07-31 22:34:31] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -55.436029+0.000962j
[2025-07-31 22:34:50] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -55.416563+0.000608j
[2025-07-31 22:35:09] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -55.376704+0.000441j
[2025-07-31 22:35:28] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -55.391133-0.000386j
[2025-07-31 22:35:47] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -55.401577-0.001664j
[2025-07-31 22:36:06] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -55.417375+0.000676j
[2025-07-31 22:36:25] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -55.402585-0.000601j
[2025-07-31 22:36:44] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -55.363758+0.000852j
[2025-07-31 22:37:03] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -55.379826-0.002194j
[2025-07-31 22:37:22] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -55.373219-0.000371j
[2025-07-31 22:37:41] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -55.340480-0.000758j
[2025-07-31 22:38:00] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -55.389230+0.001331j
[2025-07-31 22:38:20] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -55.430768-0.000305j
[2025-07-31 22:38:39] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -55.380291-0.000380j
[2025-07-31 22:38:58] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -55.403250-0.000992j
[2025-07-31 22:39:17] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -55.458895-0.001812j
[2025-07-31 22:39:36] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -55.463953+0.000704j
[2025-07-31 22:39:55] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -55.415795-0.000670j
[2025-07-31 22:40:14] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -55.461598-0.001191j
[2025-07-31 22:40:33] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -55.435168-0.000571j
[2025-07-31 22:40:52] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -55.459467-0.000546j
[2025-07-31 22:41:11] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -55.438746-0.000712j
[2025-07-31 22:41:30] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -55.478624-0.001178j
[2025-07-31 22:41:49] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -55.378981+0.000145j
[2025-07-31 22:42:08] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -55.422100+0.000170j
[2025-07-31 22:42:27] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -55.387841-0.000272j
[2025-07-31 22:42:46] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -55.440921-0.000947j
[2025-07-31 22:43:05] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -55.397648+0.000824j
[2025-07-31 22:43:24] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -55.395151+0.000281j
[2025-07-31 22:43:43] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -55.438551+0.001516j
[2025-07-31 22:44:02] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -55.406681+0.000048j
[2025-07-31 22:44:21] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -55.408239+0.000107j
[2025-07-31 22:44:40] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -55.445624+0.001656j
[2025-07-31 22:44:59] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -55.434837-0.000179j
[2025-07-31 22:45:19] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -55.443901+0.001992j
[2025-07-31 22:45:38] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -55.466316-0.000369j
[2025-07-31 22:45:57] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -55.502264+0.003989j
[2025-07-31 22:46:16] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -55.471831+0.001427j
[2025-07-31 22:46:35] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -55.477197+0.000037j
[2025-07-31 22:46:54] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -55.468811+0.000456j
[2025-07-31 22:47:13] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -55.471881+0.001920j
[2025-07-31 22:47:32] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -55.417860+0.002255j
[2025-07-31 22:47:51] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -55.421346+0.002345j
[2025-07-31 22:48:10] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -55.412623+0.000126j
[2025-07-31 22:48:29] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -55.453739+0.000013j
[2025-07-31 22:48:48] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -55.441459-0.000462j
[2025-07-31 22:49:07] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -55.446706-0.001605j
[2025-07-31 22:49:26] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -55.445290+0.000535j
[2025-07-31 22:49:45] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -55.457472-0.000654j
[2025-07-31 22:50:04] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -55.465698+0.000009j
[2025-07-31 22:50:23] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -55.459086+0.001054j
[2025-07-31 22:50:42] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -55.437950-0.001089j
[2025-07-31 22:51:01] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -55.438066+0.000489j
[2025-07-31 22:51:20] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -55.411228-0.000846j
[2025-07-31 22:51:39] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -55.420294-0.000802j
[2025-07-31 22:51:58] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -55.385168+0.000122j
[2025-07-31 22:52:17] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -55.385451-0.000344j
[2025-07-31 22:52:36] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -55.378291+0.000097j
[2025-07-31 22:52:56] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -55.369144+0.000498j
[2025-07-31 22:53:15] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -55.394138+0.001288j
[2025-07-31 22:53:34] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -55.360556-0.000122j
[2025-07-31 22:53:53] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -55.395870-0.000511j
[2025-07-31 22:54:12] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -55.439574+0.000101j
[2025-07-31 22:54:31] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -55.427029+0.000018j
[2025-07-31 22:54:50] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -55.439252-0.000787j
[2025-07-31 22:55:09] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -55.364081-0.000159j
[2025-07-31 22:55:28] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -55.401512+0.000132j
[2025-07-31 22:55:47] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -55.437638+0.000342j
[2025-07-31 22:56:06] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -55.457586+0.001030j
[2025-07-31 22:56:25] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -55.427298+0.000562j
[2025-07-31 22:56:44] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -55.441274-0.001551j
[2025-07-31 22:57:03] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -55.475598-0.001810j
[2025-07-31 22:57:22] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -55.447317-0.000516j
[2025-07-31 22:57:41] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -55.523787-0.000078j
[2025-07-31 22:58:00] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -55.510097+0.000984j
[2025-07-31 22:58:19] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -55.517357-0.000462j
[2025-07-31 22:58:38] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -55.514069+0.002908j
[2025-07-31 22:58:57] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -55.539339+0.002117j
[2025-07-31 22:59:16] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -55.546032+0.001142j
[2025-07-31 22:59:35] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -55.510274-0.000352j
[2025-07-31 22:59:55] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -55.493489+0.000465j
[2025-07-31 23:00:14] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -55.473185+0.000508j
[2025-07-31 23:00:33] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -55.494085+0.000795j
[2025-07-31 23:00:52] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -55.474676-0.001184j
[2025-07-31 23:01:11] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -55.492036-0.000792j
[2025-07-31 23:01:30] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -55.480822-0.000496j
[2025-07-31 23:01:49] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -55.519861-0.001156j
[2025-07-31 23:02:08] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -55.527189+0.001739j
[2025-07-31 23:02:27] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -55.504624+0.000966j
[2025-07-31 23:02:46] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -55.479073-0.000032j
[2025-07-31 23:03:05] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -55.487350+0.000182j
[2025-07-31 23:03:24] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -55.467869-0.000877j
[2025-07-31 23:03:43] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -55.464903-0.000688j
[2025-07-31 23:04:02] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -55.434285-0.001063j
[2025-07-31 23:04:21] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -55.470425-0.000587j
[2025-07-31 23:04:40] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -55.469544-0.001244j
[2025-07-31 23:04:59] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -55.427543-0.000738j
[2025-07-31 23:05:18] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -55.415035-0.000187j
[2025-07-31 23:05:37] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -55.414597-0.000472j
[2025-07-31 23:05:56] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -55.392751-0.000363j
[2025-07-31 23:06:15] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -55.382742-0.001689j
[2025-07-31 23:06:34] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -55.402406-0.000473j
[2025-07-31 23:06:53] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -55.386057+0.000079j
[2025-07-31 23:07:12] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -55.434950-0.001667j
[2025-07-31 23:07:31] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -55.434774+0.000764j
[2025-07-31 23:07:51] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -55.438868-0.000550j
[2025-07-31 23:08:10] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -55.405147+0.000068j
[2025-07-31 23:08:29] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -55.390371-0.000851j
[2025-07-31 23:08:48] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -55.375013-0.001478j
[2025-07-31 23:09:07] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -55.389356+0.000352j
[2025-07-31 23:09:26] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -55.382862+0.001745j
[2025-07-31 23:09:45] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -55.409722+0.001130j
[2025-07-31 23:10:04] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -55.407516-0.000076j
[2025-07-31 23:10:23] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -55.442071+0.001941j
[2025-07-31 23:10:42] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -55.435439+0.000452j
[2025-07-31 23:11:01] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -55.403564+0.000796j
[2025-07-31 23:11:20] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -55.369248+0.000574j
[2025-07-31 23:11:39] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -55.392559+0.000693j
[2025-07-31 23:11:58] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -55.386196-0.000580j
[2025-07-31 23:12:17] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -55.435289+0.001703j
[2025-07-31 23:12:36] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -55.393440-0.000400j
[2025-07-31 23:12:55] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -55.469861-0.000345j
[2025-07-31 23:13:14] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -55.437482+0.000985j
[2025-07-31 23:13:33] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -55.348511+0.000012j
[2025-07-31 23:13:52] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -55.412559+0.001427j
[2025-07-31 23:14:11] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -55.369089-0.000595j
[2025-07-31 23:14:30] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -55.441888+0.000016j
[2025-07-31 23:14:49] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -55.396841+0.000437j
[2025-07-31 23:15:08] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -55.413885-0.001156j
[2025-07-31 23:15:27] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -55.404750-0.000215j
[2025-07-31 23:15:47] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -55.389458+0.002424j
[2025-07-31 23:16:06] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -55.375894+0.001680j
[2025-07-31 23:16:25] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -55.393103+0.001026j
[2025-07-31 23:16:44] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -55.392169-0.001536j
[2025-07-31 23:17:03] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -55.419111+0.000136j
[2025-07-31 23:17:22] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -55.435304+0.001151j
[2025-07-31 23:17:41] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -55.427620-0.000384j
[2025-07-31 23:18:00] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -55.358389-0.000601j
[2025-07-31 23:18:19] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -55.381581+0.000497j
[2025-07-31 23:18:38] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -55.358262+0.000821j
[2025-07-31 23:18:57] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -55.422120+0.001751j
[2025-07-31 23:19:16] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -55.390690+0.000313j
[2025-07-31 23:19:35] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -55.383268-0.000538j
[2025-07-31 23:19:54] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -55.412364+0.000312j
[2025-07-31 23:20:13] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -55.422683-0.001973j
[2025-07-31 23:20:32] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -55.412783-0.000043j
[2025-07-31 23:20:51] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -55.509720-0.000875j
[2025-07-31 23:21:10] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -55.439006+0.001991j
[2025-07-31 23:21:29] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -55.430462-0.001625j
[2025-07-31 23:21:48] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -55.527254+0.000961j
[2025-07-31 23:22:07] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -55.499218+0.000090j
[2025-07-31 23:22:26] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -55.450005-0.001170j
[2025-07-31 23:22:45] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -55.435931+0.000310j
[2025-07-31 23:23:04] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -55.442759-0.002030j
[2025-07-31 23:23:23] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -55.484842-0.001608j
[2025-07-31 23:23:43] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -55.464277+0.001080j
[2025-07-31 23:24:02] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -55.408836+0.002325j
[2025-07-31 23:24:21] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -55.470357-0.001308j
[2025-07-31 23:24:40] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -55.472594-0.000341j
[2025-07-31 23:24:59] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -55.535273-0.000277j
[2025-07-31 23:25:18] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -55.481318+0.000919j
[2025-07-31 23:25:37] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -55.532648+0.000879j
[2025-07-31 23:25:56] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -55.504269-0.001027j
[2025-07-31 23:26:15] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -55.453343+0.000424j
[2025-07-31 23:26:34] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -55.451186-0.000726j
[2025-07-31 23:26:53] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -55.431511+0.000232j
[2025-07-31 23:27:12] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -55.448410-0.000136j
[2025-07-31 23:27:31] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -55.423483+0.001269j
[2025-07-31 23:27:50] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -55.478431+0.000639j
[2025-07-31 23:28:09] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -55.406075+0.001505j
[2025-07-31 23:28:28] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -55.442168-0.000070j
[2025-07-31 23:28:47] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -55.408118-0.001300j
[2025-07-31 23:29:06] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -55.370371-0.000552j
[2025-07-31 23:29:25] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -55.405127+0.000005j
[2025-07-31 23:29:44] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -55.382326+0.000439j
[2025-07-31 23:30:03] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -55.406239+0.001163j
[2025-07-31 23:30:22] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -55.430500-0.002337j
[2025-07-31 23:30:41] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -55.371709+0.000791j
[2025-07-31 23:31:00] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -55.377608-0.000509j
[2025-07-31 23:31:19] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -55.362300-0.001592j
[2025-07-31 23:31:38] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -55.422062-0.000707j
[2025-07-31 23:31:57] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -55.415342-0.001339j
[2025-07-31 23:32:17] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -55.434192+0.000249j
[2025-07-31 23:32:36] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -55.414017+0.000454j
[2025-07-31 23:32:55] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -55.396795+0.000266j
[2025-07-31 23:33:14] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -55.412539-0.000080j
[2025-07-31 23:33:33] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -55.353982-0.002443j
[2025-07-31 23:33:52] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -55.335065-0.000826j
[2025-07-31 23:34:11] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -55.397094-0.000568j
[2025-07-31 23:34:30] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -55.388689-0.000072j
[2025-07-31 23:34:49] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -55.340054+0.000704j
[2025-07-31 23:35:08] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -55.398926-0.000155j
[2025-07-31 23:35:27] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -55.384053-0.001224j
[2025-07-31 23:35:46] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -55.390722-0.001693j
[2025-07-31 23:36:05] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -55.383228-0.001606j
[2025-07-31 23:36:24] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -55.370659+0.000829j
[2025-07-31 23:36:43] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -55.395660+0.000586j
[2025-07-31 23:37:02] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -55.428535-0.000152j
[2025-07-31 23:37:21] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -55.461514-0.000383j
[2025-07-31 23:37:40] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -55.417908+0.000781j
[2025-07-31 23:37:59] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -55.413319-0.000789j
[2025-07-31 23:38:18] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -55.439806-0.000669j
[2025-07-31 23:38:37] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -55.407629+0.000546j
[2025-07-31 23:38:56] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -55.430507+0.000150j
[2025-07-31 23:39:15] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -55.413685+0.000956j
[2025-07-31 23:39:34] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -55.405778+0.000277j
[2025-07-31 23:39:53] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -55.433945+0.000387j
[2025-07-31 23:40:12] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -55.372174-0.000105j
[2025-07-31 23:40:31] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -55.373413+0.001426j
[2025-07-31 23:40:50] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -55.419955+0.000875j
[2025-07-31 23:41:09] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -55.467923+0.001073j
[2025-07-31 23:41:29] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -55.474718-0.000393j
[2025-07-31 23:41:48] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -55.390228+0.001175j
[2025-07-31 23:42:07] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -55.416696+0.001085j
[2025-07-31 23:42:26] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -55.393654-0.000603j
[2025-07-31 23:42:45] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -55.441653-0.001132j
[2025-07-31 23:43:04] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -55.347836+0.000720j
[2025-07-31 23:43:23] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -55.393735+0.000132j
[2025-07-31 23:43:42] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -55.392671+0.001852j
[2025-07-31 23:44:01] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -55.439379-0.000529j
[2025-07-31 23:44:20] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -55.394293-0.000196j
[2025-07-31 23:44:39] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -55.458202-0.000397j
[2025-07-31 23:44:58] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -55.471465-0.000460j
[2025-07-31 23:45:17] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -55.427070+0.000226j
[2025-07-31 23:45:36] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -55.516194-0.000964j
[2025-07-31 23:45:55] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -55.536500-0.000482j
[2025-07-31 23:46:14] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -55.468891+0.002494j
[2025-07-31 23:46:33] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -55.460110+0.000782j
[2025-07-31 23:46:52] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -55.427248+0.001451j
[2025-07-31 23:47:11] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -55.425411-0.000329j
[2025-07-31 23:47:30] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -55.449480-0.000821j
[2025-07-31 23:47:49] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -55.395124+0.001279j
[2025-07-31 23:48:08] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -55.434067+0.001099j
[2025-07-31 23:48:27] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -55.399621+0.001252j
[2025-07-31 23:48:46] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -55.378569+0.000341j
[2025-07-31 23:49:05] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -55.439258+0.000791j
[2025-07-31 23:49:24] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -55.429149-0.001287j
[2025-07-31 23:49:44] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -55.412164-0.001814j
[2025-07-31 23:50:03] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -55.344599-0.001709j
[2025-07-31 23:50:22] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -55.366198-0.002073j
[2025-07-31 23:50:41] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -55.404592+0.001555j
[2025-07-31 23:51:00] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -55.391484-0.000470j
[2025-07-31 23:51:19] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -55.332466-0.000395j
[2025-07-31 23:51:38] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -55.420602-0.000078j
[2025-07-31 23:51:57] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -55.470364-0.000202j
[2025-07-31 23:52:16] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -55.462636+0.002208j
[2025-07-31 23:52:35] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -55.399291+0.001191j
[2025-07-31 23:52:54] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -55.473134+0.002359j
[2025-07-31 23:53:13] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -55.377493+0.000893j
[2025-07-31 23:53:32] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -55.394182-0.000130j
[2025-07-31 23:53:51] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -55.395441-0.000911j
[2025-07-31 23:54:10] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -55.387609+0.000051j
[2025-07-31 23:54:29] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -55.374257+0.000142j
[2025-07-31 23:54:48] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -55.386507+0.000060j
[2025-07-31 23:55:07] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -55.395024-0.000269j
[2025-07-31 23:55:26] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -55.410266+0.000148j
[2025-07-31 23:55:45] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -55.470913-0.001568j
[2025-07-31 23:56:04] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -55.474298+0.000695j
[2025-07-31 23:56:23] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -55.447523+0.002373j
[2025-07-31 23:56:42] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -55.416080-0.000487j
[2025-07-31 23:57:01] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -55.429539+0.000971j
[2025-07-31 23:57:20] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -55.496258+0.000231j
[2025-07-31 23:57:39] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -55.452846-0.001150j
[2025-07-31 23:57:59] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -55.375032-0.001481j
[2025-07-31 23:58:18] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -55.443683-0.001103j
[2025-07-31 23:58:37] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -55.369424+0.000516j
[2025-07-31 23:58:56] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -55.354265+0.000082j
[2025-07-31 23:59:15] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -55.370530-0.000140j
[2025-07-31 23:59:34] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -55.380073+0.000635j
[2025-07-31 23:59:53] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -55.410846+0.001498j
[2025-08-01 00:00:12] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -55.481336-0.001951j
[2025-08-01 00:00:31] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -55.424426+0.000131j
[2025-08-01 00:00:50] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -55.470612-0.000457j
[2025-08-01 00:01:09] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -55.458348+0.000006j
[2025-08-01 00:01:28] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -55.434500-0.000802j
[2025-08-01 00:01:47] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -55.404567-0.001377j
[2025-08-01 00:02:06] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -55.369391+0.000313j
[2025-08-01 00:02:25] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -55.414963-0.001430j
[2025-08-01 00:02:44] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -55.371691+0.001176j
[2025-08-01 00:03:03] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -55.406123+0.000670j
[2025-08-01 00:03:22] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -55.382522+0.000799j
[2025-08-01 00:03:41] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -55.413147-0.000654j
[2025-08-01 00:04:00] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -55.450656+0.002147j
[2025-08-01 00:04:19] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -55.390012+0.000448j
[2025-08-01 00:04:38] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -55.415796-0.000310j
[2025-08-01 00:04:58] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -55.392057+0.001068j
[2025-08-01 00:05:17] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -55.378697-0.001458j
[2025-08-01 00:05:36] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -55.442378+0.000201j
[2025-08-01 00:05:55] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -55.396759+0.000828j
[2025-08-01 00:06:14] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -55.408520+0.001739j
[2025-08-01 00:06:33] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -55.425271+0.000503j
[2025-08-01 00:06:52] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -55.433328+0.002198j
[2025-08-01 00:07:11] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -55.414562+0.000218j
[2025-08-01 00:07:30] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -55.417737+0.000022j
[2025-08-01 00:07:49] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -55.374862+0.000330j
[2025-08-01 00:08:08] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -55.381145+0.001080j
[2025-08-01 00:08:27] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -55.469996-0.000759j
[2025-08-01 00:08:46] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -55.402667+0.000309j
[2025-08-01 00:09:05] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -55.406525+0.000927j
[2025-08-01 00:09:24] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -55.433615+0.000832j
[2025-08-01 00:09:43] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -55.460019-0.000575j
[2025-08-01 00:10:02] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -55.451019-0.001289j
[2025-08-01 00:10:21] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -55.465588-0.000992j
[2025-08-01 00:10:40] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -55.432894+0.002526j
[2025-08-01 00:10:59] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -55.453640-0.000369j
[2025-08-01 00:11:18] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -55.379625+0.002355j
[2025-08-01 00:11:37] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -55.448183-0.000485j
[2025-08-01 00:11:56] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -55.456131-0.000948j
[2025-08-01 00:12:15] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -55.441778+0.000531j
[2025-08-01 00:12:34] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -55.449484-0.001630j
[2025-08-01 00:12:54] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -55.422693-0.000731j
[2025-08-01 00:13:13] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -55.507400-0.002848j
[2025-08-01 00:13:32] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -55.416648+0.000196j
[2025-08-01 00:13:51] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -55.416700-0.001845j
[2025-08-01 00:14:10] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -55.424672-0.000359j
[2025-08-01 00:14:29] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -55.467939+0.000777j
[2025-08-01 00:14:48] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -55.509712+0.000643j
[2025-08-01 00:15:07] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -55.492607+0.001422j
[2025-08-01 00:15:26] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -55.522106+0.000917j
[2025-08-01 00:15:45] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -55.448685-0.001125j
[2025-08-01 00:16:04] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -55.460820+0.000968j
[2025-08-01 00:16:23] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -55.479905+0.000181j
[2025-08-01 00:16:42] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -55.452722+0.001058j
[2025-08-01 00:17:01] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -55.440001-0.001043j
[2025-08-01 00:17:20] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -55.484871-0.001178j
[2025-08-01 00:17:39] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -55.492556-0.000660j
[2025-08-01 00:17:58] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -55.506566+0.002307j
[2025-08-01 00:18:17] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -55.394686+0.001396j
[2025-08-01 00:18:36] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -55.393263-0.001272j
[2025-08-01 00:18:55] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -55.426648-0.001454j
[2025-08-01 00:19:14] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -55.380566-0.000815j
[2025-08-01 00:19:34] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -55.407829-0.002625j
[2025-08-01 00:19:53] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -55.425691-0.000069j
[2025-08-01 00:20:12] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -55.394389+0.000897j
[2025-08-01 00:20:31] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -55.450881+0.000249j
[2025-08-01 00:20:50] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -55.452681+0.000507j
[2025-08-01 00:21:09] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -55.410331+0.001049j
[2025-08-01 00:21:28] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -55.452251-0.001109j
[2025-08-01 00:21:47] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -55.392463-0.001435j
[2025-08-01 00:22:06] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -55.446258-0.000308j
[2025-08-01 00:22:25] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -55.431344-0.000917j
[2025-08-01 00:22:44] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -55.400069-0.001660j
[2025-08-01 00:23:03] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -55.410586-0.001463j
[2025-08-01 00:23:22] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -55.418485+0.001179j
[2025-08-01 00:23:41] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -55.404672-0.000763j
[2025-08-01 00:24:00] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -55.427533+0.000835j
[2025-08-01 00:24:19] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -55.431319-0.002195j
[2025-08-01 00:24:38] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -55.469759-0.000217j
[2025-08-01 00:24:57] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -55.470009-0.000650j
[2025-08-01 00:25:16] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -55.411474+0.001551j
[2025-08-01 00:25:36] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -55.333262+0.000080j
[2025-08-01 00:25:55] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -55.345753-0.000205j
[2025-08-01 00:26:14] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -55.352325+0.000538j
[2025-08-01 00:26:33] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -55.399379-0.002596j
[2025-08-01 00:26:52] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -55.431244-0.001088j
[2025-08-01 00:27:11] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -55.420957+0.000541j
[2025-08-01 00:27:30] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -55.426476-0.001074j
[2025-08-01 00:27:49] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -55.439803-0.001271j
[2025-08-01 00:28:08] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -55.451893-0.000791j
[2025-08-01 00:28:27] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -55.457495+0.001048j
[2025-08-01 00:28:46] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -55.452937-0.000096j
[2025-08-01 00:29:05] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -55.388155+0.000829j
[2025-08-01 00:29:24] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -55.385953-0.000932j
[2025-08-01 00:29:43] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -55.382784-0.000542j
[2025-08-01 00:30:02] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -55.387840-0.000294j
[2025-08-01 00:30:21] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -55.390359-0.000390j
[2025-08-01 00:30:40] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -55.443949+0.000105j
[2025-08-01 00:30:59] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -55.365303+0.002005j
[2025-08-01 00:31:18] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -55.392837+0.001815j
[2025-08-01 00:31:37] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -55.408537+0.000644j
[2025-08-01 00:31:57] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -55.442027-0.000787j
[2025-08-01 00:32:16] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -55.463234+0.000879j
[2025-08-01 00:32:35] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -55.436305+0.000830j
[2025-08-01 00:32:54] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -55.448313-0.000275j
[2025-08-01 00:33:13] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -55.494485+0.000285j
[2025-08-01 00:33:32] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -55.482433-0.000712j
[2025-08-01 00:33:51] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -55.448924-0.000442j
[2025-08-01 00:34:10] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -55.419088+0.000423j
[2025-08-01 00:34:29] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -55.343362-0.002693j
[2025-08-01 00:34:48] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -55.321039-0.000081j
[2025-08-01 00:35:07] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -55.384512+0.000903j
[2025-08-01 00:35:26] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -55.394202-0.001000j
[2025-08-01 00:35:45] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -55.409584+0.001293j
[2025-08-01 00:36:04] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -55.381955-0.000890j
[2025-08-01 00:36:23] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -55.443771-0.001130j
[2025-08-01 00:36:42] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -55.432501-0.000678j
[2025-08-01 00:37:01] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -55.467681-0.000281j
[2025-08-01 00:37:20] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -55.442894+0.001299j
[2025-08-01 00:37:39] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -55.472494+0.001933j
[2025-08-01 00:37:39] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-08-01 00:37:58] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -55.434016-0.000283j
[2025-08-01 00:38:18] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -55.444077-0.000025j
[2025-08-01 00:38:37] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -55.469510-0.000496j
[2025-08-01 00:38:56] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -55.428389+0.001332j
[2025-08-01 00:39:15] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -55.441613+0.000017j
[2025-08-01 00:39:34] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -55.405660+0.001579j
[2025-08-01 00:39:53] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -55.347336-0.000573j
[2025-08-01 00:40:12] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -55.351794-0.001135j
[2025-08-01 00:40:31] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -55.404452-0.000152j
[2025-08-01 00:40:50] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -55.354060-0.001001j
[2025-08-01 00:41:09] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -55.401771+0.000243j
[2025-08-01 00:41:28] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -55.373691-0.000348j
[2025-08-01 00:41:47] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -55.392253+0.000471j
[2025-08-01 00:42:06] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -55.429428-0.000992j
[2025-08-01 00:42:25] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -55.417218-0.000078j
[2025-08-01 00:42:44] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -55.386532-0.000307j
[2025-08-01 00:43:03] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -55.367223+0.000297j
[2025-08-01 00:43:22] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -55.379877+0.001296j
[2025-08-01 00:43:41] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -55.434669+0.000632j
[2025-08-01 00:44:01] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -55.396593+0.000636j
[2025-08-01 00:44:20] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -55.401483-0.000714j
[2025-08-01 00:44:39] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -55.423427-0.000682j
[2025-08-01 00:44:58] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -55.393698+0.000194j
[2025-08-01 00:45:17] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -55.373671+0.001795j
[2025-08-01 00:45:36] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -55.408192-0.000273j
[2025-08-01 00:45:55] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -55.427581+0.001375j
[2025-08-01 00:46:14] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -55.440950+0.000397j
[2025-08-01 00:46:33] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -55.465331-0.002082j
[2025-08-01 00:46:52] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -55.418831-0.000419j
[2025-08-01 00:47:11] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -55.402824+0.000334j
[2025-08-01 00:47:30] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -55.452513-0.000323j
[2025-08-01 00:47:49] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -55.423978+0.001766j
[2025-08-01 00:48:08] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -55.372160+0.001300j
[2025-08-01 00:48:27] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -55.355777-0.000921j
[2025-08-01 00:48:46] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -55.365967-0.000509j
[2025-08-01 00:49:05] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -55.397819+0.001429j
[2025-08-01 00:49:24] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -55.413378+0.001161j
[2025-08-01 00:49:43] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -55.424257+0.002019j
[2025-08-01 00:50:02] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -55.423449+0.001262j
[2025-08-01 00:50:22] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -55.390932+0.000356j
[2025-08-01 00:50:41] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -55.415004-0.000867j
[2025-08-01 00:51:00] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -55.450066-0.000648j
[2025-08-01 00:51:19] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -55.412224-0.001430j
[2025-08-01 00:51:38] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -55.438052+0.000489j
[2025-08-01 00:51:57] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -55.476352+0.000009j
[2025-08-01 00:52:16] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -55.479944-0.000369j
[2025-08-01 00:52:35] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -55.406807+0.000885j
[2025-08-01 00:52:54] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -55.426990+0.000597j
[2025-08-01 00:53:13] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -55.351874-0.000179j
[2025-08-01 00:53:32] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -55.390765+0.000718j
[2025-08-01 00:53:32] ✅ Training completed | Restarts: 2
[2025-08-01 00:53:32] ============================================================
[2025-08-01 00:53:32] Training completed | Runtime: 20059.3s
[2025-08-01 00:53:56] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-08-01 00:53:56] ============================================================
