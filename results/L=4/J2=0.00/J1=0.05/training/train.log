[2025-07-31 15:40:48] ✓ 从checkpoint恢复: results/L=4/J2=0.00/J1=0.04/training/checkpoints/final_GCNN.pkl
[2025-07-31 15:40:48]   - 迭代次数: final
[2025-07-31 15:40:48]   - 能量: -54.663975-0.000269j ± 0.043090
[2025-07-31 15:40:48]   - 时间戳: 2025-07-30T17:44:56.179657
[2025-07-31 15:40:56] ✓ 变分状态参数已从checkpoint恢复
[2025-07-31 15:40:56] ✓ 从final状态恢复, 重置迭代计数为0
[2025-07-31 15:40:56] ==================================================
[2025-07-31 15:40:56] GCNN for Shastry-Sutherland Model
[2025-07-31 15:40:56] ==================================================
[2025-07-31 15:40:56] System parameters:
[2025-07-31 15:40:56]   - System size: L=4, N=64
[2025-07-31 15:40:56]   - System parameters: J1=0.05, J2=0.0, Q=1.0
[2025-07-31 15:40:56] --------------------------------------------------
[2025-07-31 15:40:56] Model parameters:
[2025-07-31 15:40:56]   - Number of layers = 4
[2025-07-31 15:40:56]   - Number of features = 4
[2025-07-31 15:40:56]   - Total parameters = 12572
[2025-07-31 15:40:56] --------------------------------------------------
[2025-07-31 15:40:56] Training parameters:
[2025-07-31 15:40:56]   - Learning rate: 0.015
[2025-07-31 15:40:56]   - Total iterations: 1050
[2025-07-31 15:40:56]   - Annealing cycles: 3
[2025-07-31 15:40:56]   - Initial period: 150
[2025-07-31 15:40:56]   - Period multiplier: 2.0
[2025-07-31 15:40:56]   - Temperature range: 0.0-1.0
[2025-07-31 15:40:56]   - Samples: 16384
[2025-07-31 15:40:56]   - Discarded samples: 0
[2025-07-31 15:40:56]   - Chunk size: 2048
[2025-07-31 15:40:56]   - Diagonal shift: 0.2
[2025-07-31 15:40:56]   - Gradient clipping: 1.0
[2025-07-31 15:40:56]   - Checkpoint enabled: interval=500
[2025-07-31 15:40:56]   - Checkpoint directory: results/L=4/J2=0.00/J1=0.05/training/checkpoints
[2025-07-31 15:40:56] --------------------------------------------------
[2025-07-31 15:40:56] Device status:
[2025-07-31 15:40:56]   - Devices model: A100
[2025-07-31 15:40:56]   - Number of devices: 2
[2025-07-31 15:40:56]   - Sharding: True
[2025-07-31 15:40:56] ============================================================
[2025-07-31 15:41:34] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -55.011120+0.004284j
[2025-07-31 15:42:01] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -55.091382-0.004555j
[2025-07-31 15:42:11] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -55.054573-0.008875j
[2025-07-31 15:42:21] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -55.092047-0.005831j
[2025-07-31 15:42:31] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -55.007420-0.002496j
[2025-07-31 15:42:41] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -55.016586-0.001848j
[2025-07-31 15:42:51] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -54.992156-0.003040j
[2025-07-31 15:43:01] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -55.085307-0.001091j
[2025-07-31 15:43:12] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -55.003820-0.002850j
[2025-07-31 15:43:22] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -55.023694-0.001621j
[2025-07-31 15:43:32] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -55.027603-0.001041j
[2025-07-31 15:43:42] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -55.031967-0.002036j
[2025-07-31 15:43:52] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -55.021575-0.001489j
[2025-07-31 15:44:02] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -55.044664-0.002346j
[2025-07-31 15:44:12] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -55.034331-0.000730j
[2025-07-31 15:44:22] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -55.026400-0.000746j
[2025-07-31 15:44:32] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -54.985102-0.000572j
[2025-07-31 15:44:42] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -54.982597+0.000570j
[2025-07-31 15:44:52] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -55.083834+0.001041j
[2025-07-31 15:45:03] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -55.018668+0.001691j
[2025-07-31 15:45:13] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -55.027918-0.000556j
[2025-07-31 15:45:23] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -55.029139-0.000128j
[2025-07-31 15:45:33] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -54.982217+0.000328j
[2025-07-31 15:45:43] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -55.047807+0.000773j
[2025-07-31 15:45:53] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -55.042498-0.001225j
[2025-07-31 15:46:03] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -55.019574+0.000627j
[2025-07-31 15:46:13] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -55.119276+0.000378j
[2025-07-31 15:46:23] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -55.082616+0.000263j
[2025-07-31 15:46:33] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -55.073874+0.000181j
[2025-07-31 15:46:44] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -55.077982-0.001459j
[2025-07-31 15:46:54] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -55.046245-0.003550j
[2025-07-31 15:47:04] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -55.078368+0.002022j
[2025-07-31 15:47:14] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -55.072993+0.000584j
[2025-07-31 15:47:24] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -55.027704-0.002064j
[2025-07-31 15:47:34] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -55.041924+0.000280j
[2025-07-31 15:47:44] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -55.049895+0.001144j
[2025-07-31 15:47:54] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -55.029118+0.000004j
[2025-07-31 15:48:04] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -54.946078+0.001690j
[2025-07-31 15:48:14] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -54.981473+0.001768j
[2025-07-31 15:48:25] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -54.970498-0.001387j
[2025-07-31 15:48:35] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -55.024922+0.000086j
[2025-07-31 15:48:45] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -55.008681-0.000748j
[2025-07-31 15:48:55] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -55.018338-0.000390j
[2025-07-31 15:49:05] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -54.988720+0.002362j
[2025-07-31 15:49:15] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -55.004993+0.000628j
[2025-07-31 15:49:25] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -54.953746+0.000133j
[2025-07-31 15:49:35] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -54.973542-0.001001j
[2025-07-31 15:49:45] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -55.006247+0.000058j
[2025-07-31 15:49:55] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -54.926995+0.002871j
[2025-07-31 15:50:05] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -54.917203+0.000987j
[2025-07-31 15:50:16] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -54.966547+0.002205j
[2025-07-31 15:50:26] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -54.929948+0.000969j
[2025-07-31 15:50:36] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -55.035796+0.000500j
[2025-07-31 15:50:46] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -55.029386-0.000545j
[2025-07-31 15:50:56] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -55.031716+0.001426j
[2025-07-31 15:51:06] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -55.011981+0.001642j
[2025-07-31 15:51:16] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -55.028226-0.001469j
[2025-07-31 15:51:26] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -55.028231-0.000422j
[2025-07-31 15:51:36] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -55.022346+0.001192j
[2025-07-31 15:51:46] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -55.026413-0.000453j
[2025-07-31 15:51:56] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -55.084850+0.001253j
[2025-07-31 15:52:06] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -55.042404-0.000402j
[2025-07-31 15:52:17] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -55.039073-0.001692j
[2025-07-31 15:52:27] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -55.008125-0.000071j
[2025-07-31 15:52:37] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -55.009379+0.000476j
[2025-07-31 15:52:47] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -54.981643-0.000237j
[2025-07-31 15:52:57] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -55.042084-0.000882j
[2025-07-31 15:53:07] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -55.091983+0.000523j
[2025-07-31 15:53:17] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -55.092810-0.000873j
[2025-07-31 15:53:27] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -55.071497-0.001046j
[2025-07-31 15:53:37] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -55.082846+0.000139j
[2025-07-31 15:53:47] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -55.034769-0.001526j
[2025-07-31 15:53:57] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -55.093766-0.000109j
[2025-07-31 15:54:08] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -55.105672+0.000932j
[2025-07-31 15:54:18] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -55.091901+0.001548j
[2025-07-31 15:54:28] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -55.098001+0.001479j
[2025-07-31 15:54:38] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -55.082026+0.000399j
[2025-07-31 15:54:48] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -55.072337+0.002629j
[2025-07-31 15:54:58] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -55.116918+0.000364j
[2025-07-31 15:55:08] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -55.126758-0.002298j
[2025-07-31 15:55:18] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -55.124992-0.000521j
[2025-07-31 15:55:28] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -55.041502-0.002013j
[2025-07-31 15:55:38] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -55.062532-0.000362j
[2025-07-31 15:55:49] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -55.029442+0.002304j
[2025-07-31 15:55:59] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -55.075276-0.001133j
[2025-07-31 15:56:09] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -55.092821-0.001583j
[2025-07-31 15:56:19] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -55.068527-0.000542j
[2025-07-31 15:56:29] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -55.114321+0.001621j
[2025-07-31 15:56:39] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -55.056360+0.001230j
[2025-07-31 15:56:49] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -55.082915+0.001465j
[2025-07-31 15:56:59] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -55.052604-0.001640j
[2025-07-31 15:57:09] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -55.017301-0.000027j
[2025-07-31 15:57:19] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -55.029785-0.000237j
[2025-07-31 15:57:30] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -55.021554-0.000227j
[2025-07-31 15:57:40] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -54.995867-0.000753j
[2025-07-31 15:57:50] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -54.982223+0.000416j
[2025-07-31 15:58:00] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -54.964680-0.000150j
[2025-07-31 15:58:10] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -55.026158+0.001140j
[2025-07-31 15:58:20] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -55.001497-0.001500j
[2025-07-31 15:58:30] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -54.992882+0.001004j
[2025-07-31 15:58:40] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -55.006799-0.000144j
[2025-07-31 15:58:50] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -55.037575+0.000678j
[2025-07-31 15:59:00] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -55.035376-0.001288j
[2025-07-31 15:59:10] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -55.021239+0.001524j
[2025-07-31 15:59:21] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -55.051038+0.000394j
[2025-07-31 15:59:31] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -55.042698+0.000143j
[2025-07-31 15:59:41] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -55.054722-0.000397j
[2025-07-31 15:59:51] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -55.050322+0.000857j
[2025-07-31 16:00:01] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -55.009536+0.001545j
[2025-07-31 16:00:11] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -55.001553+0.002107j
[2025-07-31 16:00:21] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -55.041216+0.001156j
[2025-07-31 16:00:31] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -55.035841-0.000119j
[2025-07-31 16:00:41] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -55.074704-0.001629j
[2025-07-31 16:00:51] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -55.080409+0.000985j
[2025-07-31 16:01:01] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -55.104472+0.001122j
[2025-07-31 16:01:12] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -55.111108-0.001376j
[2025-07-31 16:01:22] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -55.025817+0.001632j
[2025-07-31 16:01:32] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -55.081996+0.001043j
[2025-07-31 16:01:42] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -55.050503+0.000972j
[2025-07-31 16:01:52] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -55.030942+0.000307j
[2025-07-31 16:02:02] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -55.037632+0.002646j
[2025-07-31 16:02:12] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -55.041576-0.000667j
[2025-07-31 16:02:22] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -55.037916+0.001506j
[2025-07-31 16:02:32] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -55.008692+0.002498j
[2025-07-31 16:02:42] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -55.004861-0.000172j
[2025-07-31 16:02:52] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -55.070895-0.002091j
[2025-07-31 16:03:03] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -55.038539-0.003379j
[2025-07-31 16:03:13] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -55.004616+0.001048j
[2025-07-31 16:03:23] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -55.007532+0.000287j
[2025-07-31 16:03:33] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -55.035866-0.001332j
[2025-07-31 16:03:43] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -54.989200-0.000387j
[2025-07-31 16:03:53] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -54.992020-0.001149j
[2025-07-31 16:04:03] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -54.996675+0.000856j
[2025-07-31 16:04:13] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -55.044983+0.001957j
[2025-07-31 16:04:23] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -55.017842+0.000206j
[2025-07-31 16:04:33] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -55.006487+0.001665j
[2025-07-31 16:04:44] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -55.015780+0.001648j
[2025-07-31 16:04:54] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -55.053297+0.000307j
[2025-07-31 16:05:04] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -55.019217+0.000761j
[2025-07-31 16:05:14] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -55.030984-0.000717j
[2025-07-31 16:05:24] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -55.061376+0.002209j
[2025-07-31 16:05:34] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -55.081578+0.001383j
[2025-07-31 16:05:44] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -55.045472+0.000607j
[2025-07-31 16:05:54] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -55.044969-0.000965j
[2025-07-31 16:06:04] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -55.045503+0.000853j
[2025-07-31 16:06:14] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -55.076404-0.000835j
[2025-07-31 16:06:24] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -55.082969+0.000077j
[2025-07-31 16:06:35] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -55.073056+0.000618j
[2025-07-31 16:06:45] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -55.057566-0.000201j
[2025-07-31 16:06:55] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -55.014914+0.000618j
[2025-07-31 16:06:55] RESTART #1 | Period: 300
[2025-07-31 16:07:05] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -55.011733+0.002265j
[2025-07-31 16:07:15] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -54.993131+0.000534j
[2025-07-31 16:07:25] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -55.001468-0.000843j
[2025-07-31 16:07:35] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -55.052521+0.001129j
[2025-07-31 16:07:45] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -55.084433-0.001483j
[2025-07-31 16:07:55] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -55.122046+0.002141j
[2025-07-31 16:08:05] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -55.055346-0.000308j
[2025-07-31 16:08:16] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -55.049565-0.000158j
[2025-07-31 16:08:26] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -55.022082-0.000355j
[2025-07-31 16:08:36] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -54.997574+0.001408j
[2025-07-31 16:08:46] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -55.047925-0.001742j
[2025-07-31 16:08:56] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -54.927254-0.000980j
[2025-07-31 16:09:06] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -54.965725-0.000422j
[2025-07-31 16:09:16] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -54.956311+0.001854j
[2025-07-31 16:09:26] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -54.959281+0.000894j
[2025-07-31 16:09:36] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -54.964986-0.001262j
[2025-07-31 16:09:46] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -54.971383-0.001101j
[2025-07-31 16:09:56] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -55.005172-0.000670j
[2025-07-31 16:10:07] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -54.997791+0.002090j
[2025-07-31 16:10:17] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -54.920483+0.000455j
[2025-07-31 16:10:27] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -54.979349-0.000523j
[2025-07-31 16:10:37] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -55.010833+0.001236j
[2025-07-31 16:10:47] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -55.064780+0.000255j
[2025-07-31 16:10:57] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -55.035791-0.001011j
[2025-07-31 16:11:07] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -55.041534-0.001231j
[2025-07-31 16:11:17] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -55.066146-0.000347j
[2025-07-31 16:11:27] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -55.050692-0.000512j
[2025-07-31 16:11:37] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -55.075190-0.000104j
[2025-07-31 16:11:48] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -55.052061-0.001545j
[2025-07-31 16:11:58] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -55.024022+0.000112j
[2025-07-31 16:12:08] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -55.013842+0.003082j
[2025-07-31 16:12:18] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -55.057344-0.000737j
[2025-07-31 16:12:28] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -55.018793+0.000209j
[2025-07-31 16:12:38] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -55.020939-0.001121j
[2025-07-31 16:12:48] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -55.044486+0.002988j
[2025-07-31 16:12:58] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -55.050627+0.002309j
[2025-07-31 16:13:08] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -55.033048+0.000227j
[2025-07-31 16:13:18] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -55.052653-0.000000j
[2025-07-31 16:13:28] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -55.064183-0.000944j
[2025-07-31 16:13:39] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -55.038067-0.000152j
[2025-07-31 16:13:49] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -55.032814+0.001211j
[2025-07-31 16:13:59] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -55.003012+0.003142j
[2025-07-31 16:14:09] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -54.923882+0.001463j
[2025-07-31 16:14:19] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -55.000500+0.003198j
[2025-07-31 16:14:29] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -55.008611+0.000072j
[2025-07-31 16:14:39] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -55.005882-0.001047j
[2025-07-31 16:14:49] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -55.007125+0.000362j
[2025-07-31 16:14:59] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -54.972906-0.000600j
[2025-07-31 16:15:09] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -55.017765-0.000677j
[2025-07-31 16:15:19] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -55.031994+0.000215j
[2025-07-31 16:15:30] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -55.002555+0.001312j
[2025-07-31 16:15:40] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -54.989382-0.000830j
[2025-07-31 16:15:50] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -54.976242-0.002307j
[2025-07-31 16:16:00] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -54.991164-0.001107j
[2025-07-31 16:16:10] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -55.056998+0.001132j
[2025-07-31 16:16:20] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -55.046491-0.000560j
[2025-07-31 16:16:30] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -55.019821+0.000162j
[2025-07-31 16:16:40] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -55.009933-0.000916j
[2025-07-31 16:16:50] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -55.005286+0.000320j
[2025-07-31 16:17:01] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -55.048984+0.001103j
[2025-07-31 16:17:11] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -55.005091-0.000064j
[2025-07-31 16:17:21] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -55.040786+0.001129j
[2025-07-31 16:17:31] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -55.042127-0.000082j
[2025-07-31 16:17:41] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -55.013515+0.000362j
[2025-07-31 16:17:51] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -55.031273-0.001204j
[2025-07-31 16:18:01] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -55.042333+0.001647j
[2025-07-31 16:18:11] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -55.026937+0.000409j
[2025-07-31 16:18:21] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -55.054473-0.001495j
[2025-07-31 16:18:31] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -55.085509-0.001793j
[2025-07-31 16:18:41] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -55.008969+0.000834j
[2025-07-31 16:18:51] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -55.066343+0.000319j
[2025-07-31 16:19:02] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -55.016055-0.000628j
[2025-07-31 16:19:12] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -55.049121+0.002015j
[2025-07-31 16:19:22] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -55.036815+0.000474j
[2025-07-31 16:19:32] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -55.065345+0.000160j
[2025-07-31 16:19:42] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -55.069775+0.000544j
[2025-07-31 16:19:52] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -55.059536-0.001029j
[2025-07-31 16:20:02] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -55.084901-0.000631j
[2025-07-31 16:20:12] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -55.068696-0.000394j
[2025-07-31 16:20:22] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -55.086839-0.001126j
[2025-07-31 16:20:32] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -55.040005+0.001371j
[2025-07-31 16:20:43] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -54.996333+0.001430j
[2025-07-31 16:20:53] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -55.009349+0.000729j
[2025-07-31 16:21:03] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -54.961273+0.001850j
[2025-07-31 16:21:13] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -54.973578-0.003290j
[2025-07-31 16:21:23] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -55.042476-0.000824j
[2025-07-31 16:21:33] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -55.021305+0.002080j
[2025-07-31 16:21:43] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -54.997361+0.004260j
[2025-07-31 16:21:53] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -55.002879-0.000779j
[2025-07-31 16:22:03] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -54.969166-0.002610j
[2025-07-31 16:22:13] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -54.954869-0.000485j
[2025-07-31 16:22:23] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -54.965413+0.001914j
[2025-07-31 16:22:34] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -54.934226-0.001602j
[2025-07-31 16:22:44] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -54.992605-0.000843j
[2025-07-31 16:22:54] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -54.947910-0.001375j
[2025-07-31 16:23:04] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -54.957502+0.001223j
[2025-07-31 16:23:14] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -54.939413+0.001764j
[2025-07-31 16:23:24] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -54.989680+0.002015j
[2025-07-31 16:23:34] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -55.039736+0.000133j
[2025-07-31 16:23:44] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -55.078960-0.002281j
[2025-07-31 16:23:54] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -55.038277-0.000980j
[2025-07-31 16:24:05] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -54.987926+0.000076j
[2025-07-31 16:24:15] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -55.014947-0.001396j
[2025-07-31 16:24:25] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -55.030589-0.001676j
[2025-07-31 16:24:35] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -55.070212+0.000554j
[2025-07-31 16:24:45] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -55.039190+0.000285j
[2025-07-31 16:24:55] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -55.069275+0.000545j
[2025-07-31 16:25:05] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -55.082780-0.000065j
[2025-07-31 16:25:15] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -55.049107-0.000412j
[2025-07-31 16:25:25] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -55.097457-0.001485j
[2025-07-31 16:25:35] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -55.074357-0.000786j
[2025-07-31 16:25:45] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -55.078697-0.000696j
[2025-07-31 16:25:56] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -54.973328-0.000253j
[2025-07-31 16:26:06] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -54.910169+0.001037j
[2025-07-31 16:26:16] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -54.941963+0.001090j
[2025-07-31 16:26:26] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -54.973354-0.000913j
[2025-07-31 16:26:36] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -55.048663+0.001261j
[2025-07-31 16:26:46] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -55.017906+0.001186j
[2025-07-31 16:26:56] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -55.008040-0.001135j
[2025-07-31 16:27:06] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -55.051482+0.002612j
[2025-07-31 16:27:16] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -55.084738+0.000691j
[2025-07-31 16:27:26] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -55.073656-0.001662j
[2025-07-31 16:27:36] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -55.048704+0.001843j
[2025-07-31 16:27:47] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -55.064118-0.000645j
[2025-07-31 16:27:57] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -55.032034+0.001424j
[2025-07-31 16:28:07] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -55.030915-0.001740j
[2025-07-31 16:28:17] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -55.049648+0.002059j
[2025-07-31 16:28:27] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -55.017602+0.001085j
[2025-07-31 16:28:37] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -54.970880-0.000211j
[2025-07-31 16:28:47] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -55.002182-0.001302j
[2025-07-31 16:28:57] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -54.984010+0.000188j
[2025-07-31 16:29:07] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -55.009291+0.000801j
[2025-07-31 16:29:17] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -55.040930-0.000956j
[2025-07-31 16:29:28] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -55.052164+0.001062j
[2025-07-31 16:29:38] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -55.039545+0.000151j
[2025-07-31 16:29:48] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -55.034995+0.000377j
[2025-07-31 16:29:58] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -55.067039+0.001999j
[2025-07-31 16:30:08] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -55.031480+0.000623j
[2025-07-31 16:30:18] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -55.012258+0.000839j
[2025-07-31 16:30:28] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -55.018275-0.000791j
[2025-07-31 16:30:38] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -55.013999+0.001220j
[2025-07-31 16:30:48] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -55.068935+0.001391j
[2025-07-31 16:30:58] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -55.012498+0.002220j
[2025-07-31 16:31:08] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -54.973230-0.001454j
[2025-07-31 16:31:19] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -55.041054-0.000841j
[2025-07-31 16:31:29] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -54.972758-0.000017j
[2025-07-31 16:31:39] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -55.023565+0.001687j
[2025-07-31 16:31:49] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -54.992238-0.001573j
[2025-07-31 16:31:59] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -55.000762-0.000820j
[2025-07-31 16:32:09] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -55.033311-0.001061j
[2025-07-31 16:32:19] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -55.045585-0.000353j
[2025-07-31 16:32:29] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -55.020144+0.001915j
[2025-07-31 16:32:39] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -55.058571+0.000467j
[2025-07-31 16:32:49] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -55.057703+0.000597j
[2025-07-31 16:33:00] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -55.008327+0.002006j
[2025-07-31 16:33:10] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -55.018781+0.001050j
[2025-07-31 16:33:20] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -54.968427+0.000337j
[2025-07-31 16:33:30] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -55.044847-0.001787j
[2025-07-31 16:33:40] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -55.054567-0.001336j
[2025-07-31 16:33:50] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -55.058522-0.000416j
[2025-07-31 16:34:00] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -55.001780+0.000205j
[2025-07-31 16:34:10] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -55.015070-0.000990j
[2025-07-31 16:34:20] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -55.033091-0.001654j
[2025-07-31 16:34:31] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -55.047967+0.001717j
[2025-07-31 16:34:41] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -55.048656+0.000410j
[2025-07-31 16:34:51] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -55.049612-0.000932j
[2025-07-31 16:35:01] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -54.997956-0.000229j
[2025-07-31 16:35:11] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -55.036464+0.001055j
[2025-07-31 16:35:21] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -55.004015-0.001909j
[2025-07-31 16:35:31] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -54.998426-0.002048j
[2025-07-31 16:35:41] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -54.973806-0.000825j
[2025-07-31 16:35:51] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -55.034111+0.000368j
[2025-07-31 16:36:01] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -55.004653+0.000965j
[2025-07-31 16:36:12] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -55.054072-0.001159j
[2025-07-31 16:36:22] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -55.077361-0.000202j
[2025-07-31 16:36:32] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -55.049274-0.000623j
[2025-07-31 16:36:42] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -55.027699+0.002004j
[2025-07-31 16:36:52] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -55.052582+0.000365j
[2025-07-31 16:37:02] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -54.980903+0.001508j
[2025-07-31 16:37:12] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -54.981754-0.000341j
[2025-07-31 16:37:22] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -54.955875-0.000965j
[2025-07-31 16:37:32] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -55.040447-0.001190j
[2025-07-31 16:37:42] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -55.036541-0.003335j
[2025-07-31 16:37:53] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -55.006701+0.000023j
[2025-07-31 16:38:03] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -54.973304-0.000681j
[2025-07-31 16:38:13] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -55.026700+0.001200j
[2025-07-31 16:38:23] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -55.012557+0.000072j
[2025-07-31 16:38:33] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -54.984903-0.000856j
[2025-07-31 16:38:43] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -55.007554+0.000421j
[2025-07-31 16:38:53] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -55.033892-0.000943j
[2025-07-31 16:39:03] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -55.032353+0.001243j
[2025-07-31 16:39:13] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -55.071419-0.001436j
[2025-07-31 16:39:23] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -55.042682+0.001180j
[2025-07-31 16:39:33] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -55.044575-0.000260j
[2025-07-31 16:39:44] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -55.080348-0.001420j
[2025-07-31 16:39:54] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -55.135300-0.000136j
[2025-07-31 16:40:04] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -55.092539+0.002288j
[2025-07-31 16:40:14] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -55.074592+0.000933j
[2025-07-31 16:40:24] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -55.096017+0.000332j
[2025-07-31 16:40:34] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -55.075456-0.000693j
[2025-07-31 16:40:44] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -55.082819-0.001810j
[2025-07-31 16:40:54] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -55.100581+0.001444j
[2025-07-31 16:41:04] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -55.068664-0.000134j
[2025-07-31 16:41:14] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -55.056074+0.000794j
[2025-07-31 16:41:25] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -55.049689-0.001949j
[2025-07-31 16:41:35] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -55.040255-0.000191j
[2025-07-31 16:41:45] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -55.060868+0.000005j
[2025-07-31 16:41:55] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -55.101940+0.001217j
[2025-07-31 16:42:05] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -55.046781+0.000901j
[2025-07-31 16:42:15] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -55.036856-0.001899j
[2025-07-31 16:42:25] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -55.051253+0.000302j
[2025-07-31 16:42:35] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -55.093349-0.001819j
[2025-07-31 16:42:45] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -55.044182-0.000160j
[2025-07-31 16:42:55] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -55.072713-0.000182j
[2025-07-31 16:43:05] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -55.046298+0.000209j
[2025-07-31 16:43:16] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -55.006374-0.001614j
[2025-07-31 16:43:26] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -54.986411+0.000766j
[2025-07-31 16:43:36] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -54.965255-0.000748j
[2025-07-31 16:43:46] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -54.933254-0.000177j
[2025-07-31 16:43:56] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -54.931507+0.000863j
[2025-07-31 16:44:06] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -54.946137-0.001728j
[2025-07-31 16:44:16] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -54.947316+0.002548j
[2025-07-31 16:44:26] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -55.034564+0.000563j
[2025-07-31 16:44:36] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -55.022210+0.000203j
[2025-07-31 16:44:46] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -55.023633-0.002524j
[2025-07-31 16:44:56] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -55.068314+0.000084j
[2025-07-31 16:45:07] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -55.115110-0.000475j
[2025-07-31 16:45:17] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -55.094413-0.000441j
[2025-07-31 16:45:27] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -55.075968+0.001686j
[2025-07-31 16:45:37] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -55.038929-0.000562j
[2025-07-31 16:45:47] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -54.998249-0.000939j
[2025-07-31 16:45:57] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -54.986994+0.001875j
[2025-07-31 16:46:07] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -54.992189+0.000155j
[2025-07-31 16:46:17] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -55.015829+0.000154j
[2025-07-31 16:46:27] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -54.971316-0.000383j
[2025-07-31 16:46:37] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -54.986359-0.001861j
[2025-07-31 16:46:48] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -55.012961-0.001740j
[2025-07-31 16:46:58] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -54.975959+0.000627j
[2025-07-31 16:47:08] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -54.973065-0.000107j
[2025-07-31 16:47:18] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -55.068920+0.001071j
[2025-07-31 16:47:28] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -55.069497+0.000582j
[2025-07-31 16:47:38] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -55.007649-0.000181j
[2025-07-31 16:47:48] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -55.007956-0.001041j
[2025-07-31 16:47:58] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -54.979445-0.002010j
[2025-07-31 16:48:08] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -55.056230-0.000994j
[2025-07-31 16:48:18] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -54.979365-0.002198j
[2025-07-31 16:48:28] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -55.002506+0.000314j
[2025-07-31 16:48:39] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -55.024213+0.001827j
[2025-07-31 16:48:49] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -55.041940+0.000799j
[2025-07-31 16:48:59] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -55.034911+0.000518j
[2025-07-31 16:49:09] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -55.060976-0.001471j
[2025-07-31 16:49:19] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -55.076544+0.001050j
[2025-07-31 16:49:29] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -55.089983+0.002629j
[2025-07-31 16:49:39] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -55.151005-0.000711j
[2025-07-31 16:49:49] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -55.128689-0.000270j
[2025-07-31 16:49:59] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -55.110775+0.000820j
[2025-07-31 16:50:09] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -55.099899-0.001089j
[2025-07-31 16:50:19] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -55.068531+0.000769j
[2025-07-31 16:50:30] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -55.086011-0.002122j
[2025-07-31 16:50:40] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -55.071334-0.002475j
[2025-07-31 16:50:50] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -55.051437-0.000332j
[2025-07-31 16:51:00] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -54.993914-0.002839j
[2025-07-31 16:51:10] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -55.058381+0.000130j
[2025-07-31 16:51:20] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -55.027710-0.000554j
[2025-07-31 16:51:30] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -55.069577-0.000668j
[2025-07-31 16:51:40] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -55.000636+0.000075j
[2025-07-31 16:51:50] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -55.055216+0.000378j
[2025-07-31 16:52:01] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -55.026552+0.000064j
[2025-07-31 16:52:11] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -55.072781+0.000885j
[2025-07-31 16:52:21] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -55.065387+0.000327j
[2025-07-31 16:52:31] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -54.998290-0.003388j
[2025-07-31 16:52:41] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -55.025455-0.001038j
[2025-07-31 16:52:51] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -55.027198-0.001212j
[2025-07-31 16:53:01] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -55.021203-0.000469j
[2025-07-31 16:53:11] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -54.959170+0.000970j
[2025-07-31 16:53:21] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -54.964976+0.000012j
[2025-07-31 16:53:31] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -54.984135+0.000359j
[2025-07-31 16:53:42] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -54.958893-0.001775j
[2025-07-31 16:53:52] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -55.051551+0.001009j
[2025-07-31 16:54:02] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -55.057848+0.000661j
[2025-07-31 16:54:12] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -55.042427+0.000570j
[2025-07-31 16:54:22] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -55.024417-0.000476j
[2025-07-31 16:54:32] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -54.959642+0.000196j
[2025-07-31 16:54:42] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -55.007045-0.000155j
[2025-07-31 16:54:52] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -55.023555+0.001330j
[2025-07-31 16:55:02] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -55.028004+0.000440j
[2025-07-31 16:55:12] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -55.111880-0.000354j
[2025-07-31 16:55:23] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -55.028423-0.001088j
[2025-07-31 16:55:33] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -55.036001+0.000348j
[2025-07-31 16:55:43] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -55.040481+0.000792j
[2025-07-31 16:55:53] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -55.062669+0.000903j
[2025-07-31 16:56:03] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -55.067984+0.000597j
[2025-07-31 16:56:13] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -55.062485-0.002188j
[2025-07-31 16:56:23] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -55.020301+0.001852j
[2025-07-31 16:56:33] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -54.972397-0.001487j
[2025-07-31 16:56:43] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -54.985720-0.000679j
[2025-07-31 16:56:53] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -55.063537+0.000424j
[2025-07-31 16:57:03] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -55.014086+0.000995j
[2025-07-31 16:57:14] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -55.010160+0.001493j
[2025-07-31 16:57:24] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -55.055287-0.000323j
[2025-07-31 16:57:24] RESTART #2 | Period: 600
[2025-07-31 16:57:34] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -55.051974-0.000043j
[2025-07-31 16:57:44] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -55.050842+0.003095j
[2025-07-31 16:57:54] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -55.043307-0.000889j
[2025-07-31 16:58:04] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -55.025080-0.000318j
[2025-07-31 16:58:14] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -55.021753-0.000068j
[2025-07-31 16:58:24] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -55.027831+0.000730j
[2025-07-31 16:58:34] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -55.027429+0.001462j
[2025-07-31 16:58:44] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -55.034349-0.000058j
[2025-07-31 16:58:55] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -54.995357-0.000499j
[2025-07-31 16:59:05] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -54.981355-0.001397j
[2025-07-31 16:59:15] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -54.921704+0.001789j
[2025-07-31 16:59:25] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -54.936608-0.000839j
[2025-07-31 16:59:35] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -54.987228-0.001704j
[2025-07-31 16:59:45] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -55.026364-0.000422j
[2025-07-31 16:59:55] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -55.036698-0.000029j
[2025-07-31 17:00:05] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -55.016821-0.000774j
[2025-07-31 17:00:15] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -55.082491-0.000458j
[2025-07-31 17:00:25] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -55.019219+0.000447j
[2025-07-31 17:00:35] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -55.014745-0.001094j
[2025-07-31 17:00:46] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -55.045753+0.000415j
[2025-07-31 17:00:56] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -55.062522+0.001172j
[2025-07-31 17:01:06] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -55.034771+0.001239j
[2025-07-31 17:01:16] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -55.037193+0.000602j
[2025-07-31 17:01:26] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -54.974233+0.001840j
[2025-07-31 17:01:36] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -55.011519+0.001108j
[2025-07-31 17:01:46] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -55.006484-0.001842j
[2025-07-31 17:01:56] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -55.046355-0.000557j
[2025-07-31 17:02:06] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -55.025570+0.000406j
[2025-07-31 17:02:16] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -55.020137-0.000348j
[2025-07-31 17:02:26] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -55.004845+0.000286j
[2025-07-31 17:02:37] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -54.991649-0.000177j
[2025-07-31 17:02:47] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -54.934247+0.000127j
[2025-07-31 17:02:57] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -54.950919-0.000234j
[2025-07-31 17:03:07] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -55.027754-0.000006j
[2025-07-31 17:03:17] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -55.025348+0.000292j
[2025-07-31 17:03:27] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -55.050678-0.000657j
[2025-07-31 17:03:37] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -55.068387-0.000252j
[2025-07-31 17:03:47] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -55.022035-0.001163j
[2025-07-31 17:03:57] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -55.055104-0.001039j
[2025-07-31 17:04:07] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -55.125591+0.000292j
[2025-07-31 17:04:18] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -55.090699-0.000270j
[2025-07-31 17:04:28] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -55.075977-0.002499j
[2025-07-31 17:04:38] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -55.074393+0.000922j
[2025-07-31 17:04:48] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -55.097924-0.000909j
[2025-07-31 17:04:58] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -55.082773+0.001068j
[2025-07-31 17:05:08] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -55.016347+0.000865j
[2025-07-31 17:05:18] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -55.081904-0.000904j
[2025-07-31 17:05:28] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -55.095050+0.000567j
[2025-07-31 17:05:38] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -55.097043+0.000091j
[2025-07-31 17:05:48] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -55.048964-0.001591j
[2025-07-31 17:05:48] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-07-31 17:05:58] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -55.002677+0.001140j
[2025-07-31 17:06:09] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -54.966796+0.000599j
[2025-07-31 17:06:19] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -54.967729-0.002279j
[2025-07-31 17:06:29] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -55.008061+0.001979j
[2025-07-31 17:06:39] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -55.029190+0.000206j
[2025-07-31 17:06:49] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -54.965700+0.001426j
[2025-07-31 17:06:59] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -54.999027+0.000956j
[2025-07-31 17:07:09] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -55.044869-0.000753j
[2025-07-31 17:07:19] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -55.025046+0.000747j
[2025-07-31 17:07:29] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -55.057547+0.001388j
[2025-07-31 17:07:39] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -55.037498+0.003317j
[2025-07-31 17:07:49] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -55.058501-0.000282j
[2025-07-31 17:08:00] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -55.076333-0.001169j
[2025-07-31 17:08:10] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -55.011976-0.003080j
[2025-07-31 17:08:20] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -54.994751+0.001200j
[2025-07-31 17:08:30] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -54.987886-0.001501j
[2025-07-31 17:08:40] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -54.999734-0.000966j
[2025-07-31 17:08:50] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -55.044007-0.000994j
[2025-07-31 17:09:00] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -54.993681-0.000978j
[2025-07-31 17:09:10] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -55.061895-0.002121j
[2025-07-31 17:09:20] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -55.108227+0.000523j
[2025-07-31 17:09:31] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -55.120849+0.000280j
[2025-07-31 17:09:41] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -55.078369+0.000374j
[2025-07-31 17:09:51] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -55.058904+0.001362j
[2025-07-31 17:10:01] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -54.991022-0.002110j
[2025-07-31 17:10:11] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -55.007096-0.002644j
[2025-07-31 17:10:21] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -55.007656-0.001314j
[2025-07-31 17:10:31] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -55.013464+0.000537j
[2025-07-31 17:10:41] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -54.993388+0.001757j
[2025-07-31 17:10:51] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -55.008736-0.001273j
[2025-07-31 17:11:02] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -55.021203+0.000526j
[2025-07-31 17:11:12] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -55.070525-0.001898j
[2025-07-31 17:11:22] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -55.021877-0.000654j
[2025-07-31 17:11:32] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -55.006043+0.000590j
[2025-07-31 17:11:42] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -55.002990+0.000717j
[2025-07-31 17:11:52] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -55.012104+0.000426j
[2025-07-31 17:12:02] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -55.025815+0.000527j
[2025-07-31 17:12:12] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -54.990659-0.001137j
[2025-07-31 17:12:22] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -55.000229+0.002409j
[2025-07-31 17:12:32] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -55.010662+0.002230j
[2025-07-31 17:12:42] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -55.021283-0.000352j
[2025-07-31 17:12:53] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -54.996504+0.002390j
[2025-07-31 17:13:03] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -54.993533+0.001888j
[2025-07-31 17:13:13] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -55.005821+0.001774j
[2025-07-31 17:13:23] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -55.005463+0.000027j
[2025-07-31 17:13:33] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -55.041375-0.001727j
[2025-07-31 17:13:43] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -55.070024+0.000024j
[2025-07-31 17:13:53] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -55.033066-0.000057j
[2025-07-31 17:14:03] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -55.066272+0.001224j
[2025-07-31 17:14:13] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -54.997431+0.000867j
[2025-07-31 17:14:23] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -55.045722-0.000843j
[2025-07-31 17:14:33] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -55.071858+0.000076j
[2025-07-31 17:14:44] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -55.082045-0.001292j
[2025-07-31 17:14:54] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -55.079992+0.001094j
[2025-07-31 17:15:04] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -55.074630-0.000632j
[2025-07-31 17:15:14] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -55.068587+0.000484j
[2025-07-31 17:15:24] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -55.029935-0.000665j
[2025-07-31 17:15:34] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -55.095318-0.001185j
[2025-07-31 17:15:44] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -55.091168+0.000336j
[2025-07-31 17:15:54] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -55.068653+0.002549j
[2025-07-31 17:16:04] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -55.064035-0.002095j
[2025-07-31 17:16:14] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -55.031441-0.002358j
[2025-07-31 17:16:25] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -55.022586-0.003086j
[2025-07-31 17:16:35] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -55.018053+0.000418j
[2025-07-31 17:16:45] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -55.016499-0.000029j
[2025-07-31 17:16:55] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -55.027609-0.000465j
[2025-07-31 17:17:05] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -55.021099+0.001268j
[2025-07-31 17:17:15] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -55.009862+0.000187j
[2025-07-31 17:17:25] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -55.037468-0.001274j
[2025-07-31 17:17:35] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -55.006778-0.001013j
[2025-07-31 17:17:45] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -55.083423-0.001607j
[2025-07-31 17:17:55] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -55.124870+0.001449j
[2025-07-31 17:18:05] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -55.091645+0.000905j
[2025-07-31 17:18:16] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -55.056586+0.001328j
[2025-07-31 17:18:26] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -55.036459+0.003070j
[2025-07-31 17:18:36] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -55.060248-0.000076j
[2025-07-31 17:18:46] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -55.124092-0.002164j
[2025-07-31 17:18:56] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -55.089968-0.001469j
[2025-07-31 17:19:06] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -55.052961-0.000287j
[2025-07-31 17:19:16] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -55.037307-0.000298j
[2025-07-31 17:19:26] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -55.057762-0.000266j
[2025-07-31 17:19:36] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -55.020506+0.000118j
[2025-07-31 17:19:46] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -55.039663+0.000374j
[2025-07-31 17:19:56] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -55.032973-0.000925j
[2025-07-31 17:20:07] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -55.020163+0.001057j
[2025-07-31 17:20:17] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -55.021810-0.001610j
[2025-07-31 17:20:27] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -55.032785-0.000472j
[2025-07-31 17:20:37] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -55.003266+0.001344j
[2025-07-31 17:20:47] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -54.934499+0.000095j
[2025-07-31 17:20:57] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -54.965574+0.001916j
[2025-07-31 17:21:07] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -54.976268-0.000528j
[2025-07-31 17:21:17] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -55.009990+0.000104j
[2025-07-31 17:21:27] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -54.968467+0.000045j
[2025-07-31 17:21:37] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -54.950767-0.002323j
[2025-07-31 17:21:48] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -54.983095-0.000857j
[2025-07-31 17:21:58] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -55.019405-0.000638j
[2025-07-31 17:22:08] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -55.047278-0.000719j
[2025-07-31 17:22:18] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -55.066203+0.001488j
[2025-07-31 17:22:28] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -55.063691+0.000418j
[2025-07-31 17:22:38] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -55.059970+0.000482j
[2025-07-31 17:22:48] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -55.109265+0.003175j
[2025-07-31 17:22:58] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -55.016820-0.001259j
[2025-07-31 17:23:08] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -55.063898-0.000829j
[2025-07-31 17:23:18] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -55.036616+0.000299j
[2025-07-31 17:23:28] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -55.054720-0.002421j
[2025-07-31 17:23:39] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -54.989566-0.001249j
[2025-07-31 17:23:49] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -55.006296+0.001091j
[2025-07-31 17:23:59] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -54.979466-0.001631j
[2025-07-31 17:24:09] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -54.976670-0.000552j
[2025-07-31 17:24:19] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -54.936396-0.001001j
[2025-07-31 17:24:29] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -54.990653+0.001545j
[2025-07-31 17:24:39] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -55.022692-0.000238j
[2025-07-31 17:24:49] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -55.026219+0.000979j
[2025-07-31 17:24:59] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -54.995677+0.000368j
[2025-07-31 17:25:09] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -55.035055-0.000486j
[2025-07-31 17:25:19] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -55.037303-0.001980j
[2025-07-31 17:25:30] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -54.996495+0.000939j
[2025-07-31 17:25:40] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -54.993367-0.000918j
[2025-07-31 17:25:50] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -55.006970+0.000268j
[2025-07-31 17:26:00] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -55.048073+0.002024j
[2025-07-31 17:26:10] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -55.042302+0.001782j
[2025-07-31 17:26:20] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -55.026124-0.001627j
[2025-07-31 17:26:30] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -55.056999-0.001834j
[2025-07-31 17:26:40] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -55.060560-0.001451j
[2025-07-31 17:26:50] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -55.062465-0.000268j
[2025-07-31 17:27:00] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -55.046288-0.000005j
[2025-07-31 17:27:11] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -55.074300-0.001495j
[2025-07-31 17:27:21] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -55.064558+0.000768j
[2025-07-31 17:27:31] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -55.099707+0.000002j
[2025-07-31 17:27:41] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -55.079502+0.000004j
[2025-07-31 17:27:51] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -55.079831+0.000012j
[2025-07-31 17:28:01] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -55.063621+0.001440j
[2025-07-31 17:28:11] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -55.026617-0.000479j
[2025-07-31 17:28:21] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -55.035884-0.002006j
[2025-07-31 17:28:31] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -55.030668-0.000051j
[2025-07-31 17:28:41] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -55.003475-0.001769j
[2025-07-31 17:28:51] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -54.960309+0.000643j
[2025-07-31 17:29:02] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -54.980500-0.001205j
[2025-07-31 17:29:12] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -54.990217+0.000035j
[2025-07-31 17:29:22] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -55.006742-0.000232j
[2025-07-31 17:29:32] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -54.988669-0.001730j
[2025-07-31 17:29:42] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -54.996838-0.000484j
[2025-07-31 17:29:52] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -54.991066-0.000131j
[2025-07-31 17:30:02] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -54.962365-0.000871j
[2025-07-31 17:30:12] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -55.023597-0.000427j
[2025-07-31 17:30:22] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -54.993324-0.003206j
[2025-07-31 17:30:32] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -55.043860+0.000275j
[2025-07-31 17:30:42] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -54.983846-0.000934j
[2025-07-31 17:30:53] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -55.053014+0.000675j
[2025-07-31 17:31:03] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -55.049327+0.000173j
[2025-07-31 17:31:13] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -55.062084+0.000771j
[2025-07-31 17:31:23] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -55.062557+0.000581j
[2025-07-31 17:31:33] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -55.077211-0.000205j
[2025-07-31 17:31:43] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -55.122043+0.001341j
[2025-07-31 17:31:53] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -55.098148-0.001004j
[2025-07-31 17:32:03] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -55.039077+0.000753j
[2025-07-31 17:32:13] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -55.117044-0.002389j
[2025-07-31 17:32:23] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -55.046028-0.001239j
[2025-07-31 17:32:33] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -55.053065+0.000808j
[2025-07-31 17:32:44] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -55.013001+0.000472j
[2025-07-31 17:32:54] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -55.028124+0.002936j
[2025-07-31 17:33:04] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -55.023413+0.000357j
[2025-07-31 17:33:14] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -55.044971+0.000365j
[2025-07-31 17:33:24] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -55.020163-0.000235j
[2025-07-31 17:33:34] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -54.941100+0.000819j
[2025-07-31 17:33:44] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -54.999921+0.002522j
[2025-07-31 17:33:54] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -54.996884-0.000329j
[2025-07-31 17:34:04] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -55.032878-0.000312j
[2025-07-31 17:34:14] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -54.927743-0.001919j
[2025-07-31 17:34:25] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -54.989650-0.000264j
[2025-07-31 17:34:35] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -55.017676-0.001987j
[2025-07-31 17:34:45] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -55.065922-0.001270j
[2025-07-31 17:34:55] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -55.055483-0.000005j
[2025-07-31 17:35:05] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -54.977215+0.000677j
[2025-07-31 17:35:15] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -55.005123-0.000001j
[2025-07-31 17:35:25] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -55.003758-0.000015j
[2025-07-31 17:35:35] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -54.970097-0.000390j
[2025-07-31 17:35:45] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -55.041021-0.000095j
[2025-07-31 17:35:55] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -55.011059+0.000630j
[2025-07-31 17:36:05] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -55.009950+0.002208j
[2025-07-31 17:36:16] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -55.074586+0.001952j
[2025-07-31 17:36:26] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -55.029302-0.002016j
[2025-07-31 17:36:36] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -55.068177+0.000261j
[2025-07-31 17:36:46] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -55.032407-0.001972j
[2025-07-31 17:36:56] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -55.068990-0.000665j
[2025-07-31 17:37:06] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -54.987519-0.001379j
[2025-07-31 17:37:16] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -55.075016+0.000477j
[2025-07-31 17:37:26] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -55.101476+0.001292j
[2025-07-31 17:37:36] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -55.068362-0.000711j
[2025-07-31 17:37:46] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -55.096200-0.000136j
[2025-07-31 17:37:56] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -55.111425-0.001444j
[2025-07-31 17:38:07] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -55.075589-0.001883j
[2025-07-31 17:38:17] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -55.048023-0.001118j
[2025-07-31 17:38:27] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -55.022290-0.000287j
[2025-07-31 17:38:37] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -55.029249-0.000599j
[2025-07-31 17:38:47] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -55.009747-0.000390j
[2025-07-31 17:38:57] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -55.053014+0.001205j
[2025-07-31 17:39:07] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -55.042922+0.000349j
[2025-07-31 17:39:17] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -54.991399-0.000529j
[2025-07-31 17:39:27] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -55.026175+0.000147j
[2025-07-31 17:39:37] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -55.001877+0.000709j
[2025-07-31 17:39:48] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -55.014868-0.001505j
[2025-07-31 17:39:58] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -55.063734-0.000901j
[2025-07-31 17:40:08] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -55.054169+0.002770j
[2025-07-31 17:40:18] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -55.023470+0.000365j
[2025-07-31 17:40:28] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -55.070961-0.001493j
[2025-07-31 17:40:38] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -55.072006+0.001882j
[2025-07-31 17:40:48] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -55.031039+0.001455j
[2025-07-31 17:40:58] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -55.064582+0.001487j
[2025-07-31 17:41:08] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -55.029662+0.000920j
[2025-07-31 17:41:18] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -54.975260-0.000443j
[2025-07-31 17:41:29] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -55.022714+0.001192j
[2025-07-31 17:41:39] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -55.039109+0.000811j
[2025-07-31 17:41:49] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -55.027917+0.001208j
[2025-07-31 17:41:59] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -55.034381-0.000651j
[2025-07-31 17:42:09] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -55.093421-0.000269j
[2025-07-31 17:42:19] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -55.041164+0.000505j
[2025-07-31 17:42:29] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -55.026470-0.002040j
[2025-07-31 17:42:39] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -54.982782-0.000234j
[2025-07-31 17:42:49] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -55.001831+0.000218j
[2025-07-31 17:42:59] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -55.061386+0.001721j
[2025-07-31 17:43:09] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -54.985002+0.000522j
[2025-07-31 17:43:20] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -54.967846-0.000519j
[2025-07-31 17:43:30] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -54.992579-0.002484j
[2025-07-31 17:43:40] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -54.990243-0.000707j
[2025-07-31 17:43:50] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -54.937047-0.000090j
[2025-07-31 17:44:00] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -54.953873+0.001034j
[2025-07-31 17:44:10] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -54.957174-0.000261j
[2025-07-31 17:44:20] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -55.000982-0.001691j
[2025-07-31 17:44:30] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -55.051010+0.000398j
[2025-07-31 17:44:40] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -55.029920+0.000110j
[2025-07-31 17:44:50] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -55.056924+0.000843j
[2025-07-31 17:45:01] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -54.976954-0.000158j
[2025-07-31 17:45:11] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -54.913663+0.002423j
[2025-07-31 17:45:21] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -55.030902-0.001492j
[2025-07-31 17:45:31] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -54.976115-0.002182j
[2025-07-31 17:45:41] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -55.006967-0.001501j
[2025-07-31 17:45:51] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -54.993973+0.001767j
[2025-07-31 17:46:01] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -54.982044+0.000768j
[2025-07-31 17:46:11] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -55.031599+0.000250j
[2025-07-31 17:46:21] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -54.973422-0.000270j
[2025-07-31 17:46:31] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -54.998449+0.000127j
[2025-07-31 17:46:42] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -54.994588-0.001113j
[2025-07-31 17:46:52] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -54.945806+0.000426j
[2025-07-31 17:47:02] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -54.984066-0.003198j
[2025-07-31 17:47:12] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -54.987360-0.000937j
[2025-07-31 17:47:22] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -54.967443+0.000635j
[2025-07-31 17:47:32] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -54.993115+0.000008j
[2025-07-31 17:47:42] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -55.039462+0.000834j
[2025-07-31 17:47:52] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -55.027695-0.000396j
[2025-07-31 17:48:02] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -55.061893+0.000657j
[2025-07-31 17:48:12] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -55.005659+0.001216j
[2025-07-31 17:48:22] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -55.028075+0.001276j
[2025-07-31 17:48:33] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -55.009977-0.000712j
[2025-07-31 17:48:43] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -55.082810+0.001311j
[2025-07-31 17:48:53] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -55.050956-0.000076j
[2025-07-31 17:49:03] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -55.061753-0.001035j
[2025-07-31 17:49:13] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -55.112762+0.001061j
[2025-07-31 17:49:23] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -55.101580-0.000498j
[2025-07-31 17:49:33] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -55.093578+0.001632j
[2025-07-31 17:49:43] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -55.070194+0.000625j
[2025-07-31 17:49:53] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -55.070478-0.004682j
[2025-07-31 17:50:03] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -55.062903+0.000361j
[2025-07-31 17:50:14] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -55.014377+0.003834j
[2025-07-31 17:50:24] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -55.023276-0.000746j
[2025-07-31 17:50:34] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -55.043863+0.001506j
[2025-07-31 17:50:44] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -55.095931+0.002240j
[2025-07-31 17:50:54] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -55.024689+0.002336j
[2025-07-31 17:51:04] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -55.101187+0.001758j
[2025-07-31 17:51:14] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -55.089015-0.002397j
[2025-07-31 17:51:24] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -55.058256-0.000611j
[2025-07-31 17:51:34] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -55.058778-0.003380j
[2025-07-31 17:51:44] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -55.062267-0.000125j
[2025-07-31 17:51:55] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -55.013202+0.000132j
[2025-07-31 17:52:05] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -54.939176+0.001940j
[2025-07-31 17:52:15] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -55.015391-0.001994j
[2025-07-31 17:52:25] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -55.007423+0.000101j
[2025-07-31 17:52:35] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -55.026201+0.000115j
[2025-07-31 17:52:45] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -55.047686-0.002821j
[2025-07-31 17:52:55] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -54.995908+0.000498j
[2025-07-31 17:53:05] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -54.978718-0.000754j
[2025-07-31 17:53:15] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -55.043866+0.000302j
[2025-07-31 17:53:25] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -55.033562-0.000549j
[2025-07-31 17:53:35] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -55.021114+0.000081j
[2025-07-31 17:53:46] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -55.010963+0.001900j
[2025-07-31 17:53:56] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -54.993979+0.000366j
[2025-07-31 17:54:06] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -55.045907-0.001053j
[2025-07-31 17:54:16] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -54.999905-0.001303j
[2025-07-31 17:54:26] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -54.975598-0.000187j
[2025-07-31 17:54:36] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -54.976382+0.000545j
[2025-07-31 17:54:46] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -54.997753-0.001099j
[2025-07-31 17:54:56] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -55.021346-0.001292j
[2025-07-31 17:55:06] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -55.003922-0.000174j
[2025-07-31 17:55:16] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -54.945491-0.000577j
[2025-07-31 17:55:26] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -54.882728+0.000471j
[2025-07-31 17:55:37] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -54.961669+0.000613j
[2025-07-31 17:55:47] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -54.986615-0.001359j
[2025-07-31 17:55:57] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -54.977955+0.000053j
[2025-07-31 17:56:07] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -54.988982+0.000782j
[2025-07-31 17:56:17] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -54.980291-0.002390j
[2025-07-31 17:56:27] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -54.972011-0.001861j
[2025-07-31 17:56:37] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -54.971670+0.001030j
[2025-07-31 17:56:47] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -54.972738-0.000264j
[2025-07-31 17:56:57] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -54.995918-0.000149j
[2025-07-31 17:57:07] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -54.999221+0.000850j
[2025-07-31 17:57:18] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -55.040091+0.002311j
[2025-07-31 17:57:28] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -55.053236+0.001313j
[2025-07-31 17:57:38] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -55.011121+0.001315j
[2025-07-31 17:57:48] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -55.000638-0.001341j
[2025-07-31 17:57:58] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -55.015970-0.001087j
[2025-07-31 17:58:08] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -55.051097+0.000737j
[2025-07-31 17:58:18] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -55.003752-0.000290j
[2025-07-31 17:58:28] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -55.013913-0.001322j
[2025-07-31 17:58:38] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -55.041231-0.001029j
[2025-07-31 17:58:48] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -54.990418-0.000969j
[2025-07-31 17:58:58] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -55.013912+0.000524j
[2025-07-31 17:59:09] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -55.079632-0.000657j
[2025-07-31 17:59:19] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -55.113763-0.002996j
[2025-07-31 17:59:29] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -55.034132-0.001886j
[2025-07-31 17:59:39] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -55.002410-0.001171j
[2025-07-31 17:59:49] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -54.981336-0.002580j
[2025-07-31 17:59:59] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -55.036166-0.000807j
[2025-07-31 18:00:09] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -55.035612-0.000309j
[2025-07-31 18:00:19] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -55.105903+0.001301j
[2025-07-31 18:00:29] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -55.051868+0.001987j
[2025-07-31 18:00:39] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -55.071751+0.001135j
[2025-07-31 18:00:49] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -54.990123+0.000728j
[2025-07-31 18:01:00] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -55.024080+0.001160j
[2025-07-31 18:01:10] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -55.036946+0.001620j
[2025-07-31 18:01:20] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -55.057402-0.001243j
[2025-07-31 18:01:30] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -54.996339+0.000268j
[2025-07-31 18:01:40] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -55.032341+0.001163j
[2025-07-31 18:01:50] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -55.058906+0.001643j
[2025-07-31 18:02:00] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -55.022324-0.000813j
[2025-07-31 18:02:10] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -55.041017+0.000365j
[2025-07-31 18:02:20] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -55.013825-0.001437j
[2025-07-31 18:02:31] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -55.025653-0.000595j
[2025-07-31 18:02:41] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -55.011934+0.002181j
[2025-07-31 18:02:51] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -55.031144+0.000740j
[2025-07-31 18:03:01] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -54.993721+0.000176j
[2025-07-31 18:03:11] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -54.975211-0.001151j
[2025-07-31 18:03:21] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -55.036516-0.000346j
[2025-07-31 18:03:31] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -54.999053-0.001210j
[2025-07-31 18:03:41] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -55.056758+0.001276j
[2025-07-31 18:03:51] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -54.978943+0.001270j
[2025-07-31 18:04:01] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -54.992709+0.000882j
[2025-07-31 18:04:11] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -55.022055+0.000914j
[2025-07-31 18:04:22] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -55.025336+0.001396j
[2025-07-31 18:04:32] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -55.023805+0.000255j
[2025-07-31 18:04:42] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -55.020808+0.000101j
[2025-07-31 18:04:52] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -54.993524+0.000549j
[2025-07-31 18:05:02] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -54.947847-0.001093j
[2025-07-31 18:05:12] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -54.969219+0.000717j
[2025-07-31 18:05:22] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -54.948691-0.000438j
[2025-07-31 18:05:32] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -54.947780+0.000436j
[2025-07-31 18:05:42] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -54.996987+0.001056j
[2025-07-31 18:05:52] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -55.017745+0.000108j
[2025-07-31 18:06:03] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -54.974301-0.000844j
[2025-07-31 18:06:13] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -55.004751+0.000533j
[2025-07-31 18:06:23] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -55.030463-0.001731j
[2025-07-31 18:06:33] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -55.037090-0.001515j
[2025-07-31 18:06:43] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -54.992499+0.000695j
[2025-07-31 18:06:53] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -54.965817-0.000763j
[2025-07-31 18:07:03] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -55.005435+0.001569j
[2025-07-31 18:07:13] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -55.036947-0.000899j
[2025-07-31 18:07:23] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -55.018779+0.000154j
[2025-07-31 18:07:33] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -55.060890-0.000803j
[2025-07-31 18:07:44] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -55.040938-0.000312j
[2025-07-31 18:07:54] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -55.036816+0.000004j
[2025-07-31 18:08:04] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -55.053916+0.000241j
[2025-07-31 18:08:14] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -55.026630+0.000125j
[2025-07-31 18:08:24] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -55.018681-0.000405j
[2025-07-31 18:08:34] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -55.002663-0.000562j
[2025-07-31 18:08:44] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -55.031108+0.000606j
[2025-07-31 18:08:54] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -55.026199+0.000947j
[2025-07-31 18:09:04] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -55.026994+0.000461j
[2025-07-31 18:09:14] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -54.980378+0.001081j
[2025-07-31 18:09:25] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -54.997552-0.000149j
[2025-07-31 18:09:35] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -55.011450+0.000390j
[2025-07-31 18:09:45] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -55.068195+0.001434j
[2025-07-31 18:09:55] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -55.010398+0.000706j
[2025-07-31 18:10:05] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -55.007177+0.000045j
[2025-07-31 18:10:15] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -55.059586-0.000403j
[2025-07-31 18:10:25] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -55.050077+0.000668j
[2025-07-31 18:10:35] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -54.994540+0.002364j
[2025-07-31 18:10:45] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -55.033713+0.001723j
[2025-07-31 18:10:55] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -54.998112-0.000938j
[2025-07-31 18:11:05] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -54.967710-0.000158j
[2025-07-31 18:11:16] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -54.991700+0.001303j
[2025-07-31 18:11:26] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -54.947948-0.000079j
[2025-07-31 18:11:36] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -54.973695+0.001179j
[2025-07-31 18:11:46] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -54.975351-0.000183j
[2025-07-31 18:11:56] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -54.988254+0.001778j
[2025-07-31 18:12:06] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -55.065938+0.000530j
[2025-07-31 18:12:16] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -55.049459+0.000118j
[2025-07-31 18:12:26] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -55.054273-0.001794j
[2025-07-31 18:12:36] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -55.078423-0.001345j
[2025-07-31 18:12:46] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -55.083863-0.001678j
[2025-07-31 18:12:56] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -55.093403+0.000750j
[2025-07-31 18:13:07] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -55.071066+0.000164j
[2025-07-31 18:13:17] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -55.053184-0.001932j
[2025-07-31 18:13:27] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -55.079610+0.001861j
[2025-07-31 18:13:37] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -55.093471-0.000957j
[2025-07-31 18:13:47] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -55.055572-0.000700j
[2025-07-31 18:13:57] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -54.985773-0.000122j
[2025-07-31 18:14:07] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -55.034243+0.001158j
[2025-07-31 18:14:17] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -55.082584-0.000605j
[2025-07-31 18:14:27] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -55.069954-0.000968j
[2025-07-31 18:14:37] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -55.112218-0.000643j
[2025-07-31 18:14:48] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -55.073344+0.001988j
[2025-07-31 18:14:58] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -55.115641+0.000063j
[2025-07-31 18:15:08] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -55.123291+0.000491j
[2025-07-31 18:15:18] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -55.120386+0.000620j
[2025-07-31 18:15:28] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -55.084427+0.001416j
[2025-07-31 18:15:38] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -55.056544-0.000574j
[2025-07-31 18:15:48] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -55.037115-0.000131j
[2025-07-31 18:15:58] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -55.098951+0.000490j
[2025-07-31 18:16:08] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -55.086805-0.001534j
[2025-07-31 18:16:18] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -55.105717-0.001632j
[2025-07-31 18:16:28] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -55.061538+0.000410j
[2025-07-31 18:16:39] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -55.039873-0.000684j
[2025-07-31 18:16:49] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -55.069208-0.001703j
[2025-07-31 18:16:59] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -55.038089-0.000400j
[2025-07-31 18:17:09] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -55.064391-0.001247j
[2025-07-31 18:17:19] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -55.042197-0.000987j
[2025-07-31 18:17:29] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -55.005088-0.001613j
[2025-07-31 18:17:39] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -54.958895+0.000657j
[2025-07-31 18:17:49] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -54.995347-0.000794j
[2025-07-31 18:17:59] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -55.003603-0.000789j
[2025-07-31 18:18:09] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -54.993295-0.000230j
[2025-07-31 18:18:19] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -54.967785+0.000521j
[2025-07-31 18:18:30] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -54.974792+0.000891j
[2025-07-31 18:18:40] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -55.016210+0.001644j
[2025-07-31 18:18:50] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -55.058793-0.001231j
[2025-07-31 18:19:00] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -55.059689-0.000995j
[2025-07-31 18:19:10] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -55.054906-0.000925j
[2025-07-31 18:19:20] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -55.011138+0.000800j
[2025-07-31 18:19:30] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -55.004932-0.000223j
[2025-07-31 18:19:40] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -54.959685-0.000063j
[2025-07-31 18:19:50] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -54.967877-0.000587j
[2025-07-31 18:20:01] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -54.974553-0.001656j
[2025-07-31 18:20:11] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -55.053465-0.000954j
[2025-07-31 18:20:21] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -55.000183-0.000764j
[2025-07-31 18:20:31] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -54.970449+0.000332j
[2025-07-31 18:20:41] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -54.962424-0.000661j
[2025-07-31 18:20:51] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -54.993089+0.000670j
[2025-07-31 18:21:01] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -54.961815+0.000189j
[2025-07-31 18:21:11] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -54.975672-0.000065j
[2025-07-31 18:21:21] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -54.997399-0.001135j
[2025-07-31 18:21:31] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -54.992702-0.000218j
[2025-07-31 18:21:41] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -55.005977+0.001425j
[2025-07-31 18:21:52] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -55.076667-0.001406j
[2025-07-31 18:22:02] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -55.048485-0.002903j
[2025-07-31 18:22:12] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -55.020137-0.000590j
[2025-07-31 18:22:22] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -55.054573-0.001185j
[2025-07-31 18:22:32] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -55.142132-0.002073j
[2025-07-31 18:22:42] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -55.060772+0.002167j
[2025-07-31 18:22:52] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -55.102929+0.000420j
[2025-07-31 18:23:02] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -55.074334-0.003119j
[2025-07-31 18:23:12] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -55.072390-0.001526j
[2025-07-31 18:23:22] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -55.068706-0.000764j
[2025-07-31 18:23:32] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -55.099600-0.000535j
[2025-07-31 18:23:42] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -55.056692-0.000417j
[2025-07-31 18:23:53] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -55.046968+0.001383j
[2025-07-31 18:24:03] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -55.048189+0.002274j
[2025-07-31 18:24:13] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -55.062197+0.001019j
[2025-07-31 18:24:23] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -55.066781+0.000069j
[2025-07-31 18:24:33] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -55.118030+0.000688j
[2025-07-31 18:24:43] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -55.140887+0.003295j
[2025-07-31 18:24:53] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -55.140661-0.000655j
[2025-07-31 18:25:03] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -55.129880+0.000955j
[2025-07-31 18:25:13] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -55.076034-0.000977j
[2025-07-31 18:25:23] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -55.055386+0.001177j
[2025-07-31 18:25:33] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -54.980183-0.000287j
[2025-07-31 18:25:44] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -55.071869+0.001073j
[2025-07-31 18:25:54] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -55.058659-0.002076j
[2025-07-31 18:26:04] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -55.094907+0.001408j
[2025-07-31 18:26:14] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -55.093458-0.000177j
[2025-07-31 18:26:24] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -55.089492-0.000970j
[2025-07-31 18:26:34] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -55.032377+0.000980j
[2025-07-31 18:26:44] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -55.064915+0.000791j
[2025-07-31 18:26:54] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -54.977882+0.001075j
[2025-07-31 18:27:05] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -54.996299+0.001073j
[2025-07-31 18:27:15] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -55.043439-0.000598j
[2025-07-31 18:27:25] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -55.021658+0.000404j
[2025-07-31 18:27:35] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -55.040627-0.000445j
[2025-07-31 18:27:45] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -55.041509-0.001300j
[2025-07-31 18:27:55] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -55.030386-0.001183j
[2025-07-31 18:28:05] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -55.048704-0.000763j
[2025-07-31 18:28:15] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -55.041519-0.001941j
[2025-07-31 18:28:25] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -55.040426-0.001365j
[2025-07-31 18:28:35] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -55.089929+0.000018j
[2025-07-31 18:28:45] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -55.118821+0.000796j
[2025-07-31 18:28:56] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -55.054005-0.001616j
[2025-07-31 18:29:06] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -55.025946-0.000441j
[2025-07-31 18:29:16] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -55.036817+0.000879j
[2025-07-31 18:29:26] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -55.001556-0.001155j
[2025-07-31 18:29:36] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -54.996371+0.003144j
[2025-07-31 18:29:46] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -55.049469-0.000142j
[2025-07-31 18:29:56] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -54.947096-0.002791j
[2025-07-31 18:29:56] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-07-31 18:30:06] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -55.044764+0.002536j
[2025-07-31 18:30:16] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -55.033271-0.000172j
[2025-07-31 18:30:26] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -54.966941+0.000167j
[2025-07-31 18:30:36] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -55.003009-0.000920j
[2025-07-31 18:30:47] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -55.029209-0.000959j
[2025-07-31 18:30:57] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -54.961053-0.000788j
[2025-07-31 18:31:07] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -54.966214+0.001150j
[2025-07-31 18:31:17] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -54.973337-0.001430j
[2025-07-31 18:31:27] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -55.015183-0.000341j
[2025-07-31 18:31:37] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -55.063303-0.000612j
[2025-07-31 18:31:47] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -55.018409+0.000266j
[2025-07-31 18:31:57] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -54.956561-0.000552j
[2025-07-31 18:32:07] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -55.060569-0.000039j
[2025-07-31 18:32:17] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -55.015762+0.001020j
[2025-07-31 18:32:28] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -55.034412-0.000215j
[2025-07-31 18:32:38] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -55.029502-0.000774j
[2025-07-31 18:32:48] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -55.092161-0.001408j
[2025-07-31 18:32:58] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -54.996965-0.000134j
[2025-07-31 18:33:08] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -55.030720+0.001407j
[2025-07-31 18:33:18] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -55.062552+0.001635j
[2025-07-31 18:33:28] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -55.024722-0.001622j
[2025-07-31 18:33:38] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -54.933287+0.000176j
[2025-07-31 18:33:48] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -54.944636+0.001138j
[2025-07-31 18:33:58] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -54.982239+0.000277j
[2025-07-31 18:34:09] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -54.964533-0.000005j
[2025-07-31 18:34:19] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -54.974590+0.000585j
[2025-07-31 18:34:29] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -55.018099+0.000169j
[2025-07-31 18:34:39] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -55.017191-0.001558j
[2025-07-31 18:34:49] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -54.979372+0.004229j
[2025-07-31 18:34:59] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -54.991903+0.000383j
[2025-07-31 18:35:09] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -54.986433+0.000359j
[2025-07-31 18:35:19] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -55.006380+0.000409j
[2025-07-31 18:35:29] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -54.977069-0.000052j
[2025-07-31 18:35:39] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -55.042922-0.000712j
[2025-07-31 18:35:49] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -54.972903+0.001915j
[2025-07-31 18:36:00] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -55.023625+0.001580j
[2025-07-31 18:36:10] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -55.053893+0.000226j
[2025-07-31 18:36:20] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -54.991895-0.001168j
[2025-07-31 18:36:30] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -54.994168-0.001895j
[2025-07-31 18:36:40] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -54.973701-0.000503j
[2025-07-31 18:36:50] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -54.974951+0.000309j
[2025-07-31 18:37:00] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -55.058403-0.001690j
[2025-07-31 18:37:10] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -55.071068+0.000823j
[2025-07-31 18:37:20] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -55.027295+0.000938j
[2025-07-31 18:37:31] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -55.038338+0.000595j
[2025-07-31 18:37:41] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -55.062690+0.000063j
[2025-07-31 18:37:51] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -55.020903+0.000557j
[2025-07-31 18:38:01] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -55.030972-0.000550j
[2025-07-31 18:38:11] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -55.024416-0.000128j
[2025-07-31 18:38:21] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -55.048144-0.000689j
[2025-07-31 18:38:21] ✅ Training completed | Restarts: 2
[2025-07-31 18:38:21] ============================================================
[2025-07-31 18:38:21] Training completed | Runtime: 10644.6s
[2025-07-31 18:38:37] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-07-31 18:38:37] ============================================================
