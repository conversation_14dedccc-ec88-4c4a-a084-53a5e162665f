[2025-08-01 01:44:40] 使用checkpoint文件: results/L=4/J2=0.00/J1=0.03/training/checkpoints/final_GCNN.pkl
[2025-08-01 01:44:52] ✓ 从checkpoint加载参数: final
[2025-08-01 01:44:52]   - 能量: -54.205744+0.001305j ± 0.042579
[2025-08-01 01:44:52] ================================================================================
[2025-08-01 01:44:52] 加载量子态: L=4, J2=0.00, J1=0.03
[2025-08-01 01:44:52] 设置样本数为: 1048576
[2025-08-01 01:44:52] 开始生成共享样本集...
[2025-08-01 01:47:25] 样本生成完成,耗时: 152.646 秒
[2025-08-01 01:47:25] ================================================================================
[2025-08-01 01:47:25] 开始计算自旋结构因子...
[2025-08-01 01:47:25] 初始化操作符缓存...
[2025-08-01 01:47:25] 预构建所有自旋相关操作符...
[2025-08-01 01:47:25] 开始计算自旋相关函数...
[2025-08-01 01:47:37] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 11.658s
[2025-08-01 01:47:50] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 13.783s
[2025-08-01 01:47:59] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 8.123s
[2025-08-01 01:48:07] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 8.095s
[2025-08-01 01:48:15] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 8.134s
[2025-08-01 01:48:23] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 8.134s
[2025-08-01 01:48:31] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 8.093s
[2025-08-01 01:48:39] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 8.119s
[2025-08-01 01:48:47] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 8.093s
[2025-08-01 01:48:55] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 8.094s
[2025-08-01 01:49:03] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 8.118s
[2025-08-01 01:49:12] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 8.096s
[2025-08-01 01:49:20] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 8.127s
[2025-08-01 01:49:28] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 8.106s
[2025-08-01 01:49:36] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 8.142s
[2025-08-01 01:49:44] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 8.098s
[2025-08-01 01:49:52] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 8.144s
[2025-08-01 01:50:00] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 8.142s
[2025-08-01 01:50:08] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 8.118s
[2025-08-01 01:50:17] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 8.148s
[2025-08-01 01:50:25] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 8.097s
[2025-08-01 01:50:33] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 8.097s
[2025-08-01 01:50:41] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 8.171s
[2025-08-01 01:50:49] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 8.094s
[2025-08-01 01:50:57] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 8.125s
[2025-08-01 01:51:05] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 8.125s
[2025-08-01 01:51:13] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 8.095s
[2025-08-01 01:51:21] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 8.119s
[2025-08-01 01:51:30] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 8.093s
[2025-08-01 01:51:38] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 8.095s
[2025-08-01 01:51:46] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 8.155s
[2025-08-01 01:51:54] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 8.167s
[2025-08-01 01:52:02] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 8.101s
[2025-08-01 01:52:10] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 8.105s
[2025-08-01 01:52:18] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 8.147s
[2025-08-01 01:52:26] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 8.103s
[2025-08-01 01:52:35] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 8.167s
[2025-08-01 01:52:43] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 8.141s
[2025-08-01 01:52:51] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 8.100s
[2025-08-01 01:52:59] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 8.140s
[2025-08-01 01:53:07] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 8.103s
[2025-08-01 01:53:15] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 8.102s
[2025-08-01 01:53:23] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 8.150s
[2025-08-01 01:53:31] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 8.103s
[2025-08-01 01:53:40] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 8.153s
[2025-08-01 01:53:48] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 8.139s
[2025-08-01 01:53:56] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 8.137s
[2025-08-01 01:54:04] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 8.154s
[2025-08-01 01:54:12] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 8.121s
[2025-08-01 01:54:20] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 8.103s
[2025-08-01 01:54:28] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 8.145s
[2025-08-01 01:54:37] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 8.106s
[2025-08-01 01:54:45] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 8.103s
[2025-08-01 01:54:53] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 8.125s
[2025-08-01 01:55:01] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 8.155s
[2025-08-01 01:55:09] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 8.104s
[2025-08-01 01:55:17] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 8.147s
[2025-08-01 01:55:25] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 8.125s
[2025-08-01 01:55:33] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 8.113s
[2025-08-01 01:55:42] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 8.168s
[2025-08-01 01:55:50] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 8.106s
[2025-08-01 01:55:58] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 8.132s
[2025-08-01 01:56:06] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 8.129s
[2025-08-01 01:56:14] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 8.104s
[2025-08-01 01:56:14] 自旋相关函数计算完成,总耗时 529.10 秒
[2025-08-01 01:56:14] 计算傅里叶变换...
[2025-08-01 01:56:15] 自旋结构因子计算完成
[2025-08-01 01:56:16] 自旋相关函数平均误差: 0.000666
