[2025-07-31 16:25:00] ✓ 从checkpoint恢复: results/L=4/J2=0.00/J1=0.04/training/checkpoints/final_GCNN.pkl
[2025-07-31 16:25:00]   - 迭代次数: final
[2025-07-31 16:25:00]   - 能量: -54.663975-0.000269j ± 0.043090
[2025-07-31 16:25:00]   - 时间戳: 2025-07-30T17:44:56.179657
[2025-07-31 16:25:08] ✓ 变分状态参数已从checkpoint恢复
[2025-07-31 16:25:08] ✓ 从final状态恢复, 重置迭代计数为0
[2025-07-31 16:25:08] ==================================================
[2025-07-31 16:25:08] GCNN for Shastry-Sutherland Model
[2025-07-31 16:25:08] ==================================================
[2025-07-31 16:25:08] System parameters:
[2025-07-31 16:25:08]   - System size: L=4, N=64
[2025-07-31 16:25:08]   - System parameters: J1=0.03, J2=0.0, Q=1.0
[2025-07-31 16:25:08] --------------------------------------------------
[2025-07-31 16:25:08] Model parameters:
[2025-07-31 16:25:08]   - Number of layers = 4
[2025-07-31 16:25:08]   - Number of features = 4
[2025-07-31 16:25:08]   - Total parameters = 12572
[2025-07-31 16:25:08] --------------------------------------------------
[2025-07-31 16:25:08] Training parameters:
[2025-07-31 16:25:08]   - Learning rate: 0.015
[2025-07-31 16:25:08]   - Total iterations: 1050
[2025-07-31 16:25:08]   - Annealing cycles: 3
[2025-07-31 16:25:08]   - Initial period: 150
[2025-07-31 16:25:08]   - Period multiplier: 2.0
[2025-07-31 16:25:08]   - Temperature range: 0.0-1.0
[2025-07-31 16:25:08]   - Samples: 16384
[2025-07-31 16:25:08]   - Discarded samples: 0
[2025-07-31 16:25:08]   - Chunk size: 2048
[2025-07-31 16:25:08]   - Diagonal shift: 0.2
[2025-07-31 16:25:08]   - Gradient clipping: 1.0
[2025-07-31 16:25:08]   - Checkpoint enabled: interval=500
[2025-07-31 16:25:08]   - Checkpoint directory: results/L=4/J2=0.00/J1=0.03/training/checkpoints
[2025-07-31 16:25:08] --------------------------------------------------
[2025-07-31 16:25:08] Device status:
[2025-07-31 16:25:08]   - Devices model: A100
[2025-07-31 16:25:08]   - Number of devices: 1
[2025-07-31 16:25:08]   - Sharding: True
[2025-07-31 16:25:08] ============================================================
[2025-07-31 16:25:54] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -54.200499+0.002262j
[2025-07-31 16:26:27] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -54.270617-0.006706j
[2025-07-31 16:26:46] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -54.262059-0.005491j
[2025-07-31 16:27:05] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -54.259533-0.004685j
[2025-07-31 16:27:24] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -54.247390-0.003662j
[2025-07-31 16:27:43] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -54.257377-0.001027j
[2025-07-31 16:28:03] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -54.218383-0.002621j
[2025-07-31 16:28:22] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -54.243974-0.002701j
[2025-07-31 16:28:41] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -54.246820-0.000872j
[2025-07-31 16:29:00] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -54.280810-0.002356j
[2025-07-31 16:29:19] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -54.231551-0.003069j
[2025-07-31 16:29:38] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -54.287572+0.000403j
[2025-07-31 16:29:57] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -54.226011-0.000709j
[2025-07-31 16:30:17] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -54.266395-0.001575j
[2025-07-31 16:30:36] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -54.282592+0.000412j
[2025-07-31 16:30:55] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -54.241883+0.000284j
[2025-07-31 16:31:14] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -54.227928-0.000248j
[2025-07-31 16:31:33] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -54.272222+0.002936j
[2025-07-31 16:31:52] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -54.235609+0.000740j
[2025-07-31 16:32:12] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -54.185359+0.000817j
[2025-07-31 16:32:31] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -54.182887-0.002661j
[2025-07-31 16:32:50] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -54.284023+0.000054j
[2025-07-31 16:33:09] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -54.323188+0.000659j
[2025-07-31 16:33:28] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -54.277139-0.002284j
[2025-07-31 16:33:47] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -54.284374-0.000391j
[2025-07-31 16:34:06] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -54.262973+0.000013j
[2025-07-31 16:34:25] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -54.265995-0.000466j
[2025-07-31 16:34:45] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -54.252669-0.000134j
[2025-07-31 16:35:04] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -54.221600-0.000347j
[2025-07-31 16:35:23] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -54.196874+0.000561j
[2025-07-31 16:35:42] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -54.177553-0.001104j
[2025-07-31 16:36:01] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -54.259786-0.001393j
[2025-07-31 16:36:20] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -54.178427+0.000310j
[2025-07-31 16:36:39] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -54.206302-0.000781j
[2025-07-31 16:36:59] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -54.213271-0.000164j
[2025-07-31 16:37:18] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -54.197871-0.000481j
[2025-07-31 16:37:37] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -54.156755+0.001117j
[2025-07-31 16:37:56] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -54.133698-0.001298j
[2025-07-31 16:38:15] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -54.176104-0.000264j
[2025-07-31 16:38:34] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -54.216216+0.000623j
[2025-07-31 16:38:54] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -54.168847+0.001331j
[2025-07-31 16:39:13] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -54.161846+0.000503j
[2025-07-31 16:39:32] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -54.196841+0.002494j
[2025-07-31 16:39:51] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -54.201725+0.002156j
[2025-07-31 16:40:10] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -54.231716-0.000646j
[2025-07-31 16:40:29] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -54.230284-0.000225j
[2025-07-31 16:40:48] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -54.168725-0.000139j
[2025-07-31 16:41:07] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -54.205971+0.000302j
[2025-07-31 16:41:27] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -54.269630-0.000277j
[2025-07-31 16:41:46] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -54.298112+0.000514j
[2025-07-31 16:42:05] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -54.291891-0.000333j
[2025-07-31 16:42:24] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -54.302335-0.000446j
[2025-07-31 16:42:43] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -54.307848-0.000035j
[2025-07-31 16:43:02] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -54.289956+0.000015j
[2025-07-31 16:43:21] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -54.308805-0.000703j
[2025-07-31 16:43:41] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -54.334414-0.000920j
[2025-07-31 16:44:00] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -54.311335-0.000874j
[2025-07-31 16:44:19] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -54.299638+0.000877j
[2025-07-31 16:44:38] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -54.365154+0.001001j
[2025-07-31 16:44:57] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -54.244905+0.000013j
[2025-07-31 16:45:16] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -54.269598+0.000150j
[2025-07-31 16:45:36] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -54.278713-0.000194j
[2025-07-31 16:45:55] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -54.292713-0.001269j
[2025-07-31 16:46:14] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -54.247143+0.002203j
[2025-07-31 16:46:33] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -54.209078+0.000929j
[2025-07-31 16:46:52] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -54.245646+0.001582j
[2025-07-31 16:47:11] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -54.226573+0.001787j
[2025-07-31 16:47:31] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -54.239571+0.001064j
[2025-07-31 16:47:50] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -54.304396+0.000590j
[2025-07-31 16:48:09] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -54.272529+0.000768j
[2025-07-31 16:48:28] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -54.285096+0.000231j
[2025-07-31 16:48:47] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -54.258417+0.002253j
[2025-07-31 16:49:06] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -54.303875+0.001025j
[2025-07-31 16:49:25] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -54.234541-0.001262j
[2025-07-31 16:49:44] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -54.238481-0.003304j
[2025-07-31 16:50:04] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -54.215993+0.000939j
[2025-07-31 16:50:23] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -54.196957-0.001111j
[2025-07-31 16:50:42] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -54.223531-0.001964j
[2025-07-31 16:51:01] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -54.226663+0.000059j
[2025-07-31 16:51:20] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -54.230888-0.001784j
[2025-07-31 16:51:39] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -54.255754-0.001033j
[2025-07-31 16:51:59] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -54.217466-0.000170j
[2025-07-31 16:52:18] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -54.238937+0.002178j
[2025-07-31 16:52:37] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -54.217154-0.000486j
[2025-07-31 16:52:56] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -54.269292+0.000104j
[2025-07-31 16:53:15] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -54.195570-0.000100j
[2025-07-31 16:53:34] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -54.307426-0.001288j
[2025-07-31 16:53:54] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -54.273477+0.002206j
[2025-07-31 16:54:13] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -54.278113-0.002200j
[2025-07-31 16:54:32] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -54.219521-0.000848j
[2025-07-31 16:54:51] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -54.306075-0.000157j
[2025-07-31 16:55:10] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -54.272355-0.000545j
[2025-07-31 16:55:29] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -54.238701+0.001385j
[2025-07-31 16:55:48] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -54.240259-0.000849j
[2025-07-31 16:56:08] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -54.247755+0.000365j
[2025-07-31 16:56:27] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -54.256782+0.000628j
[2025-07-31 16:56:46] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -54.209851-0.000040j
[2025-07-31 16:57:05] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -54.206792+0.000821j
[2025-07-31 16:57:24] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -54.239866+0.000655j
[2025-07-31 16:57:43] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -54.210678+0.000638j
[2025-07-31 16:58:02] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -54.201279-0.001532j
[2025-07-31 16:58:22] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -54.205058+0.000264j
[2025-07-31 16:58:41] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -54.222424+0.000013j
[2025-07-31 16:59:00] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -54.238187-0.000418j
[2025-07-31 16:59:19] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -54.209638-0.001429j
[2025-07-31 16:59:38] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -54.143162-0.001336j
[2025-07-31 16:59:57] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -54.197229-0.000441j
[2025-07-31 17:00:16] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -54.209290+0.000103j
[2025-07-31 17:00:36] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -54.246164-0.000314j
[2025-07-31 17:00:55] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -54.259696+0.001104j
[2025-07-31 17:01:14] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -54.236146-0.001236j
[2025-07-31 17:01:33] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -54.214406+0.002270j
[2025-07-31 17:01:52] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -54.176386-0.000366j
[2025-07-31 17:02:11] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -54.238754+0.000872j
[2025-07-31 17:02:30] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -54.245247+0.001917j
[2025-07-31 17:02:50] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -54.201666+0.001109j
[2025-07-31 17:03:09] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -54.210109-0.000605j
[2025-07-31 17:03:28] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -54.265526-0.001405j
[2025-07-31 17:03:47] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -54.211637-0.002770j
[2025-07-31 17:04:06] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -54.216342-0.000156j
[2025-07-31 17:04:25] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -54.235339+0.001662j
[2025-07-31 17:04:44] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -54.239880+0.001438j
[2025-07-31 17:05:04] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -54.223805-0.000334j
[2025-07-31 17:05:23] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -54.209669+0.000336j
[2025-07-31 17:05:42] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -54.185621-0.000164j
[2025-07-31 17:06:01] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -54.214562+0.000318j
[2025-07-31 17:06:20] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -54.205133+0.001485j
[2025-07-31 17:06:39] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -54.240645+0.000026j
[2025-07-31 17:06:58] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -54.205200+0.000376j
[2025-07-31 17:07:18] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -54.230427-0.000709j
[2025-07-31 17:07:37] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -54.242397-0.000769j
[2025-07-31 17:07:56] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -54.262654-0.000225j
[2025-07-31 17:08:15] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -54.269543-0.000714j
[2025-07-31 17:08:34] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -54.255381+0.000949j
[2025-07-31 17:08:53] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -54.230855+0.000403j
[2025-07-31 17:09:12] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -54.235373+0.000396j
[2025-07-31 17:09:32] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -54.290759-0.000591j
[2025-07-31 17:09:51] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -54.291137-0.000354j
[2025-07-31 17:10:10] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -54.260542+0.003924j
[2025-07-31 17:10:29] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -54.251822-0.001131j
[2025-07-31 17:10:48] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -54.238130-0.001076j
[2025-07-31 17:11:07] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -54.239325-0.001561j
[2025-07-31 17:11:26] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -54.225229-0.001528j
[2025-07-31 17:11:46] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -54.269908+0.000540j
[2025-07-31 17:12:05] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -54.268070+0.000458j
[2025-07-31 17:12:24] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -54.241277+0.000995j
[2025-07-31 17:12:43] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -54.213740+0.000590j
[2025-07-31 17:13:02] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -54.211093+0.000418j
[2025-07-31 17:13:21] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -54.188702-0.001434j
[2025-07-31 17:13:40] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -54.175090+0.000273j
[2025-07-31 17:13:40] RESTART #1 | Period: 300
[2025-07-31 17:14:00] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -54.169098-0.002398j
[2025-07-31 17:14:19] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -54.185486+0.003159j
[2025-07-31 17:14:38] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -54.228529+0.003249j
[2025-07-31 17:14:57] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -54.218012-0.003166j
[2025-07-31 17:15:16] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -54.214037-0.001369j
[2025-07-31 17:15:35] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -54.192254+0.000699j
[2025-07-31 17:15:54] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -54.207377-0.000273j
[2025-07-31 17:16:14] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -54.271777+0.000236j
[2025-07-31 17:16:33] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -54.286616+0.001147j
[2025-07-31 17:16:52] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -54.222940+0.001670j
[2025-07-31 17:17:11] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -54.275320+0.001064j
[2025-07-31 17:17:30] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -54.246897-0.000654j
[2025-07-31 17:17:49] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -54.260002-0.000637j
[2025-07-31 17:18:08] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -54.237342+0.000542j
[2025-07-31 17:18:27] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -54.293936+0.000732j
[2025-07-31 17:18:47] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -54.317610-0.000301j
[2025-07-31 17:19:06] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -54.268334-0.000247j
[2025-07-31 17:19:25] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -54.276921-0.001194j
[2025-07-31 17:19:44] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -54.327561+0.000597j
[2025-07-31 17:20:03] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -54.288839-0.000482j
[2025-07-31 17:20:22] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -54.243756-0.000671j
[2025-07-31 17:20:41] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -54.219945-0.000591j
[2025-07-31 17:21:01] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -54.232693+0.002524j
[2025-07-31 17:21:20] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -54.228700-0.000596j
[2025-07-31 17:21:39] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -54.210209-0.000776j
[2025-07-31 17:21:58] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -54.197711-0.000149j
[2025-07-31 17:22:17] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -54.200993+0.000114j
[2025-07-31 17:22:36] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -54.199045+0.000386j
[2025-07-31 17:22:56] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -54.166384+0.000367j
[2025-07-31 17:23:15] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -54.180476+0.003217j
[2025-07-31 17:23:34] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -54.200336-0.000140j
[2025-07-31 17:23:53] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -54.246086-0.002288j
[2025-07-31 17:24:12] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -54.178311-0.000140j
[2025-07-31 17:24:31] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -54.198467-0.000176j
[2025-07-31 17:24:50] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -54.228809-0.000653j
[2025-07-31 17:25:09] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -54.191371-0.000794j
[2025-07-31 17:25:29] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -54.185911+0.002414j
[2025-07-31 17:25:48] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -54.249576-0.001408j
[2025-07-31 17:26:07] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -54.212667+0.002885j
[2025-07-31 17:26:26] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -54.210855+0.000231j
[2025-07-31 17:26:45] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -54.265837-0.003161j
[2025-07-31 17:27:04] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -54.265282-0.001728j
[2025-07-31 17:27:24] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -54.215991+0.000426j
[2025-07-31 17:27:43] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -54.241138+0.000789j
[2025-07-31 17:28:02] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -54.262361-0.000860j
[2025-07-31 17:28:21] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -54.303444-0.001136j
[2025-07-31 17:28:40] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -54.285513-0.000214j
[2025-07-31 17:28:59] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -54.341573+0.001250j
[2025-07-31 17:29:18] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -54.309325-0.000494j
[2025-07-31 17:29:38] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -54.307136+0.000563j
[2025-07-31 17:29:57] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -54.325582+0.000972j
[2025-07-31 17:30:16] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -54.327120+0.000303j
[2025-07-31 17:30:35] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -54.260618+0.003201j
[2025-07-31 17:30:54] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -54.298998+0.001306j
[2025-07-31 17:31:13] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -54.274015+0.001739j
[2025-07-31 17:31:32] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -54.249936+0.000459j
[2025-07-31 17:31:52] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -54.243497+0.001261j
[2025-07-31 17:32:11] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -54.251495+0.003187j
[2025-07-31 17:32:30] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -54.247952-0.000910j
[2025-07-31 17:32:49] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -54.256293+0.001843j
[2025-07-31 17:33:08] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -54.193157-0.000597j
[2025-07-31 17:33:27] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -54.190974+0.000647j
[2025-07-31 17:33:46] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -54.205687-0.000502j
[2025-07-31 17:34:06] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -54.246663-0.000957j
[2025-07-31 17:34:25] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -54.213337-0.002755j
[2025-07-31 17:34:44] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -54.193592-0.000253j
[2025-07-31 17:35:03] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -54.198370+0.000416j
[2025-07-31 17:35:22] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -54.238193-0.003723j
[2025-07-31 17:35:41] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -54.261732+0.001076j
[2025-07-31 17:36:00] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -54.213900-0.001439j
[2025-07-31 17:36:20] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -54.197696+0.001334j
[2025-07-31 17:36:39] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -54.213437+0.000804j
[2025-07-31 17:36:58] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -54.206195-0.000066j
[2025-07-31 17:37:17] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -54.201038+0.001588j
[2025-07-31 17:37:36] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -54.202670+0.000619j
[2025-07-31 17:37:55] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -54.269049+0.001769j
[2025-07-31 17:38:14] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -54.202220+0.000874j
[2025-07-31 17:38:34] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -54.198907+0.001354j
[2025-07-31 17:38:53] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -54.237727+0.000629j
[2025-07-31 17:39:12] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -54.256584+0.001327j
[2025-07-31 17:39:31] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -54.273340+0.001106j
[2025-07-31 17:39:50] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -54.253292+0.001263j
[2025-07-31 17:40:09] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -54.212858-0.001274j
[2025-07-31 17:40:28] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -54.260681-0.001166j
[2025-07-31 17:40:48] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -54.300398-0.000041j
[2025-07-31 17:41:07] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -54.286847+0.000644j
[2025-07-31 17:41:26] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -54.291819-0.001184j
[2025-07-31 17:41:45] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -54.275187-0.000839j
[2025-07-31 17:42:04] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -54.247046-0.000988j
[2025-07-31 17:42:23] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -54.237170+0.000651j
[2025-07-31 17:42:42] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -54.236870+0.000325j
[2025-07-31 17:43:02] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -54.278481+0.000851j
[2025-07-31 17:43:21] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -54.232784+0.005543j
[2025-07-31 17:43:40] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -54.206012-0.000995j
[2025-07-31 17:43:59] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -54.202462+0.001320j
[2025-07-31 17:44:18] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -54.221424-0.000340j
[2025-07-31 17:44:37] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -54.185454+0.001069j
[2025-07-31 17:44:56] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -54.226058+0.000688j
[2025-07-31 17:45:15] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -54.233925+0.000834j
[2025-07-31 17:45:35] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -54.264341+0.000761j
[2025-07-31 17:45:54] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -54.254105-0.002369j
[2025-07-31 17:46:13] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -54.204590-0.002795j
[2025-07-31 17:46:32] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -54.201955+0.000844j
[2025-07-31 17:46:51] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -54.198228-0.003151j
[2025-07-31 17:47:10] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -54.186052-0.000914j
[2025-07-31 17:47:29] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -54.151589+0.000249j
[2025-07-31 17:47:49] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -54.137858-0.002425j
[2025-07-31 17:48:08] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -54.141500-0.001666j
[2025-07-31 17:48:27] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -54.095579+0.001075j
[2025-07-31 17:48:46] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -54.131533+0.001947j
[2025-07-31 17:49:05] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -54.193603+0.000997j
[2025-07-31 17:49:24] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -54.111180-0.000922j
[2025-07-31 17:49:43] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -54.128519+0.000860j
[2025-07-31 17:50:03] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -54.140756+0.001562j
[2025-07-31 17:50:22] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -54.119710+0.000979j
[2025-07-31 17:50:41] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -54.135674+0.000653j
[2025-07-31 17:51:00] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -54.171766-0.000399j
[2025-07-31 17:51:19] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -54.155729+0.000512j
[2025-07-31 17:51:38] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -54.204921-0.000590j
[2025-07-31 17:51:57] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -54.230390-0.000014j
[2025-07-31 17:52:17] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -54.183413-0.001725j
[2025-07-31 17:52:36] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -54.137448-0.000227j
[2025-07-31 17:52:55] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -54.150720-0.000381j
[2025-07-31 17:53:14] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -54.177034+0.000892j
[2025-07-31 17:53:33] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -54.210593+0.000980j
[2025-07-31 17:53:52] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -54.262902+0.000881j
[2025-07-31 17:54:11] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -54.264408+0.000355j
[2025-07-31 17:54:31] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -54.308315+0.000451j
[2025-07-31 17:54:50] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -54.290456-0.001770j
[2025-07-31 17:55:09] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -54.284501+0.000967j
[2025-07-31 17:55:28] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -54.284130+0.001129j
[2025-07-31 17:55:47] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -54.257957+0.000617j
[2025-07-31 17:56:06] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -54.275845-0.000338j
[2025-07-31 17:56:25] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -54.286213+0.001161j
[2025-07-31 17:56:45] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -54.265153+0.001911j
[2025-07-31 17:57:04] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -54.210256+0.003292j
[2025-07-31 17:57:23] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -54.263948-0.000476j
[2025-07-31 17:57:42] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -54.249547-0.000046j
[2025-07-31 17:58:01] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -54.208541+0.001816j
[2025-07-31 17:58:20] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -54.218896-0.001571j
[2025-07-31 17:58:39] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -54.202995-0.000200j
[2025-07-31 17:58:59] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -54.179804+0.000235j
[2025-07-31 17:59:18] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -54.194086-0.000895j
[2025-07-31 17:59:37] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -54.239257+0.000069j
[2025-07-31 17:59:56] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -54.230139+0.000430j
[2025-07-31 18:00:15] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -54.212446+0.000837j
[2025-07-31 18:00:34] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -54.217302+0.000347j
[2025-07-31 18:00:54] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -54.242153+0.001944j
[2025-07-31 18:01:13] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -54.290250+0.000342j
[2025-07-31 18:01:32] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -54.224420+0.001002j
[2025-07-31 18:01:51] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -54.213267-0.000133j
[2025-07-31 18:02:10] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -54.283033-0.000451j
[2025-07-31 18:02:29] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -54.300873+0.001553j
[2025-07-31 18:02:48] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -54.236157+0.001243j
[2025-07-31 18:03:08] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -54.259382+0.000963j
[2025-07-31 18:03:27] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -54.247316-0.001275j
[2025-07-31 18:03:46] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -54.230577-0.000939j
[2025-07-31 18:04:05] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -54.232803-0.001472j
[2025-07-31 18:04:24] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -54.253471-0.001025j
[2025-07-31 18:04:43] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -54.244973+0.002590j
[2025-07-31 18:05:02] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -54.270393+0.003060j
[2025-07-31 18:05:21] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -54.325666+0.003806j
[2025-07-31 18:05:41] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -54.321631+0.003566j
[2025-07-31 18:06:00] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -54.315471+0.000022j
[2025-07-31 18:06:19] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -54.346237+0.000245j
[2025-07-31 18:06:38] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -54.327460+0.002467j
[2025-07-31 18:06:57] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -54.307675-0.001461j
[2025-07-31 18:07:16] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -54.280229-0.000561j
[2025-07-31 18:07:36] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -54.319661-0.000889j
[2025-07-31 18:07:55] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -54.283834-0.000508j
[2025-07-31 18:08:14] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -54.235744-0.000445j
[2025-07-31 18:08:33] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -54.263683-0.001344j
[2025-07-31 18:08:52] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -54.211362-0.001288j
[2025-07-31 18:09:11] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -54.276194-0.001239j
[2025-07-31 18:09:30] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -54.314358-0.003122j
[2025-07-31 18:09:50] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -54.259223-0.001143j
[2025-07-31 18:10:09] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -54.266938+0.001340j
[2025-07-31 18:10:28] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -54.290995-0.000273j
[2025-07-31 18:10:47] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -54.287803+0.000621j
[2025-07-31 18:11:06] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -54.305865-0.001973j
[2025-07-31 18:11:25] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -54.300134+0.002467j
[2025-07-31 18:11:44] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -54.268283+0.002152j
[2025-07-31 18:12:04] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -54.230588-0.000126j
[2025-07-31 18:12:23] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -54.238613-0.001100j
[2025-07-31 18:12:42] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -54.293287-0.001707j
[2025-07-31 18:13:01] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -54.235198-0.000506j
[2025-07-31 18:13:20] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -54.242065-0.001371j
[2025-07-31 18:13:39] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -54.271978-0.001463j
[2025-07-31 18:13:58] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -54.300284-0.001665j
[2025-07-31 18:14:18] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -54.247146-0.002032j
[2025-07-31 18:14:37] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -54.270514+0.002905j
[2025-07-31 18:14:56] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -54.301104+0.001300j
[2025-07-31 18:15:15] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -54.289557-0.000762j
[2025-07-31 18:15:34] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -54.248127+0.000010j
[2025-07-31 18:15:53] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -54.238564+0.000852j
[2025-07-31 18:16:12] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -54.244208+0.002602j
[2025-07-31 18:16:32] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -54.281258+0.001590j
[2025-07-31 18:16:51] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -54.284495-0.000446j
[2025-07-31 18:17:10] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -54.273435-0.000280j
[2025-07-31 18:17:29] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -54.339506+0.000052j
[2025-07-31 18:17:48] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -54.263675-0.001249j
[2025-07-31 18:18:07] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -54.241433+0.000344j
[2025-07-31 18:18:26] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -54.268345+0.000927j
[2025-07-31 18:18:46] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -54.306191-0.001637j
[2025-07-31 18:19:05] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -54.311233-0.000961j
[2025-07-31 18:19:24] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -54.286364+0.000068j
[2025-07-31 18:19:43] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -54.299491-0.002386j
[2025-07-31 18:20:02] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -54.353345-0.000141j
[2025-07-31 18:20:21] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -54.327702+0.000329j
[2025-07-31 18:20:40] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -54.281074-0.001178j
[2025-07-31 18:21:00] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -54.250812+0.000418j
[2025-07-31 18:21:19] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -54.232649+0.000657j
[2025-07-31 18:21:38] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -54.261360-0.000627j
[2025-07-31 18:21:57] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -54.235320+0.000283j
[2025-07-31 18:22:16] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -54.271799-0.001884j
[2025-07-31 18:22:35] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -54.260623-0.002025j
[2025-07-31 18:22:54] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -54.222363-0.004525j
[2025-07-31 18:23:14] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -54.274029+0.000222j
[2025-07-31 18:23:33] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -54.297288+0.000056j
[2025-07-31 18:23:52] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -54.248033-0.002320j
[2025-07-31 18:24:11] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -54.263519+0.001306j
[2025-07-31 18:24:30] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -54.301797+0.000022j
[2025-07-31 18:24:49] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -54.318777+0.002132j
[2025-07-31 18:25:08] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -54.293423-0.000275j
[2025-07-31 18:25:27] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -54.286197-0.000028j
[2025-07-31 18:25:47] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -54.339400-0.001737j
[2025-07-31 18:26:06] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -54.310714-0.000646j
[2025-07-31 18:26:25] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -54.292244+0.000094j
[2025-07-31 18:26:44] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -54.240322+0.001032j
[2025-07-31 18:27:03] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -54.228569+0.000989j
[2025-07-31 18:27:22] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -54.239449+0.000415j
[2025-07-31 18:27:42] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -54.299527-0.000982j
[2025-07-31 18:28:01] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -54.294657-0.000670j
[2025-07-31 18:28:20] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -54.306793+0.001161j
[2025-07-31 18:28:39] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -54.266628+0.000157j
[2025-07-31 18:28:58] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -54.224988+0.001014j
[2025-07-31 18:29:17] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -54.272474-0.001571j
[2025-07-31 18:29:36] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -54.235849-0.000129j
[2025-07-31 18:29:55] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -54.229431-0.001004j
[2025-07-31 18:30:15] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -54.292106-0.002270j
[2025-07-31 18:30:34] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -54.189916+0.000621j
[2025-07-31 18:30:53] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -54.181898-0.001732j
[2025-07-31 18:31:12] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -54.263514-0.000717j
[2025-07-31 18:31:31] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -54.258305+0.000074j
[2025-07-31 18:31:50] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -54.213820-0.002143j
[2025-07-31 18:32:09] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -54.259497-0.000295j
[2025-07-31 18:32:29] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -54.229834-0.002264j
[2025-07-31 18:32:48] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -54.264388-0.000446j
[2025-07-31 18:33:07] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -54.254271-0.001542j
[2025-07-31 18:33:26] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -54.235320-0.001596j
[2025-07-31 18:33:45] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -54.279119+0.000754j
[2025-07-31 18:34:04] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -54.284617+0.001172j
[2025-07-31 18:34:24] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -54.277044+0.001296j
[2025-07-31 18:34:43] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -54.257391+0.000995j
[2025-07-31 18:35:02] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -54.215787+0.000220j
[2025-07-31 18:35:21] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -54.185587+0.002152j
[2025-07-31 18:35:40] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -54.222756+0.002027j
[2025-07-31 18:35:59] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -54.147373-0.001304j
[2025-07-31 18:36:18] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -54.165562-0.000667j
[2025-07-31 18:36:38] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -54.147844-0.000144j
[2025-07-31 18:36:57] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -54.141093+0.000174j
[2025-07-31 18:37:16] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -54.142650-0.004769j
[2025-07-31 18:37:35] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -54.160302+0.001787j
[2025-07-31 18:37:54] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -54.220260-0.001887j
[2025-07-31 18:38:13] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -54.140263-0.000374j
[2025-07-31 18:38:32] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -54.156154+0.000928j
[2025-07-31 18:38:52] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -54.165586-0.000218j
[2025-07-31 18:39:11] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -54.200051-0.000493j
[2025-07-31 18:39:30] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -54.187281-0.000613j
[2025-07-31 18:39:49] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -54.219164-0.000882j
[2025-07-31 18:40:08] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -54.227558-0.000786j
[2025-07-31 18:40:27] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -54.245427-0.000002j
[2025-07-31 18:40:46] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -54.219578+0.000842j
[2025-07-31 18:41:06] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -54.199261+0.000597j
[2025-07-31 18:41:25] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -54.178557-0.001749j
[2025-07-31 18:41:44] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -54.204419+0.000259j
[2025-07-31 18:42:03] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -54.229316-0.001408j
[2025-07-31 18:42:22] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -54.238502-0.000478j
[2025-07-31 18:42:41] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -54.258764+0.001363j
[2025-07-31 18:43:00] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -54.218695+0.001187j
[2025-07-31 18:43:20] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -54.242033+0.001757j
[2025-07-31 18:43:39] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -54.237933-0.000817j
[2025-07-31 18:43:58] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -54.187031+0.001128j
[2025-07-31 18:44:17] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -54.226194-0.000835j
[2025-07-31 18:44:36] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -54.281628-0.001532j
[2025-07-31 18:44:55] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -54.307406-0.001641j
[2025-07-31 18:45:14] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -54.301694-0.001958j
[2025-07-31 18:45:34] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -54.297713+0.001126j
[2025-07-31 18:45:53] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -54.230054-0.002121j
[2025-07-31 18:46:12] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -54.262946-0.000030j
[2025-07-31 18:46:31] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -54.269827+0.000047j
[2025-07-31 18:46:50] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -54.252386-0.000480j
[2025-07-31 18:47:09] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -54.270012-0.001336j
[2025-07-31 18:47:28] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -54.232542-0.001779j
[2025-07-31 18:47:48] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -54.299838-0.000760j
[2025-07-31 18:48:07] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -54.321946-0.000118j
[2025-07-31 18:48:26] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -54.241019-0.001553j
[2025-07-31 18:48:45] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -54.223680+0.001602j
[2025-07-31 18:49:04] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -54.268358-0.000204j
[2025-07-31 18:49:23] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -54.259556+0.000172j
[2025-07-31 18:49:23] RESTART #2 | Period: 600
[2025-07-31 18:49:42] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -54.313281-0.003116j
[2025-07-31 18:50:02] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -54.250512-0.001400j
[2025-07-31 18:50:21] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -54.195238-0.000131j
[2025-07-31 18:50:40] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -54.244995+0.001315j
[2025-07-31 18:50:59] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -54.230450-0.000479j
[2025-07-31 18:51:18] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -54.295692+0.000217j
[2025-07-31 18:51:37] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -54.265259-0.001032j
[2025-07-31 18:51:56] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -54.258845-0.001345j
[2025-07-31 18:52:16] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -54.269460+0.000876j
[2025-07-31 18:52:35] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -54.245824+0.000238j
[2025-07-31 18:52:54] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -54.282100-0.000820j
[2025-07-31 18:53:13] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -54.297520+0.001757j
[2025-07-31 18:53:32] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -54.316919-0.004486j
[2025-07-31 18:53:51] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -54.322989+0.000656j
[2025-07-31 18:54:10] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -54.281481-0.000496j
[2025-07-31 18:54:30] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -54.280763+0.001458j
[2025-07-31 18:54:49] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -54.220600-0.001138j
[2025-07-31 18:55:08] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -54.220652-0.000469j
[2025-07-31 18:55:27] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -54.209178+0.000028j
[2025-07-31 18:55:46] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -54.223359+0.000357j
[2025-07-31 18:56:05] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -54.249365+0.002405j
[2025-07-31 18:56:24] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -54.186557+0.000196j
[2025-07-31 18:56:44] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -54.299900-0.002597j
[2025-07-31 18:57:03] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -54.237132+0.000367j
[2025-07-31 18:57:22] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -54.234987+0.000569j
[2025-07-31 18:57:41] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -54.277534-0.000969j
[2025-07-31 18:58:00] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -54.260469-0.002330j
[2025-07-31 18:58:19] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -54.278349-0.000132j
[2025-07-31 18:58:38] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -54.196431+0.000041j
[2025-07-31 18:58:58] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -54.163156-0.000187j
[2025-07-31 18:59:17] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -54.234340-0.000250j
[2025-07-31 18:59:36] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -54.238061+0.001509j
[2025-07-31 18:59:55] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -54.215735+0.001393j
[2025-07-31 19:00:14] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -54.283632+0.002267j
[2025-07-31 19:00:33] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -54.273086-0.000091j
[2025-07-31 19:00:52] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -54.293779+0.001093j
[2025-07-31 19:01:12] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -54.277560-0.001144j
[2025-07-31 19:01:31] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -54.286336+0.000238j
[2025-07-31 19:01:50] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -54.242805-0.001113j
[2025-07-31 19:02:09] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -54.274617+0.001160j
[2025-07-31 19:02:28] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -54.256674-0.000856j
[2025-07-31 19:02:47] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -54.241228+0.003559j
[2025-07-31 19:03:06] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -54.195987-0.000260j
[2025-07-31 19:03:26] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -54.237564+0.000289j
[2025-07-31 19:03:45] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -54.211288+0.000877j
[2025-07-31 19:04:04] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -54.241914+0.000672j
[2025-07-31 19:04:23] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -54.226323+0.002168j
[2025-07-31 19:04:42] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -54.216059+0.000929j
[2025-07-31 19:05:01] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -54.241715-0.000354j
[2025-07-31 19:05:20] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -54.260156-0.000391j
[2025-07-31 19:05:20] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-07-31 19:05:39] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -54.231721-0.000127j
[2025-07-31 19:05:59] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -54.241094+0.001182j
[2025-07-31 19:06:18] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -54.195632-0.000641j
[2025-07-31 19:06:37] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -54.197773-0.002737j
[2025-07-31 19:06:56] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -54.244502+0.000494j
[2025-07-31 19:07:15] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -54.202812-0.001441j
[2025-07-31 19:07:34] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -54.235112+0.000545j
[2025-07-31 19:07:54] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -54.159905-0.000157j
[2025-07-31 19:08:13] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -54.144492-0.001076j
[2025-07-31 19:08:32] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -54.208771+0.000858j
[2025-07-31 19:08:51] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -54.174843+0.000793j
[2025-07-31 19:09:10] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -54.201378-0.000951j
[2025-07-31 19:09:29] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -54.200776-0.000506j
[2025-07-31 19:09:48] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -54.219506-0.000739j
[2025-07-31 19:10:08] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -54.200515-0.001900j
[2025-07-31 19:10:27] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -54.177020+0.000328j
[2025-07-31 19:10:46] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -54.218710-0.001254j
[2025-07-31 19:11:05] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -54.235716-0.001024j
[2025-07-31 19:11:24] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -54.189352+0.000785j
[2025-07-31 19:11:43] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -54.165003-0.001543j
[2025-07-31 19:12:02] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -54.249365-0.000854j
[2025-07-31 19:12:22] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -54.268091+0.001113j
[2025-07-31 19:12:41] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -54.213207+0.000583j
[2025-07-31 19:13:00] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -54.218330-0.001269j
[2025-07-31 19:13:19] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -54.194542-0.000968j
[2025-07-31 19:13:38] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -54.209640-0.001850j
[2025-07-31 19:13:57] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -54.198709-0.002055j
[2025-07-31 19:14:16] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -54.215635-0.001872j
[2025-07-31 19:14:36] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -54.187412+0.000525j
[2025-07-31 19:14:55] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -54.195430-0.001023j
[2025-07-31 19:15:14] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -54.243772-0.001106j
[2025-07-31 19:15:33] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -54.179613-0.001917j
[2025-07-31 19:15:52] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -54.192714-0.000254j
[2025-07-31 19:16:11] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -54.153677+0.000661j
[2025-07-31 19:16:30] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -54.183827-0.001696j
[2025-07-31 19:16:50] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -54.166084+0.000928j
[2025-07-31 19:17:09] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -54.168919-0.000505j
[2025-07-31 19:17:28] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -54.214373-0.000519j
[2025-07-31 19:17:47] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -54.247372-0.000589j
[2025-07-31 19:18:06] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -54.196219+0.000154j
[2025-07-31 19:18:25] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -54.199863-0.000585j
[2025-07-31 19:18:44] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -54.203423-0.001090j
[2025-07-31 19:19:03] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -54.161534+0.000104j
[2025-07-31 19:19:23] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -54.237603-0.002243j
[2025-07-31 19:19:42] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -54.214123-0.001020j
[2025-07-31 19:20:01] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -54.205241+0.000293j
[2025-07-31 19:20:20] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -54.193301-0.000747j
[2025-07-31 19:20:39] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -54.152609-0.000519j
[2025-07-31 19:20:58] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -54.211607-0.001512j
[2025-07-31 19:21:18] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -54.220032+0.002290j
[2025-07-31 19:21:37] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -54.205459+0.001951j
[2025-07-31 19:21:56] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -54.157535+0.001073j
[2025-07-31 19:22:15] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -54.214780-0.002348j
[2025-07-31 19:22:34] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -54.283723+0.001106j
[2025-07-31 19:22:53] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -54.226032-0.001939j
[2025-07-31 19:23:12] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -54.274448-0.001166j
[2025-07-31 19:23:32] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -54.188473-0.001402j
[2025-07-31 19:23:51] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -54.205624+0.000221j
[2025-07-31 19:24:10] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -54.240910+0.001054j
[2025-07-31 19:24:29] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -54.236952+0.000906j
[2025-07-31 19:24:48] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -54.230600-0.000471j
[2025-07-31 19:25:07] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -54.234403+0.001184j
[2025-07-31 19:25:26] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -54.234210-0.001783j
[2025-07-31 19:25:46] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -54.220182-0.000909j
[2025-07-31 19:26:05] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -54.235314-0.000837j
[2025-07-31 19:26:24] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -54.212223+0.000439j
[2025-07-31 19:26:43] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -54.220428-0.000995j
[2025-07-31 19:27:02] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -54.265682-0.000538j
[2025-07-31 19:27:21] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -54.286207+0.000690j
[2025-07-31 19:27:40] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -54.247113+0.002679j
[2025-07-31 19:28:00] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -54.185271-0.001067j
[2025-07-31 19:28:19] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -54.212286+0.001372j
[2025-07-31 19:28:38] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -54.199987-0.000182j
[2025-07-31 19:28:57] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -54.199785-0.000191j
[2025-07-31 19:29:16] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -54.152675-0.000816j
[2025-07-31 19:29:35] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -54.197340+0.007745j
[2025-07-31 19:29:54] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -54.203200-0.002302j
[2025-07-31 19:30:14] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -54.166130+0.000531j
[2025-07-31 19:30:33] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -54.194443-0.000516j
[2025-07-31 19:30:52] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -54.190825-0.003789j
[2025-07-31 19:31:11] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -54.191796+0.001310j
[2025-07-31 19:31:30] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -54.178968+0.000981j
[2025-07-31 19:31:49] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -54.180363-0.000082j
[2025-07-31 19:32:08] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -54.173842+0.000241j
[2025-07-31 19:32:27] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -54.214459+0.001275j
[2025-07-31 19:32:47] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -54.186357+0.001599j
[2025-07-31 19:33:06] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -54.192080+0.000630j
[2025-07-31 19:33:25] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -54.169437+0.001120j
[2025-07-31 19:33:44] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -54.164035+0.001085j
[2025-07-31 19:34:03] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -54.203563+0.002051j
[2025-07-31 19:34:22] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -54.262601-0.000329j
[2025-07-31 19:34:42] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -54.222793+0.002713j
[2025-07-31 19:35:01] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -54.273676+0.000724j
[2025-07-31 19:35:20] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -54.262566+0.000277j
[2025-07-31 19:35:39] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -54.274360-0.000151j
[2025-07-31 19:35:58] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -54.209778-0.000579j
[2025-07-31 19:36:17] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -54.234852+0.001074j
[2025-07-31 19:36:36] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -54.221437+0.000512j
[2025-07-31 19:36:56] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -54.203995-0.000719j
[2025-07-31 19:37:15] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -54.177088-0.001102j
[2025-07-31 19:37:34] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -54.177129-0.001778j
[2025-07-31 19:37:53] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -54.245846-0.000237j
[2025-07-31 19:38:12] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -54.290324-0.000925j
[2025-07-31 19:38:31] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -54.308816-0.000891j
[2025-07-31 19:38:50] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -54.258644-0.000458j
[2025-07-31 19:39:10] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -54.273046-0.001998j
[2025-07-31 19:39:29] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -54.201978+0.000546j
[2025-07-31 19:39:48] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -54.231912+0.001313j
[2025-07-31 19:40:07] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -54.229801-0.000681j
[2025-07-31 19:40:26] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -54.249629-0.000677j
[2025-07-31 19:40:45] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -54.252645+0.001704j
[2025-07-31 19:41:04] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -54.278388+0.001951j
[2025-07-31 19:41:24] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -54.244683-0.000730j
[2025-07-31 19:41:43] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -54.250227+0.000736j
[2025-07-31 19:42:02] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -54.245711+0.000149j
[2025-07-31 19:42:21] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -54.247853-0.001437j
[2025-07-31 19:42:40] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -54.234595-0.001061j
[2025-07-31 19:42:59] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -54.211998-0.001841j
[2025-07-31 19:43:18] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -54.192026+0.000607j
[2025-07-31 19:43:38] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -54.190377-0.000088j
[2025-07-31 19:43:57] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -54.226965-0.000176j
[2025-07-31 19:44:16] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -54.214162-0.001048j
[2025-07-31 19:44:35] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -54.237813+0.000261j
[2025-07-31 19:44:54] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -54.215096+0.001572j
[2025-07-31 19:45:13] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -54.193012+0.000467j
[2025-07-31 19:45:32] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -54.249413-0.000866j
[2025-07-31 19:45:52] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -54.258633+0.000999j
[2025-07-31 19:46:11] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -54.282493-0.000431j
[2025-07-31 19:46:30] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -54.237682-0.000192j
[2025-07-31 19:46:49] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -54.228677-0.000279j
[2025-07-31 19:47:08] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -54.232063+0.000358j
[2025-07-31 19:47:27] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -54.227387+0.000311j
[2025-07-31 19:47:46] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -54.132814-0.003584j
[2025-07-31 19:48:06] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -54.192272+0.000709j
[2025-07-31 19:48:25] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -54.153570+0.000947j
[2025-07-31 19:48:44] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -54.158148-0.000746j
[2025-07-31 19:49:03] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -54.195860+0.001388j
[2025-07-31 19:49:22] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -54.184045+0.000309j
[2025-07-31 19:49:41] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -54.238376-0.000633j
[2025-07-31 19:50:00] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -54.260340+0.000973j
[2025-07-31 19:50:20] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -54.309774-0.001202j
[2025-07-31 19:50:39] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -54.311451-0.001912j
[2025-07-31 19:50:58] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -54.273239-0.002020j
[2025-07-31 19:51:17] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -54.307356+0.001462j
[2025-07-31 19:51:36] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -54.254515+0.000077j
[2025-07-31 19:51:55] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -54.254989+0.000620j
[2025-07-31 19:52:14] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -54.245246-0.000216j
[2025-07-31 19:52:34] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -54.289284-0.000078j
[2025-07-31 19:52:53] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -54.213094+0.000425j
[2025-07-31 19:53:12] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -54.275719+0.001248j
[2025-07-31 19:53:31] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -54.223712-0.000322j
[2025-07-31 19:53:50] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -54.296747+0.000992j
[2025-07-31 19:54:09] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -54.248693+0.000365j
[2025-07-31 19:54:28] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -54.262379+0.002235j
[2025-07-31 19:54:48] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -54.204766-0.000332j
[2025-07-31 19:55:07] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -54.247828-0.000167j
[2025-07-31 19:55:26] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -54.267648-0.001126j
[2025-07-31 19:55:45] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -54.271226-0.000140j
[2025-07-31 19:56:04] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -54.335017+0.001719j
[2025-07-31 19:56:23] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -54.268001-0.000488j
[2025-07-31 19:56:42] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -54.244805+0.000182j
[2025-07-31 19:57:02] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -54.329376-0.000299j
[2025-07-31 19:57:21] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -54.283813-0.001460j
[2025-07-31 19:57:40] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -54.307771+0.000012j
[2025-07-31 19:57:59] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -54.238360+0.002628j
[2025-07-31 19:58:18] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -54.216980-0.000262j
[2025-07-31 19:58:37] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -54.276419+0.001865j
[2025-07-31 19:58:56] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -54.229104+0.000200j
[2025-07-31 19:59:16] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -54.228287+0.001470j
[2025-07-31 19:59:35] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -54.241183+0.000446j
[2025-07-31 19:59:54] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -54.253824+0.000531j
[2025-07-31 20:00:13] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -54.197045-0.001959j
[2025-07-31 20:00:32] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -54.218743-0.000103j
[2025-07-31 20:00:51] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -54.241124-0.001256j
[2025-07-31 20:01:10] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -54.225082-0.000047j
[2025-07-31 20:01:29] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -54.187306-0.000165j
[2025-07-31 20:01:49] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -54.198556+0.001076j
[2025-07-31 20:02:08] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -54.202679+0.001757j
[2025-07-31 20:02:27] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -54.205467-0.000500j
[2025-07-31 20:02:46] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -54.171352-0.000150j
[2025-07-31 20:03:05] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -54.256399-0.001936j
[2025-07-31 20:03:24] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -54.238744+0.000759j
[2025-07-31 20:03:44] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -54.241135-0.000137j
[2025-07-31 20:04:03] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -54.216929-0.000275j
[2025-07-31 20:04:22] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -54.237670-0.002337j
[2025-07-31 20:04:41] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -54.248483-0.000629j
[2025-07-31 20:05:00] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -54.185288-0.000166j
[2025-07-31 20:05:19] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -54.146566+0.001174j
[2025-07-31 20:05:38] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -54.189017-0.001032j
[2025-07-31 20:05:57] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -54.158941+0.000197j
[2025-07-31 20:06:17] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -54.163111+0.001266j
[2025-07-31 20:06:36] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -54.158561-0.000572j
[2025-07-31 20:06:55] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -54.179351-0.000007j
[2025-07-31 20:07:14] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -54.212975-0.000464j
[2025-07-31 20:07:33] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -54.210640-0.000997j
[2025-07-31 20:07:52] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -54.179882+0.000562j
[2025-07-31 20:08:11] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -54.222250+0.001276j
[2025-07-31 20:08:31] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -54.220864+0.001096j
[2025-07-31 20:08:50] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -54.260247+0.001590j
[2025-07-31 20:09:09] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -54.254141+0.000157j
[2025-07-31 20:09:28] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -54.243119-0.000244j
[2025-07-31 20:09:47] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -54.184208-0.001363j
[2025-07-31 20:10:06] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -54.193324+0.000428j
[2025-07-31 20:10:25] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -54.172883+0.001668j
[2025-07-31 20:10:45] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -54.121506+0.001723j
[2025-07-31 20:11:04] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -54.166600+0.001611j
[2025-07-31 20:11:23] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -54.213740+0.002619j
[2025-07-31 20:11:42] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -54.202238-0.000969j
[2025-07-31 20:12:01] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -54.212718+0.001262j
[2025-07-31 20:12:20] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -54.176591-0.000169j
[2025-07-31 20:12:39] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -54.208110-0.003417j
[2025-07-31 20:12:59] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -54.202865+0.000588j
[2025-07-31 20:13:18] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -54.178890+0.000306j
[2025-07-31 20:13:37] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -54.195411+0.000097j
[2025-07-31 20:13:56] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -54.209768+0.000723j
[2025-07-31 20:14:15] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -54.223090-0.000812j
[2025-07-31 20:14:34] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -54.196571-0.000794j
[2025-07-31 20:14:54] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -54.233233-0.001218j
[2025-07-31 20:15:13] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -54.243595+0.000392j
[2025-07-31 20:15:32] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -54.230221-0.000257j
[2025-07-31 20:15:51] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -54.262117-0.000478j
[2025-07-31 20:16:10] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -54.253827+0.001341j
[2025-07-31 20:16:29] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -54.255724+0.000467j
[2025-07-31 20:16:48] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -54.210446-0.000858j
[2025-07-31 20:17:07] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -54.194988+0.001594j
[2025-07-31 20:17:27] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -54.169447+0.000198j
[2025-07-31 20:17:46] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -54.184794-0.003498j
[2025-07-31 20:18:05] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -54.176258+0.001167j
[2025-07-31 20:18:24] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -54.158684-0.001858j
[2025-07-31 20:18:43] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -54.162148+0.000982j
[2025-07-31 20:19:02] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -54.151813+0.000424j
[2025-07-31 20:19:21] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -54.166640+0.003538j
[2025-07-31 20:19:41] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -54.217331-0.001628j
[2025-07-31 20:20:00] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -54.249637+0.000857j
[2025-07-31 20:20:19] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -54.255522+0.000250j
[2025-07-31 20:20:38] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -54.225252-0.000450j
[2025-07-31 20:20:57] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -54.158925+0.003381j
[2025-07-31 20:21:16] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -54.192829+0.001726j
[2025-07-31 20:21:35] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -54.175628-0.000029j
[2025-07-31 20:21:55] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -54.193867-0.000552j
[2025-07-31 20:22:14] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -54.180729+0.000707j
[2025-07-31 20:22:33] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -54.166920+0.001536j
[2025-07-31 20:22:52] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -54.171578-0.000776j
[2025-07-31 20:23:11] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -54.173135+0.003550j
[2025-07-31 20:23:30] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -54.139721-0.002212j
[2025-07-31 20:23:50] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -54.151328+0.000249j
[2025-07-31 20:24:09] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -54.198598+0.001030j
[2025-07-31 20:24:28] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -54.144173+0.000215j
[2025-07-31 20:24:47] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -54.202113-0.000384j
[2025-07-31 20:25:06] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -54.216992+0.002120j
[2025-07-31 20:25:25] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -54.211933-0.000778j
[2025-07-31 20:25:44] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -54.246005+0.000893j
[2025-07-31 20:26:04] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -54.186214+0.000257j
[2025-07-31 20:26:23] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -54.196632-0.001343j
[2025-07-31 20:26:42] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -54.248253-0.001444j
[2025-07-31 20:27:01] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -54.264305-0.002490j
[2025-07-31 20:27:20] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -54.234314-0.001803j
[2025-07-31 20:27:39] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -54.163691-0.001083j
[2025-07-31 20:27:58] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -54.179419+0.000791j
[2025-07-31 20:28:18] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -54.176612-0.000933j
[2025-07-31 20:28:37] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -54.172455-0.000513j
[2025-07-31 20:28:56] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -54.198033+0.002746j
[2025-07-31 20:29:15] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -54.226195-0.000899j
[2025-07-31 20:29:34] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -54.210514-0.000747j
[2025-07-31 20:29:53] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -54.190928-0.000677j
[2025-07-31 20:30:12] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -54.223729-0.000906j
[2025-07-31 20:30:32] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -54.241275+0.000528j
[2025-07-31 20:30:51] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -54.281154+0.000423j
[2025-07-31 20:31:10] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -54.253411-0.000883j
[2025-07-31 20:31:29] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -54.215768+0.001093j
[2025-07-31 20:31:48] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -54.246117-0.000824j
[2025-07-31 20:32:07] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -54.221652-0.001339j
[2025-07-31 20:32:26] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -54.199987-0.000070j
[2025-07-31 20:32:46] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -54.176991-0.000436j
[2025-07-31 20:33:05] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -54.206024+0.001166j
[2025-07-31 20:33:24] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -54.263430+0.002166j
[2025-07-31 20:33:43] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -54.220662+0.000576j
[2025-07-31 20:34:02] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -54.268025-0.000852j
[2025-07-31 20:34:21] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -54.205459+0.000964j
[2025-07-31 20:34:40] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -54.160791+0.000824j
[2025-07-31 20:35:00] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -54.216826+0.001095j
[2025-07-31 20:35:19] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -54.195413+0.001707j
[2025-07-31 20:35:38] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -54.147625+0.000345j
[2025-07-31 20:35:57] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -54.197421+0.000453j
[2025-07-31 20:36:16] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -54.191660-0.000272j
[2025-07-31 20:36:35] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -54.215013-0.000230j
[2025-07-31 20:36:54] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -54.252511-0.000532j
[2025-07-31 20:37:14] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -54.157493-0.003155j
[2025-07-31 20:37:33] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -54.135000-0.000568j
[2025-07-31 20:37:52] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -54.190926-0.002107j
[2025-07-31 20:38:11] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -54.227553-0.000973j
[2025-07-31 20:38:30] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -54.245543+0.000347j
[2025-07-31 20:38:49] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -54.244368-0.002868j
[2025-07-31 20:39:08] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -54.251093-0.000787j
[2025-07-31 20:39:27] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -54.203836-0.000117j
[2025-07-31 20:39:47] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -54.211837+0.001091j
[2025-07-31 20:40:06] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -54.147515-0.000057j
[2025-07-31 20:40:25] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -54.122663-0.000648j
[2025-07-31 20:40:44] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -54.167396-0.000397j
[2025-07-31 20:41:03] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -54.201432-0.001292j
[2025-07-31 20:41:22] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -54.196185+0.000163j
[2025-07-31 20:41:42] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -54.215863-0.000464j
[2025-07-31 20:42:01] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -54.209365+0.000593j
[2025-07-31 20:42:20] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -54.236366-0.001098j
[2025-07-31 20:42:39] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -54.216938+0.000733j
[2025-07-31 20:42:58] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -54.177693-0.000809j
[2025-07-31 20:43:17] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -54.173737-0.000543j
[2025-07-31 20:43:36] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -54.197091+0.000596j
[2025-07-31 20:43:56] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -54.151842-0.000967j
[2025-07-31 20:44:15] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -54.153012+0.001268j
[2025-07-31 20:44:34] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -54.140902-0.000181j
[2025-07-31 20:44:53] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -54.138224+0.000913j
[2025-07-31 20:45:12] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -54.091495-0.000211j
[2025-07-31 20:45:31] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -54.126599-0.000409j
[2025-07-31 20:45:50] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -54.159660+0.002039j
[2025-07-31 20:46:09] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -54.129246-0.001674j
[2025-07-31 20:46:29] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -54.162524+0.008993j
[2025-07-31 20:46:48] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -54.177512+0.001091j
[2025-07-31 20:47:07] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -54.097922-0.000402j
[2025-07-31 20:47:26] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -54.140814-0.001415j
[2025-07-31 20:47:45] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -54.198295-0.000729j
[2025-07-31 20:48:04] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -54.146632+0.000377j
[2025-07-31 20:48:24] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -54.170584-0.000428j
[2025-07-31 20:48:43] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -54.213519+0.000012j
[2025-07-31 20:49:02] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -54.223943-0.000358j
[2025-07-31 20:49:21] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -54.212694-0.000234j
[2025-07-31 20:49:40] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -54.195793+0.000188j
[2025-07-31 20:49:59] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -54.219716+0.002000j
[2025-07-31 20:50:18] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -54.219936+0.000799j
[2025-07-31 20:50:38] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -54.249022-0.000296j
[2025-07-31 20:50:57] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -54.267453-0.000115j
[2025-07-31 20:51:16] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -54.242552-0.000384j
[2025-07-31 20:51:35] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -54.252478-0.000830j
[2025-07-31 20:51:54] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -54.205147+0.000347j
[2025-07-31 20:52:13] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -54.248799-0.000846j
[2025-07-31 20:52:32] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -54.225690-0.000781j
[2025-07-31 20:52:51] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -54.249823-0.001220j
[2025-07-31 20:53:11] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -54.247849-0.001651j
[2025-07-31 20:53:30] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -54.195866+0.000096j
[2025-07-31 20:53:49] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -54.263729+0.000205j
[2025-07-31 20:54:08] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -54.229918+0.000067j
[2025-07-31 20:54:27] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -54.218039+0.000783j
[2025-07-31 20:54:46] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -54.233944-0.000996j
[2025-07-31 20:55:06] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -54.215540+0.000959j
[2025-07-31 20:55:25] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -54.230251-0.000180j
[2025-07-31 20:55:44] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -54.224816+0.000438j
[2025-07-31 20:56:03] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -54.239556+0.001371j
[2025-07-31 20:56:22] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -54.215672+0.002116j
[2025-07-31 20:56:41] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -54.286784-0.001286j
[2025-07-31 20:57:00] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -54.299852-0.001623j
[2025-07-31 20:57:20] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -54.291031+0.001439j
[2025-07-31 20:57:39] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -54.256138+0.000332j
[2025-07-31 20:57:58] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -54.233620-0.001934j
[2025-07-31 20:58:17] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -54.209362+0.000028j
[2025-07-31 20:58:36] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -54.205852-0.002618j
[2025-07-31 20:58:55] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -54.180125-0.000064j
[2025-07-31 20:59:14] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -54.163150+0.000249j
[2025-07-31 20:59:33] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -54.228343-0.000233j
[2025-07-31 20:59:53] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -54.218200-0.003777j
[2025-07-31 21:00:12] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -54.225524-0.001448j
[2025-07-31 21:00:31] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -54.201091-0.000809j
[2025-07-31 21:00:50] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -54.250621-0.001802j
[2025-07-31 21:01:09] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -54.222949+0.000505j
[2025-07-31 21:01:28] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -54.263470-0.001521j
[2025-07-31 21:01:48] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -54.281333-0.000329j
[2025-07-31 21:02:07] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -54.260876-0.000434j
[2025-07-31 21:02:26] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -54.240087-0.001436j
[2025-07-31 21:02:45] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -54.269680-0.000729j
[2025-07-31 21:03:04] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -54.300949-0.001855j
[2025-07-31 21:03:23] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -54.273185-0.001560j
[2025-07-31 21:03:42] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -54.262486+0.000852j
[2025-07-31 21:04:02] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -54.233765+0.001059j
[2025-07-31 21:04:21] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -54.311812+0.001335j
[2025-07-31 21:04:40] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -54.298005-0.001686j
[2025-07-31 21:04:59] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -54.277998+0.001810j
[2025-07-31 21:05:18] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -54.326593+0.001764j
[2025-07-31 21:05:37] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -54.292747-0.000424j
[2025-07-31 21:05:56] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -54.271759-0.001498j
[2025-07-31 21:06:16] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -54.343051-0.000163j
[2025-07-31 21:06:35] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -54.326996-0.000650j
[2025-07-31 21:06:54] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -54.342644+0.001138j
[2025-07-31 21:07:13] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -54.305663-0.000163j
[2025-07-31 21:07:32] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -54.261711+0.001466j
[2025-07-31 21:07:51] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -54.258454+0.002359j
[2025-07-31 21:08:10] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -54.263884-0.000249j
[2025-07-31 21:08:30] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -54.242769-0.001061j
[2025-07-31 21:08:49] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -54.248006-0.002807j
[2025-07-31 21:09:08] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -54.294339+0.000830j
[2025-07-31 21:09:27] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -54.320250-0.001424j
[2025-07-31 21:09:46] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -54.291334+0.000080j
[2025-07-31 21:10:05] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -54.338178+0.000297j
[2025-07-31 21:10:24] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -54.324568-0.002428j
[2025-07-31 21:10:44] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -54.279687-0.002109j
[2025-07-31 21:11:03] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -54.243093+0.001302j
[2025-07-31 21:11:22] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -54.229091-0.000675j
[2025-07-31 21:11:41] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -54.239587+0.000427j
[2025-07-31 21:12:00] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -54.246441+0.000973j
[2025-07-31 21:12:19] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -54.265534+0.004353j
[2025-07-31 21:12:38] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -54.335866+0.000013j
[2025-07-31 21:12:58] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -54.317895+0.000318j
[2025-07-31 21:13:17] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -54.326891-0.002191j
[2025-07-31 21:13:36] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -54.315557+0.000432j
[2025-07-31 21:13:55] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -54.334913-0.000530j
[2025-07-31 21:14:14] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -54.264206-0.001273j
[2025-07-31 21:14:33] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -54.265329+0.001491j
[2025-07-31 21:14:52] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -54.254163+0.000619j
[2025-07-31 21:15:11] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -54.275067-0.001530j
[2025-07-31 21:15:31] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -54.270043+0.001758j
[2025-07-31 21:15:50] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -54.290716+0.000710j
[2025-07-31 21:16:09] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -54.262878-0.001227j
[2025-07-31 21:16:28] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -54.217554+0.002085j
[2025-07-31 21:16:47] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -54.183081-0.000804j
[2025-07-31 21:17:06] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -54.235445+0.001837j
[2025-07-31 21:17:26] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -54.258426-0.001256j
[2025-07-31 21:17:45] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -54.237109+0.000286j
[2025-07-31 21:18:04] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -54.307911-0.000013j
[2025-07-31 21:18:23] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -54.297894-0.000278j
[2025-07-31 21:18:42] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -54.275066+0.000242j
[2025-07-31 21:19:01] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -54.293393+0.000658j
[2025-07-31 21:19:20] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -54.270988-0.000757j
[2025-07-31 21:19:39] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -54.262457-0.000790j
[2025-07-31 21:19:59] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -54.218641-0.001451j
[2025-07-31 21:20:18] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -54.243148-0.000119j
[2025-07-31 21:20:37] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -54.248886+0.000146j
[2025-07-31 21:20:56] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -54.257834+0.001267j
[2025-07-31 21:21:15] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -54.264322+0.000401j
[2025-07-31 21:21:34] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -54.281282+0.000047j
[2025-07-31 21:21:54] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -54.252219+0.000341j
[2025-07-31 21:22:13] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -54.193755-0.001619j
[2025-07-31 21:22:32] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -54.268530-0.000087j
[2025-07-31 21:22:51] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -54.222194+0.000326j
[2025-07-31 21:23:10] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -54.224551+0.001836j
[2025-07-31 21:23:29] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -54.230324+0.000003j
[2025-07-31 21:23:48] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -54.211275+0.000579j
[2025-07-31 21:24:08] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -54.236246+0.000673j
[2025-07-31 21:24:27] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -54.188953+0.002563j
[2025-07-31 21:24:46] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -54.191842-0.001399j
[2025-07-31 21:25:05] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -54.195284-0.002051j
[2025-07-31 21:25:24] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -54.240929+0.002220j
[2025-07-31 21:25:43] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -54.225902+0.001413j
[2025-07-31 21:26:02] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -54.207723+0.003871j
[2025-07-31 21:26:21] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -54.230728+0.001183j
[2025-07-31 21:26:41] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -54.251715-0.000412j
[2025-07-31 21:27:00] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -54.210785-0.000434j
[2025-07-31 21:27:19] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -54.227868+0.000980j
[2025-07-31 21:27:38] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -54.216191+0.000692j
[2025-07-31 21:27:57] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -54.203293+0.000557j
[2025-07-31 21:28:16] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -54.223242+0.000235j
[2025-07-31 21:28:35] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -54.169764+0.000336j
[2025-07-31 21:28:55] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -54.143209-0.000084j
[2025-07-31 21:29:14] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -54.229993-0.000998j
[2025-07-31 21:29:33] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -54.220821-0.001396j
[2025-07-31 21:29:52] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -54.210951-0.000815j
[2025-07-31 21:30:11] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -54.233617-0.002566j
[2025-07-31 21:30:30] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -54.262766-0.001452j
[2025-07-31 21:30:50] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -54.251430-0.003092j
[2025-07-31 21:31:09] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -54.234449-0.000361j
[2025-07-31 21:31:28] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -54.179757+0.000087j
[2025-07-31 21:31:47] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -54.259744-0.001232j
[2025-07-31 21:32:06] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -54.268239-0.000723j
[2025-07-31 21:32:25] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -54.259153+0.001594j
[2025-07-31 21:32:44] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -54.302485+0.000483j
[2025-07-31 21:33:04] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -54.267390+0.000310j
[2025-07-31 21:33:23] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -54.281757-0.001953j
[2025-07-31 21:33:42] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -54.269448+0.001249j
[2025-07-31 21:34:01] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -54.233582-0.001008j
[2025-07-31 21:34:20] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -54.248560-0.002204j
[2025-07-31 21:34:39] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -54.271497+0.002313j
[2025-07-31 21:34:58] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -54.238264+0.001421j
[2025-07-31 21:35:18] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -54.264420+0.000878j
[2025-07-31 21:35:37] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -54.188217-0.000300j
[2025-07-31 21:35:56] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -54.206853-0.001232j
[2025-07-31 21:36:15] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -54.208823-0.000855j
[2025-07-31 21:36:34] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -54.199242+0.000545j
[2025-07-31 21:36:53] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -54.223233-0.000410j
[2025-07-31 21:37:12] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -54.194313+0.000501j
[2025-07-31 21:37:32] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -54.246223-0.001495j
[2025-07-31 21:37:51] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -54.240887-0.000462j
[2025-07-31 21:38:10] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -54.201358-0.001831j
[2025-07-31 21:38:29] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -54.151728-0.000040j
[2025-07-31 21:38:48] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -54.191276-0.001399j
[2025-07-31 21:39:07] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -54.205105-0.000929j
[2025-07-31 21:39:26] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -54.187602-0.000913j
[2025-07-31 21:39:46] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -54.235011+0.001006j
[2025-07-31 21:40:05] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -54.149777-0.000408j
[2025-07-31 21:40:24] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -54.201767+0.000667j
[2025-07-31 21:40:43] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -54.225477-0.000732j
[2025-07-31 21:41:02] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -54.241436-0.000384j
[2025-07-31 21:41:21] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -54.186764-0.002175j
[2025-07-31 21:41:40] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -54.195374-0.000069j
[2025-07-31 21:42:00] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -54.184341+0.000548j
[2025-07-31 21:42:19] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -54.226240-0.000177j
[2025-07-31 21:42:38] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -54.233631+0.001466j
[2025-07-31 21:42:57] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -54.227228+0.000024j
[2025-07-31 21:43:16] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -54.241146-0.000870j
[2025-07-31 21:43:35] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -54.280020-0.000513j
[2025-07-31 21:43:54] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -54.265915-0.000448j
[2025-07-31 21:44:14] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -54.239345-0.001645j
[2025-07-31 21:44:33] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -54.249232+0.001739j
[2025-07-31 21:44:52] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -54.231258+0.000338j
[2025-07-31 21:44:52] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-07-31 21:45:11] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -54.278959-0.000386j
[2025-07-31 21:45:30] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -54.243200-0.000177j
[2025-07-31 21:45:49] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -54.278136+0.001309j
[2025-07-31 21:46:08] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -54.253425+0.000799j
[2025-07-31 21:46:27] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -54.239443-0.000641j
[2025-07-31 21:46:47] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -54.268124-0.001740j
[2025-07-31 21:47:06] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -54.232206-0.000313j
[2025-07-31 21:47:25] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -54.232552+0.000326j
[2025-07-31 21:47:44] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -54.204142+0.001451j
[2025-07-31 21:48:03] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -54.252857+0.000024j
[2025-07-31 21:48:22] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -54.226049+0.000497j
[2025-07-31 21:48:41] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -54.174904-0.000710j
[2025-07-31 21:49:01] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -54.181724-0.000367j
[2025-07-31 21:49:20] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -54.182358-0.000894j
[2025-07-31 21:49:39] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -54.174176-0.000850j
[2025-07-31 21:49:58] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -54.185366-0.000236j
[2025-07-31 21:50:17] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -54.204166-0.001382j
[2025-07-31 21:50:36] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -54.159371-0.000402j
[2025-07-31 21:50:55] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -54.234580+0.001456j
[2025-07-31 21:51:15] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -54.230324-0.000565j
[2025-07-31 21:51:34] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -54.200675+0.001203j
[2025-07-31 21:51:53] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -54.243314-0.002792j
[2025-07-31 21:52:12] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -54.103634-0.000141j
[2025-07-31 21:52:31] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -54.121616+0.000804j
[2025-07-31 21:52:50] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -54.184113+0.000717j
[2025-07-31 21:53:09] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -54.195536-0.000999j
[2025-07-31 21:53:29] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -54.226142-0.000664j
[2025-07-31 21:53:48] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -54.218509+0.000661j
[2025-07-31 21:54:07] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -54.193707-0.001109j
[2025-07-31 21:54:26] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -54.199412-0.000553j
[2025-07-31 21:54:45] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -54.160598+0.002102j
[2025-07-31 21:55:04] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -54.149922+0.001036j
[2025-07-31 21:55:24] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -54.138357-0.000389j
[2025-07-31 21:55:43] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -54.174058-0.000245j
[2025-07-31 21:56:02] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -54.250113-0.000925j
[2025-07-31 21:56:21] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -54.235922-0.000379j
[2025-07-31 21:56:40] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -54.197597+0.000400j
[2025-07-31 21:56:59] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -54.229826-0.001374j
[2025-07-31 21:57:18] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -54.248390-0.001271j
[2025-07-31 21:57:38] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -54.246700+0.001018j
[2025-07-31 21:57:57] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -54.264025+0.000379j
[2025-07-31 21:58:16] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -54.244923+0.001410j
[2025-07-31 21:58:35] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -54.203138-0.000550j
[2025-07-31 21:58:54] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -54.202628+0.000218j
[2025-07-31 21:59:13] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -54.144690-0.000855j
[2025-07-31 21:59:32] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -54.180418-0.001124j
[2025-07-31 21:59:52] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -54.237171+0.000804j
[2025-07-31 22:00:11] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -54.207106-0.001090j
[2025-07-31 22:00:30] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -54.187536+0.000878j
[2025-07-31 22:00:49] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -54.283451-0.000020j
[2025-07-31 22:00:49] ✅ Training completed | Restarts: 2
[2025-07-31 22:00:49] ============================================================
[2025-07-31 22:00:49] Training completed | Runtime: 20141.1s
[2025-07-31 22:01:13] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-07-31 22:01:13] ============================================================
